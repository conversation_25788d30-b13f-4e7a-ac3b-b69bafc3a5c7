
DROP SEQUENCE test_id_seq;

DROP
  TABLE test CASCADE CONSTRAINTS ;

CREATE SEQUENCE test_id_seq START WITH 1 INCREMENT BY 1 MAXVALUE 9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE TABLE test (
	test_id     NUMBER NOT NULL ,
	version     NUMBER NOT NULL ,
	updated     TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
	updated_by  NUMBER NOT NULL ,
	name        VARCHAR2 (256 CHAR) ,
	datetime    TIMESTAMP WITH TIME ZONE
);

ALTER TABLE test ADD CONSTRAINT test_PK PRIMARY KEY (test_id);
ALTER TABLE test ADD CONSTRAINT test_updated_by_FK FOREIGN KEY ( updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;


-- System update person is required for integration tests, and make sure
-- all sequences that are used in JUnit tests are set out of the range of
-- anything hard-coded in this file.
insert into PERSON (PERSON_ID, VERSION, UPDATED, UPDATED_BY, FIRST_NAME, LAST_NAME)
  values (1, 0, systimestamp, 1, 'System', 'Update');
drop sequence person_id_seq;
create sequence person_id_seq start with 1000 increment by 1 nomaxvalue;
drop sequence employee_id_seq;
create sequence employee_id_seq start with 1000 increment by 1 nomaxvalue;
drop sequence instructor_id_seq;
create sequence instructor_id_seq start with 1000 increment by 1 nomaxvalue;
drop sequence location_id_seq;
create sequence location_id_seq start with 1000 increment by 1 nomaxvalue;
drop sequence availability_id_seq;
create sequence availability_id_seq start with 1000 increment by 1 nomaxvalue;
drop sequence person_role_id_seq;
create sequence person_role_id_seq start with 1000 increment by 1 nomaxvalue;

insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (99,0,to_timestamp('11-APR-13 09.42.07.305720000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,null,'Site','Update','<EMAIL>','111-111-111');

insert into TEST (TEST_ID,VERSION,UPDATED,UPDATED_BY,NAME,DATETIME) values (1,9,to_timestamp('19-SEP-12 04.48.22.433037000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'Ricardo',to_timestamp_tz('29-OCT-12 01.54.45.284000000 AM +01:00','DD-MON-RR HH.MI.SS.FF AM TZR'));
insert into TEST (TEST_ID,VERSION,UPDATED,UPDATED_BY,NAME,DATETIME) values (2,5,to_timestamp('07-JUL-13 02.06.20.755315000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'Shannyn',to_timestamp_tz('29-OCT-12 01.54.45.284000000 AM +02:00','DD-MON-RR HH.MI.SS.FF AM TZR'));
insert into TEST (TEST_ID,VERSION,UPDATED,UPDATED_BY,NAME,DATETIME) values (3,7,to_timestamp('28-JUL-13 11.24.08.426312000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'Loretta',to_timestamp_tz('29-OCT-12 01.54.45.284000000 AM +03:00','DD-MON-RR HH.MI.SS.FF AM TZR'));
insert into TEST (TEST_ID,VERSION,UPDATED,UPDATED_BY,NAME,DATETIME) values (4,7,to_timestamp('22-OCT-12 06.33.37.603739000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'Brendan',to_timestamp_tz('29-OCT-12 01.54.45.284000000 AM +04:00','DD-MON-RR HH.MI.SS.FF AM TZR'));
insert into TEST (TEST_ID,VERSION,UPDATED,UPDATED_BY,NAME,DATETIME) values (5,7,to_timestamp('12-DEC-12 05.14.28.749900000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'Rachel',to_timestamp_tz('29-OCT-12 01.54.45.284000000 AM -01:00','DD-MON-RR HH.MI.SS.FF AM TZR'));
insert into TEST (TEST_ID,VERSION,UPDATED,UPDATED_BY,NAME,DATETIME) values (6,99,to_timestamp('28-JUN-13 02.47.43.470718000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'Merrilee',to_timestamp_tz('29-OCT-12 01.54.45.284000000 AM -02:00','DD-MON-RR HH.MI.SS.FF AM TZR'));
insert into TEST (TEST_ID,VERSION,UPDATED,UPDATED_BY,NAME,DATETIME) values (7,0,to_timestamp('26-JUL-13 05.29.18.493840000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'Joe',to_timestamp_tz('29-OCT-12 01.54.45.284000000 AM -03:00','DD-MON-RR HH.MI.SS.FF AM TZR'));
insert into TEST (TEST_ID,VERSION,UPDATED,UPDATED_BY,NAME,DATETIME) values (8,5,to_timestamp('14-JUL-13 01.26.20.242551000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'Fred',to_timestamp_tz('29-OCT-12 01.54.45.284000000 AM -04:00','DD-MON-RR HH.MI.SS.FF AM TZR'));
insert into TEST (TEST_ID,VERSION,UPDATED,UPDATED_BY,NAME,DATETIME) values (9,99,to_timestamp('27-FEB-13 08.49.40.311976000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'Michelle',to_timestamp_tz('29-OCT-12 01.54.45.284000000 AM +05:00','DD-MON-RR HH.MI.SS.FF AM TZR'));
insert into TEST (TEST_ID,VERSION,UPDATED,UPDATED_BY,NAME,DATETIME) values (10,0,to_timestamp('15-FEB-13 07.41.03.855116000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'Elijah',to_timestamp_tz('29-OCT-12 01.54.45.284000000 AM -05:00','DD-MON-RR HH.MI.SS.FF AM TZR'));

alter sequence test_id_seq increment by 10;
select test_id_seq.nextval from dual;
alter sequence test_id_seq increment by 1;

insert into SITE (SITE_ID,EXTERNAL_ID,URL,VERSION,UPDATED,UPDATED_BY,NAME) values (100,'9917','http://Seann.org',99,to_timestamp('13-DEC-12 03.15.53.767390000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'Tilson Landscape');
insert into SITE (SITE_ID,EXTERNAL_ID,URL,VERSION,UPDATED,UPDATED_BY,NAME) values (101,'5108','http://Alannah.com',99,to_timestamp('18-JUL-13 01.16.54.466469000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'Typhoon');
insert into SITE (SITE_ID,EXTERNAL_ID,URL,VERSION,UPDATED,UPDATED_BY,NAME) values (102,'8525',null,0,to_timestamp('29-JAN-13 10.33.01.983500000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'Alden Systems');
insert into SITE (SITE_ID,EXTERNAL_ID,URL,VERSION,UPDATED,UPDATED_BY,NAME) values (103,null,'http://Rosco.org',0,to_timestamp('26-FEB-13 02.03.04.156280000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'Employer Services');
insert into SITE (SITE_ID,EXTERNAL_ID,URL,VERSION,UPDATED,UPDATED_BY,NAME) values (104,'7145','http://Noah.com',0,to_timestamp('09-FEB-13 07.38.29.800199000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'Anworth Mortgage');
insert into SITE (SITE_ID,EXTERNAL_ID,URL,VERSION,UPDATED,UPDATED_BY,NAME) values (105,'0176',null,99,to_timestamp('26-NOV-12 08.34.45.226508000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'Acsis');
insert into SITE (SITE_ID,EXTERNAL_ID,URL,VERSION,UPDATED,UPDATED_BY,NAME) values (106,'0100','http://Nile.com',99,to_timestamp('20-SEP-12 02.05.43.272169000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'General Mills');
insert into SITE (SITE_ID,EXTERNAL_ID,URL,VERSION,UPDATED,UPDATED_BY,NAME) values (107,'9720','http://Kenny.org',99,to_timestamp('29-APR-13 11.30.24.699561000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'Outsource Group');
insert into SITE (SITE_ID,EXTERNAL_ID,URL,VERSION,UPDATED,UPDATED_BY,NAME) values (108,null,'http://Jamie.com',99,to_timestamp('05-NOV-12 11.38.45.148509000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'Cima Consulting Group');
insert into SITE (SITE_ID,EXTERNAL_ID,URL,VERSION,UPDATED,UPDATED_BY,NAME) values (109,'5486','http://Andrea.org',0,to_timestamp('05-NOV-12 02.47.54.415627000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'Electrical Solutions');

insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (100,0,to_timestamp('11-APR-13 09.42.07.305720000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'6684','Joan','Rowlands','<EMAIL>','797-956-729');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (101,0,to_timestamp('12-MAY-13 06.42.02.773301000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'6534','Billy','Leary','<EMAIL>','732-277-794');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (102,0,to_timestamp('07-NOV-12 01.42.04.195739000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'6012','Kelli','Ripley','<EMAIL>','653-181-485');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (103,0,to_timestamp('14-OCT-12 06.35.27.875250000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'4993','Selma','Feliciano','<EMAIL>','530-286-924');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (104,0,to_timestamp('22-MAY-13 03.23.53.382506000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'8389','Amanda','Kinnear','<EMAIL>','262-647-397');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (105,0,to_timestamp('10-MAY-13 09.24.10.402718000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'7804','Jude','Tate','<EMAIL>','498-441-682');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (106,0,to_timestamp('19-SEP-12 05.02.47.620480000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'6401','Mena','Hidalgo','<EMAIL>','321-597-027');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (107,0,to_timestamp('01-MAY-13 04.28.39.159597000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'0790','Maureen','Lattimore','<EMAIL>','204-343-293');
	
insert into CUSTOMER_STATUS (CUSTOMER_STATUS_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,STATUS_NAME) values (100,0,to_timestamp('27-NOV-12 04.15.41.517721000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,'status_4');
insert into CUSTOMER_STATUS (CUSTOMER_STATUS_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,STATUS_NAME) values (101,0,to_timestamp('20-JUL-13 09.36.00.141744000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,'status_2');
insert into CUSTOMER_STATUS (CUSTOMER_STATUS_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,STATUS_NAME) values (102,0,to_timestamp('23-NOV-12 03.46.23.422108000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,'status_2');
insert into CUSTOMER_STATUS (CUSTOMER_STATUS_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,STATUS_NAME) values (103,0,to_timestamp('14-DEC-12 03.32.42.879038000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,'status_4');
insert into CUSTOMER_STATUS (CUSTOMER_STATUS_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,STATUS_NAME) values (104,0,to_timestamp('29-APR-13 02.10.04.982914000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,'status_4');
insert into CUSTOMER_STATUS (CUSTOMER_STATUS_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,STATUS_NAME) values (105,0,to_timestamp('28-MAR-13 10.14.10.196574000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,'status_4');
insert into CUSTOMER_STATUS (CUSTOMER_STATUS_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,STATUS_NAME) values (106,0,to_timestamp('08-APR-13 11.47.57.296600000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,'status_3');
insert into CUSTOMER_STATUS (CUSTOMER_STATUS_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,STATUS_NAME) values (107,0,to_timestamp('04-MAY-13 09.09.33.339310000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,'status_2');


insert into CUSTOMER (CUSTOMER_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,CUSTOMER_STATUS_ID,EXTERNAL_ID) values (100,0,to_timestamp('07-MAY-13 06.08.28.619110000 PM','DD-MON-RR HH.MI.SSXFF AM'),100,99,100,100,'1387');
insert into CUSTOMER (CUSTOMER_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,CUSTOMER_STATUS_ID,EXTERNAL_ID) values (101,0,to_timestamp('17-NOV-12 11.03.10.743075000 PM','DD-MON-RR HH.MI.SSXFF AM'),100,99,101,101,'9976');
insert into CUSTOMER (CUSTOMER_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,CUSTOMER_STATUS_ID,EXTERNAL_ID) values (102,0,to_timestamp('24-OCT-12 08.41.44.683682000 AM','DD-MON-RR HH.MI.SSXFF AM'),100,99,102,102,'0649');
insert into CUSTOMER (CUSTOMER_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,CUSTOMER_STATUS_ID,EXTERNAL_ID) values (103,0,to_timestamp('08-JUN-13 10.16.30.847508000 AM','DD-MON-RR HH.MI.SSXFF AM'),100,99,103,103,'9180');
insert into CUSTOMER (CUSTOMER_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,CUSTOMER_STATUS_ID,EXTERNAL_ID) values (104,0,to_timestamp('04-JUN-13 04.47.42.621994000 PM','DD-MON-RR HH.MI.SSXFF AM'),100,99,104,104,'7191');
insert into CUSTOMER (CUSTOMER_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,CUSTOMER_STATUS_ID,EXTERNAL_ID) values (105,0,to_timestamp('28-JUL-13 01.30.21.139744000 PM','DD-MON-RR HH.MI.SSXFF AM'),100,99,105,105,'8588');
insert into CUSTOMER (CUSTOMER_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,CUSTOMER_STATUS_ID,EXTERNAL_ID) values (106,0,to_timestamp('08-MAY-13 11.30.09.120385000 PM','DD-MON-RR HH.MI.SSXFF AM'),100,99,106,106,'9818');
insert into CUSTOMER (CUSTOMER_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,CUSTOMER_STATUS_ID,EXTERNAL_ID) values (107,0,to_timestamp('30-SEP-12 11.26.09.579887000 AM','DD-MON-RR HH.MI.SSXFF AM'),100,99,107,107,'9826');

insert into LOCATION (LOCATION_ID,EXTERNAL_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,LOCATION_NAME,PROFILE_ID,ADDRESS_1,ADDRESS_2,CITY,STATE,ZIP,COUNTRY,PHONE,FAX) values (100,'5234',0,to_timestamp('19-DEC-12 06.13.36.139292000 AM','DD-MON-RR HH.MI.SSXFF AM'),100,99,'Auction Systems Auctioneers Appraisers',null,'24 Bob Road','Office 99','Woodland Hills','CA','33765','USA','366-520-579','406-658-954');
insert into LOCATION (LOCATION_ID,EXTERNAL_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,LOCATION_NAME,PROFILE_ID,ADDRESS_1,ADDRESS_2,CITY,STATE,ZIP,COUNTRY,PHONE,FAX) values (101,'6799',0,to_timestamp('15-OCT-12 04.23.51.764028000 PM','DD-MON-RR HH.MI.SSXFF AM'),100,99,'Accredited Home Lenders',null,'91st Street',null,'Farmington Hills','MI','33532','USA','768-449-028','676-101-579');
insert into LOCATION (LOCATION_ID,EXTERNAL_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,LOCATION_NAME,PROFILE_ID,ADDRESS_1,ADDRESS_2,CITY,STATE,ZIP,COUNTRY,PHONE,FAX) values (102,'1000',0,to_timestamp('13-AUG-13 02.00.10.125000000 PM','DD-MON-RR HH.MI.SSXFF AM'),100,99,'Matthew''s test studio',null,'123 Main St','Suite 1','Beverly Hills','CA','90210','USA','************','************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) values (100,0,to_timestamp('01-DEC-12 08.27.26.530756000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,to_timestamp_tz('29-OCT-12 01.54.45.284000000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 05.14.51.283916000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 01.07.57.640776000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 09.12.53.685872000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 08.41.43.387581000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 02.34.21.112713000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 11.31.31.100801000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 03.01.32.972945000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 06.28.04.844217000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 11.29.31.606933000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 01.05.13.551062000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 02.03.21.396336000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 10.09.24.812001000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 08.18.58.197143000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null);
insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) values (101,0,to_timestamp('01-JUN-13 02.43.22.761990000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,to_timestamp_tz('01-JUN-13 07.14.54.691593000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.26.20.538430000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 08.34.26.991536000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.42.23.180269000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 02.22.49.528360000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.15.58.936420000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.50.46.100982000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.55.50.903423000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.06.23.590326000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 01.53.54.687150000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 05.29.08.224554000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.50.11.681858000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.02.11.843844000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.07.23.842921000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null);

insert into LOCATION_PROFILE (PROFILE_ID,EXTERNAL_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ENABLED,TZ,AVAILABILITY_ID) values (100,null,0,to_timestamp('22-JAN-13 11.37.22.717004000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'Y','Asia/Chongqing',100);
insert into LOCATION_PROFILE (PROFILE_ID,EXTERNAL_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ENABLED,TZ,AVAILABILITY_ID) values (101,null,0,to_timestamp('22-JAN-13 11.37.22.717004000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'N','Asia/Chongqing',101);

update LOCATION t set t.PROFILE_ID = 100 where t.LOCATION_ID = 100;
update LOCATION t set t.PROFILE_ID = 101 where t.LOCATION_ID = 101;

insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (108,0,to_timestamp('13-NOV-12 10.32.20.507666000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'4332','Holly','Cazale','<EMAIL>','492-020-552');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (109,0,to_timestamp('12-JUN-13 07.58.06.369760000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'0772','Parker','Griffiths','<EMAIL>','077-856-443');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (110,0,to_timestamp('21-JAN-13 06.16.07.230551000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'4546','Pelvic','Rivers','<EMAIL>','566-830-226');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (111,0,to_timestamp('10-AUG-13 08.28.44.870685000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'1399','Jody','Albright','<EMAIL>','741-022-087');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (112,0,to_timestamp('02-SEP-12 02.52.04.141677000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'8100','April','Van Helden','<EMAIL>','390-530-943');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (113,0,to_timestamp('04-FEB-13 11.05.55.262792000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'5404','Crystal','Arkin','<EMAIL>','635-263-704');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (114,0,to_timestamp('21-APR-13 05.18.23.291331000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'0737','Alec','Cross','<EMAIL>','660-283-760');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (115,0,to_timestamp('20-NOV-12 06.56.19.922034000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'5708','Claude','Shalhoub','<EMAIL>','704-349-737');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (116,0,to_timestamp('29-AUG-12 04.00.56.436691000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'9274','Keith','Domino','<EMAIL>','416-314-662');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (117,0,to_timestamp('20-AUG-12 08.55.25.436343000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'2591','Jody','Richardson','<EMAIL>','891-470-525');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (118,0,to_timestamp('07-MAR-13 08.32.04.563220000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'9248','Ritchie','Farrell','<EMAIL>','117-406-596');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (119,0,to_timestamp('25-APR-13 10.56.34.527274000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'1843','Hilton','Gooding','<EMAIL>','058-714-755');

insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID) values (100,0,to_timestamp('21-JAN-13 10.26.32.250033000 PM','DD-MON-RR HH.MI.SSXFF AM'),100,99,108,'status2',100,null);
insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID) values (101,0,to_timestamp('31-AUG-12 03.35.52.246874000 PM','DD-MON-RR HH.MI.SSXFF AM'),100,99,109,'status4',100,null);
insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID) values (102,0,to_timestamp('26-MAY-13 07.28.06.755560000 AM','DD-MON-RR HH.MI.SSXFF AM'),100,99,110,'status3',100,null);
insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID) values (103,0,to_timestamp('29-OCT-12 03.28.17.790183000 PM','DD-MON-RR HH.MI.SSXFF AM'),100,99,111,'status3',100,null);
insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID) values (104,0,to_timestamp('01-NOV-12 09.37.50.438281000 AM','DD-MON-RR HH.MI.SSXFF AM'),100,99,112,'status2',100,null);
insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID) values (105,0,to_timestamp('09-SEP-12 03.55.30.635173000 AM','DD-MON-RR HH.MI.SSXFF AM'),100,99,113,'status6',101,null);
insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID) values (106,0,to_timestamp('05-NOV-12 10.35.09.446020000 PM','DD-MON-RR HH.MI.SSXFF AM'),100,99,114,'status4',101,null);
insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID) values (107,0,to_timestamp('05-AUG-13 04.22.23.224309000 PM','DD-MON-RR HH.MI.SSXFF AM'),100,99,115,'status4',101,null);
insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID) values (108,0,to_timestamp('17-DEC-12 04.39.05.401384000 PM','DD-MON-RR HH.MI.SSXFF AM'),100,99,116,'status4',101,null);
insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID) values (109,0,to_timestamp('26-MAY-13 10.28.32.413335000 PM','DD-MON-RR HH.MI.SSXFF AM'),100,99,117,'status6',102,null);
insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID) values (110,0,to_timestamp('05-MAY-13 11.28.05.279330000 AM','DD-MON-RR HH.MI.SSXFF AM'),100,99,118,'status3',102,null);
insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID) values (111,0,to_timestamp('22-OCT-12 09.31.51.688022000 AM','DD-MON-RR HH.MI.SSXFF AM'),100,99,119,'status2',102,null);

insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (120,0,to_timestamp('08-FEB-13 05.54.12.756731000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'5011','Fionnula','Prowse','<EMAIL>','************');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (121,0,to_timestamp('31-DEC-12 01.55.11.924921000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'4046','Anita','Blackwell','<EMAIL>','************');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (122,0,to_timestamp('11-JAN-13 06.25.24.597321000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'3541','Jodie','Pryce','<EMAIL>','************');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (123,0,to_timestamp('02-NOV-12 01.29.00.447599000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'8477','Pat','Eder','<EMAIL>','************');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (124,0,to_timestamp('16-SEP-12 01.40.50.980970000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'4097','Leelee','Winwood','<EMAIL>','************');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (125,0,to_timestamp('21-OCT-12 01.00.10.381807000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'9473','Jack','Woods','<EMAIL>','************');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (126,0,to_timestamp('06-SEP-12 03.26.52.740313000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,'3998','William','Vincent','<EMAIL>','************');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (127,0,to_timestamp('12-OCT-12 09.35.28.633866000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'6156','Jet','Sizemore','<EMAIL>','************');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (128,0,to_timestamp('07-MAR-13 03.28.14.285954000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'9804','Phoebe','Checker','<EMAIL>','************');
insert into PERSON (PERSON_ID,VERSION,UPDATED,UPDATED_BY,EXTERNAL_ID,FIRST_NAME,LAST_NAME,EMAIL,PHONE) values (129,0,to_timestamp('28-AUG-12 05.29.36.581320000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,'0692','Donna','Trevino','<EMAIL>','************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) values (102,0,to_timestamp('01-DEC-12 08.27.26.530756000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,to_timestamp_tz('29-OCT-12 01.54.45.284000000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 05.14.51.283916000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 01.07.57.640776000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 09.12.53.685872000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 08.41.43.387581000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 02.34.21.112713000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 11.31.31.100801000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 03.01.32.972945000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 06.28.04.844217000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 11.29.31.606933000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 01.05.13.551062000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 02.03.21.396336000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 10.09.24.812001000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 08.18.58.197143000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null);
insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) values (103,0,to_timestamp('01-JUN-13 02.43.22.761990000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,to_timestamp_tz('01-JUN-13 07.14.54.691593000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.26.20.538430000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 08.34.26.991536000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.42.23.180269000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 02.22.49.528360000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.15.58.936420000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.50.46.100982000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.55.50.903423000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.06.23.590326000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 01.53.54.687150000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 05.29.08.224554000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.50.11.681858000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.02.11.843844000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.07.23.842921000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null);
insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) values (104,0,to_timestamp('01-JUN-13 02.43.22.761990000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,to_timestamp_tz('01-JUN-13 07.14.54.691593000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.26.20.538430000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 08.34.26.991536000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.42.23.180269000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 02.22.49.528360000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.15.58.936420000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.50.46.100982000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.55.50.903423000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.06.23.590326000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 01.53.54.687150000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 05.29.08.224554000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.50.11.681858000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.02.11.843844000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.07.23.842921000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null);
insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) values (105,0,to_timestamp('01-JUN-13 02.43.22.761990000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,to_timestamp_tz('01-JUN-13 07.14.54.691593000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.26.20.538430000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 08.34.26.991536000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.42.23.180269000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 02.22.49.528360000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.15.58.936420000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.50.46.100982000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.55.50.903423000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.06.23.590326000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 01.53.54.687150000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 05.29.08.224554000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.50.11.681858000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.02.11.843844000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.07.23.842921000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null);
insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) values (106,0,to_timestamp('01-JUN-13 02.43.22.761990000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,to_timestamp_tz('01-JUN-13 07.14.54.691593000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.26.20.538430000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 08.34.26.991536000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.42.23.180269000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 02.22.49.528360000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.15.58.936420000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.50.46.100982000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.55.50.903423000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.06.23.590326000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 01.53.54.687150000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 05.29.08.224554000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.50.11.681858000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.02.11.843844000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.07.23.842921000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null);
insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) values (107,0,to_timestamp('01-JUN-13 02.43.22.761990000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,to_timestamp_tz('01-JUN-13 07.14.54.691593000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.26.20.538430000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 08.34.26.991536000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.42.23.180269000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 02.22.49.528360000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.15.58.936420000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.50.46.100982000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.55.50.903423000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.06.23.590326000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 01.53.54.687150000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 05.29.08.224554000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.50.11.681858000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.02.11.843844000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.07.23.842921000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null);
insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) values (108,0,to_timestamp('01-JUN-13 02.43.22.761990000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,to_timestamp_tz('01-JUN-13 07.14.54.691593000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.26.20.538430000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 08.34.26.991536000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.42.23.180269000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 02.22.49.528360000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.15.58.936420000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.50.46.100982000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.55.50.903423000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.06.23.590326000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 01.53.54.687150000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 05.29.08.224554000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.50.11.681858000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.02.11.843844000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.07.23.842921000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null);
insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) values (109,0,to_timestamp('01-JUN-13 02.43.22.761990000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,to_timestamp_tz('01-JUN-13 07.14.54.691593000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.26.20.538430000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 08.34.26.991536000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.42.23.180269000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 02.22.49.528360000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.15.58.936420000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.50.46.100982000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.55.50.903423000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.06.23.590326000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 01.53.54.687150000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 05.29.08.224554000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.50.11.681858000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.02.11.843844000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.07.23.842921000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null);
insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) values (110,0,to_timestamp('01-JUN-13 02.43.22.761990000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,to_timestamp_tz('01-JUN-13 07.14.54.691593000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.26.20.538430000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 08.34.26.991536000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.42.23.180269000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 02.22.49.528360000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.15.58.936420000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.50.46.100982000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.55.50.903423000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.06.23.590326000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 01.53.54.687150000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 05.29.08.224554000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.50.11.681858000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.02.11.843844000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.07.23.842921000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null);
insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) values (111,0,to_timestamp('01-JUN-13 02.43.22.761990000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,to_timestamp_tz('01-JUN-13 07.14.54.691593000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.26.20.538430000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 08.34.26.991536000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.42.23.180269000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 02.22.49.528360000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.15.58.936420000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 07.50.46.100982000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.55.50.903423000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.06.23.590326000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 01.53.54.687150000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 05.29.08.224554000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 03.50.11.681858000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 11.02.11.843844000 AM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-JUN-13 06.07.23.842921000 PM +08:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED) values (100,0,to_timestamp('23-OCT-12 06.48.52.313784000 AM','DD-MON-RR HH.MI.SSXFF AM'),100,99,'status3',120,100,102,null,'Y');
insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED) values (101,0,to_timestamp('17-OCT-12 07.49.06.493390000 AM','DD-MON-RR HH.MI.SSXFF AM'),100,99,'status2',121,100,103,null,'N');
insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED) values (102,0,to_timestamp('24-JUL-13 04.20.09.100394000 AM','DD-MON-RR HH.MI.SSXFF AM'),100,99,'status2',122,100,104,null,'Y');
insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED) values (103,0,to_timestamp('01-APR-13 09.26.29.789226000 AM','DD-MON-RR HH.MI.SSXFF AM'),100,99,'status1',123,101,105,null,'N');
insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED) values (104,0,to_timestamp('17-JAN-13 03.41.31.843900000 AM','DD-MON-RR HH.MI.SSXFF AM'),100,99,'status1',124,101,106,null,'Y');
insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED) values (105,0,to_timestamp('08-JAN-13 08.16.24.359009000 PM','DD-MON-RR HH.MI.SSXFF AM'),100,99,'status1',125,101,107,null,'N');
insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED) values (106,0,to_timestamp('16-JUL-13 03.52.18.359431000 PM','DD-MON-RR HH.MI.SSXFF AM'),100,99,'status3',126,102,108,null,'Y');
insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED) values (107,0,to_timestamp('29-DEC-12 01.44.49.778703000 AM','DD-MON-RR HH.MI.SSXFF AM'),100,99,'status2',127,102,109,null,'N');
insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED) values (108,0,to_timestamp('18-MAY-13 08.11.31.454770000 AM','DD-MON-RR HH.MI.SSXFF AM'),100,99,'status2',128,102,110,null,'Y');
insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED) values (109,0,to_timestamp('14-NOV-12 05.44.30.423925000 AM','DD-MON-RR HH.MI.SSXFF AM'),100,99,'status2',129,102,111,null,'Y');

insert into INSTRUMENT (INSTRUMENT_ID,VERSION,UPDATED,EXTERNAL_ID,INSTRUMENT_NAME,UPDATED_BY,SITE_ID) values (100,0,to_timestamp('03-AUG-13 02.35.13.474084000 AM','DD-MON-RR HH.MI.SSXFF AM'),null,'instrument_1',99,100);
insert into INSTRUMENT (INSTRUMENT_ID,VERSION,UPDATED,EXTERNAL_ID,INSTRUMENT_NAME,UPDATED_BY,SITE_ID) values (101,0,to_timestamp('15-JUN-13 11.46.12.350857000 PM','DD-MON-RR HH.MI.SSXFF AM'),null,'instrument_2',99,100);
insert into INSTRUMENT (INSTRUMENT_ID,VERSION,UPDATED,EXTERNAL_ID,INSTRUMENT_NAME,UPDATED_BY,SITE_ID) values (102,0,to_timestamp('07-JAN-13 09.53.53.820790000 AM','DD-MON-RR HH.MI.SSXFF AM'),null,'instrument_3',99,100);
insert into INSTRUMENT (INSTRUMENT_ID,VERSION,UPDATED,EXTERNAL_ID,INSTRUMENT_NAME,UPDATED_BY,SITE_ID) values (103,0,to_timestamp('01-MAY-13 04.01.01.938635000 PM','DD-MON-RR HH.MI.SSXFF AM'),null,'instrument_4',99,100);
insert into INSTRUMENT (INSTRUMENT_ID,VERSION,UPDATED,EXTERNAL_ID,INSTRUMENT_NAME,UPDATED_BY,SITE_ID) values (104,0,to_timestamp('06-JUL-13 06.40.08.414243000 PM','DD-MON-RR HH.MI.SSXFF AM'),null,'instrument_5',99,100);
insert into INSTRUMENT (INSTRUMENT_ID,VERSION,UPDATED,EXTERNAL_ID,INSTRUMENT_NAME,UPDATED_BY,SITE_ID) values (105,0,to_timestamp('26-DEC-12 08.08.21.716330000 AM','DD-MON-RR HH.MI.SSXFF AM'),null,'instrument_6',99,100);
insert into INSTRUMENT (INSTRUMENT_ID,VERSION,UPDATED,EXTERNAL_ID,INSTRUMENT_NAME,UPDATED_BY,SITE_ID) values (106,0,to_timestamp('31-DEC-12 05.38.58.748735000 PM','DD-MON-RR HH.MI.SSXFF AM'),null,'instrument_7',99,100);
insert into INSTRUMENT (INSTRUMENT_ID,VERSION,UPDATED,EXTERNAL_ID,INSTRUMENT_NAME,UPDATED_BY,SITE_ID) values (107,0,to_timestamp('18-NOV-12 04.11.38.418215000 PM','DD-MON-RR HH.MI.SSXFF AM'),null,'instrument_8',99,100);
insert into INSTRUMENT (INSTRUMENT_ID,VERSION,UPDATED,EXTERNAL_ID,INSTRUMENT_NAME,UPDATED_BY,SITE_ID) values (108,0,to_timestamp('28-FEB-13 09.44.16.138202000 AM','DD-MON-RR HH.MI.SSXFF AM'),null,'instrument_9',99,100);
insert into INSTRUMENT (INSTRUMENT_ID,VERSION,UPDATED,EXTERNAL_ID,INSTRUMENT_NAME,UPDATED_BY,SITE_ID) values (109,0,to_timestamp('25-DEC-12 02.39.16.377016000 PM','DD-MON-RR HH.MI.SSXFF AM'),null,'instrument_10',99,100);

insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (100, 100);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (100, 101);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (100, 102);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (100, 103);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (100, 105);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (100, 106);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (101, 102);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (101, 104);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (101, 105);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (101, 106);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (101, 107);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (102, 103);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (102, 104);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (102, 107);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (102, 108);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (102, 109);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (103, 100);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (103, 104);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (103, 105);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (103, 106);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (104, 100);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (104, 101);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (104, 104);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (104, 106);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (104, 107);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (104, 109);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (105, 100);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (105, 101);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (105, 104);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (105, 107);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (106, 102);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (106, 104);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (106, 106);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (106, 107);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (106, 108);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (106, 109);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (107, 100);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (107, 103);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (107, 105);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (107, 106);
insert into customer_instrument (CUSTOMER_ID, INSTRUMENT_ID) values (107, 107);

insert into ROLE (ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROLE_NAME,DESCRIPTION,EXTERNAL_ID) values (100,0,to_timestamp('06-APR-13 09.37.55.573810000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'role_1',null,null);
insert into ROLE (ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROLE_NAME,DESCRIPTION,EXTERNAL_ID) values (101,0,to_timestamp('18-OCT-12 03.12.32.894371000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'role_2',null,null);
insert into ROLE (ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROLE_NAME,DESCRIPTION,EXTERNAL_ID) values (102,0,to_timestamp('06-JUL-13 01.40.12.693433000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'role_3',null,null);
insert into ROLE (ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROLE_NAME,DESCRIPTION,EXTERNAL_ID) values (103,0,to_timestamp('30-APR-13 07.16.25.429385000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'role_4',null,null);

insert into ROOM_NUMBER (ROOM_NUMBER_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_NUMBER,EXTERNAL_ID) values (100,0,to_timestamp('23-JUN-13 11.11.17.935197000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_number_1',null);
insert into ROOM_NUMBER (ROOM_NUMBER_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_NUMBER,EXTERNAL_ID) values (101,0,to_timestamp('22-MAY-13 07.33.23.218358000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_number_2',null);
insert into ROOM_NUMBER (ROOM_NUMBER_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_NUMBER,EXTERNAL_ID) values (102,0,to_timestamp('13-APR-13 07.44.26.491077000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_number_3',null);
insert into ROOM_NUMBER (ROOM_NUMBER_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_NUMBER,EXTERNAL_ID) values (103,0,to_timestamp('15-OCT-12 11.41.55.722369000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_number_4',null);
insert into ROOM_NUMBER (ROOM_NUMBER_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_NUMBER,EXTERNAL_ID) values (104,0,to_timestamp('06-MAR-13 08.03.25.765001000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_number_5',null);
insert into ROOM_NUMBER (ROOM_NUMBER_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_NUMBER,EXTERNAL_ID) values (105,0,to_timestamp('30-SEP-12 11.11.24.855550000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_number_6',null);
insert into ROOM_NUMBER (ROOM_NUMBER_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_NUMBER,EXTERNAL_ID) values (106,0,to_timestamp('19-JUN-13 04.33.14.612556000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_number_7',null);
insert into ROOM_NUMBER (ROOM_NUMBER_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_NUMBER,EXTERNAL_ID) values (107,0,to_timestamp('12-DEC-12 07.07.02.885886000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_number_8',null);
insert into ROOM_NUMBER (ROOM_NUMBER_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_NUMBER,EXTERNAL_ID) values (108,0,to_timestamp('26-MAR-13 03.11.28.767692000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_number_9',null);
insert into ROOM_NUMBER (ROOM_NUMBER_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_NUMBER,EXTERNAL_ID) values (109,0,to_timestamp('12-MAY-13 07.40.15.724471000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_number_10',null);

insert into ROOM_TYPE (ROOM_TYPE_ID,VERSION,UPDATED,ROOM_TYPE,UPDATED_BY,SITE_ID,CAN_SPLIT_ROOM,ENABLED,EXTERNAL_ID) values (100,0,to_timestamp('13-AUG-13 04.13.16.407000000 PM','DD-MON-RR HH.MI.SSXFF AM'),'Rehearsal',99,100,'Y','Y',null);
insert into ROOM_TYPE (ROOM_TYPE_ID,VERSION,UPDATED,ROOM_TYPE,UPDATED_BY,SITE_ID,CAN_SPLIT_ROOM,ENABLED,EXTERNAL_ID) values (101,0,to_timestamp('13-AUG-13 04.13.16.467000000 PM','DD-MON-RR HH.MI.SSXFF AM'),'Lesson',99,100,'N','Y',null);

insert into ROOM_SIZE (ROOM_SIZE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_TYPE_ID,ROOM_SIZE_NAME,EXTERNAL_ID) values (100,0,to_timestamp('13-AUG-13 04.13.16.467000000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,'Large',null);
insert into ROOM_SIZE (ROOM_SIZE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_TYPE_ID,ROOM_SIZE_NAME,EXTERNAL_ID) values (101,0,to_timestamp('13-AUG-13 04.13.16.467000000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,'Medium',null);
insert into ROOM_SIZE (ROOM_SIZE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_TYPE_ID,ROOM_SIZE_NAME,EXTERNAL_ID) values (102,0,to_timestamp('13-AUG-13 04.13.16.467000000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,'Small',null);

insert into ROOM_TEMPLATE (ROOM_TEMPLATE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_TEMPLATE_NAME,ROOM_TYPE_ID,IS_SPLIT_ROOM,ENABLED,ROOM_SIZE_ID,EXTERNAL_ID) values (100,0,to_timestamp('23-JUN-13 06.52.19.695196000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_template_1',100,'N','N',100,null);
insert into ROOM_TEMPLATE (ROOM_TEMPLATE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_TEMPLATE_NAME,ROOM_TYPE_ID,IS_SPLIT_ROOM,ENABLED,ROOM_SIZE_ID,EXTERNAL_ID) values (101,0,to_timestamp('15-NOV-12 06.32.48.981673000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_template_2',100,'Y','N',101,null);
insert into ROOM_TEMPLATE (ROOM_TEMPLATE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_TEMPLATE_NAME,ROOM_TYPE_ID,IS_SPLIT_ROOM,ENABLED,ROOM_SIZE_ID,EXTERNAL_ID) values (102,0,to_timestamp('21-NOV-12 10.54.14.908908000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_template_3',100,'Y','Y',102,null);
insert into ROOM_TEMPLATE (ROOM_TEMPLATE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_TEMPLATE_NAME,ROOM_TYPE_ID,IS_SPLIT_ROOM,ENABLED,ROOM_SIZE_ID,EXTERNAL_ID) values (103,0,to_timestamp('09-JAN-13 02.32.51.670318000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_template_4',100,'Y','Y',102,null);
insert into ROOM_TEMPLATE (ROOM_TEMPLATE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_TEMPLATE_NAME,ROOM_TYPE_ID,IS_SPLIT_ROOM,ENABLED,ROOM_SIZE_ID,EXTERNAL_ID) values (104,0,to_timestamp('01-FEB-13 08.25.05.999176000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_template_5',100,'N','Y',101,null);
insert into ROOM_TEMPLATE (ROOM_TEMPLATE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_TEMPLATE_NAME,ROOM_TYPE_ID,IS_SPLIT_ROOM,ENABLED,ROOM_SIZE_ID,EXTERNAL_ID) values (105,0,to_timestamp('19-AUG-12 09.06.00.372380000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_template_6',101,'N','N',null,null);
insert into ROOM_TEMPLATE (ROOM_TEMPLATE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_TEMPLATE_NAME,ROOM_TYPE_ID,IS_SPLIT_ROOM,ENABLED,ROOM_SIZE_ID,EXTERNAL_ID) values (106,0,to_timestamp('05-AUG-13 02.41.43.300497000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_template_7',101,'N','Y',null,null);
insert into ROOM_TEMPLATE (ROOM_TEMPLATE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_TEMPLATE_NAME,ROOM_TYPE_ID,IS_SPLIT_ROOM,ENABLED,ROOM_SIZE_ID,EXTERNAL_ID) values (107,0,to_timestamp('21-AUG-12 11.23.35.513239000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_template_8',101,'N','Y',null,null);
insert into ROOM_TEMPLATE (ROOM_TEMPLATE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_TEMPLATE_NAME,ROOM_TYPE_ID,IS_SPLIT_ROOM,ENABLED,ROOM_SIZE_ID,EXTERNAL_ID) values (108,0,to_timestamp('20-MAY-13 02.13.03.631353000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_template_9',101,'N','Y',null,null);
insert into ROOM_TEMPLATE (ROOM_TEMPLATE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,ROOM_TEMPLATE_NAME,ROOM_TYPE_ID,IS_SPLIT_ROOM,ENABLED,ROOM_SIZE_ID,EXTERNAL_ID) values (109,0,to_timestamp('18-DEC-12 08.11.19.429178000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,'room_template_10',101,'N','N',null,null);

insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (100,0,to_timestamp('05-MAY-13 10.09.37.112774000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,114,100,100);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (101,0,to_timestamp('24-AUG-12 08.40.55.311183000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,123,103,100);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (102,0,to_timestamp('24-NOV-12 04.34.48.612156000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,102,102,102);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (103,0,to_timestamp('26-DEC-12 11.55.26.939008000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,112,102,101);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (104,0,to_timestamp('13-JAN-13 10.27.12.545933000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,115,100,101);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (105,0,to_timestamp('21-JAN-13 11.23.46.644925000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,119,103,100);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (106,0,to_timestamp('05-MAR-13 01.16.23.464946000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,121,101,100);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (107,0,to_timestamp('21-MAR-13 03.34.58.647880000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,116,101,101);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (108,0,to_timestamp('21-JAN-13 11.13.15.241028000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,122,102,102);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (109,0,to_timestamp('24-MAY-13 10.45.37.846963000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,102,101);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (110,0,to_timestamp('04-SEP-12 06.39.05.722294000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,129,102,100);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (111,0,to_timestamp('19-FEB-13 02.21.50.223455000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,125,100,102);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (112,0,to_timestamp('28-OCT-12 01.11.31.436006000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,101,102,102);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (113,0,to_timestamp('06-SEP-12 02.51.41.468088000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,109,101,100);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (114,0,to_timestamp('30-MAR-13 09.48.09.415553000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,116,102,102);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (116,0,to_timestamp('22-MAR-13 10.25.55.843561000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,120,101,101);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (117,0,to_timestamp('08-JAN-13 04.02.25.782213000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,110,100,102);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (118,0,to_timestamp('06-AUG-13 02.01.37.328265000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,120,100,100);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (119,0,to_timestamp('06-MAR-13 04.19.28.240875000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,128,102,101);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (120,0,to_timestamp('05-MAR-13 07.33.58.805758000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,120,103,101);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (121,0,to_timestamp('11-MAY-13 03.55.37.475990000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,116,103,102);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (122,0,to_timestamp('01-MAR-13 06.30.53.134400000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,119,102,101);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (123,0,to_timestamp('27-FEB-13 05.56.49.787673000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,113,100,102);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (124,0,to_timestamp('25-OCT-12 06.42.57.430931000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,122,100,101);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (125,0,to_timestamp('19-APR-13 10.27.00.945111000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,119,103,101);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (126,0,to_timestamp('14-JAN-13 10.33.57.713140000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,124,102,101);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (127,0,to_timestamp('31-AUG-12 06.41.31.536362000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,121,103,100);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (128,0,to_timestamp('21-JAN-13 11.43.17.324482000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,116,103,100);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (129,0,to_timestamp('28-SEP-12 03.07.16.963587000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,120,102,102);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (130,0,to_timestamp('06-MAR-13 07.02.24.637633000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,101,100,100);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (131,0,to_timestamp('10-SEP-12 01.24.04.875406000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,115,101,101);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (132,0,to_timestamp('11-FEB-13 05.52.39.379202000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,125,102,101);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (133,0,to_timestamp('01-JUN-13 05.26.26.271103000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,114,103,100);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (135,0,to_timestamp('26-SEP-12 01.26.35.378685000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,110,100,100);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (136,0,to_timestamp('11-DEC-12 10.31.07.615807000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,115,102,102);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (138,0,to_timestamp('09-APR-13 01.37.33.735154000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,108,102,100);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (139,0,to_timestamp('18-AUG-12 03.46.08.530600000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,102,101,100);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (141,0,to_timestamp('16-SEP-12 03.18.24.795420000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,106,103,101);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (142,0,to_timestamp('20-NOV-12 10.26.46.722470000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,119,101,102);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (143,0,to_timestamp('01-MAR-13 02.17.43.171718000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,122,102,101);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (144,0,to_timestamp('24-JUN-13 10.13.14.758155000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,112,101,102);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (145,0,to_timestamp('28-DEC-12 01.41.35.981579000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,102,100,102);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (146,0,to_timestamp('15-DEC-12 07.15.37.389930000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,103,102,100);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (148,0,to_timestamp('19-FEB-13 05.18.05.880021000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,111,103,101);
insert into PERSON_ROLE (PERSON_ROLE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PERSON_ID,ROLE_ID,LOCATION_ID) values (149,0,to_timestamp('05-SEP-12 06.40.17.114475000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,105,101,101);

insert into SERVICE (SERVICE_ID, VERSION, UPDATED, SERVICE_NAME, UPDATED_BY, SITE_ID, ENABLED, REQUIRES_INSTRUCTOR, ALLOW_BAND_NAME, EXTERNAL_ID) values (100, 0, to_timestamp('01-SEP-13 10.25.46.301000 PM','DD-MON-RR HH.MI.SSXFF AM'), 'Rehearsal', 99, 100, 'Y', 'N', 'Y', null);
insert into SERVICE (SERVICE_ID, VERSION, UPDATED, SERVICE_NAME, UPDATED_BY, SITE_ID, ENABLED, REQUIRES_INSTRUCTOR, ALLOW_BAND_NAME, EXTERNAL_ID) values (101, 0, to_timestamp('01-SEP-13 10.25.46.363000 PM','DD-MON-RR HH.MI.SSXFF AM'), 'Lesson', 99, 100, 'Y', 'R', 'Y', null);

insert into ACTIVITY (ACTIVITY_ID, VERSION, UPDATED, ACTIVITY_NAME, UPDATED_BY, SITE_ID, SERVICE_ID, MINIMUM_ATTENDEES, MAXIMUM_ATTENDEES, MINIMUM_DURATION, REQUIRES_INSTRUCTOR, ENABLED, EXTERNAL_ID) values (100, 0, to_timestamp('01-SEP-13 10.25.46.363000 PM','DD-MON-RR HH.MI.SSXFF AM'), 'Rehearsal', 99, 100, 100, 1, null, 2, 'N', 'Y', null);
insert into ACTIVITY (ACTIVITY_ID, VERSION, UPDATED, ACTIVITY_NAME, UPDATED_BY, SITE_ID, SERVICE_ID, MINIMUM_ATTENDEES, MAXIMUM_ATTENDEES, MINIMUM_DURATION, REQUIRES_INSTRUCTOR, ENABLED, EXTERNAL_ID) values (101, 0, to_timestamp('01-SEP-13 10.25.46.363000 PM','DD-MON-RR HH.MI.SSXFF AM'), 'Solo Rehearsal', 99, 100, 100, 1, 1, 1, 'N', 'Y', null);
insert into ACTIVITY (ACTIVITY_ID, VERSION, UPDATED, ACTIVITY_NAME, UPDATED_BY, SITE_ID, SERVICE_ID, MINIMUM_ATTENDEES, MAXIMUM_ATTENDEES, MINIMUM_DURATION, REQUIRES_INSTRUCTOR, ENABLED, EXTERNAL_ID) values (102, 0, to_timestamp('01-SEP-13 10.25.46.363000 PM','DD-MON-RR HH.MI.SSXFF AM'), 'Clinic', 99, 100, 100, 0, null, 2, 'N', 'Y', null);
insert into ACTIVITY (ACTIVITY_ID, VERSION, UPDATED, ACTIVITY_NAME, UPDATED_BY, SITE_ID, SERVICE_ID, MINIMUM_ATTENDEES, MAXIMUM_ATTENDEES, MINIMUM_DURATION, REQUIRES_INSTRUCTOR, ENABLED, EXTERNAL_ID) values (103, 0, to_timestamp('01-SEP-13 10.25.46.363000 PM','DD-MON-RR HH.MI.SSXFF AM'), 'Group Class', 99, 100, 101, 1, null, 1, 'R', 'Y', null);
insert into ACTIVITY (ACTIVITY_ID, VERSION, UPDATED, ACTIVITY_NAME, UPDATED_BY, SITE_ID, SERVICE_ID, MINIMUM_ATTENDEES, MAXIMUM_ATTENDEES, MINIMUM_DURATION, REQUIRES_INSTRUCTOR, ENABLED, EXTERNAL_ID) values (104, 0, to_timestamp('01-SEP-13 10.25.46.379000 PM','DD-MON-RR HH.MI.SSXFF AM'), 'Summer Camp', 99, 100, 101, 4, null, 1, 'R', 'Y', null);
insert into ACTIVITY (ACTIVITY_ID, VERSION, UPDATED, ACTIVITY_NAME, UPDATED_BY, SITE_ID, SERVICE_ID, MINIMUM_ATTENDEES, MAXIMUM_ATTENDEES, MINIMUM_DURATION, REQUIRES_INSTRUCTOR, ENABLED, EXTERNAL_ID) values (105, 0, to_timestamp('01-SEP-13 10.25.46.379000 PM','DD-MON-RR HH.MI.SSXFF AM'), 'Rock Show', 99, 100, 101, 2, null, 1, 'R', 'Y', null);
insert into ACTIVITY (ACTIVITY_ID, VERSION, UPDATED, ACTIVITY_NAME, UPDATED_BY, SITE_ID, SERVICE_ID, MINIMUM_ATTENDEES, MAXIMUM_ATTENDEES, MINIMUM_DURATION, REQUIRES_INSTRUCTOR, ENABLED, EXTERNAL_ID) values (106, 0, to_timestamp('01-SEP-13 10.25.46.379000 PM','DD-MON-RR HH.MI.SSXFF AM'), 'Open Office', 99, 100, 101, 0, null, 1, 'R', 'Y', null);
insert into ACTIVITY (ACTIVITY_ID, VERSION, UPDATED, ACTIVITY_NAME, UPDATED_BY, SITE_ID, SERVICE_ID, MINIMUM_ATTENDEES, MAXIMUM_ATTENDEES, MINIMUM_DURATION, REQUIRES_INSTRUCTOR, ENABLED, EXTERNAL_ID) values (107, 0, to_timestamp('01-SEP-13 10.25.46.379000 PM','DD-MON-RR HH.MI.SSXFF AM'), 'Guitar Lesson', 99, 100, 101, 1, 1, 0.5, 'R', 'Y', null);
insert into ACTIVITY (ACTIVITY_ID, VERSION, UPDATED, ACTIVITY_NAME, UPDATED_BY, SITE_ID, SERVICE_ID, MINIMUM_ATTENDEES, MAXIMUM_ATTENDEES, MINIMUM_DURATION, REQUIRES_INSTRUCTOR, ENABLED, EXTERNAL_ID) values (108, 0, to_timestamp('01-SEP-13 10.25.46.379000 PM','DD-MON-RR HH.MI.SSXFF AM'), 'Drum Lesson', 99, 100, 101, 1, 1, 0.5, 'R', 'Y', null);

insert into instructor_activities (INSTRUCTOR_ID, ACTIVITY_ID) values (101, 108);
insert into instructor_activities (INSTRUCTOR_ID, ACTIVITY_ID) values (102, 105);
insert into instructor_activities (INSTRUCTOR_ID, ACTIVITY_ID) values (103, 103);
insert into instructor_activities (INSTRUCTOR_ID, ACTIVITY_ID) values (104, 105);
insert into instructor_activities (INSTRUCTOR_ID, ACTIVITY_ID) values (104, 106);
insert into instructor_activities (INSTRUCTOR_ID, ACTIVITY_ID) values (105, 104);
insert into instructor_activities (INSTRUCTOR_ID, ACTIVITY_ID) values (107, 104);
insert into instructor_activities (INSTRUCTOR_ID, ACTIVITY_ID) values (108, 104);
insert into instructor_activities (INSTRUCTOR_ID, ACTIVITY_ID) values (108, 107);

insert into PROFILE_SERVICE (PROFILE_SERVICE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,SERVICE_ID, PROFILE_ID, ENABLED) values (100, 0,to_timestamp('24-JUN-13 10.13.14.758155000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100, 100, 'Y');
insert into PROFILE_SERVICE (PROFILE_SERVICE_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,SERVICE_ID, PROFILE_ID, ENABLED) values (101, 0,to_timestamp('24-JUN-13 10.13.14.758155000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,101, 100, 'Y');

insert into profile_activity (profile_activity_id,VERSION,UPDATED,UPDATED_BY,SITE_ID, ACTIVITY_ID, PROFILE_ID, ENABLED) values (100, 0,to_timestamp('24-JUN-13 10.13.14.758155000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100, 100, 'Y');
insert into profile_activity (profile_activity_id,VERSION,UPDATED,UPDATED_BY,SITE_ID, ACTIVITY_ID, PROFILE_ID, ENABLED) values (101, 0,to_timestamp('24-JUN-13 10.13.14.758155000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,101, 100, 'Y');
insert into profile_activity (profile_activity_id,VERSION,UPDATED,UPDATED_BY,SITE_ID, ACTIVITY_ID, PROFILE_ID, ENABLED) values (102, 0,to_timestamp('24-JUN-13 10.13.14.758155000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,102, 100, 'Y');
insert into profile_activity (profile_activity_id,VERSION,UPDATED,UPDATED_BY,SITE_ID, ACTIVITY_ID, PROFILE_ID, ENABLED) values (103, 0,to_timestamp('24-JUN-13 10.13.14.758155000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,103, 100, 'Y');
insert into profile_activity (profile_activity_id,VERSION,UPDATED,UPDATED_BY,SITE_ID, ACTIVITY_ID, PROFILE_ID, ENABLED) values (104, 0,to_timestamp('24-JUN-13 10.13.14.758155000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,104, 100, 'Y');
insert into profile_activity (profile_activity_id,VERSION,UPDATED,UPDATED_BY,SITE_ID, ACTIVITY_ID, PROFILE_ID, ENABLED) values (105, 0,to_timestamp('24-JUN-13 10.13.14.758155000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,105, 100, 'Y');
insert into profile_activity (profile_activity_id,VERSION,UPDATED,UPDATED_BY,SITE_ID, ACTIVITY_ID, PROFILE_ID, ENABLED) values (106, 0,to_timestamp('24-JUN-13 10.13.14.758155000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,106, 100, 'Y');
insert into profile_activity (profile_activity_id,VERSION,UPDATED,UPDATED_BY,SITE_ID, ACTIVITY_ID, PROFILE_ID, ENABLED) values (107, 0,to_timestamp('24-JUN-13 10.13.14.758155000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,107, 100, 'Y');
insert into profile_activity (profile_activity_id,VERSION,UPDATED,UPDATED_BY,SITE_ID, ACTIVITY_ID, PROFILE_ID, ENABLED) values (108, 0,to_timestamp('24-JUN-13 10.13.14.758155000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,108, 100, 'Y');

insert into ROOM_TEMPLATE_SERVICES (SERVICE_ID, ROOM_TEMPLATE_ID) values (100, 100);
insert into ROOM_TEMPLATE_SERVICES (SERVICE_ID, ROOM_TEMPLATE_ID) values (100, 101);
insert into ROOM_TEMPLATE_SERVICES (SERVICE_ID, ROOM_TEMPLATE_ID) values (100, 102);
insert into ROOM_TEMPLATE_SERVICES (SERVICE_ID, ROOM_TEMPLATE_ID) values (100, 103);
insert into ROOM_TEMPLATE_SERVICES (SERVICE_ID, ROOM_TEMPLATE_ID) values (100, 104);
insert into ROOM_TEMPLATE_SERVICES (SERVICE_ID, ROOM_TEMPLATE_ID) values (101, 105);
insert into ROOM_TEMPLATE_SERVICES (SERVICE_ID, ROOM_TEMPLATE_ID) values (101, 106);
insert into ROOM_TEMPLATE_SERVICES (SERVICE_ID, ROOM_TEMPLATE_ID) values (101, 107);
insert into ROOM_TEMPLATE_SERVICES (SERVICE_ID, ROOM_TEMPLATE_ID) values (101, 108);
insert into ROOM_TEMPLATE_SERVICES (SERVICE_ID, ROOM_TEMPLATE_ID) values (101, 109);

insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (100, 100);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (100, 103);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (101, 101);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (101, 104);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (102, 102);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (102, 103);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (102, 104);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (103, 108);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (103, 109);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (104, 105);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (104, 106);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (104, 107);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (104, 109);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (105, 106);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (105, 107);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (105, 109);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (106, 105);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (106, 106);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (106, 107);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (106, 109);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (107, 106);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (107, 108);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (108, 105);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (108, 107);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (108, 108);
insert into room_template_activities (ACTIVITY_ID, ROOM_TEMPLATE_ID) values (108, 109);

insert into ROOM (ROOM_ID, VERSION, UPDATED, UPDATED_BY, SITE_ID, ROOM_TYPE_ID, ROOM_TEMPLATE_ID, ROOM_NUMBER_ID, ROOM_SIZE_ID, IS_SPLIT_ROOM, PARENT_ID, PROFILE_ROOM_NAME, ENABLED, PROFILE_ID, EXTERNAL_ID) values (100, 0, to_timestamp('22-JUN-13 03.52.19.695196 PM','DD-MON-RR HH.MI.SSXFF AM'), 99, 100, 100, 100, 100, 100, 'N', null, 'room_1', 'N', 100, null);
insert into ROOM (ROOM_ID, VERSION, UPDATED, UPDATED_BY, SITE_ID, ROOM_TYPE_ID, ROOM_TEMPLATE_ID, ROOM_NUMBER_ID, ROOM_SIZE_ID, IS_SPLIT_ROOM, PARENT_ID, PROFILE_ROOM_NAME, ENABLED, PROFILE_ID, EXTERNAL_ID) values (101, 0, to_timestamp('15-NOV-12 03.32.48.981673 AM','DD-MON-RR HH.MI.SSXFF AM'), 99, 100, 100, 101, 101, 101, 'Y', null, 'room_2', 'N', 100, null);
insert into ROOM (ROOM_ID, VERSION, UPDATED, UPDATED_BY, SITE_ID, ROOM_TYPE_ID, ROOM_TEMPLATE_ID, ROOM_NUMBER_ID, ROOM_SIZE_ID, IS_SPLIT_ROOM, PARENT_ID, PROFILE_ROOM_NAME, ENABLED, PROFILE_ID, EXTERNAL_ID) values (102, 0, to_timestamp('21-NOV-12 07.54.14.908908 AM','DD-MON-RR HH.MI.SSXFF AM'), 99, 100, 100, 102, 102, 102, 'Y', null, 'room_3', 'Y', 100, null);
insert into ROOM (ROOM_ID, VERSION, UPDATED, UPDATED_BY, SITE_ID, ROOM_TYPE_ID, ROOM_TEMPLATE_ID, ROOM_NUMBER_ID, ROOM_SIZE_ID, IS_SPLIT_ROOM, PARENT_ID, PROFILE_ROOM_NAME, ENABLED, PROFILE_ID, EXTERNAL_ID) values (103, 0, to_timestamp('08-JAN-13 11.32.51.670318 PM','DD-MON-RR HH.MI.SSXFF AM'), 99, 100, 100, 103, 103, 102, 'Y', null, 'room_4', 'Y', 100, null);
insert into ROOM (ROOM_ID, VERSION, UPDATED, UPDATED_BY, SITE_ID, ROOM_TYPE_ID, ROOM_TEMPLATE_ID, ROOM_NUMBER_ID, ROOM_SIZE_ID, IS_SPLIT_ROOM, PARENT_ID, PROFILE_ROOM_NAME, ENABLED, PROFILE_ID, EXTERNAL_ID) values (104, 0, to_timestamp('31-JAN-13 05.25.05.999176 PM','DD-MON-RR HH.MI.SSXFF AM'), 99, 100, 100, 104, 104, 101, 'N', null, 'room_5', 'Y', 100, null);
insert into ROOM (ROOM_ID, VERSION, UPDATED, UPDATED_BY, SITE_ID, ROOM_TYPE_ID, ROOM_TEMPLATE_ID, ROOM_NUMBER_ID, ROOM_SIZE_ID, IS_SPLIT_ROOM, PARENT_ID, PROFILE_ROOM_NAME, ENABLED, PROFILE_ID, EXTERNAL_ID) values (105, 0, to_timestamp('19-AUG-12 06.06.00.372380 AM','DD-MON-RR HH.MI.SSXFF AM'), 99, 100, 101, 105, 105, null, 'N', null, 'room_6', 'N', 100, null);
insert into ROOM (ROOM_ID, VERSION, UPDATED, UPDATED_BY, SITE_ID, ROOM_TYPE_ID, ROOM_TEMPLATE_ID, ROOM_NUMBER_ID, ROOM_SIZE_ID, IS_SPLIT_ROOM, PARENT_ID, PROFILE_ROOM_NAME, ENABLED, PROFILE_ID, EXTERNAL_ID) values (106, 0, to_timestamp('04-AUG-13 11.41.43.300497 PM','DD-MON-RR HH.MI.SSXFF AM'), 99, 100, 101, 106, 106, null, 'N', null, 'room_7', 'Y', 100, null);
insert into ROOM (ROOM_ID, VERSION, UPDATED, UPDATED_BY, SITE_ID, ROOM_TYPE_ID, ROOM_TEMPLATE_ID, ROOM_NUMBER_ID, ROOM_SIZE_ID, IS_SPLIT_ROOM, PARENT_ID, PROFILE_ROOM_NAME, ENABLED, PROFILE_ID, EXTERNAL_ID) values (107, 0, to_timestamp('21-AUG-12 08.23.35.513239 AM','DD-MON-RR HH.MI.SSXFF AM'), 99, 100, 101, 107, 107, null, 'N', null, 'room_8', 'Y', 100, null);
insert into ROOM (ROOM_ID, VERSION, UPDATED, UPDATED_BY, SITE_ID, ROOM_TYPE_ID, ROOM_TEMPLATE_ID, ROOM_NUMBER_ID, ROOM_SIZE_ID, IS_SPLIT_ROOM, PARENT_ID, PROFILE_ROOM_NAME, ENABLED, PROFILE_ID, EXTERNAL_ID) values (108, 0, to_timestamp('19-MAY-13 11.13.03.631353 AM','DD-MON-RR HH.MI.SSXFF AM'), 99, 100, 101, 108, 108, null, 'N', null, 'room_9', 'Y', 100, null);
insert into ROOM (ROOM_ID, VERSION, UPDATED, UPDATED_BY, SITE_ID, ROOM_TYPE_ID, ROOM_TEMPLATE_ID, ROOM_NUMBER_ID, ROOM_SIZE_ID, IS_SPLIT_ROOM, PARENT_ID, PROFILE_ROOM_NAME, ENABLED, PROFILE_ID, EXTERNAL_ID) values (109, 0, to_timestamp('18-DEC-12 05.11.19.429178 AM','DD-MON-RR HH.MI.SSXFF AM'), 99, 100, 101, 109, 109, null, 'N', null, 'room_10', 'N', 100, null);
insert into ROOM (ROOM_ID, VERSION, UPDATED, UPDATED_BY, SITE_ID, ROOM_TYPE_ID, ROOM_TEMPLATE_ID, ROOM_NUMBER_ID, ROOM_SIZE_ID, IS_SPLIT_ROOM, PARENT_ID, PROFILE_ROOM_NAME, ENABLED, PROFILE_ID, EXTERNAL_ID) values (110, 0, to_timestamp('15-NOV-12 03.32.48.981673 AM','DD-MON-RR HH.MI.SSXFF AM'), 99, 100, 100, 101, 101, 101, 'N', 101, 'room_2_a', 'N', 100, null);
insert into ROOM (ROOM_ID, VERSION, UPDATED, UPDATED_BY, SITE_ID, ROOM_TYPE_ID, ROOM_TEMPLATE_ID, ROOM_NUMBER_ID, ROOM_SIZE_ID, IS_SPLIT_ROOM, PARENT_ID, PROFILE_ROOM_NAME, ENABLED, PROFILE_ID, EXTERNAL_ID) values (111, 0, to_timestamp('21-NOV-12 07.54.14.908908 AM','DD-MON-RR HH.MI.SSXFF AM'), 99, 100, 101, 101, 101, null, 'N', 101, 'room_2_b', 'N', 100, null);
insert into ROOM (ROOM_ID, VERSION, UPDATED, UPDATED_BY, SITE_ID, ROOM_TYPE_ID, ROOM_TEMPLATE_ID, ROOM_NUMBER_ID, ROOM_SIZE_ID, IS_SPLIT_ROOM, PARENT_ID, PROFILE_ROOM_NAME, ENABLED, PROFILE_ID, EXTERNAL_ID) values (112, 0, to_timestamp('15-NOV-12 03.32.48.981673 AM','DD-MON-RR HH.MI.SSXFF AM'), 99, 100, 101, 102, 102, null, 'N', 102, 'room_3_a', 'Y', 100, null);
insert into ROOM (ROOM_ID, VERSION, UPDATED, UPDATED_BY, SITE_ID, ROOM_TYPE_ID, ROOM_TEMPLATE_ID, ROOM_NUMBER_ID, ROOM_SIZE_ID, IS_SPLIT_ROOM, PARENT_ID, PROFILE_ROOM_NAME, ENABLED, PROFILE_ID, EXTERNAL_ID) values (113, 0, to_timestamp('21-NOV-12 07.54.14.908908 AM','DD-MON-RR HH.MI.SSXFF AM'), 99, 100, 101, 102, 102, null, 'N', 102, 'room_3_b', 'Y', 100, null);
insert into ROOM (ROOM_ID, VERSION, UPDATED, UPDATED_BY, SITE_ID, ROOM_TYPE_ID, ROOM_TEMPLATE_ID, ROOM_NUMBER_ID, ROOM_SIZE_ID, IS_SPLIT_ROOM, PARENT_ID, PROFILE_ROOM_NAME, ENABLED, PROFILE_ID, EXTERNAL_ID) values (114, 0, to_timestamp('15-NOV-12 03.32.48.981673 AM','DD-MON-RR HH.MI.SSXFF AM'), 99, 100, 100, 103, 103, 101, 'N', 103, 'room_4_a', 'Y', 100, null);
insert into ROOM (ROOM_ID, VERSION, UPDATED, UPDATED_BY, SITE_ID, ROOM_TYPE_ID, ROOM_TEMPLATE_ID, ROOM_NUMBER_ID, ROOM_SIZE_ID, IS_SPLIT_ROOM, PARENT_ID, PROFILE_ROOM_NAME, ENABLED, PROFILE_ID, EXTERNAL_ID) values (115, 0, to_timestamp('21-NOV-12 07.54.14.908908 AM','DD-MON-RR HH.MI.SSXFF AM'), 99, 100, 100, 103, 103, 101, 'N', 103, 'room_4_b', 'Y', 100, null);

insert into ROOM_SERVICES (SERVICE_ID, ROOM_ID) values (100, 100);
insert into ROOM_SERVICES (SERVICE_ID, ROOM_ID) values (100, 101);
insert into ROOM_SERVICES (SERVICE_ID, ROOM_ID) values (100, 102);
insert into ROOM_SERVICES (SERVICE_ID, ROOM_ID) values (100, 103);
insert into ROOM_SERVICES (SERVICE_ID, ROOM_ID) values (100, 104);
insert into ROOM_SERVICES (SERVICE_ID, ROOM_ID) values (100, 110);
insert into ROOM_SERVICES (SERVICE_ID, ROOM_ID) values (100, 114);
insert into ROOM_SERVICES (SERVICE_ID, ROOM_ID) values (100, 115);
insert into ROOM_SERVICES (SERVICE_ID, ROOM_ID) values (101, 105);
insert into ROOM_SERVICES (SERVICE_ID, ROOM_ID) values (101, 106);
insert into ROOM_SERVICES (SERVICE_ID, ROOM_ID) values (101, 107);
insert into ROOM_SERVICES (SERVICE_ID, ROOM_ID) values (101, 108);
insert into ROOM_SERVICES (SERVICE_ID, ROOM_ID) values (101, 109);
insert into ROOM_SERVICES (SERVICE_ID, ROOM_ID) values (101, 111);
insert into ROOM_SERVICES (SERVICE_ID, ROOM_ID) values (101, 112);
insert into ROOM_SERVICES (SERVICE_ID, ROOM_ID) values (101, 113);

insert into room_activities (ACTIVITY_ID, ROOM_ID) values (100, 100);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (100, 103);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (100, 110);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (101, 101);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (101, 104);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (101, 114);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (102, 102);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (102, 103);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (102, 104);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (102, 115);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (103, 108);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (103, 109);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (104, 105);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (104, 106);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (104, 107);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (104, 109);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (105, 106);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (105, 107);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (105, 109);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (106, 105);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (106, 106);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (106, 107);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (106, 109);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (106, 113);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (107, 106);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (107, 108);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (107, 112);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (108, 105);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (108, 107);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (108, 108);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (108, 109);
insert into room_activities (ACTIVITY_ID, ROOM_ID) values (108, 111);

insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (100,0,to_timestamp('19-AUG-13 07.00.44.360379000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('17-JUN-13 11.29.25.578332000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,104,'Terrasa',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (101,0,to_timestamp('24-DEC-12 04.39.04.661421000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('05-DEC-12 07.29.00.141399000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,108,'Oyten',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (102,0,to_timestamp('23-JUN-13 07.42.48.732053000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('15-DEC-12 02.53.52.281830000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,105,'Lbeck',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (103,0,to_timestamp('15-JAN-13 01.33.42.172094000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('25-DEC-12 07.22.38.546021000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,101,'Ohtsu',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (104,0,to_timestamp('04-MAR-13 08.22.08.373253000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('28-JAN-13 03.35.43.571546000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,102,'Fuchstal-asch',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (105,0,to_timestamp('15-JUN-13 02.04.23.328532000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('07-OCT-12 04.53.14.793560000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,108,'Kagoshima',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (106,0,to_timestamp('08-MAR-13 08.08.52.673117000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('18-MAY-13 08.41.06.954708000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,106,'Kaohsiung',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (107,0,to_timestamp('23-OCT-12 07.20.30.623550000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('21-MAY-13 10.46.38.120871000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,105,'Carlin','NV');
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (108,0,to_timestamp('12-NOV-12 10.55.16.903154000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('30-MAY-13 01.49.31.281920000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,103,'Lund',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (109,0,to_timestamp('01-OCT-12 01.56.17.845838000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('24-JUN-13 02.15.24.924953000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,106,'Horsham','PA');
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (110,0,to_timestamp('22-JUN-13 06.11.41.894897000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('06-MAY-13 04.02.05.177018000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,104,'Goslar',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (111,0,to_timestamp('27-AUG-13 10.42.32.650602000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('11-JUL-13 09.13.18.823244000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,100,'Libertyville','IL');
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (112,0,to_timestamp('21-JUL-13 09.56.03.646590000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('23-MAR-13 01.47.08.986308000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,101,'Fairfax','VA');
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (113,0,to_timestamp('04-JUN-13 10.18.52.632722000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('20-JAN-13 11.27.39.850698000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,106,'Pasadena','MD');
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (114,0,to_timestamp('04-MAR-13 07.52.28.722179000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('04-APR-13 04.40.09.754714000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,102,'Zipf',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (115,0,to_timestamp('20-SEP-12 12.19.50.234505000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('21-APR-13 04.09.01.437263000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,101,'Hiroshima',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (116,0,to_timestamp('29-JUN-13 12.44.40.980894000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('17-SEP-12 07.49.50.539225000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,105,'Dublin',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (117,0,to_timestamp('20-NOV-12 01.20.29.304965000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('02-DEC-12 03.22.37.741684000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,103,'Webster Groves','MO');
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (118,0,to_timestamp('24-APR-13 02.14.37.702131000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('24-NOV-12 08.07.38.171674000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,102,'Obfelden',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (119,0,to_timestamp('03-APR-13 08.57.31.210020000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('06-DEC-12 09.39.39.348577000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,104,'Whittier','CA');
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (120,0,to_timestamp('09-OCT-12 05.29.32.667576000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('12-JUL-13 07.45.58.320512000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,101,'Edwardstown',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (121,0,to_timestamp('19-SEP-12 12.19.56.513440000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('10-MAR-13 03.15.18.301800000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,105,'Bountiful','UT');
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (122,0,to_timestamp('23-AUG-13 09.57.02.318488000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('09-APR-13 04.07.43.185990000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,106,'Niles','IL');
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (123,0,to_timestamp('19-MAR-13 02.14.52.652924000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('17-APR-13 05.07.17.495457000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,101,'Kerava',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (124,0,to_timestamp('03-SEP-13 06.57.54.995164000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('24-OCT-12 11.37.27.602235000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,106,'Milwaukee','WI');
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (125,0,to_timestamp('30-MAY-13 10.48.53.732171000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('16-AUG-13 05.39.34.858403000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,100,'Tottori',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (126,0,to_timestamp('23-JUN-13 01.35.23.617264000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('25-OCT-12 01.43.55.971005000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,101,'Colombo',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (127,0,to_timestamp('18-MAR-13 06.12.45.198321000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('28-APR-13 09.28.04.795260000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,103,'Pitstone',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (128,0,to_timestamp('05-SEP-12 08.47.47.886132000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('23-DEC-12 03.06.33.316932000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,101,'Pecs',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (129,0,to_timestamp('10-OCT-12 11.57.22.402886000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('18-MAY-13 09.48.02.665066000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,100,'Tottori',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (130,0,to_timestamp('21-MAY-13 08.09.09.669801000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('15-FEB-13 03.18.24.203229000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,100,'Clark','NJ');
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (131,0,to_timestamp('18-OCT-12 10.14.50.509709000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('01-OCT-12 05.58.11.141121000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,106,'Bruxelles',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (132,0,to_timestamp('26-APR-13 11.27.18.125224000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('12-MAY-13 04.49.57.972326000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,108,'Ankara',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (133,0,to_timestamp('03-SEP-13 06.24.25.983790000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('24-JUL-13 09.16.13.228914000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,102,'StJean de Soudain',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (134,0,to_timestamp('26-OCT-12 06.06.48.233105000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('22-FEB-13 08.03.37.480466000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,107,'Fairfax','VA');
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (135,0,to_timestamp('26-JUL-13 01.29.09.390929000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('14-AUG-13 10.47.38.279835000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,105,'Cannock',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (136,0,to_timestamp('14-NOV-12 04.09.16.418220000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('01-MAR-13 03.51.34.144352000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,103,'Qunbec',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (137,0,to_timestamp('31-MAY-13 01.56.53.617133000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('29-APR-13 02.07.53.589396000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,108,'Monument','CO');
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (138,0,to_timestamp('26-DEC-12 05.39.05.884328000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('09-APR-13 08.15.15.112441000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,100,'Kungki',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (139,0,to_timestamp('03-JUL-13 04.51.55.225319000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('11-JUL-13 05.42.26.735680000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,108,'Zrich',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (140,0,to_timestamp('19-JAN-13 08.00.55.562368000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('23-OCT-12 06.12.48.385615000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,102,'Boston','MA');
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (141,0,to_timestamp('20-OCT-12 02.52.08.854972000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('01-SEP-13 05.52.48.856662000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,100,'Nynshamn',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (142,0,to_timestamp('08-AUG-13 09.29.49.349734000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('18-APR-13 04.34.40.333670000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,108,'Raleigh','NC');
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (143,0,to_timestamp('20-APR-13 07.10.28.971950000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('21-SEP-12 01.56.45.943190000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,103,'Southampton',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (144,0,to_timestamp('19-OCT-12 05.48.35.273291000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('29-OCT-12 08.53.30.766474000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,107,'Monterey','CA');
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (145,0,to_timestamp('22-FEB-13 06.48.47.507704000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('05-DEC-12 03.11.03.633804000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,107,'Hochwald',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (146,0,to_timestamp('04-DEC-12 02.29.50.599253000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('03-OCT-12 11.39.19.440804000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,101,'Durban',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (147,0,to_timestamp('01-FEB-13 06.01.29.190053000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('27-APR-13 11.30.00.287320000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,102,'Buffalo Grove','IL');
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (148,0,to_timestamp('06-OCT-12 11.18.09.835818000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('24-AUG-13 08.07.18.465345000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,107,'Alleroed',null);
insert into APPOINTMENT_SERIES (APPOINTMENT_SERIES_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,PROFILE_ID,EXTERNAL_ID,IS_RECURRING,SERIES_START_TIME,SERIES_END_TIME,ACTIVITY_ID,BAND_NAME,NOTE) values (149,0,to_timestamp('19-OCT-12 12.39.34.835865000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,100,null,'N',to_timestamp_tz('21-FEB-13 02.28.50.302598000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),null,103,'Braintree',null);

insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (107,100);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (101,101);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (104,102);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (106,103);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (105,104);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (102,105);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (104,106);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (102,107);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (100,108);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (101,109);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (100,110);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (106,111);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (105,112);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (107,113);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (103,114);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (107,115);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (101,116);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (105,117);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (102,118);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (103,119);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (106,120);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (101,121);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (107,122);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (104,123);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (104,124);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (102,125);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (104,126);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (100,127);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (102,128);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (101,129);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (107,130);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (106,131);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (105,132);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (102,133);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (100,134);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (103,135);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (107,136);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (106,137);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (106,138);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (103,139);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (101,140);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (100,141);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (103,142);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (104,143);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (105,144);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (107,145);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (104,146);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (104,147);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (104,148);
insert into CUSTOMER_APPOINTMENT_SERIES (CUSTOMER_ID,APPOINTMENT_SERIES_ID) values (107,149);

insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (100,0,to_timestamp('13-FEB-13 11.39.47.178360000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,100,104,100,108,106,to_timestamp_tz('17-JUN-13 11.29.25.578332000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('18-JUN-13 12.29.25.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Terrasa',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (101,0,to_timestamp('03-SEP-13 01.02.00.858924000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,101,108,100,101,107,to_timestamp_tz('05-DEC-12 07.29.00.141399000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('05-DEC-12 07.59.00.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Oyten',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (102,0,to_timestamp('15-MAY-13 10.42.15.375818000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,102,105,100,102,107,to_timestamp_tz('15-DEC-12 02.53.52.281830000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('15-DEC-12 03.53.52.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Lbeck',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (103,0,to_timestamp('03-MAR-13 01.47.28.288260000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,103,101,100,null,101,to_timestamp_tz('25-DEC-12 07.22.38.546021000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('25-DEC-12 08.22.38.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Ohtsu',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (104,0,to_timestamp('03-MAR-13 05.54.37.500506000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,104,102,100,null,104,to_timestamp_tz('28-JAN-13 03.35.43.571546000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('28-JAN-13 05.35.43.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Fuchstal-asch',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (105,0,to_timestamp('20-APR-13 11.52.16.164944000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,105,108,100,101,105,to_timestamp_tz('07-OCT-12 04.53.14.793560000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('07-OCT-12 05.23.14.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Kagoshima',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (106,0,to_timestamp('05-MAY-13 09.52.10.290993000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,106,106,100,104,109,to_timestamp_tz('18-MAY-13 08.41.06.954708000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('18-MAY-13 09.41.06.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Kaohsiung',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (107,0,to_timestamp('05-APR-13 12.36.44.269923000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,107,105,100,102,107,to_timestamp_tz('21-MAY-13 10.46.38.120871000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('21-MAY-13 11.46.38.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Carlin','NV');
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (108,0,to_timestamp('17-APR-13 01.47.22.267203000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,108,103,100,103,109,to_timestamp_tz('30-MAY-13 01.49.31.281920000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('30-MAY-13 02.49.31.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Lund',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (109,0,to_timestamp('21-SEP-12 06.17.08.877886000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,109,106,100,104,105,to_timestamp_tz('24-JUN-13 02.15.24.924953000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('24-JUN-13 03.15.24.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Horsham','PA');
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (110,0,to_timestamp('17-MAY-13 06.13.52.401700000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,110,104,100,108,105,to_timestamp_tz('06-MAY-13 04.02.05.177018000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('06-MAY-13 05.02.05.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Goslar',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (111,0,to_timestamp('01-FEB-13 01.13.08.511841000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,111,100,100,null,103,to_timestamp_tz('11-JUL-13 09.13.18.823244000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('11-JUL-13 11.13.18.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Libertyville','IL');
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (112,0,to_timestamp('29-SEP-12 02.47.22.401039000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,112,101,100,null,114,to_timestamp_tz('23-MAR-13 01.47.08.986308000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('23-MAR-13 02.47.08.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Fairfax','VA');
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (113,0,to_timestamp('22-OCT-12 12.28.15.823001000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,113,106,100,104,109,to_timestamp_tz('20-JAN-13 11.27.39.850698000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('20-JAN-13 12.27.39.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Pasadena','MD');
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (114,0,to_timestamp('17-DEC-12 04.58.21.885707000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,114,102,100,null,115,to_timestamp_tz('04-APR-13 04.40.09.754714000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('04-APR-13 06.40.09.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Zipf',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (115,0,to_timestamp('18-MAY-13 10.22.17.967727000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,115,101,100,null,104,to_timestamp_tz('21-APR-13 04.09.01.437263000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('21-APR-13 05.09.01.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Hiroshima',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (116,0,to_timestamp('10-APR-13 07.44.43.759546000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,116,105,100,102,107,to_timestamp_tz('17-SEP-12 07.49.50.539225000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('17-SEP-12 08.49.50.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Dublin',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (117,0,to_timestamp('03-DEC-12 01.38.39.215037000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,117,103,100,103,109,to_timestamp_tz('02-DEC-12 03.22.37.741684000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('02-DEC-12 04.22.37.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Webster Groves','MO');
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (118,0,to_timestamp('11-MAY-13 12.57.14.520492000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,118,102,100,null,115,to_timestamp_tz('24-NOV-12 08.07.38.171674000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('24-NOV-12 10.07.38.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Obfelden',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (119,0,to_timestamp('14-JUL-13 08.21.53.525640000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,119,104,100,107,109,to_timestamp_tz('06-DEC-12 09.39.39.348577000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('06-DEC-12 10.39.39.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Whittier','CA');
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (120,0,to_timestamp('02-NOV-12 10.08.42.534517000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,120,101,100,null,101,to_timestamp_tz('12-JUL-13 07.45.58.320512000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('12-JUL-13 08.45.58.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Edwardstown',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (121,0,to_timestamp('22-SEP-12 04.30.24.749270000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,121,105,100,104,109,to_timestamp_tz('10-MAR-13 03.15.18.301800000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('10-MAR-13 04.15.18.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Bountiful','UT');
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (122,0,to_timestamp('19-JAN-13 09.08.53.646620000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,122,106,100,104,106,to_timestamp_tz('09-APR-13 04.07.43.185990000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('09-APR-13 05.07.43.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Niles','IL');
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (123,0,to_timestamp('24-MAY-13 11.37.02.370110000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,123,101,100,null,104,to_timestamp_tz('17-APR-13 05.07.17.495457000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('17-APR-13 06.07.17.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Kerava',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (124,0,to_timestamp('26-OCT-12 12.14.34.347716000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,124,106,100,104,113,to_timestamp_tz('24-OCT-12 11.37.27.602235000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('25-OCT-12 12.37.27.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Milwaukee','WI');
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (125,0,to_timestamp('15-JUN-13 04.27.24.500773000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,125,100,100,null,110,to_timestamp_tz('16-AUG-13 05.39.34.858403000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('16-AUG-13 07.39.34.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Tottori',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (126,0,to_timestamp('20-APR-13 10.34.48.498142000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,126,101,100,null,114,to_timestamp_tz('25-OCT-12 01.43.55.971005000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('25-OCT-12 02.43.55.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Colombo',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (127,0,to_timestamp('01-NOV-12 08.15.36.538537000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,127,103,100,103,109,to_timestamp_tz('28-APR-13 09.28.04.795260000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('28-APR-13 10.28.04.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Pitstone',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (128,0,to_timestamp('24-JAN-13 08.52.15.915418000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,128,101,100,null,114,to_timestamp_tz('23-DEC-12 03.06.33.316932000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('23-DEC-12 04.06.33.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Pecs',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (129,0,to_timestamp('03-NOV-12 05.32.26.892094000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,129,100,100,null,110,to_timestamp_tz('18-MAY-13 09.48.02.665066000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('18-MAY-13 11.48.02.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Tottori',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (130,0,to_timestamp('23-JUN-13 12.51.44.229082000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,130,100,100,null,100,to_timestamp_tz('15-FEB-13 03.18.24.203229000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('15-FEB-13 05.18.24.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Clark','NJ');
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (131,0,to_timestamp('16-MAR-13 06.06.25.545075000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,131,106,100,104,113,to_timestamp_tz('01-OCT-12 05.58.11.141121000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-OCT-12 06.58.11.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Bruxelles',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (132,0,to_timestamp('02-DEC-12 07.51.52.633496000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,132,108,100,101,109,to_timestamp_tz('12-MAY-13 04.49.57.972326000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('12-MAY-13 05.19.57.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Ankara',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (133,0,to_timestamp('25-JUN-13 11.49.52.598851000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,133,102,100,null,102,to_timestamp_tz('24-JUL-13 09.16.13.228914000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('24-JUL-13 11.16.13.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'StJean de Soudain',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (134,0,to_timestamp('02-MAR-13 02.18.11.893416000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,134,107,100,108,112,to_timestamp_tz('22-FEB-13 08.03.37.480466000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('22-FEB-13 08.33.37.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Fairfax','VA');
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (135,0,to_timestamp('23-JAN-13 09.26.42.385102000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,135,105,100,104,107,to_timestamp_tz('14-AUG-13 10.47.38.279835000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('14-AUG-13 11.47.38.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Cannock',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (136,0,to_timestamp('30-AUG-13 04.19.36.713886000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,136,103,100,103,109,to_timestamp_tz('01-MAR-13 03.51.34.144352000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-MAR-13 04.51.34.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Qubec',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (137,0,to_timestamp('26-SEP-12 12.51.31.334360000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,137,108,100,101,105,to_timestamp_tz('29-APR-13 02.07.53.589396000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-APR-13 02.37.53.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Monument','CO');
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (138,0,to_timestamp('04-APR-13 12.21.17.412779000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,138,100,100,null,103,to_timestamp_tz('09-APR-13 08.15.15.112441000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('09-APR-13 10.15.15.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Kungki',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (139,0,to_timestamp('16-FEB-13 05.19.35.490540000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,139,108,100,101,108,to_timestamp_tz('11-JUL-13 05.42.26.735680000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('11-JUL-13 06.12.26.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Zrich',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (140,0,to_timestamp('30-JUN-13 09.46.45.436815000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,140,102,100,null,115,to_timestamp_tz('23-OCT-12 06.12.48.385615000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('23-OCT-12 08.12.48.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Boston','MA');
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (141,0,to_timestamp('15-MAR-13 07.42.18.947168000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,141,100,100,null,100,to_timestamp_tz('01-SEP-13 05.52.48.856662000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('01-SEP-13 07.52.48.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Nynshamn',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (142,0,to_timestamp('05-DEC-12 07.42.03.304040000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,142,108,100,101,109,to_timestamp_tz('18-APR-13 04.34.40.333670000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('18-APR-13 05.04.40.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Raleigh','NC');
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (143,0,to_timestamp('29-JUN-13 05.28.52.502593000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,143,103,100,103,109,to_timestamp_tz('21-SEP-12 01.56.45.943190000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('21-SEP-12 02.56.45.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Southampton',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (144,0,to_timestamp('07-JAN-13 11.48.51.459001000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,144,107,100,108,106,to_timestamp_tz('29-OCT-12 08.53.30.766474000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('29-OCT-12 09.23.30.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Monterey','CA');
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (145,0,to_timestamp('20-MAR-13 07.44.02.751732000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,145,107,100,108,108,to_timestamp_tz('05-DEC-12 03.11.03.633804000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('05-DEC-12 03.41.03.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Hochwald',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (146,0,to_timestamp('11-JUL-13 05.53.16.379626000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,146,101,100,null,104,to_timestamp_tz('03-OCT-12 11.39.19.440804000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('04-OCT-12 12.39.19.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Durban',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (147,0,to_timestamp('06-MAY-13 02.33.45.636063000 PM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,147,102,100,null,115,to_timestamp_tz('27-APR-13 11.30.00.287320000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('28-APR-13 01.30.00.000000000 AM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Buffalo Grove','IL');
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (148,0,to_timestamp('09-FEB-13 05.47.55.257445000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,148,107,100,108,108,to_timestamp_tz('24-AUG-13 08.07.18.465345000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('24-AUG-13 08.37.18.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate-1,null,'Alleroed',null);
insert into APPOINTMENT (APPOINTMENT_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,APPOINTMENT_SERIES_ID,ACTIVITY_ID,PROFILE_ID,INSTRUCTOR_ID,ROOM_ID,START_TIME,END_TIME,CREATE_TIME,CANCELED,BAND_NAME,NOTE) values (149,0,to_timestamp('05-MAY-13 01.48.04.605870000 AM','DD-MON-RR HH.MI.SSXFF AM'),99,100,null,149,103,100,103,109,to_timestamp_tz('21-FEB-13 02.28.50.302598000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),to_timestamp_tz('21-FEB-13 03.28.50.000000000 PM -07:00','DD-MON-RR HH.MI.SS.FF AM TZR'),sysdate+1,null,'Braintree',null);

insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (100,108);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (100,110);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (100,127);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (100,134);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (100,141);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (101,101);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (101,109);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (101,116);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (101,121);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (101,129);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (101,140);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (102,105);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (102,107);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (102,118);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (102,125);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (102,128);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (102,133);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (103,114);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (103,119);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (103,135);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (103,139);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (103,142);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (104,102);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (104,106);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (104,123);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (104,124);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (104,126);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (104,143);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (104,146);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (104,147);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (104,148);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (105,104);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (105,112);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (105,117);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (105,132);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (105,144);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (106,103);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (106,111);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (106,120);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (106,131);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (106,137);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (106,138);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (107,100);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (107,113);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (107,115);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (107,122);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (107,130);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (107,136);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (107,145);
insert into APPOINTMENT_CUSTOMERS (CUSTOMER_ID,APPOINTMENT_ID) values (107,149);
