<?xml version="1.0" encoding="UTF-8"?>
<!-- GC Studios Scheduling Solr schema
     - enables searching for people and locations
-->
<schema name="gcss" version="1.5">
  <types>
    <!-- Defines the unique index type for the schema -->
    <fieldType name="id" class="solr.StrField" indexed="true" stored="true"
               required="true" multiValued="false"/>
    
    <!-- General text field; note that there is no requirement, or
         specification of index, store, etc. These properties will be set
         on a field-by-field basis -->
    <fieldType name="string" class="solr.StrField" sortMissingLast="false"
               sortMissingFirst="false"/>
    
    <!-- Contains a long integer -->
    <fieldType name="long" class="solr.TrieLongField" precisionStep="0"
               positionIncrementGap="0"/>
               
    <!-- Fields that are essentially ignored by Solr; typically these are
         copied into another field that is copied into a multiValued field but
         may have a different requirement in the future -->
    <fieldType name="ignored" class="solr.StrField" indexed="false"
               stored="false" required="false" multiValued="true"/> 
    
    <!-- Special field used for combo-type searching; performs case-insensitive
         indexing and querying and supports sub-word searching -->
    <fieldType name="user_search" class="solr.TextField"
               positionIncrementGap="1" indexed="true" stored="false"
               required="false" multiValued="false" sortMissingLast="true">
      <analyzer type="index">
        <tokenizer class="solr.KeywordTokenizerFactory"/>
        <filter class="solr.ASCIIFoldingFilterFactory"/>
        <filter class="solr.LowerCaseFilterFactory"/>
        <filter class="solr.PatternReplaceFilterFactory" pattern="[^a-zA-Z0-9 ]" replacement="" replace="all"/>
        <filter class="solr.TrimFilterFactory"/>
        <filter class="solr.EdgeNGramFilterFactory" side="front" minGramSize="1" maxGramSize="30"/>
      </analyzer>
      <analyzer type="query">
        <tokenizer class="solr.KeywordTokenizerFactory"/>
        <filter class="solr.ASCIIFoldingFilterFactory"/>
        <filter class="solr.LowerCaseFilterFactory"/>
        <filter class="solr.PatternReplaceFilterFactory" pattern="[^a-zA-Z0-9 ]" replacement="" replace="all"/>
        <filter class="solr.TrimFilterFactory"/>
        <filter class="solr.LengthFilterFactory" min="2" max="100"/>
      </analyzer>
    </fieldType>
  </types>
  
  <fields>
    <!-- The unique identifier of a Solr record -->
    <field name="id" type="id"/>
    
    <!-- Version of the Solr record; used for cache validation, etc -->
    <field name="_version_" type="long" indexed="true" stored="true"/>
    
    <!-- The site id of the record -->
    <field name="siteId" type="long" indexed="true" stored="false"
           required="true" multiValued="false"/>
    
    <!-- The 'type' of record; Customer, Location, etc. -->
    <field name="type" type="string" indexed="true" stored="true"
           required="true" multiValued="false"/>
    
    <!-- The identifier of the record in GCSS database -->
    <field name="recordId" type="long" indexed="true" stored="true"
           required="true" multiValued="false"/>
    
    <!-- The external identifier of the record; not required for all records -->
    <field name="externalId" type="string" indexed="false" stored="true"
           required="false" multiValued="false"/>
           
    <!-- GSSP:220 location_external_id added :: The location external identifier of the record; not required for all records -->
    <field name="locationExternalId" type="string" indexed="false" stored="true"
           required="false" multiValued="false"/>
           
              <!-- POS-808:Lesson Counts-->
    <field name="lessonCount" type="string" indexed="false" stored="true"
           required="false" multiValued="false"/>
    
    <!-- Person fields -->
    <field name="firstName" type="string" indexed="true" stored="true"
           required="false" multiValued="false"/>
    <field name="lastName" type="string" indexed="true" stored="true"
           required="false" multiValued="false"/>
    <field name="email" type="string" indexed="false" stored="true"
           required="false" multiValued="false"/>
    <field name="phone" type="string" indexed="false" stored="true"
           required="false" multiValued="false"/>
           
    <!-- Customer fields -->
    <field name="status" type="string" indexed="true" stored="true"
           required="false" multiValued="false"/>
    <field name="secondaryEmail" type="string" indexed="true" stored="true"
           required="false" multiValued="false"/>
    <field name="instrumentType" type="string" indexed="false" stored="true"
    	   required="false" multiValued="false"/>
           
     <!-- Location fields -->
  	<field name="locationName" type="string" indexed="true" stored="true" required="false" multiValued="false" />
	<field name="address1" type="string" indexed="false" stored="true" required="false" multiValued="false" />
	<field name="address2" type="string" indexed="false" stored="true" multiValued="false" />
	<field name="state" type="string" indexed="false" stored="true" required="false" multiValued="false" />
	<field name="city" type="string" indexed="false" stored="true" required="false" multiValued="false" />
	<field name="zip" type="string" indexed="false" stored="true" required="false" multiValued="false" />
	<field name="country" type="string" indexed="false" stored="true" required="false" multiValued="false" />
	<field name="fax" type="string" indexed="false" stored="true" required="false" multiValued="false" />
	<field name="enabled" type="string" indexed="false" stored="true" required="false" multiValued="false" />

    <!-- Special copies of the name fields for efficient searching -->
    <field name="searchFirstName" type="user_search" indexed="true"
           stored="false" required="false" multiValued="false"/>
    <field name="searchLastName" type="user_search" indexed="true"
           stored="false" required="false" multiValued="false"/>
	<!-- GSSP:220 location_external_id added :: locationExternalId added -->
	<field name="searchLocationName" type="user_search" indexed="true"
		   stored="false" required="false" multiValued="false" />
	<field name="searchStudioNumber" type="user_search" indexed="true"
		   stored="false" required="false" multiValued="false" />
	<field name="searchEmail" type="user_search" indexed="true"
		   stored="false" required="false" multiValued="false" />
	<field name="searchPhone" type="user_search" indexed="true"
           stored="false" required="false" multiValued="false" />	   
    <field name="searchExternalId" type="user_search" indexed="true"
           stored="false" required="false" multiValued="false"/>
    <field name="searchLocationExternalId" type="user_search" indexed="true"
           stored="false" required="false" multiValued="false"/>
	
  </fields>
  
  <!-- Make sure that id is used to enforce uniqueness -->
  <uniqueKey>id</uniqueKey>
  
  <!-- Define the fields to copy into searchable fields -->
  <copyField source="firstName" dest="searchFirstName"/>
  <copyField source="lastName" dest="searchLastName"/>
  
  <!-- The field works for Location Search -->
  <!-- GSSP:220 location_external_id added :: locationExternalId added -->
  <copyField source="locationName" dest="searchLocationName" />
  <copyField source="externalId" dest="searchStudioNumber" />
  <copyField source="email" dest="searchEmail" />
  <copyField source="phone" dest="searchPhone" />
  <copyField source="externalId" dest="searchExternalId"/>
  <copyField source="locationExternalId" dest="searchLocationExternalId"/>
</schema>