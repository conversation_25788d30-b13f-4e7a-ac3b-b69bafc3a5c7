<?xml version="1.0" encoding="UTF-8" ?>
<!--
  GC data configuration
  
  $Id: $
-->
<dataConfig>
  <!-- Note: the value to use for JDBC data source will be provided when the
       instance configuration is registered with Solr -->
  <dataSource jndiName="java:comp/env/jdbc/schedulerDB"
              type="JdbcDataSource" name="db" convertType="false"/>
  <document name="gc">
    <!-- Customer documents -->
    <!-- GSSP:220 location_external_id added -->
    <!-- POS:808 adding Lesson Count changes  -->
    <entity docRoot="true" name="customer"
            dataSource="db"
            query="select c.customer_id, c.site_id, c.external_id,c.location_external_id,c.lesson_count,
                          p.first_name, p.last_name, p.email, p.phone,
                          cs.external_id as status_name,
                          pd.secondary_email
                          from customer c
                          inner join person p on
                          c.person_id = p.person_id
                          left outer join customer_status cs on
                          c.customer_status_id = cs.customer_status_id
                          left join  parent_details pd on 
                          c.parent_id = pd.parent_id"
            transformer="TemplateTransformer">
      <field name="id" column="id"
             template="customer_${customer.CUSTOMER_ID}"/>
      <field name="siteId" column="SITE_ID"/>
      <field name="type" column="type" template="customer"/>
      <field name="recordId" column="CUSTOMER_ID"/>
      <field name="externalId" column="EXTERNAL_ID"/>
      <field name="locationExternalId" column="LOCATION_EXTERNAL_ID"/>
       <field name="lessonCount" column="LESSON_COUNT"/>
      <field name="firstName" column="FIRST_NAME"/>
      <field name="lastName" column="LAST_NAME"/>
      <field name="email" column="EMAIL"/>
      <field name="phone" column="PHONE"/>
      <field name="status" column="STATUS_NAME"/>
	  <field name="secondaryEmail" column="SECONDARY_EMAIL"/>
    </entity>
    
    <entity docRoot="true" name="location" dataSource="db"
    		query="select l.location_id, l.site_id, l.location_name,
    			l.address_1, l.address_2, l.city, l.state, l.zip, l.external_id,
    			l.country, l.phone, l.fax,
				CASE lp.enabled
				when 'Y' then 'Yes'
				when 'N' then 'No'
				else
				null
				end as enabled
    		from location l left join location_profile lp
    		 	on l.profile_id=lp.profile_id"
    		transformer="TemplateTransformer">
    	<field name="id" column="id" template="location_${location.LOCATION_ID}" />
 	    <field name="siteId" column="SITE_ID"/>
      	<field name="type" column="type" template="location"/>
      	<field name="recordId" column="LOCATION_ID"/>
      	<field name="externalId" column="EXTERNAL_ID"/>
      	<field name="locationName" column="LOCATION_NAME" />
      	<field name="address1" column="ADDRESS_1" />
      	<field name="address2" column="ADDRESS_2" />
      	<field name="city" column="CITY" />
      	<field name="state" column="STATE" />
      	<field name="zip" column="ZIP" />
      	<field name="country" column="COUNTRY" />
      	<field name="phone" column="PHONE" />
      	<field name="fax" column="FAX" />
      	<field name="enabled" column="ENABLED" />
    </entity>
  </document>
</dataConfig>
