<?xml version="1.0" encoding="UTF-8"?>
<!-- GCSS Solr configuration -->
<config>
  <luceneMatchVersion>LUCENE_43</luceneMatchVersion>
  
  <!-- Location of Solr indexes; defaults to ./data if unset -->
  <dataDir>${solr.data.dir:}</dataDir>
  
  <!-- Directory factory implementation to use; defaults to
       NRTCachingDirectoryFactory -->
  <directoryFactory name="DirectoryFactory"
                    class="${solr.directoryFactory:solr.NRTCachingDirectoryFactory}"/>
  
  <codecFactory class="solr.SchemaCodecFactory"/>
  <schemaFactory class="ClassicIndexSchemaFactory"/>
  
  <indexConfig>
    <lockType>${solr.lock.type:native}</lockType>
  </indexConfig>
  
  <jmx/>
  
  <updateHandler class="solr.DirectUpdateHandler2">
    <updateLog>
      <str name="dir">${solr.ulog.dir:}</str>
    </updateLog>
    <!-- Note that auto-commits are very aggressive since this file is for
         test-cases and changes need to be seen very quickly -->
    <autoCommit>
      <maxTime>1000</maxTime>
      <openSearcher>false</openSearcher>
    </autoCommit>
    <autoSoftCommit>
      <maxTime>100</maxTime>
    </autoSoftCommit>
  </updateHandler>
  
  <query>
    <maxBooleanClauses>1024</maxBooleanClauses>
    
    <filterCache class="solr.FastLRUCache"
                 size="512"
                 initialSize="512"
                 autowarmCount="0"/>
    
    <queryResultCache class="solr.LRUCache"
                      size="512"
                      initialSize="512"
                      autowarmCount="0"/>
    
    <documentCache class="solr.LRUCache"
                   size="512"
                   initialSize="512"
                   autowarmCount="0"/>
    
    <enableLazyFieldLoading>true</enableLazyFieldLoading>
    
    <queryResultWindowSize>20</queryResultWindowSize>
    
    <queryResultMaxDocsCached>200</queryResultMaxDocsCached>
    
    <listener event="newSearcher" class="solr.QuerySenderListener">
      <arr name="queries">
      </arr>
    </listener>
    <listener event="firstSearcher" class="solr.QuerySenderListener">
      <arr name="queries">
        <lst>
          <str name="q">static firstSearcher warming in solrconfig.xml</str>
        </lst>
      </arr>
    </listener>

    <useColdSearcher>false</useColdSearcher>

    <maxWarmingSearchers>2</maxWarmingSearchers>
  </query>
  
  <requestDispatcher handleSelect="false">
  
    <requestParsers enableRemoteStreaming="false"/>
    
    <httpCaching never304="true" />
  </requestDispatcher>
  
  <!-- Default /select handler -->
  <requestHandler name="/select" class="solr.SearchHandler">
    <lst name="defaults">
      <str name="echoParams">explicit</str>
      <str name="rows">10</str>
      <str name="df">searchFirstName</str>
    </lst>
  </requestHandler>
  
  <!-- Handler for javabin (SolrJ) updates -->
  <requestHandler name="/update" class="solr.UpdateRequestHandler"/>
  
  <requestHandler name="/admin" class="solr.admin.AdminHandlers"/>
  
  <requestHandler name="/admin/ping" class="solr.PingRequestHandler">
    <lst name="invariants">
      <str name="q">solrpingquery</str>
    </lst>
    <lst name="defaults">
      <str name="echoParams">all</str>
    </lst>
  </requestHandler>

  <!-- DIH for testing purposes; disabled by default -->
  <requestHandler name="/dataimport"
                  class="org.apache.solr.handler.dataimport.DataImportHandler">
    <lst name="defaults">
      <str name="config">data-config.xml</str>
    </lst>
  </requestHandler>

  <!-- These entries are to support replication in GC environments -->
  <requestHandler name="/replication" class="solr.ReplicationHandler"
                  startup="lazy"/>
  <requestHandler name="/get" class="solr.RealTimeGetHandler">
    <lst name="defaults">
      <str name="omitHeader">true</str>
    </lst>
  </requestHandler>
  
  <!-- Legacy config for the admin interface -->
  <admin>
    <defaultQuery>*:*</defaultQuery>
  </admin>
</config>