
export file:  GCIDW_ENTPR_EMPMST_DAT_yyyymmdd

-- select basis:  include all employees where last update date (re: LST_UPDT) is within one week of current date
-- (ie: current_date -8)

Column		Data Type		
------		---------
EMP_SRCNBR	varchar(30),	
EMP_FST_NAME	varchar(50),
EMP_MID_NAME	varchar(50),
EMP_LST_NAME	varchar(50),
EMP_PREFER_NAME	varchar(50),
EMP_NAME_SFX	varchar(30),
EMP_WORK_PHN	varchar(30),
EMP_WORK_EXT	varchar(30),
EMP_EMAIL	varchar(100),
EMP_STS_CD	varchar(3),
EMP_ACTN_CD	varchar(3), 
HR_EFF_DT	date,
HR_EXP_DT	date,
EMP_CMPY_CD	varchar(10),
EMP_PYGRP_CD	varchar(10),
EMP_HIRE_DT	date,             
EMP_REHIRE_DT	date,
EMP_SENRTY_DT	date,
EMP_TERM_DT	date,
REG_SRCNUM	varchar(30),
DST_SRCNUM	varchar(30),	
PHY_LOC_SRCNBR	varchar(6),	
RPT_LOC_SRCNBR	varchar(6),
JOB_SRCNBR	varchar(6),
JOB_DESC	varchar(30),
JOB_ENT_DT	date,
JOB_WRK_COMP_CD	varchar(4),
JOB_GCPRO_FLG	char(1),
MGR_LVL_CD	varchar(3),
MGR_LVL_DESC	varchar(8),
HR_DEPT_SRCNBR	varchar(6),
HR_DEPT_DESC	varchar(35),
HR_DEPT_LOC	varchar(8),
HR_DEPT_LVL	integer,
MGR_SRCNBR	varchar(30),
MGR_LST_NAME	varchar(50),
MGR_FST_NAME	varchar(50),
MGR_MID_NAME	varchar(50),
SUPV_SRCNBR	varchar(30),
SUPV_LST_NAME	varchar(50),
SUPV_FST_NAME	varchar(50),
SUPV_MID_NAME	varchar(50),
STD_HRS		NUMERIC(18,2)
FULL_PART_TM_CD	char(1),
EMP_REG_TEMP_CD	char(1),
AD_DOMAIN_NAME	varchar(30),
AD_LOGIN_ID	varchar(50),
FEDERATION_ID	varchar(50),
STR_MENU_LVL	varchar(3),
HLDY_SCHD_CD	varchar(4),
SHIFT_CD	varchar(10),
LEAD_CD		char(1),
LOAD_DT		date,
LST_UPDT	date


