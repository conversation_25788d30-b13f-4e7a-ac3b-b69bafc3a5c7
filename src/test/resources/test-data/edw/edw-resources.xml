<?xml version="1.0" encoding="UTF-8"?>
<!-- EDW integration testing resources -->
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
                        http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">
    <!-- These resources contain EDW Studio and Employee records provided by
         the client. These files do not contain any PII details, so the files are
         included as-is
    -->
    <bean id="resEDWStudioFullSample"
          class="org.springframework.core.io.ClassPathResource">
        <constructor-arg>
            <value>test-data/edw/GCIDW_ENTPR_STOREMST_FULL_DAT_20140330</value>
        </constructor-arg>
    </bean>
    <bean id="resEDWStudioIncrSample"
          class="org.springframework.core.io.ClassPathResource">
        <constructor-arg>
            <value>test-data/edw/GCIDW_ENTPR_STOREMST_INCR_DAT_20140330</value>
        </constructor-arg>
    </bean>
    <bean id="resEDWEmployeeFullSample"
          class="org.springframework.core.io.ClassPathResource">
        <constructor-arg>
            <value>test-data/edw/GCIDW_ENTPR_EMPMST_FULL_DAT_20140330</value>
        </constructor-arg>
    </bean>
    <bean id="resEDWEmployeeIncrSample"
          class="org.springframework.core.io.ClassPathResource">
        <constructor-arg>
            <value>test-data/edw/GCIDW_ENTPR_EMPMST_INCR_DAT_20140330</value>
        </constructor-arg>
    </bean>
</beans>
