
export file:  GCIDW_ENTPR_STOREMST_DAT_yyyymmdd

-- select basis:  include all stores daily, regardless of last update date


Column			Data Type		
------			---------		
CORP_SRCNBR		varchar(30),
CORP_DESC		varchar(50),
SLS_CMPY_SRCNBR		varchar(30),
SLS_CMPY_DESC		varchar(50),
INV_CMPY_SRCNBR		varchar(30),
INV_CMPY_DESC		varchar(50),
ENTPR_DIV_SRCNBR	varchar(30),
ENTPR_DIV_DESC		varchar(50),
ENTPR_REG_SRCNBR	varchar(30),
ENTPR_REG_DESC		varchar(50),
ENTPR_REG_MGR_SRCNBR	varchar(30),
ENTPR_REG_MGR_NAME	varchar(50),
ENTPR_DST_SRCNBR	varchar(30),
ENTPR_DST_DESC		varchar(50),
ENTPR_DST_MGR_SRCNBR	varchar(30),
ENTPR_DST_MGR_NAME	varchar(50),
ENTPR_STR_SRCNBR	varchar(30),
ENTPR_STR_DESC		varchar(50),
ENTPR_STR_SDESC		varchar(15),
ENTPR_STR_NAME		varchar(50),
ENTPR_STR_MGR_SRCNBR	varchar(30),
ENTPR_STR_MGR_NAME	varchar(50),
ENTPR_STR_ADDR1		varchar(100),
ENTPR_STR_ADDR2		varchar(100),
ENTPR_STR_ADDR3		varchar(100),
ENTPR_STR_CNTY		varchar(50),
ENTPR_STR_CTY		varchar(50),
ENTPR_STR_ST		varchar(15),
ENTPR_STR_CNTRY		varchar(15),
ENTPR_STR_ZIPCD		varchar(15),
LATITUDE   		NUMERIC(16,6),
LONGITUDE  		NUMERIC(16,6),
ENTPR_STR_PHN1		varchar(50),
ENTPR_STR_PHN1_DESC	varchar(50),
ENTPR_STR_PHN2		varchar(50),
ENTPR_STR_PHN2_DESC	varchar(50),
ENTPR_STR_FAX		varchar(50),
ENTPR_STR_TYPE_SRCNBR	varchar(30),
ENTPR_STR_TYPE_DESC	varchar(50),
ENTPR_FIN_STR_TYP_SRCNBR	varchar(10),
ENTPR_STR_OPN_DT	date,
ENTPR_STR_CLS_DT	date,
ENTPR_STR_GRAND_OPN_DT	date,
ENTPR_STR_COMP_DT	date,
ENTPR_STR_COMP_FLG	varchar(5),
ENTPR_STR_ACTV_FLG	varchar(5),
ENTPR_STR_GCPRO_FLG	integer,
ENTPR_STR_SELLNG_FLG	varchar(5),
ENTPR_STUDIO_FLG 	varchar(5), 
STR_SLS_SQFT     	INTEGER, 
STR_STK_SQFT     	INTEGER, 
STR_USABLE_SQFT  	INTEGER,
LOAD_DT	  		date,
LST_UPDT  		date


