<?xml version="1.0" encoding="UTF-8"?>
<!-- EDW integration testing resources -->
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
                        http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">
    <!-- These resources contain EDW Studio and Employee records provided by
         the client. These files do not contain any PII details, so the files are
         included as-is
    -->
    
    <bean id="resADPInstructorFullSample"
          class="org.springframework.core.io.ClassPathResource">
        <constructor-arg>
            <value>test-data/adp/GCSS_Instructor_eMails.062218033640</value>
        </constructor-arg>
    </bean>
    
</beans>
