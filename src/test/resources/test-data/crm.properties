# Parameters for integration tests
#  - note that some properties related to remote access are blank until a suitable
#    server is available for use (AAXIS old DEV server is being shutdown).
#
#  Tests that use RemoteDataIntegrationServiceImpl should use an assume-type
#  clause to not fail under this scenario.
crm.hostname=
crm.username=
crm.privateKeyPath=target/test-classes/test-data/test_rsa
crm.knownHostsPath=target/test-classes/test-data/known_hosts
crm.locationTriggerPath=GCIDW_ENTPR_STOREMST_TRG
crm.locationFilePathPrefix=GCIDW_ENTPR_STOREMST_FULL_DAT_
crm.employeeTriggerPath=GCIDW_ENTPR_EMPMST_TRG
crm.employeeFilePathPrefix=GCIDW_ENTPR_EMPMST_FULL_DAT_

crm.original=${catalina.base}/temp/FollowUpStudentsList.txt
crm.encrypt=${catalina.base}/temp/encrypt.pgp
crm.decrypt=${catalina.base}/temp/decrypt.txt
crm.publicKeyFile=${catalina.base}/temp/publicKey.asc
crm.secretKeyFile=${catalina.base}/temp/secretKey.asc
crm.passphrase=12345678
#crm.passphrase = pietro