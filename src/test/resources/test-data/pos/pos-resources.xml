<?xml version="1.0" encoding="UTF-8"?>
<!-- POS integration testing resources -->
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
                        http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">
	<!-- These resources contain POS customer records pulled from POS queue in
	     GC DEV environment, but with PII details changed. All records
	     are now set for store/studio #1
	-->
	<bean id="resPOSCustomerJohnSmith"
		  class="org.springframework.core.io.ClassPathResource">
		<constructor-arg>
			<value>test-data/pos/customer_JohnSmith.xml</value>
		</constructor-arg>
	</bean>
	<bean id="resPOSCustomerJaneSmith"
		  class="org.springframework.core.io.ClassPathResource">
		<constructor-arg>
			<value>test-data/pos/customer_JaneSmith.xml</value>
		</constructor-arg>
	</bean>
	<bean id="resPOSCustomerJohnJones"
		  class="org.springframework.core.io.ClassPathResource">
		<constructor-arg>
			<value>test-data/pos/customer_JohnJones.xml</value>
		</constructor-arg>
	</bean>
</beans>
