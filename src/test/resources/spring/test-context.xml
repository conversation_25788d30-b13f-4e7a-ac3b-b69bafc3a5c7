<?xml version="1.0" encoding="UTF-8"?>
<!-- Initialisation file for testing $Id: $ -->
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:jdbc="http://www.springframework.org/schema/jdbc"
	xmlns:p="http://www.springframework.org/schema/p"
	xmlns:jms="http://www.springframework.org/schema/jms"
	xmlns:amq="http://activemq.apache.org/schema/core"
    xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
			    http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
			    http://www.springframework.org/schema/jdbc
			    http://www.springframework.org/schema/jdbc/spring-jdbc-3.2.xsd
			    http://www.springframework.org/schema/jms
			    http://www.springframework.org/schema/jms/spring-jms-3.2.xsd
			    http://activemq.apache.org/schema/core
			    http://activemq.apache.org/schema/core/activemq-core-5.8.0.xsd
                http://www.springframework.org/schema/context
                http://www.springframework.org/schema/context/spring-context-3.2.xsd">

	<!-- Start with standard context import -->
	<import resource="context.xml" />

	<!-- Override beans as necessary for testing -->

	<!-- Use an embedded database for test cases -->
	<bean id="schedulerDB" class="org.apache.tomcat.dbcp.dbcp.BasicDataSource"
		destroy-method="close">
		<property name="driverClassName" value="org.hsqldb.jdbc.JDBCDriver" />
		<property name="url"
			value="******************************************************" />
		<property name="username" value="sa" />
		<property name="password" value="" />
	</bean>

	<jdbc:initialize-database data-source="schedulerDB"
		ignore-failures="DROPS">
		<jdbc:script location="classpath:00_create_schema.sql" />
		<jdbc:script location="classpath:sql/10_create_junit_test_environment.sql" />
	</jdbc:initialize-database>

	<!-- use HSQL Dialect -->
	<bean id="hibernateProperties"
		class="org.springframework.beans.factory.config.PropertiesFactoryBean">
		<property name="properties">
			<props>
				<prop key="hibernate.dialect">org.hibernate.dialect.HSQLDialect</prop>
				<prop key="hibernate.show_sql">false</prop>
				<prop key="hibernate.connection.autocommit">false</prop>
				<prop key="hibernate.current_session_context_class">org.springframework.orm.hibernate5.SpringSessionContext</prop>
                <prop key="hibernate.cache.use_second_level_cache">true</prop>
                <prop key="hibernate.cache.use_query_cache">true</prop>
                <prop key="hibernate.cache.region.factory_class">org.hibernate.cache.ehcache.EhCacheRegionFactory</prop>
			</props>
		</property>
	</bean>
	<!-- Use an embedded Solr instance for testing; using the factory method
		allows EmbeddedSolrServer to find configuration files in the classpath -->
	<bean id="solrInitialiser" class="org.apache.solr.core.CoreContainer.Initializer" />
	<bean id="solrCore" factory-bean="solrInitialiser" factory-method="initialize" />
	<bean id="solrServer"
		class="org.apache.solr.client.solrj.embedded.EmbeddedSolrServer"
		destroy-method="shutdown">
		<constructor-arg ref="solrCore" />
		<constructor-arg>
			<value>collection1</value>
		</constructor-arg>
	</bean>

	<bean id="mailSender" class="org.springframework.mail.javamail.JavaMailSenderImpl">
		<property name="host" value="localhost" />
		<property name="port" value="3025" />
		<property name="username" value="<EMAIL>" />
		<property name="password" value="greenmail" />
		<property name="defaultEncoding" value="UTF-8" />
		<property name="javaMailProperties">
			<props>
				<prop key="mail.smtp.auth">true</prop>
			</props>
		</property>
	</bean>

	<bean id="greenmail"
		class="com.guitarcenter.scheduler.service.mail.MailServerSimulator"
		lazy-init="false">
		<property name="port" value="3025" />
	</bean>

	<bean id="freemarkerConfiguration"
		class="org.springframework.ui.freemarker.FreeMarkerConfigurationFactoryBean">
		<property name="templateLoaderPath" value="classpath:/email" />
	</bean>

	<!-- Embedded JMS broker for testing -->
	<bean id="connectionFactory" class="org.apache.activemq.ActiveMQConnectionFactory"
	      p:brokerURL="vm://pos?broker.persistent=false"/>

	<!-- A destination for test messages -->
	<bean id="testJMSDestination" class="org.apache.activemq.command.ActiveMQQueue">
	  <constructor-arg>
            <value>${pos.queue:pub.gc.gcs.customer.outbound}</value>
	  </constructor-arg>
	</bean>
	<bean id="testJMSTemplate" class="org.springframework.jms.core.JmsTemplate"
	      p:connectionFactory-ref="connectionFactory"
	      p:defaultDestination-ref="testJMSDestination"/>

	<!-- Bring in any test-data resources -->
	<import resource="classpath:test-data/pos/pos-resources.xml"/>
	<import resource="classpath:test-data/edw/edw-resources.xml"/>
	<import resource="classpath:test-data/adp/adp-resources.xml"/>

	<!-- Override properties for testing -->
	<context:property-placeholder order="0" location="classpath:test-data/adp.properties"/>
</beans>
