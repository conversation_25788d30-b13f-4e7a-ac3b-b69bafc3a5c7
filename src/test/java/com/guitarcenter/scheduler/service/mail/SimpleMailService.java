package com.guitarcenter.scheduler.service.mail;

import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

@Service("simpleMailService")
public class SimpleMailService {

	private static Logger		logger			= LoggerFactory.getLogger(SimpleMailService.class);

	@Autowired
	@Qualifier("mailSender")
	private JavaMailSender		mailSender;

	private final static String	TEXT_TEMPLATE	= "User : %s has modified at %tF. \n System Administrator.";



	public void sendNotificationMail(String userName) {
		SimpleMailMessage msg = new SimpleMailMessage();
		msg.setFrom("<EMAIL>");
		msg.setTo("<EMAIL>");
		msg.setSubject("Notification");

		String content = String.format(TEXT_TEMPLATE, userName, new Date());
		msg.setText(content);

		try {
			mailSender.send(msg);
			if (logger.isInfoEnabled()) {
				StringBuilder sb = new StringBuilder();
				String[] messages = msg.getTo();
				for (int i = 0, length = messages.length; i < length; i++) {
					sb.append(messages[i]);
					if (i != length - 1) {
						sb.append(",");
					}
				}
				logger.info("Message is sent to {}", sb.toString());
			}
		} catch (Exception e) {
			logger.error("Sent message has error", e);
		}
	}

}
