package com.guitarcenter.scheduler.service.mail;

import static org.junit.Assert.*;

import java.io.IOException;

import jakarta.mail.MessagingException;
import jakarta.mail.Multipart;
import jakarta.mail.Part;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimeMultipart;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.icegreen.greenmail.util.GreenMail;
import com.icegreen.greenmail.util.GreenMailUtil;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class MailServiceTest {

	@Autowired
	@Qualifier("mimeMailService")
	private MimeMailService		mimeMailService;

	@Autowired
	@Qualifier("simpleMailService")
	private SimpleMailService	simpleMailService;

	@Autowired
	@Qualifier("greenmail")
	private GreenMail			greenMail;



	@Test
	public void sendSimpleMail() throws MessagingException, InterruptedException, IOException {
		simpleMailService.sendNotificationMail("Amy");

		greenMail.waitForIncomingEmail(2000, 1);

		//MimeMessage[] messages = greenMail.getReceivedMessages();
		//MimeMessage message = messages[messages.length - 1];
/*
		assertEquals("<EMAIL>", message.getFrom()[0].toString());
		assertEquals("Notification", message.getSubject());

		assertTrue(((String) message.getContent()).contains("User : Amy has modified"));*/

	}



	@Test
	public void sendMimeMail() throws InterruptedException, MessagingException, IOException {
		mimeMailService.sendNotificationMail("Amy");

		greenMail.waitForIncomingEmail(2000, 1);
		/*MimeMessage[] messages = greenMail.getReceivedMessages();
		MimeMessage message = messages[messages.length - 1];

		assertEquals("<EMAIL>", message.getFrom()[0].toString());
		assertEquals("User EMail Notification", message.getSubject());

		MimeMultipart mimeMultipart = (MimeMultipart) message.getContent();

		assertEquals(2, mimeMultipart.getCount());

		String mainPartText = getMainPartText(mimeMultipart.getBodyPart(0));

		assertTrue(mainPartText.contains("<h1>User : Amy has modified.</h1>"));

		assertEquals("Hello,i am a attachment.", GreenMailUtil.getBody(mimeMultipart.getBodyPart(1)).trim());*/

	}



	private String getMainPartText(Part mainPart) throws MessagingException, IOException {
		return (String) ((Multipart) mainPart.getContent()).getBodyPart(0).getContent();
	}
}
