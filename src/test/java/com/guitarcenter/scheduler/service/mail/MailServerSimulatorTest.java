package com.guitarcenter.scheduler.service.mail;

import static org.junit.Assert.*;
import static org.hamcrest.CoreMatchers.*;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.icegreen.greenmail.util.GreenMail;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class MailServerSimulatorTest {

	@Autowired
	@Qualifier("greenmail")
	private GreenMail	greenMail;



	@Test
	public void testGreenMail() throws Exception {
		assertThat(greenMail, notNullValue());
		assertThat(greenMail.getSmtp().getPort(), is(3025));
	}

}
