package com.guitarcenter.scheduler.service.impl;

import com.guitarcenter.scheduler.service.ReportService;
import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.junit.Assert.assertThat;

/**
 * Created by josedeng on 12/13/13.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring/test-context.xml"})
public class ReportServiceImplTest {

    @Autowired
    private ReportService reportService;

    @Test
    public void testGenerateInstructorScheduleReport() throws Exception {
        long pLocationId = 100;
        Date pStartTime = new DateTime(2013, 12, 4, 0, 0, 0).toDate();
        Date pEndTime = new DateTime(2013, 12, 4, 23, 59, 59).toDate();
        List<Map<String,Object>> list = reportService.generateInstructorScheduleReport(pLocationId, pStartTime, pEndTime);

        assertThat(list, notNullValue());
        assertThat(list.size(), is(0));
    }
}
