package com.guitarcenter.scheduler.service;

import java.util.Date;

import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class LackingAppointmentServiceTest {

	@Autowired
	@Qualifier("lackingAppointmentService")
	private LackingAppointmentService	mLackingAppointmentService;



	@Test
	public void testScheduled() throws Exception {
		Date pBeginningDate = new DateTime(2013, 11, 6, 17, 37, 0).toDate();
		mLackingAppointmentService.scheduedTask(pBeginningDate);
	}

}
