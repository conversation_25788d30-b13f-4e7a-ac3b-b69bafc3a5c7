package com.guitarcenter.scheduler.integration.adapter;

import static org.junit.Assert.*;

import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.guitarcenter.scheduler.dto.LocationDTO;
import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;

/**
 * Test harness for EDW Studio file to LocationDTO List adapter.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class EDWStudioMasterLocationDTOListAdapterTest {
    /**
     * Sample EDW Studio files
     */
    @Autowired
    private Resource resEDWStudioFullSample;

    @Autowired
    private Resource resEDWStudioIncrSample;
    
    @Before
    public void setup() {
        assertNotNull("resEDWStudioFullSample is null", resEDWStudioFullSample);
        assertTrue("resEDWStudioFullSample.exists() != true",
                   resEDWStudioFullSample.exists());
        assertNotNull("resEDWStudioIncrSample is null", resEDWStudioIncrSample);
        assertTrue("resEDWStudioIncrSample.exists() != true",
                   resEDWStudioIncrSample.exists());
    }
    
    /* Helper to make sure that all expected fields are present in a LocationDTO
     * 
     * @param location an instance of LocationDTO to test
     */
    private void validateLocationDTO(LocationDTO location) {
        assertNotNull("location.externalSource is null",
                      location.getExternalSource());
        String expectedResult = "EDW";
        assertEquals("location.externalSource != " + expectedResult,
                     expectedResult, location.getExternalSource());
        assertNotNull("location.site is null", location.getSite());
        /* With change of file, the site could be any valid GC business unit.
         * Just check for not-blank.
         */
        assertFalse("location.site is blank",
                    StringUtils.isBlank(location.getSite()));
        assertNotNull("location.externalId is null", location.getExternalId());
        assertFalse("location.externalId is blank",
                    StringUtils.isBlank(location.getExternalId()));
        /* Expect the external id to be a studio number; will fail if not a
         * valid number
         */
        Long.parseLong(location.getExternalId());
        assertNotNull("location.name is null", location.getName());
        assertFalse("location.name is blank",
                    StringUtils.isBlank(location.getName()));
        assertNotNull("location.address1 is null", location.getAddress1());
        assertFalse("location.address1 is blank",
                    StringUtils.isBlank(location.getAddress1()));
        /* Note that address2 is not tested as it will be null if not present
         */
        assertNotNull("location.city is null", location.getCity());
        assertFalse("location.city is blank",
                    StringUtils.isBlank(location.getCity()));
        assertNotNull("location.state is null", location.getState());
        assertFalse("location.state is blank",
                    StringUtils.isBlank(location.getState()));
        assertNotNull("location.zip is null", location.getZip());
        assertFalse("location.zip is blank",
                    StringUtils.isBlank(location.getZip()));
        assertTrue("location.zip is not at least 5 characters: " +
                   location.getZip().length(), location.getZip().length() >= 5);
        assertNotNull("location.country is null", location.getCountry());
        expectedResult = "USA";
        assertEquals("location.country != " + expectedResult,
                     expectedResult, location.getCountry());
        /* Disabling these tests; new source EDW file can have null phone/fax
         *
        assertNotNull("location.phone is null", location.getPhone());
        assertFalse("location.phone is blank",
                    StringUtils.isBlank(location.getPhone()));
        assertNotNull("location.fax is null", location.getFax());
        assertFalse("location.fax is blank",
                    StringUtils.isBlank(location.getFax()));
         */

    }
    
    /* Make sure that the supplied FULL sample file can be parsed and validates!
     */
    @Test
    public void testFullSampleFile()
        throws IntegrationServiceException, IOException
    {
        List<LocationDTO> locations =
                ExternalAdapterFactory.getExternalAdapter(new InputStreamReader(resEDWStudioFullSample.getInputStream(), Charset.forName("UTF-8")), LocationDTO.class).getEntities();
        assertNotNull("locations is null", locations);
        assertFalse("locations is empty", locations.isEmpty());
        /* There are 2683 lines in the file, 62 of which are studio locations
         */
        int expectedCount = 62;
        assertTrue("locations.size(" + locations.size() + ") != " + expectedCount,
                   expectedCount == locations.size());
        /* Perform a minimal validation on each record
         */
        for (LocationDTO location: locations) {
            validateLocationDTO(location);
        }
    }
    
    /* Make sure that the supplied INCR sample file can be parsed and validates!
     */
    @Test
    public void testIncrSampleFile()
        throws IntegrationServiceException, IOException
    {
        List<LocationDTO> locations =
                ExternalAdapterFactory.getExternalAdapter(new InputStreamReader(resEDWStudioIncrSample.getInputStream(), Charset.forName("UTF-8")), LocationDTO.class).getEntities();
        assertNotNull("locations is null", locations);
        /* There are 8 lines in the file, none of which are studio locations
         */
        int expectedCount = 0;
        assertTrue("locations.size(" + locations.size() + ") != " + expectedCount,
                   expectedCount == locations.size());
        /* Perform a minimal validation on each record
         */
        for (LocationDTO location: locations) {
            validateLocationDTO(location);
        }
    }
}
