package com.guitarcenter.scheduler.integration.adapter;

import static org.junit.Assert.*;

import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.guitarcenter.scheduler.integration.dto.EmployeeDTO;
import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;

/**
 * Test harness for EDW Employee file to EmployeeDTO List adapter.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class EDWEmployeeMasterEmployeeDTOListAdapterTest {
    /**
     * Sample EDW employee files
     */
    @Autowired
    private Resource resEDWEmployeeFullSample;
    
    @Autowired
    private Resource resEDWEmployeeIncrSample;
    
    @Before
    public void setup() {
        assertNotNull("resEDWEmployeeFullSample is null", resEDWEmployeeFullSample);
        assertTrue("resEDWEmployeeFullSample.exists() != true",
                   resEDWEmployeeFullSample.exists());
        assertNotNull("resEDWEmployeeIncrSample is null", resEDWEmployeeIncrSample);
        assertTrue("resEDWEmployeeIncrSample.exists() != true",
                   resEDWEmployeeIncrSample.exists());
    }
    
    /* Helper to make sure that all expected fields are present in an
     * EmployeeDTO
     * 
     * @param location an instance of EmployeeDTO to test
     */
    private void validateEmployeeDTO(EmployeeDTO employee) {
        assertNotNull("employee.externalSource is null",
                      employee.getExternalSource());
        String expectedResult = "EDW";
        assertEquals("employee.externalSource != " + expectedResult,
                     expectedResult, employee.getExternalSource());
        /* Latest sample EDW FULL file contains one entry that will fail to
         * validate. If the record is for that 'employee' skip the tests.
         */
        if ("787999".equals(employee.getExternalId())) {
            return;
        }
        assertNotNull("employee.site is null", employee.getSite());
        /* EDW files may contain sites other than GCS; filtering happens in
         * service business logic, so just check for non-blank.
         */
        assertFalse("employee.site is blank",
                    StringUtils.isBlank(employee.getSite()));
        assertNotNull("employee.studio is null", employee.getStudio());
        /* Expect the studio to be a studio number; will fail if not a
         * valid number. Modified because not all studio numbers are strictly
         * numeric - MFI has a store number of '9901M'. Changed the validation
         * to simply check for non-blank studio identifier.
         */
        assertFalse("employee.studio is blank",
                    StringUtils.isBlank(employee.getStudio()));
        assertNotNull("employee.externalId is null", employee.getExternalId());
        assertFalse("employee.externalId is blank",
                    StringUtils.isBlank(employee.getExternalId()));
        assertNotNull("employee.firstName is null", employee.getFirstName());
        assertFalse("employee.firstName is blank",
                    StringUtils.isBlank(employee.getLastName()));
        assertNotNull("employee.lastName is null", employee.getLastName());
        assertFalse("employee.lastName is blank",
                    StringUtils.isBlank(employee.getFirstName()));
        /* Do not test email; some employees do not have an email address in
         * system of record.
         */
        assertFalse("employee.jobCode == 0", 0 == employee.getJobCode());
        assertNotNull("employee.status is null", employee.getStatus());
        assertFalse("employee.status is blank",
                    StringUtils.isBlank(employee.getStatus()));
    }
    
    /* Make sure that the supplied FULL sample file can be parsed and validates!
     */
    @Test
    public void testFullSampleFile()
        throws IntegrationServiceException, IOException
    {
        List<EmployeeDTO> employees =
                ExternalAdapterFactory.getExternalAdapter(new InputStreamReader(resEDWEmployeeFullSample.getInputStream(), Charset.forName("UTF-8")), EmployeeDTO.class).getEntities();
        assertNotNull("employees is null", employees);
        assertFalse("employees is empty", employees.isEmpty());
        /* There are 11478 lines in the file, but only 9903 are for GCS.
         * However, filtering happens in service business logic so expect all
         * 11478 to be converted to DTO.
         */
        int expectedCount = 11478;
        assertTrue("employees.size(" + employees.size() + ") != " +
                   expectedCount, expectedCount == employees.size());
        /* Perform a minimal validation on each record
         */
        for (EmployeeDTO employee: employees) {
            validateEmployeeDTO(employee);
        }
    }
    
    /* Make sure that the supplied INCR sample file can be parsed and validates!
     */
    @Test
    public void testIncrSampleFile()
        throws IntegrationServiceException, IOException
    {
        List<EmployeeDTO> employees =
                ExternalAdapterFactory.getExternalAdapter(new InputStreamReader(resEDWEmployeeIncrSample.getInputStream(), Charset.forName("UTF-8")), EmployeeDTO.class).getEntities();
        assertNotNull("employees is null", employees);
        assertFalse("employees is empty", employees.isEmpty());
        /* There are 837 lines in the file, none of which are for GCS.
         * However, filtering happens in service business logic so expect all
         * 837 to be converted to DTO.
         */
        int expectedCount = 837;
        assertTrue("employees.size(" + employees.size() + ") != " +
                   expectedCount, expectedCount == employees.size());
        /* Perform a minimal validation on each record
         */
        for (EmployeeDTO employee: employees) {
            validateEmployeeDTO(employee);
        }
    }
}
