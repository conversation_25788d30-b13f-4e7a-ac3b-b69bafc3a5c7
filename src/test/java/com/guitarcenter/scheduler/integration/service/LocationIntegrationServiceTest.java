package com.guitarcenter.scheduler.integration.service;

import static org.junit.Assert.*;
import static org.junit.Assume.assumeTrue;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.LocationDAO;
import com.guitarcenter.scheduler.dao.SiteDAO;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.service.LocationManagerService;
import com.guitarcenter.scheduler.service.PersonManagerService;

/**
 * Implements a test harness for LocationIntegrationService.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class LocationIntegrationServiceTest {
    /* EDW Location sample files
     */
    @Autowired
    private Resource resEDWStudioFullSample;
    
    @Autowired
    private Resource resEDWStudioIncrSample;
    
    @Autowired
    private LocationIntegrationService locationIntegrationService;
    
    @Autowired
    private PersonManagerService personManagerService;
    
    @Autowired
    private LocationManagerService locationManagerService;
    
    @Autowired
    private SiteDAO siteDAO;
    
    @Autowired
    private LocationDAO locationDAO;
    
    private Person systemUpdater = null;
    private Site gcs = null;
    
    @Before
    @Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = Throwable.class)
    public void setup() {
        assertNotNull("resEDWStudioFullSample is null", resEDWStudioFullSample);
        assertTrue("resEDWStudioFullSample.exists() != true",
                   resEDWStudioFullSample.exists());
        assertNotNull("resEDWStudioIncrSample is null", resEDWStudioIncrSample);
        assertTrue("resEDWStudioIncrSample.exists() != true",
                   resEDWStudioIncrSample.exists());
        systemUpdater = personManagerService.getSystemUpdatePerson();
        gcs = new Site();
        gcs.setExternalId("GCS");
        siteDAO.save(gcs, systemUpdater);
    }
    
    @After
    @Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = Throwable.class)
    public void tearDown() {
        /* Clean up database
         */
        List<Location> locations = locationManagerService.locationsInSite(gcs);
        for (Location location: locations) {
            locationDAO.delete(location);
        }
        /* Clean up added reference values
         */
        siteDAO.delete(gcs);
        gcs = null;
        systemUpdater = null;
    }
    
    @Test
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    public void testFullSampleLocationFile()
        throws IntegrationServiceException, IOException
    {
        List<Location> locations = locationManagerService.locationsInSite(gcs);
        assertNotNull("locations is null", locations);
        assertTrue("locations is not empty: " + locations.size(),
                   locations.isEmpty());
        locationIntegrationService.processLocations(new InputStreamReader(resEDWStudioFullSample.getInputStream()));
        /* There are 2683 lines in the full sample file, 62 of which are GCS
         * studio locations.
         */
        int expectedCount = 62;
        locations = locationManagerService.locationsInSite(gcs);
        assertNotNull("locations is null", locations);
        assertTrue("locations.size(" + locations.size() + ") != " + expectedCount,
                   expectedCount == locations.size());
    }
    
    @Test
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    public void testIncrSampleLocationFile()
        throws IntegrationServiceException, IOException
    {
        List<Location> locations = locationManagerService.locationsInSite(gcs);
        assertNotNull("locations is null", locations);
        assertTrue("locations is not empty: " + locations.size(),
                   locations.isEmpty());
        locationIntegrationService.processLocations(new InputStreamReader(resEDWStudioIncrSample.getInputStream()));
        /* There are 8 lines in the incremental sample file, 0 of which are GCS
         * studio locations.
         */
        int expectedCount = 0;
        locations = locationManagerService.locationsInSite(gcs);
        assertNotNull("locations is null", locations);
        assertTrue("locations.size(" + locations.size() + ") != " + expectedCount,
                   expectedCount == locations.size());
    }

    /* Invoke the scheduled update method and verify that locations have been
     * updated.
     */
    @Test
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    public void testRemoteLocationSample() throws IntegrationServiceException
    {
        List<Location> locations = locationManagerService.locationsInSite(gcs);
        assertNotNull("locations is null", locations);
        assertTrue("locations is not empty: " + locations.size(),
                   locations.isEmpty());
        try {
            locationIntegrationService.scheduledUpdate();
            /* There are 2683 lines in the full sample file, 62 of which are GCS
             * studio locations.
             */
            int expectedCount = 62;
            locations = locationManagerService.locationsInSite(gcs);
            assertNotNull("locations is null", locations);
            assertTrue("locations.size(" + locations.size() + ") != " + expectedCount,
                       expectedCount == locations.size());
        } catch (IntegrationServiceException ise) {
            /* Don't fail the test if remote server is not configured
             */
            assumeTrue(!"EDW integration properties are invalid".equals(ise.getMessage()));
            fail("IntegrationServiceException raised: " + ise.getMessage());
        }
    }
}
