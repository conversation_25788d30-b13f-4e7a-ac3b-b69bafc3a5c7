package com.guitarcenter.scheduler.integration.service;

import static org.junit.Assert.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

import jakarta.jms.JMSException;
import jakarta.jms.Message;
import jakarta.jms.Session;


import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.core.MessageCreator;
import org.springframework.oxm.Marshaller;
import org.springframework.oxm.Unmarshaller;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.guitarcenter.scheduler.integration.generated.SyncCustomer;

/**
 * This test suite is not tied to a specific Scheduler custom class. Instead it
 * is designed to make sure that required framework dependencies are in-place
 * so other JUnit tests can concentrate on business logic and not framework.
 *
 * <AUTHOR> Emes <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class IntegrationFrameworkTest {
    
    @Autowired
    @Qualifier("posCustomerMarshaller")
    private Unmarshaller unmarshaller;
    
    @Autowired
    @Qualifier("posCustomerMarshaller")
    private Marshaller marshaller;
    
    @Autowired
    private JmsTemplate jmsTemplate;
    
    @Before
    public void setup() {
        assertNotNull("unmarshaller is null", unmarshaller);
        assertNotNull("marshaller is null", marshaller);
        assertNotNull("jmsTemplate is null", jmsTemplate);
    }
    
    /* Helper to create a minimally valid SyncCustomer
     */
    private SyncCustomer buildSyncCustomer() {
        SyncCustomer syncCustomer = new SyncCustomer();
        syncCustomer.setCustomerDetailsName("Test Customer");
        return syncCustomer;
    }
    
    /* Helper to validate two SyncCustomer instances are equivalent
     */
    private void validateCustomers(SyncCustomer alpha, SyncCustomer beta) {
        assertNotNull("alpha is null", alpha);
        assertNotNull("beta is null", beta);
        assertTrue("alpha != beta", EqualsBuilder.reflectionEquals(alpha, beta));
    }
    
    /* Verify that marshalling/unmarshalling a SyncCustomer works as expected
     */
    @Test
    public void testMarshallUnmarshal()
        throws IOException
    {
        SyncCustomer customer = buildSyncCustomer();
        ByteArrayOutputStream buf = new ByteArrayOutputStream();
       // marshaller.marshal(customer, new StreamResult(buf));
        assertTrue("buf.length !>0: " + buf.size(), buf.size() > 0);
       /* SyncCustomer copy = (SyncCustomer)
            unmarshaller.unmarshal(new StreamSource(new ByteArrayInputStream(buf.toByteArray())));
        validateCustomers(customer, copy);*/
    }
    
    @Test
    public void testSendSyncCustomerJMS()
        throws JMSException
    {
        jmsTemplate.send(new MessageCreator() {
            public Message createMessage(Session session)
                throws JMSException
            {
                String msg = null;
                try {
                    ByteArrayOutputStream buf = new ByteArrayOutputStream();
                  //  marshaller.marshal(buildSyncCustomer(), new StreamResult(buf));
                    msg = IOUtils.toString(new ByteArrayInputStream(buf.toByteArray()), "UTF-8");
                } catch (IOException ioe) {
                    fail("Caught an IOException: " + ioe);
                }
                return session.createTextMessage(msg);
            }
        });
    }
    
    /* Send an XML that is valid but does not match the XSD provided by GC
     */
    @Test
    public void testSendBadJMS()
        throws JMSException
    {
        jmsTemplate.send(new MessageCreator() {
            public Message createMessage(Session session)
                throws JMSException
            {
                String msg =
                        "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                        "<root>\n" +
                        "  <foo>bar</foo>\n" +
                        "</root>";
                return session.createTextMessage(msg);
            }
        });
    }
}
