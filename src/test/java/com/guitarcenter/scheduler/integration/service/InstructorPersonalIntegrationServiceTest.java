package com.guitarcenter.scheduler.integration.service;

import static org.junit.Assert.*;
import static org.junit.Assume.assumeTrue;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Set;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.dao.EmployeeDAO;
import com.guitarcenter.scheduler.dao.InstructorDAO;
import com.guitarcenter.scheduler.dao.LocationDAO;
import com.guitarcenter.scheduler.dao.PersonDAO;
import com.guitarcenter.scheduler.dao.RoleDAO;
import com.guitarcenter.scheduler.dao.SiteDAO;
import com.guitarcenter.scheduler.dao.criterion.InstructorCriterion;
import com.guitarcenter.scheduler.model.Employee;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.PersonRole;
import com.guitarcenter.scheduler.model.Role;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.service.EmployeeService;
import com.guitarcenter.scheduler.service.LocationManagerService;
import com.guitarcenter.scheduler.service.PersonManagerService;
import com.guitarcenter.scheduler.service.PersonRoleService;

/**
 * Implements a test harness for EmployeeIntegrationService.
 *
 * <AUTHOR> Emes <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class InstructorPersonalIntegrationServiceTest {
    
    /* EDW sample files
     */
    @Autowired
    private Resource resADPInstructorFullSample;
    
   /* @Autowired
    private Resource resEDWEmployeeIncrSample;
    
    @Autowired
    private Resource resEDWStudioFullSample;
    */
    @Autowired
    private EmployeeIntegrationService employeeIntegrationService;
    
    @Autowired
    private LocationIntegrationService locationIntegrationService;
    
    @Autowired
    private EmployeeService employeeService;
    
    @Autowired
    private PersonManagerService personManagerService;
    
    @Autowired
    private LocationManagerService locationManagerService;
    
    @Autowired
    private PersonRoleService personRoleService;
    
    @Autowired
    private SiteDAO siteDAO;
    
    @Autowired
    private EmployeeDAO employeeDAO;
    
    @Autowired
    private InstructorDAO instructorDAO;
    
    @Autowired
    private PersonDAO personDAO;
    
    @Autowired
    private LocationDAO locationDAO;
    
    @Autowired
    private RoleDAO roleDAO;
   /* 
    private Person systemUpdater = null;
    private Site gcs = null;
    private Role siteAdmin = null;
    private Role studioManager = null;
    private Role studioLead = null;
    private Role studioAssociate = null;*/
    
    @Before
    @Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = Throwable.class)
    public void setup()
        throws IntegrationServiceException, IOException
    {
        assertNotNull("resADPInstructorFullSample is null", resADPInstructorFullSample);
        assertTrue("resADPInstructorFullSample.exists() != true",
        		resADPInstructorFullSample.exists());
        
        locationIntegrationService.processLocations(new InputStreamReader(resADPInstructorFullSample.getInputStream()));
    }
    
    @After
    @Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = Throwable.class)
    public void tearDown() {
    	
          }
    
    /**
     * Instructor service does not provide an interface for getting
     * instructors by site; rather than add an unnecessary service method,
     * use a DAO search to get matching instructors.
     */
   /* private List<Instructor> getInstructorsForGCS() {
        return instructorDAO.search(InstructorCriterion.findBySiteId(gcs.getSiteId()));
    }*/
    
    /**
     * Test full sample data file
     */
    @Test
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    public void testFullSampleEmployeeFile()
        throws IntegrationServiceException, IOException
    {
    	boolean ans = true;
  	  boolean val = true;
  	 assertEquals(ans,val);
    }

    /**
     * Helper to validate the full set of employees and instructors
     */
    private void validateFullEmployeeRecords() {
        /* The FULL employee file is a mix of sites and instructors/staff, with the
         * following breakdown:-
         * 11478 total records
         *    9903 of which are GCS (all we care about for unit test)
         *      2385 are in studios found in sample location file
         *        752 have the job code 1878 or 3016, and will be processed as
         *            instructors
         *        1633 will be employees of which
         *          143 have a job code of 1877, 2004, 2005, 2164, 3002, 3013
         *              or 3014 which will automatically be granted a role and
         *              employee status
         *          1490 will be inserted as employees but without a role
         */
    	boolean ans = true;
    	  boolean val = true;
    	 assertEquals(ans,val);
    }
    
    
}
