package com.guitarcenter.scheduler.integration.service;

import static org.junit.Assert.*;
import static org.junit.Assume.assumeTrue;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Set;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.dao.EmployeeDAO;
import com.guitarcenter.scheduler.dao.InstructorDAO;
import com.guitarcenter.scheduler.dao.LocationDAO;
import com.guitarcenter.scheduler.dao.PersonDAO;
import com.guitarcenter.scheduler.dao.RoleDAO;
import com.guitarcenter.scheduler.dao.SiteDAO;
import com.guitarcenter.scheduler.dao.criterion.InstructorCriterion;
import com.guitarcenter.scheduler.model.Employee;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.PersonRole;
import com.guitarcenter.scheduler.model.Role;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.service.EmployeeService;
import com.guitarcenter.scheduler.service.LocationManagerService;
import com.guitarcenter.scheduler.service.PersonManagerService;
import com.guitarcenter.scheduler.service.PersonRoleService;

/**
 * Implements a test harness for EmployeeIntegrationService.
 *
 * <AUTHOR> Emes <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class EmployeeIntegrationServiceTest {
    
    /* EDW sample files
     */
    @Autowired
    private Resource resEDWEmployeeFullSample;
    
    @Autowired
    private Resource resEDWEmployeeIncrSample;
    
    @Autowired
    private Resource resEDWStudioFullSample;
    
    @Autowired
    private EmployeeIntegrationService employeeIntegrationService;
    
    @Autowired
    private LocationIntegrationService locationIntegrationService;
    
    @Autowired
    private EmployeeService employeeService;
    
    @Autowired
    private PersonManagerService personManagerService;
    
    @Autowired
    private LocationManagerService locationManagerService;
    
    @Autowired
    private PersonRoleService personRoleService;
    
    @Autowired
    private SiteDAO siteDAO;
    
    @Autowired
    private EmployeeDAO employeeDAO;
    
    @Autowired
    private InstructorDAO instructorDAO;
    
    @Autowired
    private PersonDAO personDAO;
    
    @Autowired
    private LocationDAO locationDAO;
    
    @Autowired
    private RoleDAO roleDAO;
    
    private Person systemUpdater = null;
    private Site gcs = null;
    private Role siteAdmin = null;
    private Role studioManager = null;
    private Role studioLead = null;
    private Role studioAssociate = null;
    
    @Before
    @Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = Throwable.class)
    public void setup()
        throws IntegrationServiceException, IOException
    {
        assertNotNull("resEDWEmployeeFullSample is null", resEDWEmployeeFullSample);
        assertTrue("resEDWEmployeeFullSample.exists() != true",
                   resEDWEmployeeFullSample.exists());
        assertNotNull("resEDWEmployeeIncrSample is null", resEDWEmployeeIncrSample);
        assertTrue("resEDWEmployeeIncrSample.exists() != true",
                   resEDWEmployeeIncrSample.exists());
        assertNotNull("resEDWStudioFullSample is null", resEDWStudioFullSample);
        assertTrue("resEDWStudioFullSample.exists() != true",
                   resEDWStudioFullSample.exists());
        systemUpdater = personManagerService.getSystemUpdatePerson();
        gcs = new Site();
        gcs.setExternalId("GCS");
        siteDAO.save(gcs, systemUpdater);
        siteAdmin = new Role();
        siteAdmin.setRoleName(AppConstants.SITE_ADMIN_STRING);
        siteAdmin.setSite(gcs);
        roleDAO.save(siteAdmin, systemUpdater);
        studioManager = new Role();
        studioManager.setRoleName(AppConstants.STUDIO_MANAGER_STRING);
        studioManager.setSite(gcs);
        roleDAO.save(studioManager, systemUpdater);
        studioLead = new Role();
        studioLead.setRoleName(AppConstants.STUDIO_LEAD_STRING);
        studioLead.setSite(gcs);
        roleDAO.save(studioLead, systemUpdater);
        studioAssociate = new Role();
        studioAssociate.setRoleName(AppConstants.STUDIO_ASSOCIATE_STRING);
        studioAssociate.setSite(gcs);
        roleDAO.save(studioAssociate, systemUpdater);
        
        /* Populate locations from other EDW sample file
         */
        locationIntegrationService.processLocations(new InputStreamReader(resEDWStudioFullSample.getInputStream()));
    }
    
    @After
    @Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = Throwable.class)
    public void tearDown() {
        /* Clean up database
         */
        List<Employee> employees = employeeService.getAllBySite(gcs);
        for (Employee employee: employees) {
            employeeDAO.delete(employee);
            personDAO.delete(employee.getPerson());
        }
        
        List<Instructor> instructors = getInstructorsForGCS();
        for (Instructor instructor: instructors) {
            instructorDAO.delete(instructor);
            personDAO.delete(instructor.getPerson());
        }
        
        List<Location> locations = locationManagerService.locationsInSite(gcs);
        for (Location location: locations) {
            locationDAO.delete(location);
        }
        
        /* Clean up added reference values
         */
        roleDAO.delete(studioAssociate);
        roleDAO.delete(studioLead);
        roleDAO.delete(studioManager);
        roleDAO.delete(siteAdmin);
        siteDAO.delete(gcs);
        gcs = null;
        systemUpdater = null;
    }
    
    /**
     * Instructor service does not provide an interface for getting
     * instructors by site; rather than add an unnecessary service method,
     * use a DAO search to get matching instructors.
     */
    private List<Instructor> getInstructorsForGCS() {
        return instructorDAO.search(InstructorCriterion.findBySiteId(gcs.getSiteId()));
    }
    
    /**
     * Test full sample data file
     */
    @Test
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    public void testFullSampleEmployeeFile()
        throws IntegrationServiceException, IOException
    {
        List<Employee> employees = employeeService.getAllBySite(gcs);
        assertNotNull("employees is null", employees);
        assertTrue("employees is not empty: " + employees.size(),
                   employees.isEmpty());
        List<Instructor> instructors = getInstructorsForGCS();
        assertNotNull("instructors is null", instructors);
        assertTrue("instructors is not empty: " + instructors.size(),
                   instructors.isEmpty());
        employeeIntegrationService.processEmployees(new InputStreamReader(resEDWEmployeeFullSample.getInputStream()));
        validateFullEmployeeRecords();
    }

    /**
     * Helper to validate the full set of employees and instructors
     */
    private void validateFullEmployeeRecords() {
        /* The FULL employee file is a mix of sites and instructors/staff, with the
         * following breakdown:-
         * 11478 total records
         *    9903 of which are GCS (all we care about for unit test)
         *      2385 are in studios found in sample location file
         *        752 have the job code 1878 or 3016, and will be processed as
         *            instructors
         *        1633 will be employees of which
         *          143 have a job code of 1877, 2004, 2005, 2164, 3002, 3013
         *              or 3014 which will automatically be granted a role and
         *              employee status
         *          1490 will be inserted as employees but without a role
         */
        int expectedInstructors = 752;
        List<Instructor> instructors = getInstructorsForGCS();
        assertNotNull("instructors is null", instructors);
        assertTrue("instructors.size(" + instructors.size() + ") != " +
                   expectedInstructors, expectedInstructors == instructors.size());
        int expectedEmployees = 1633;
        List<Employee> employees = employeeService.getAllBySite(gcs);
        assertNotNull("employees is null", employees);
        assertTrue("employees.size(" + employees.size() + ") != " +
                   expectedEmployees, expectedEmployees == employees.size());
        int expectedHaveRole = 143;
        int expectedNoRole = expectedEmployees - expectedHaveRole;
        int roleEmployeeCount = 0;
        int noroleEmployeeCount = 0;
        for (Employee employee: employees) {
            Set<PersonRole> roles =
                personRoleService.findByPerson(employee.getSite().getSiteId(),
                                               employee.getPerson().getPersonId());
            if (roles == null || roles.isEmpty()) {
                noroleEmployeeCount++;
            } else {
                roleEmployeeCount++;
            }
        }
        assertTrue("expectedHaveRole(" + expectedHaveRole +
                   ") != roleEmployeeCount(" + roleEmployeeCount + ")",
                   expectedHaveRole == roleEmployeeCount);
        assertTrue("expectedNoRole(" + expectedNoRole +
                   ") != noroleEmployeeCount(" + noroleEmployeeCount + ")",
                   expectedNoRole == noroleEmployeeCount);
    }
    
    /**
     * Run the same test on a remote hosted copy of the sample file.
     */
    @Test
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    public void testRemoteEmployeeFullSample() throws IntegrationServiceException
    {
        List<Employee> employees = employeeService.getAllBySite(gcs);
        assertNotNull("employees is null", employees);
        assertTrue("employees is not empty: " + employees.size(),
                   employees.isEmpty());
        List<Instructor> instructors = getInstructorsForGCS();
        assertNotNull("instructors is null", instructors);
        assertTrue("instructors is not empty: " + instructors.size(),
                   instructors.isEmpty());
        
        try {
            employeeIntegrationService.scheduledUpdate();
            validateFullEmployeeRecords();
        } catch (IntegrationServiceException ise) {
            /* Don't fail the test if remote server is not configured
             */
            assumeTrue(!"EDW integration properties are invalid".equals(ise.getMessage()));
            fail("IntegrationServiceException raised: " + ise.getMessage());
        }
    }
    
    /**
     * Test incremental sample data file
     */
    @Test
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    public void testIncrSampleEmployeeFile()
        throws IntegrationServiceException, IOException
    {
        List<Employee> employees = employeeService.getAllBySite(gcs);
        assertNotNull("employees is null", employees);
        assertTrue("employees is not empty: " + employees.size(),
                   employees.isEmpty());
        List<Instructor> instructors = getInstructorsForGCS();
        assertNotNull("instructors is null", instructors);
        assertTrue("instructors is not empty: " + instructors.size(),
                   instructors.isEmpty());
        employeeIntegrationService.processEmployees(new InputStreamReader(resEDWEmployeeIncrSample.getInputStream()));
        validateIncrEmployeeRecords();
    }

    /**
     * Helper to validate the incremental set of employees and instructors
     */
    private void validateIncrEmployeeRecords() {
        /* The INCR employee file is a mix of sites and instructors/staff, with the
         * following breakdown:-
         * 837 total records
         *    all of which are GCS (all we care about for unit test)
         *      0 are in studios found in sample location file
         */
        int expectedInstructors = 0;
        List<Instructor> instructors = getInstructorsForGCS();
        assertNotNull("instructors is null", instructors);
        assertTrue("instructors.size(" + instructors.size() + ") != " +
                   expectedInstructors, expectedInstructors == instructors.size());
        int expectedEmployees = 0;
        List<Employee> employees = employeeService.getAllBySite(gcs);
        assertNotNull("employees is null", employees);
        assertTrue("employees.size(" + employees.size() + ") != " +
                   expectedEmployees, expectedEmployees == employees.size());
        int expectedHaveRole = 0;
        int expectedNoRole = expectedEmployees - expectedHaveRole;
        int roleEmployeeCount = 0;
        int noroleEmployeeCount = 0;
        for (Employee employee: employees) {
            Set<PersonRole> roles =
                personRoleService.findByPerson(employee.getSite().getSiteId(),
                                               employee.getPerson().getPersonId());
            if (roles == null || roles.isEmpty()) {
                noroleEmployeeCount++;
            } else {
                roleEmployeeCount++;
            }
        }
        assertTrue("expectedHaveRole(" + expectedHaveRole +
                   ") != roleEmployeeCount(" + roleEmployeeCount + ")",
                   expectedHaveRole == roleEmployeeCount);
        assertTrue("expectedNoRole(" + expectedNoRole +
                   ") != noroleEmployeeCount(" + noroleEmployeeCount + ")",
                   expectedNoRole == noroleEmployeeCount);
    }
}
