package com.guitarcenter.scheduler.integration.service;

import static org.junit.Assert.*;
import static org.junit.Assume.assumeTrue;

import java.io.IOException;
import java.io.InputStream;
import java.security.DigestInputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Test harness for RemoteDataIntegrationService
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class RemoteADPDataIntegrationServiceTest {
    /* EDW sample files
     */
    @Autowired
    private Resource resADPInstructorFullSample;
   
    
    @Autowired
    private RemoteADPDataIntegrationService remoteDataADPIntegrationService;
    
    @Before
    public void setup() {
        assertNotNull("remoteDataADPIntegrationService is null");
        assertNotNull("resADPInstructorFullSample is null", resADPInstructorFullSample);
        assertTrue("resADPInstructorFullSample.exists() returned false",
        		resADPInstructorFullSample.exists());
        
    }
    
    /**
     * Helper function to calculate a digest of the supplied InputStream
     * 
     * @param input InputStream to read
     * @return digest in form of a byte array
     * @throws NoSuchAlgorithmException 
     * @throws IOException 
     */
    private byte[] getDigest(InputStream is) throws NoSuchAlgorithmException, IOException {
        DigestInputStream dis =
            new DigestInputStream(is, MessageDigest.getInstance("SHA-256"));
        byte[] buf = new byte[4096];
        do {
            if (dis.read(buf) <=0 ) {
                break;
            }
        } while (true);
        return dis.getMessageDigest().digest();
    }
    
    /* Verify that the copy of EDW FULL Employee sample on remote server returns
     * the same checksum as the local copy.
     */
    @Test
    public void testRemoteEmployeeFullSample()
        throws IOException, IntegrationServiceException, NoSuchAlgorithmException
    {
        byte[] localDigest = getDigest(resADPInstructorFullSample.getInputStream());
        assertNotNull("localDigest is null", localDigest);
        assertTrue("localDigest.length is zero", localDigest.length > 0);
     
            byte[] remoteDigest =getDigest(resADPInstructorFullSample.getInputStream());
            assertNotNull("remoteDigest is null", remoteDigest);
            assertTrue("remoteDigest.length is zero", remoteDigest.length > 0);
            assertTrue("remoteDigest != localDigest",
                       Arrays.equals(localDigest, remoteDigest));
        /*catch (IntegrationServiceException ise) {
             Don't fail the test if remote server is not configured
             
            assumeTrue(!"ADP integration properties are invalid".equals(ise.getMessage()));
            fail("IntegrationServiceException raised: " + ise.getMessage());
        }*/
    }
    
    
    @Test
    public void testInvalidFile()
    {
        try {
        	remoteDataADPIntegrationService.getRemoteData("/does/not/exist");
            fail("IntegrationServiceException expected");
        } catch (IntegrationServiceException ise) {
            /* Don't fail the test if remote server is not configured
             */
            assumeTrue(!"ADP integration properties are invalid".equals(ise.getMessage()));
            fail("IntegrationServiceException raised: " + ise.getMessage());
        }
    }
}
