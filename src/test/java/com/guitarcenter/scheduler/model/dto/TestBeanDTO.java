package com.guitarcenter.scheduler.model.dto;

import java.util.Date;

public class TestBeanDTO implements java.io.Serializable {

	private static final long	serialVersionUID	= -126420795206757856L;
	private long				testId;
	private long				updatedBy;
	private Date				updated;
	private String				name;



	public TestBeanDTO(long pTestId, long pUpdatedBy, Date pUpdated, String pName) {
		super();
		testId = pTestId;
		updatedBy = pUpdatedBy;
		updated = pUpdated;
		name = pName;
	}



	public long getTestId() {
		return testId;
	}



	public void setTestId(long pTestId) {
		testId = pTestId;
	}



	public long getUpdatedBy() {
		return updatedBy;
	}



	public void setUpdatedBy(long pUpdatedBy) {
		updatedBy = pUpdatedBy;
	}



	public Date getUpdated() {
		return updated;
	}



	public void setUpdated(Date pUpdated) {
		updated = pUpdated;
	}



	public String getName() {
		return name;
	}



	public void setName(String pName) {
		name = pName;
	}

}
