package com.guitarcenter.scheduler.model;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

import org.hibernate.annotations.Type;
import org.joda.time.DateTime;

@Entity
@Table(name = "Test")
@SequenceGenerator(name = "TEST_ID_SEQ", sequenceName = "TEST_ID_SEQ", allocationSize = 1)
public class TestBean implements java.io.Serializable {

	private static final long	serialVersionUID	= -126420795206757856L;
	private long				testId;
	private long				version;
	private Person				updatedBy;
	private Date				updated;
	private String				name;
	private DateTime			dateTime;



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TEST_ID_SEQ")
	@Column(name = "TEST_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public long getTestId() {
		return testId;
	}



	public void setTestId(long pTestId) {
		testId = pTestId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return version;
	}



	public void setVersion(long pVersion) {
		version = pVersion;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return updatedBy;
	}



	public void setUpdatedBy(Person pUpdatedBy) {
		updatedBy = pUpdatedBy;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return updated;
	}



	public void setUpdated(Date pUpdated) {
		updated = pUpdated;
	}



	@Column(name = "NAME", length = 512)
	public String getName() {
		return name;
	}



	public void setName(String pName) {
		name = pName;
	}



	@Column(name = "DATETIME")
	//@Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
	public DateTime getDateTime() {
		return dateTime;
	}



	public void setDateTime(DateTime pDateTime) {
		dateTime = pDateTime;
	}

}
