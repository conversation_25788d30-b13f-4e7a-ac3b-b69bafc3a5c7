package com.guitarcenter.scheduler.dao.criterion;

import static org.junit.Assert.*;

import org.junit.Test;

public class InstructorFloorTimeCriterionTest {

	@Test
	public void testGetFetchScript() {
		fail("Not yet implemented");
	}

	@Test
	public void testSearchSessionInt() {
		fail("Not yet implemented");
	}

	@Test
	public void testGetSessionInt() {
		fail("Not yet implemented");
	}

	@Test
	public void testFindInstructorFloorTime() {
		fail("Not yet implemented");
	}

	@Test
	public void testGetRecipientEmailIds() {
		fail("Not yet implemented");
	}

	@Test
	public void testAbstractCriterion() {
		fail("Not yet implemented");
	}

	@Test
	public void testGetTClass() {
		fail("Not yet implemented");
	}

	@Test
	public void testGetAbstractInstance() {
		fail("Not yet implemented");
	}

	@Test
	public void testSearchSession() {
		fail("Not yet implemented");
	}

	@Test
	public void testSearchAllSession() {
		fail("Not yet implemented");
	}

	@Test
	public void testGetSession() {
		fail("Not yet implemented");
	}

	@Test
	public void testSearchAllSessionInt() {
		fail("Not yet implemented");
	}

	@Test
	public void testObject() {
		fail("Not yet implemented");
	}

	@Test
	public void testGetClass() {
		fail("Not yet implemented");
	}

	@Test
	public void testHashCode() {
		fail("Not yet implemented");
	}

	@Test
	public void testEquals() {
		fail("Not yet implemented");
	}

	@Test
	public void testClone() {
		fail("Not yet implemented");
	}

	@Test
	public void testToString() {
		fail("Not yet implemented");
	}

	@Test
	public void testNotify() {
		fail("Not yet implemented");
	}

	@Test
	public void testNotifyAll() {
		fail("Not yet implemented");
	}

	@Test
	public void testWaitLong() {
		fail("Not yet implemented");
	}

	@Test
	public void testWaitLongInt() {
		fail("Not yet implemented");
	}

	@Test
	public void testWait() {
		fail("Not yet implemented");
	}

	@Test
	public void testFinalize() {
		fail("Not yet implemented");
	}

}
