package com.guitarcenter.scheduler.dao.impl;

import static org.junit.Assert.*;
import static org.hamcrest.CoreMatchers.*;

import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.guitarcenter.scheduler.dao.ProfileActivityDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.ProfileActivityCriterion;
import com.guitarcenter.scheduler.model.ProfileActivity;
import com.guitarcenter.scheduler.model.enums.Enabled;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
@DirtiesContext
public class ProfileActivityDAOImplTest {

	@Autowired
	@Qualifier("profileActivityDAO")
	private ProfileActivityDAO	mProfileActivityDAO;



	@Test
	public void testFindByProfileId() throws Exception {
		Criterion<ProfileActivity, ProfileActivity> criterion = ProfileActivityCriterion.findByProfileId(100);
		List<ProfileActivity> list = mProfileActivityDAO.search(criterion);

		assertThat(list, notNullValue());
		assertThat(list.size(), is(9));
	}



	@Test
	public void testFindByProfileIdAndEnabled() throws Exception {
		{
			Criterion<ProfileActivity, ProfileActivity> criterion = ProfileActivityCriterion.findByProfileIdAndEnabled(
					100, Enabled.N);
			List<ProfileActivity> list = mProfileActivityDAO.search(criterion);

			assertThat(list, notNullValue());
			assertThat(list.size(), is(0));
		}
		{
			Criterion<ProfileActivity, ProfileActivity> criterion = ProfileActivityCriterion.findByProfileIdAndEnabled(
					100, Enabled.Y);
			List<ProfileActivity> list = mProfileActivityDAO.search(criterion);

			assertThat(list, notNullValue());
			assertThat(list.size(), is(9));
		}
	}



	@Test
	public void testGetByProfileIdAndActivityId() throws Exception {
		Criterion<ProfileActivity, ProfileActivity> criterion = ProfileActivityCriterion.getByProfileIdAndActivityId(
				100, 100);
		ProfileActivity profileActivity = mProfileActivityDAO.get(criterion);

		assertThat(profileActivity, notNullValue());
	}



	@Test
	public void testFindByProfileIdAndServiceIdAndEnabled() throws Exception {
		Criterion<ProfileActivity, ProfileActivity> criterion = ProfileActivityCriterion
				.findByProfileIdAndServiceIdAndEnabled(100, 100, Enabled.Y);
		List<ProfileActivity> list = mProfileActivityDAO.search(criterion);

		assertThat(list, notNullValue());
		assertThat(list.size(), is(3));
	}



	@Test
	public void testFindByProfileIdAndServiceIds() throws Exception {
		Criterion<ProfileActivity, ProfileActivity> criterion = ProfileActivityCriterion.findByProfileIdAndServiceIds(
				100, 100L);
		List<ProfileActivity> list = mProfileActivityDAO.search(criterion);

		assertThat(list, notNullValue());
		assertThat(list.size(), is(3));
	}



	@Test
	public void testFindByActivityIdAndSiteIdAndEnabled() throws Exception {
		Criterion<ProfileActivity, ProfileActivity> criterion = ProfileActivityCriterion
				.findByActivityIdAndSiteIdAndEnabled(100, 100, Enabled.Y);
		List<ProfileActivity> list = mProfileActivityDAO.search(criterion);

		assertThat(list, notNullValue());
		assertThat(list.size(), is(1));
	}



	@Test
	public void testHasActivityByActivityIds() throws Exception {
		Criterion<ProfileActivity, Boolean> criterion = ProfileActivityCriterion
				.hasActivityByActivityIds(new Long[] { 100L });
		Boolean result = mProfileActivityDAO.get(criterion);

		assertThat(result, is(Boolean.TRUE));
	}



	@Test
	public void testHasByProfileIdAndServiceIdAndEnabled() throws Exception {
		Criterion<ProfileActivity, Boolean> criterion = ProfileActivityCriterion.hasByProfileIdAndServiceIdAndEnabled(
				100L, 100L, Enabled.Y);
		Boolean result = mProfileActivityDAO.get(criterion);

		assertThat(result, is(Boolean.TRUE));
	}



	@Test
	public void testHasBySiteIdAndActivityId() throws Exception {
		Criterion<ProfileActivity, Boolean> criterion = ProfileActivityCriterion.hasBySiteIdAndActivityId(100L, 100L);
		Boolean result = mProfileActivityDAO.get(criterion);

		assertThat(result, is(Boolean.FALSE));
	}

}
