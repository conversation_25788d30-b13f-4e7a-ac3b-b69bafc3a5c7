package com.guitarcenter.scheduler.dao.impl;

import com.guitarcenter.scheduler.dao.ActivityDAO;
import com.guitarcenter.scheduler.dao.criterion.ActivityCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.enums.Enabled;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.junit.Assert.assertThat;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring/test-context.xml"})
public class ActivityDAOImplTest {

    @Autowired
    @Qualifier("activityDAO")
    private ActivityDAO mActivityDAO;


    @Test
    public void testFindByService() throws Exception {
        Criterion<Activity, Activity> criterion = ActivityCriterion.findByService(100);
        List<Activity> list = mActivityDAO.search(criterion);

        assertThat(list, notNullValue());
        assertThat(list.size(), is(3));
        Activity activity = list.get(0);
        assertThat(activity.getActivityName(), is("Rehearsal"));
    }


    @Test
    public void testFindBySiteId() throws Exception {
        Criterion<Activity, Activity> criterion = ActivityCriterion.findBySiteId(100);
        List<Activity> list = mActivityDAO.search(criterion, DAOHelper.FETCH_SERVICE);

        assertThat(list, notNullValue());
        assertThat(list.size(), is(9));
        assertThat(list.get(0).getService().getServiceName(), is("Rehearsal"));
    }


    @Test
    public void testGetByActivityId() throws Exception {
        Activity activity = mActivityDAO.get(100, DAOHelper.FETCH_SERVICE);

        assertThat(activity, notNullValue());
        assertThat(activity.getService().getServiceName(), is("Rehearsal"));
    }


    @Test
    public void testFindByActivityName() throws Exception {
        Criterion<Activity, Activity> criterion = ActivityCriterion.findByActivityName("re", Enabled.Y);
        List<Activity> list = mActivityDAO.search(criterion);

        assertThat(list, notNullValue());
        assertThat(list.size(), is(2));
    }


    @Test
    public void testHasByServiceIdAndEnabled() throws Exception {
        Criterion<Activity, Boolean> criterion = ActivityCriterion.hasByServiceIdAndEnabled(100L, Enabled.Y);
        Boolean result = mActivityDAO.get(criterion);

        assertThat(result, is(Boolean.TRUE));
    }

    @Test
    public void testFindByProfileIdAndServiceIddAndRoomId() throws Exception {
        long profileId = 100;
        long serviceId = 101;
        long roomId = 100;
        Criterion<Activity, Activity> criterion = ActivityCriterion.findByProfileIdAndServiceIddAndRoomId(profileId, serviceId, roomId);
        List<Activity> list = mActivityDAO.search(criterion);

        assertThat(list.size(), is(0));

    }


    @Test
    public void testFindByProfileIdAndServiceIddAndInstructorId() throws Exception {
        long profileId = 100;
        long serviceId = 101;
        long instructorId = 104;
        Criterion<Activity, Activity> criterion = ActivityCriterion.findByProfileIdAndServiceIddAndInstructorId(profileId, serviceId, instructorId);
        List<Activity> list = mActivityDAO.search(criterion);

        assertThat(list.size(), is(2));

    }
}
