package com.guitarcenter.scheduler.dao.impl;

import static org.junit.Assert.*;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.*;
import static org.hamcrest.CoreMatchers.*;

import java.util.List;

import org.hibernate.HibernateException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.guitarcenter.scheduler.dao.RoleDAO;
import com.guitarcenter.scheduler.model.Role;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class RoleDAOImplTest {

	@Autowired
	@Qualifier("roleDAO")
	private RoleDAO	mRoleDAO;



	@Test(expected = HibernateException.class)
	public void testGet() throws Exception {
		Role role = mRoleDAO.get(100);
		assertThat(role.getRoleName(), is("role_1"));
		role.getUpdatedBy().getLastName();
	}



	@Test
	public void testGetByMode() throws Exception {
		Role role = mRoleDAO.get(100, FETCH_UPDATEBY_PERSON | FETCH_SITE);
		assertThat(role.getRoleName(), is("role_1"));
		assertThat(role.getUpdatedBy().getLastName(), is("Update"));
		assertThat(role.getSite().getName(), is("Tilson Landscape"));

	}



	@Test(expected = HibernateException.class)
	public void testExample() throws Exception {
		Role example = new Role();
		example.setRoleName("role_1");
		List<Role> list = mRoleDAO.search(example);
		assertThat(list.size(), is(1));
		Role role = list.get(0);
		assertThat(role.getRoleName(), is("role_1"));
		role.getUpdatedBy().getLastName();
	}



	@Test
	public void testExampleByMode() throws Exception {
		Role example = new Role();
		example.setRoleName("role_1");
		List<Role> list = mRoleDAO.search(example, FETCH_UPDATEBY_PERSON | FETCH_SITE);
		assertThat(list.size(), is(1));
		Role role = list.get(0);
		assertThat(role.getRoleName(), is("role_1"));
		assertThat(role.getUpdatedBy().getLastName(), is("Update"));
		assertThat(role.getSite().getName(), is("Tilson Landscape"));
	}



	@Test(expected = HibernateException.class)
	public void testSearchAll() throws Exception {
		List<Role> all = mRoleDAO.searchAll();
		assertThat(all.size(), is(4));
		Role role = all.get(0);
		role.getSite().getName();
	}



	@Test
	public void testSearchAllByMode() throws Exception {
		List<Role> all = mRoleDAO.searchAll(FETCH_UPDATEBY_PERSON | FETCH_SITE);
		assertThat(all.size(), is(4));
		Role role = all.get(0);
		assertThat(role.getSite().getName(), notNullValue());
	}

}
