/*
package com.guitarcenter.scheduler.dao.impl;

import static org.junit.Assert.*;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.*;

import java.util.Date;
import java.util.List;

//import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.AbstractDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.TestBeanCriterion;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.TestBean;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
@Transactional
public class AbstractDAOImplTest {

	@Autowired
	@Qualifier("testSiteDAO")
	private TestSiteDAO	mTestSiteDAO;



	@Test
	public void testSave() throws Exception {
		TestBean bean = new TestBean();
		bean.setName("abc");
		mTestSiteDAO.save(bean, 1);
	}



	@Test
	public void testUpdate() throws Exception {
		TestBean updateTestBean = new TestBean();
		updateTestBean.setTestId(1);
		updateTestBean.setName("bbb");
		mTestSiteDAO.update(updateTestBean, 1);
	}



	@Test
	public void testGet() throws Exception {
		TestBean testBean = mTestSiteDAO.get(1);
		assertEquals(1, testBean.getTestId());
		assertEquals("Ricardo", testBean.getName());
		assertEquals(9, testBean.getVersion());
	}



	@Test
	public void testDelete() throws Exception {
		TestBean deletTestBean = new TestBean();
		deletTestBean.setTestId(1);
		mTestSiteDAO.delete(deletTestBean);
	}



	@Test
	public void testSearchByExample() throws Exception {

		TestBean example = new TestBean();
		example.setName("Joe");

		List<TestBean> list = mTestSiteDAO.search(example);

		assertEquals(1, list.size());

		assertEquals(7, list.get(0).getTestId());
		assertEquals("Joe", list.get(0).getName());
		assertEquals(0, list.get(0).getVersion());
	}



	@Test
	public void testSearchAll() throws Exception {
		List<TestBean> list = mTestSiteDAO.searchAll();
		assertEquals(10, list.size());
	}



	@Test
	public void testSearchWithModel() throws Exception {

		Criterion<TestBean, TestBean> criterion = TestBeanCriterion.findByName("a");
		List<TestBean> list = mTestSiteDAO.search(criterion);

		assertEquals(6, list.size());
	}



	@Test
	public void testGetUseFetchMode() throws Exception {
		TestBean testBean = mTestSiteDAO.get(1, FETCH_UPDATEBY_PERSON);
		assertEquals(1, testBean.getTestId());
		assertEquals("Ricardo", testBean.getName());
		assertEquals(9, testBean.getVersion());
		assertEquals("Update", testBean.getUpdatedBy().getLastName());
	}



	@Test
	public void testSearchByExampleUseFetchMode() throws Exception {

		TestBean example = new TestBean();
		example.setName("Joe");

		List<TestBean> list = mTestSiteDAO.search(example, FETCH_UPDATEBY_PERSON);

		assertEquals(1, list.size());

		assertEquals(7, list.get(0).getTestId());
		assertEquals("Joe", list.get(0).getName());
		assertEquals(0, list.get(0).getVersion());
		assertEquals("Update", list.get(0).getUpdatedBy().getLastName());
	}



	@Test
	public void testSearchAllUseFetchMode() throws Exception {
		List<TestBean> list = mTestSiteDAO.searchAll(FETCH_UPDATEBY_PERSON);
		assertEquals(10, list.size());
		assertNotNull(list.get(0).getUpdatedBy().getLastName());
	}



	@Test
	public void testSearchWithModelUseFetchMode() throws Exception {

		Criterion<TestBean, TestBean> criterion = TestBeanCriterion.findByName("a");
		List<TestBean> list = mTestSiteDAO.search(criterion, FETCH_UPDATEBY_PERSON);

		assertEquals(6, list.size());
		assertNotNull(list.get(0).getUpdatedBy().getLastName());
	}

	public interface TestSiteDAO extends AbstractDAO<TestBean> {
	}

	@Repository("testSiteDAO")
	public static class TestSiteDAOImpl extends AbstractDAOImpl<TestBean> implements TestSiteDAO {

		TestSiteDAOImpl() {
			super(TestBean.class);
		}



		@Override
		protected void updateAuditor(TestBean pT, Person pUpdatedBy) {
			pT.setUpdated(new Date());
			pT.setUpdatedBy(pUpdatedBy);
		}



		@Override
		protected void fetchOne(TestBean pResult, int pFetchMode) {
			addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		}



		*/
/*
		 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		 * addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy",
		 * FetchMode.JOIN, pCriteria); }
		 *//*



		@Override
		protected void fetchMany(TestBean pResult, int pFetchMode) {
			// addFetchCriteria(FETCH_ROOM_SIZES, pFetchMode,
			// pResult.getRoomSizes());
		}



		@Override
		protected Criterion<TestBean, TestBean> getCriterionInstance() {
			return TestBeanCriterion.getInstance();
		}

	}

}
*/
