package com.guitarcenter.scheduler.dao.impl;

import static org.junit.Assert.*;

import java.util.Date;
import java.util.List;

import org.hamcrest.CoreMatchers;
import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.guitarcenter.scheduler.dao.AppointmentSeriesDAO;
import com.guitarcenter.scheduler.dao.criterion.AppointmentSeriesCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.dto.AppointmentSeriesDTO;
import com.guitarcenter.scheduler.model.AppointmentSeries;


@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class AppointmentSeriesDAOImplTest {
	
	@Autowired
	@Qualifier("appointmentSeriesDAO")
	private AppointmentSeriesDAO mAppointmentSeriesDAO;
	
	@Test
	public void testFindLackingAppointmentSeries() throws Exception {
		Date beginningDate = new DateTime(2013,11,6, 0, 0, 0).toDate();
		Criterion<AppointmentSeries, AppointmentSeriesDTO> criterion = AppointmentSeriesCriterion.findLackingAppointmentSeries(beginningDate);
		List<AppointmentSeriesDTO> list = mAppointmentSeriesDAO.search(criterion);
		
		assertThat(list, CoreMatchers.notNullValue());
	}
}
