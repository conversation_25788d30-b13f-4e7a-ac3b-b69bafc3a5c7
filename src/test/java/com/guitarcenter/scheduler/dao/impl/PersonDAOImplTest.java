package com.guitarcenter.scheduler.dao.impl;

import static org.junit.Assert.*;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.*;
import static org.hamcrest.CoreMatchers.*;

import java.util.List;

import org.hibernate.HibernateException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.guitarcenter.scheduler.dao.PersonDAO;
import com.guitarcenter.scheduler.model.Person;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class PersonDAOImplTest {

	@Autowired
	@Qualifier("personDAO")
	private PersonDAO	mPersonDAO;



	@Test(expected = HibernateException.class)
	public void testGet() throws Exception {
		Person person = mPersonDAO.get(100);
		assertThat(person.getLastName(), is("Rowland<PERSON>"));
		person.getUpdatedBy().getLastName();
	}



	@Test
	public void testGetByMode() throws Exception {
		Person person = mPersonDAO.get(100, FETCH_UPDATEBY_PERSON);
		assertThat(person.getLastName(), is("Rowlands"));
		assertThat(person.getUpdatedBy().getLastName(), is("Update"));
	}



	@Test(expected = HibernateException.class)
	public void testExample() throws Exception {
		Person example = new Person();
		example.setLastName("Rowlands");
		List<Person> list = mPersonDAO.search(example);
		assertThat(list.size(), is(1));
		Person person = list.get(0);
		assertThat(person.getLastName(), is("Rowlands"));
		person.getUpdatedBy().getLastName();
	}



	@Test
	public void testExampleByMode() throws Exception {
		Person example = new Person();
		example.setLastName("Rowlands");
		List<Person> list = mPersonDAO.search(example, FETCH_UPDATEBY_PERSON);
		assertThat(list.size(), is(1));
		Person person = list.get(0);
		assertThat(person.getLastName(), is("Rowlands"));
		assertThat(person.getUpdatedBy().getLastName(), is("Update"));
	}



	@Test
	public void testSearchAll() throws Exception {
		List<Person> all = mPersonDAO.searchAll();
		assertThat(all.size(), is(32));
		Person person = all.get(31);
		person.getUpdatedBy().getLastName();
	}



	@Test
	public void testSearchAllByMode() throws Exception {
		List<Person> all = mPersonDAO.searchAll(FETCH_UPDATEBY_PERSON);
		assertThat(all.size(), is(32));
		Person person = all.get(0);
		assertThat(person.getUpdatedBy().getLastName(), is("Update"));
	}

}
