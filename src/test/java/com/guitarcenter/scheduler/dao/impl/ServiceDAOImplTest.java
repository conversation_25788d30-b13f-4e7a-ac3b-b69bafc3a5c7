package com.guitarcenter.scheduler.dao.impl;

import static org.hamcrest.CoreMatchers.*;
import static org.junit.Assert.*;

import java.util.List;

import com.guitarcenter.scheduler.model.enums.Enabled;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.guitarcenter.scheduler.dao.ServiceDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.ServiceCriterion;
import com.guitarcenter.scheduler.model.Service;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class ServiceDAOImplTest {

	@Autowired
	@Qualifier("serviceDAO")
	private ServiceDAO	mServiceDAO;



	@Test
	public void testFindBySiteId() throws Exception {
		Criterion<Service, Service> criterion = ServiceCriterion.findBySiteId(100);
		List<Service> list = mServiceDAO.search(criterion);

		assertThat(list, notNullValue());
		assertThat(list.size(), is(2));
	}

    @Test
    public void testFindByProfileIdAndRoomIdAndEnabled() throws Exception {
        long profileId = 100;
        long roomId = 100;
        Enabled enabled = Enabled.Y;
        Criterion<Service, Service> criterion = ServiceCriterion.findByProfileIdAndRoomIdAndEnabled(profileId, roomId, enabled);
        List<Service> list = mServiceDAO.search(criterion);

        assertThat(list.size(), is(1));

    }

    @Test
    public void testFindByProfileIdAndInstructorIdAndEnabled() throws Exception {
        long profileId = 100;
        long instructorId = 105;
        Enabled enabled = Enabled.Y;
        Criterion<Service, Service> criterion = ServiceCriterion.findByProfileIdAndInstructorIdAndEnabled(profileId, instructorId, enabled);
        List<Service> list = mServiceDAO.search(criterion);

        assertThat(list.size(), is(1));

    }
}
