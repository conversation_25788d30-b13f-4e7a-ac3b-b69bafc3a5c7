package com.guitarcenter.scheduler.dao.impl;

import com.guitarcenter.scheduler.dao.RoomDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.RoomCriterion;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.enums.Enabled;
import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.junit.Assert.assertThat;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring/test-context.xml"})
public class RoomDAOImplTest {

    @Autowired
    @Qualifier("roomDAO")
    private RoomDAO mRoomDAO;


    @Test
    public void testFindByRoomIds() throws Exception {
        {
            Criterion<Room, Room> criterion = RoomCriterion.findByRoomIds(100L);
            List<Room> list = mRoomDAO.search(criterion);

            assertThat(list, notNullValue());
            assertThat(list.size(), is(1));
        }
        {
            Criterion<Room, Room> criterion = RoomCriterion.findByRoomIds(100L, 101L);
            List<Room> list = mRoomDAO.search(criterion);

            assertThat(list, notNullValue());
            assertThat(list.size(), is(2));
        }
        {
            Criterion<Room, Room> criterion = RoomCriterion.findByRoomIds();
            List<Room> list = mRoomDAO.search(criterion);

            assertThat(list, notNullValue());
            assertThat(list.size(), is(0));
        }
    }


    @Test
    public void testFindByServiceId() throws Exception {
        Criterion<Room, Room> criterion = RoomCriterion.findByServiceId(100);
        List<Room> list = mRoomDAO.search(criterion);

        assertThat(list, notNullValue());
        assertThat(list.size(), is(8));
    }


    @Test
    public void testFindByLocationProfileId() throws Exception {
        Criterion<Room, Room> criterion = RoomCriterion.findByLocationProfileId(100);
        List<Room> list = mRoomDAO.search(criterion);

        assertThat(list, notNullValue());
        assertThat(list.size(), is(16));
    }


    @Test
    public void testFindByProfileIdAndEnabled() throws Exception {
        {
            Criterion<Room, Room> criterion = RoomCriterion.findByProfileIdAndEnabled(100, Enabled.Y);
            List<Room> list = mRoomDAO.search(criterion);

            assertThat(list, notNullValue());
            assertThat(list.size(), is(10));
        }
        {
            Criterion<Room, Room> criterion = RoomCriterion.findByProfileIdAndEnabled(100, Enabled.N);
            List<Room> list = mRoomDAO.search(criterion);

            assertThat(list, notNullValue());
            assertThat(list.size(), is(6));
        }
    }


    @Test
    public void testFindByActivityIdAndEnabled() throws Exception {
        {
            Criterion<Room, Room> criterion = RoomCriterion.findByActivityIdAndEnabled(100, Enabled.Y);
            List<Room> list = mRoomDAO.search(criterion);

            assertThat(list, notNullValue());
            assertThat(list.size(), is(1));
        }
        {
            Criterion<Room, Room> criterion = RoomCriterion.findByActivityIdAndEnabled(100, Enabled.N);
            List<Room> list = mRoomDAO.search(criterion);

            assertThat(list, notNullValue());
            assertThat(list.size(), is(2));
        }
    }


    @Test
    public void testFindByProfileIdAndActivityIds() throws Exception {
        Criterion<Room, Room> criterion = RoomCriterion.findByProfileIdAndActivityIds(100, 100L);
        List<Room> list = mRoomDAO.search(criterion);

        assertThat(list, notNullValue());
        assertThat(list.size(), is(1));
    }


    @Test
    public void testFindByRoomTemplateIdAndEnabled() throws Exception {
        Criterion<Room, Room> criterion = RoomCriterion.findByRoomTemplateIdAndEnabled(101L, Enabled.N);
        List<Room> list = mRoomDAO.search(criterion);

        assertThat(list, notNullValue());
        assertThat(list.size(), is(3));
    }


    @Test
    public void testFindByRoomTemplateId() throws Exception {
        Criterion<Room, Room> criterion = RoomCriterion.findByRoomTemplateId(101L);
        List<Room> list = mRoomDAO.search(criterion);

        assertThat(list, notNullValue());
        assertThat(list.size(), is(3));
    }

    @Test
    public void testFindByProfileIdAndActivityIdAndDateTime() throws Exception {
        long profileId = 100;
        long activityId = 105;
        DateTime startDate = new DateTime(2013, 12, 10, 10, 30, 0);
        DateTime endDate = new DateTime(2013, 12, 10, 11, 30, 0);
        Long pExcludeAppointmentId = null;
		Criterion<Room, Room> criterion = RoomCriterion.findByProfileIdAndActivityIdAndDateTime(profileId, activityId, startDate.toDate(), endDate.toDate(), pExcludeAppointmentId);
        List<Room> list = mRoomDAO.search(criterion);

        assertThat(list.size(), is(2));

    }
}
