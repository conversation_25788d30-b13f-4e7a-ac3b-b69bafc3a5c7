package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.*;
import static org.junit.Assert.*;
import static org.hamcrest.CoreMatchers.*;

import java.util.List;

import org.hibernate.HibernateException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.guitarcenter.scheduler.dao.LocationDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.LocationCriterion;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.enums.Enabled;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class LocationDAOImplTest {

	@Autowired
	@Qualifier("locationDAO")
	private LocationDAO	mLocationDAO;



	@Test(expected = HibernateException.class)
	public void testGet() throws Exception {
		Location location = mLocationDAO.get(100);
		assertThat(location.getLocationName(), is("Auction Systems Auctioneers Appraisers"));
		location.getUpdatedBy().getLastName();
	}



	@Test
	public void testGetByMode() throws Exception {
		Location location = mLocationDAO.get(100, FETCH_UPDATEBY_PERSON | FETCH_LOCATION_PROFILE | FETCH_SITE);
		assertThat(location.getLocationName(), is("Auction Systems Auctioneers Appraisers"));
		assertThat(location.getUpdatedBy().getLastName(), is("Update"));
		assertThat(location.getSite().getName(), is("Tilson Landscape"));
	}



	@Test(expected = HibernateException.class)
	public void testExample() throws Exception {
		Location example = new Location();
		example.setLocationName("Auction Systems Auctioneers Appraisers");
		List<Location> list = mLocationDAO.search(example);
		assertThat(list.size(), is(1));
		Location location = list.get(0);
		assertThat(location.getLocationName(), is("Auction Systems Auctioneers Appraisers"));
		location.getUpdatedBy().getLastName();
	}



	@Test
	public void testExampleByMode() throws Exception {
		Location example = new Location();
		example.setLocationName("Auction Systems Auctioneers Appraisers");
		List<Location> list = mLocationDAO.search(example, FETCH_UPDATEBY_PERSON | FETCH_LOCATION_PROFILE | FETCH_SITE);
		assertThat(list.size(), is(1));
		Location location = list.get(0);
		assertThat(location.getLocationName(), is("Auction Systems Auctioneers Appraisers"));
		assertThat(location.getUpdatedBy().getLastName(), is("Update"));
		assertThat(location.getSite().getName(), is("Tilson Landscape"));
	}



	@Test(expected = HibernateException.class)
	public void testSearchAll() throws Exception {
		List<Location> all = mLocationDAO.searchAll();
		assertThat(all.size(), is(3));
		Location location = all.get(0);
		location.getSite().getName();
	}



	@Test
	public void testSearchAllByMode() throws Exception {
		List<Location> all = mLocationDAO.searchAll(FETCH_UPDATEBY_PERSON | FETCH_LOCATION_PROFILE | FETCH_SITE);
		assertThat(all.size(), is(3));
		Location location = all.get(0);
		assertThat(location.getSite().getName(), notNullValue());
	}



	@Test
	public void testFindByLocationName() throws Exception {
		{
			Criterion<Location, Location> criterion = LocationCriterion.findByLocationName("m");
			List<Location> list = mLocationDAO.search(criterion, FETCH_LOCATION_PROFILE);
			assertThat(list.size(), is(1));
			Location location = list.get(0);
			assertThat(location.getLocationProfile(), nullValue());
		}
		{
			Criterion<Location, Location> criterion = LocationCriterion.findByLocationName("ac");
			List<Location> list = mLocationDAO.search(criterion, FETCH_LOCATION_PROFILE);
			assertThat(list.size(), is(1));
			Location location = list.get(0);
			assertThat(location.getLocationProfile().getEnabled(), is(Enabled.N));
		}
	}



	@Test
	public void testGetByProfileId() throws Exception {
		{
			Criterion<Location, Location> criterion = LocationCriterion.getByProfileId(100);
			Location location = mLocationDAO.get(criterion);

			assertThat(location, notNullValue());
			assertThat(location.getAddress1(), is("24 Bob Road"));
		}
		{
			Criterion<Location, Location> criterion = LocationCriterion.getByProfileId(0);
			Location location = mLocationDAO.get(criterion);

			assertThat(location, nullValue());
		}
	}
}
