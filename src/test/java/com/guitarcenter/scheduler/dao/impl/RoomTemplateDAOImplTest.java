package com.guitarcenter.scheduler.dao.impl;

import static org.junit.Assert.*;
import static org.hamcrest.CoreMatchers.*;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.guitarcenter.scheduler.dao.RoomTemplateDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.RoomTemplateCriterion;
import com.guitarcenter.scheduler.model.RoomTemplate;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class RoomTemplateDAOImplTest {

	@Autowired
	@Qualifier("roomTemplateDAO")
	private RoomTemplateDAO	mRoomTemplateDAO;



	@Test
	public void testHasByActivityId() throws Exception {
		Criterion<RoomTemplate, Boolean> criterion = RoomTemplateCriterion.hasByActivityId(100L);
		Boolean result = mRoomTemplateDAO.get(criterion);

		assertThat(result, is(Boolean.TRUE));
	}



	@Test
	public void testHasByServiceId() throws Exception {
		Criterion<RoomTemplate, Boolean> criterion = RoomTemplateCriterion.hasByServiced(100L);
		Boolean result = mRoomTemplateDAO.get(criterion);

		assertThat(result, is(Boolean.TRUE));
	}

}
