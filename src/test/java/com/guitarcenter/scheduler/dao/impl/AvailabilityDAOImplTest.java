package com.guitarcenter.scheduler.dao.impl;

import com.guitarcenter.scheduler.dao.AvailabilityDAO;
import com.guitarcenter.scheduler.dao.criterion.AvailabilityCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.dto.CheckingResultAvailabilityDTO;
import com.guitarcenter.scheduler.model.Availability;
import org.hamcrest.CoreMatchers;
import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Date;

import static org.junit.Assert.assertThat;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring/test-context.xml"})
public class AvailabilityDAOImplTest {

    @Autowired
    @Qualifier("availabilityDAO")
    private AvailabilityDAO mAvailabilityDAO;


    @Test
    public void testCheckProfileAndInstructorAndRoomAvailability() throws Exception {
        Long profileId = 100L;
        Long instructorId = 100L;
        Long roomId = 100L;
        Long parentRoomId = 101L;
        DateTime dateTime = new DateTime(2014, 9, 2, 20, 30, 00);
        Date startTime = dateTime.toDate();
        Date endTime = dateTime.plusMinutes(15).toDate();
        int dayOfWeek = dateTime.getDayOfWeek();
       /*Criterion<Availability, CheckingResultAvailabilityDTO> criterion = AvailabilityCriterion.checkProfileAndInstructorAndRoomAvailability(
                profileId, instructorId, dayOfWeek, startTime, endTime, roomId, parentRoomId);
        CheckingResultAvailabilityDTO result = mAvailabilityDAO.get(criterion);

        assertThat(result.getResult(), CoreMatchers.is(Boolean.FALSE));*/
    }

    @Test
    public void testGetByProfileIdAndInstructorId() throws Exception {
        long profileId = 100;
        long instructorId = 100;
        Criterion<Availability, Availability> criterion = AvailabilityCriterion.getByProfileIdAndInstructorId(profileId, instructorId);
        Availability availability = mAvailabilityDAO.get(criterion);

        assertThat(availability, CoreMatchers.notNullValue());

    }
}
