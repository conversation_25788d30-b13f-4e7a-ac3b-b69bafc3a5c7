package com.guitarcenter.scheduler.dao.impl;

import static org.junit.Assert.*;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.*;
import static org.hamcrest.CoreMatchers.*;

import java.util.List;

import org.hibernate.HibernateException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.guitarcenter.scheduler.dao.SiteDAO;
import com.guitarcenter.scheduler.model.Site;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class SiteDAOImplTest {

	@Autowired
	@Qualifier("siteDAO")
	private SiteDAO	mSiteDAO;



	@Test(expected = HibernateException.class)
	public void testSearchAll() throws Exception {
		List<Site> all = mSiteDAO.searchAll();
		assertThat(all.size(), is(10));
		all.get(0).getUpdatedBy().getLastName();
	}



	@Test
	public void testSearchAllByMode() throws Exception {
		List<Site> all = mSiteDAO.searchAll(FETCH_UPDATEBY_PERSON);
		assertThat(all.size(), is(10));
		assertThat(all.get(0).getUpdatedBy().getLastName(), is("Update"));
	}



	@Test
	public void testGet() throws Exception {
		Site site = mSiteDAO.get(100);
		assertThat(site.getSiteId(), is(100L));
		assertThat(site.getExternalId(), is("9917"));
	}



	@Test
	public void testGetByMode() throws Exception {
		Site site = mSiteDAO.get(100, FETCH_UPDATEBY_PERSON);
		assertThat(site.getSiteId(), is(100L));
		assertThat(site.getExternalId(), is("9917"));
		assertThat(site.getUpdatedBy(), notNullValue());
		assertThat(site.getUpdatedBy().getLastName(), is("Update"));
	}



	@Test
	public void testSearchExample() throws Exception {
		Site example = new Site();
		example.setName("Tilson Landscape");
		List<Site> list = mSiteDAO.search(example);
		assertThat(list.size(), is(1));
		Site site = list.get(0);
		assertThat(site.getSiteId(), is(100L));
		assertThat(site.getExternalId(), is("9917"));
	}



	@Test
	public void testSearchExampleByMode() throws Exception {
		Site example = new Site();
		example.setName("Tilson Landscape");
		List<Site> list = mSiteDAO.search(example, FETCH_UPDATEBY_PERSON);
		assertThat(list.size(), is(1));
		Site site = list.get(0);
		assertThat(site.getSiteId(), is(100L));
		assertThat(site.getExternalId(), is("9917"));
		assertThat(site.getUpdatedBy(), notNullValue());
		assertThat(site.getUpdatedBy().getLastName(), is("Update"));
	}

}
