package com.guitarcenter.scheduler.dao.impl;

import static org.hamcrest.CoreMatchers.*;
import static org.junit.Assert.*;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.*;

import java.util.List;

import org.hibernate.HibernateException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.hibernate6.Hibernate6Module;
import com.guitarcenter.scheduler.dao.PersonRoleDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.PersonRoleCriterion;
import com.guitarcenter.scheduler.model.PersonRole;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class PersonRoleDAOImplTest {

	@Autowired
	@Qualifier("personRoleDAO")
	private PersonRoleDAO	mPersonRoleDAO;



	@Test(expected = HibernateException.class)
	public void testGet() throws Exception {
		PersonRole personRole = mPersonRoleDAO.get(100);
		personRole.getLocation().getAddress1();
	}



	@Test
	public void testGetByMode() throws Exception {
		PersonRole personRole = mPersonRoleDAO.get(100, FETCH_UPDATEBY_PERSON | FETCH_LOCATION | FETCH_PERSON
				| FETCH_ROLE | FETCH_SITE);
		assertThat(personRole.getLocation().getCity(), is("Woodland Hills"));
		assertThat(personRole.getPerson().getLastName(), is("Cross"));
		assertThat(personRole.getRole().getRoleName(), is("role_1"));
		assertThat(personRole.getSite().getName(), is("Tilson Landscape"));
	}



	@Test(expected = UnsupportedOperationException.class)
	public void testExample() throws Exception {
		PersonRole example = new PersonRole();
		mPersonRoleDAO.search(example);
	}



	@Test(expected = UnsupportedOperationException.class)
	public void testExampleByMode() throws Exception {
		PersonRole example = new PersonRole();
		mPersonRoleDAO.search(example, FETCH_UPDATEBY_PERSON | FETCH_LOCATION | FETCH_PERSON | FETCH_ROLE | FETCH_SITE);
	}



	@Test(expected = HibernateException.class)
	public void testSearchAll() throws Exception {
		List<PersonRole> all = mPersonRoleDAO.searchAll();
		assertThat(all.size(), is(45));
		PersonRole personRole = all.get(0);
		personRole.getPerson().getFirstName();
	}



	@Test
	public void testSearchAllByMode() throws Exception {
		List<PersonRole> all = mPersonRoleDAO.searchAll(FETCH_UPDATEBY_PERSON | FETCH_LOCATION | FETCH_PERSON
				| FETCH_ROLE | FETCH_SITE);
		assertThat(all.size(), is(45));
		PersonRole personRole = all.get(0);
		assertThat(personRole.getPerson().getFirstName(), notNullValue());
	}



	@Test
	public void testFindByPerson() throws Exception {
		{
			Criterion<PersonRole, PersonRole> criterion = PersonRoleCriterion.findByPerson(100, 100);
			List<PersonRole> list = mPersonRoleDAO.search(criterion);
			assertThat(list.size(), is(1));
		}
		{
			Criterion<PersonRole, PersonRole> criterion = PersonRoleCriterion.findByPerson(100, 100);
			List<PersonRole> list = mPersonRoleDAO.search(criterion, FETCH_UPDATEBY_PERSON | FETCH_LOCATION
					| FETCH_PERSON | FETCH_ROLE | FETCH_SITE);
			assertThat(list.size(), is(1));
			assertThat(list.get(0).getPerson().getLastName(), is("Rowlands"));
		}
	}



	@Test
	public void testFindByPersonLocation() throws Exception {
		{
			Criterion<PersonRole, PersonRole> criterion = PersonRoleCriterion.findByPersonLocation(100, 100, 101);
			List<PersonRole> list = mPersonRoleDAO.search(criterion);
			assertThat(list.size(), is(1));
		}
		{
			Criterion<PersonRole, PersonRole> criterion = PersonRoleCriterion.findByPersonLocation(100, 100, 101);
			List<PersonRole> list = mPersonRoleDAO.search(criterion, FETCH_UPDATEBY_PERSON | FETCH_LOCATION
					| FETCH_PERSON | FETCH_ROLE | FETCH_SITE);
			assertThat(list.size(), is(1));
			assertThat(list.get(0).getPerson().getLastName(), is("Rowlands"));
			assertThat(list.get(0).getLocation().getLocationName(), is("Accredited Home Lenders"));
			assertThat(list.get(0).getRole().getRoleName(), is("role_3"));
		}
	}



	@Test
	public void testJSON() throws Exception {
		{
			PersonRole personRole = mPersonRoleDAO.get(100);
			ObjectMapper mapper = new ObjectMapper();
			mapper.registerModule(new Hibernate6Module());
			String json = mapper.writeValueAsString(personRole);
			assertThat(json, notNullValue());
			assertThat(json.length() > 0, is(true));
		}

		{
			PersonRole personRole = mPersonRoleDAO.get(100, FETCH_LOCATION);
			ObjectMapper mapper = new ObjectMapper();
			mapper.registerModule(new Hibernate6Module());
			String json = mapper.writeValueAsString(personRole);
			assertThat(json, notNullValue());
			assertThat(json.length() > 0, is(true));
		}
	}

}
