package com.guitarcenter.scheduler.dao.impl;

import static org.junit.Assert.*;
import static org.hamcrest.CoreMatchers.*;

import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.guitarcenter.scheduler.dao.ProfileServiceDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.ProfileServiceCriterion;
import com.guitarcenter.scheduler.model.ProfileService;
import com.guitarcenter.scheduler.model.enums.Enabled;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class ProfileServiceDAOImplTest {

	@Autowired
	@Qualifier("profileServiceDAO")
	private ProfileServiceDAO	mProfileServiceDAO;



	@Test
	public void testFindByProfileId() throws Exception {
		Criterion<ProfileService, ProfileService> criterion = ProfileServiceCriterion.findByProfileId(100);
		List<ProfileService> list = mProfileServiceDAO.search(criterion);

		assertThat(list, notNullValue());
		assertThat(list.size(), is(2));
	}



	@Test
	public void testFindByProfileIdAndEnabled() throws Exception {
		{
			Criterion<ProfileService, ProfileService> criterion = ProfileServiceCriterion.findByProfileIdAndEnabled(
					100, Enabled.N);
			List<ProfileService> list = mProfileServiceDAO.search(criterion);

			assertThat(list, notNullValue());
			assertThat(list.size(), is(0));
		}
		{
			Criterion<ProfileService, ProfileService> criterion = ProfileServiceCriterion.findByProfileIdAndEnabled(
					100, Enabled.Y);
			List<ProfileService> list = mProfileServiceDAO.search(criterion);

			assertThat(list, notNullValue());
			assertThat(list.size(), is(2));
		}
	}



	@Test
	public void testFindByServiceIdAndEnabled() throws Exception {
		{
			Criterion<ProfileService, ProfileService> criterion = ProfileServiceCriterion.findByServiceIdAndEnabled(
					100, Enabled.N);
			List<ProfileService> list = mProfileServiceDAO.search(criterion);

			assertThat(list, notNullValue());
			assertThat(list.size(), is(0));
		}
		{
			Criterion<ProfileService, ProfileService> criterion = ProfileServiceCriterion.findByServiceIdAndEnabled(
					100, Enabled.Y);
			List<ProfileService> list = mProfileServiceDAO.search(criterion);

			assertThat(list, notNullValue());
			assertThat(list.size(), is(1));
		}
	}



	@Test
	public void testGetByProfileIdAndServiceId() throws Exception {
		Criterion<ProfileService, ProfileService> criterion = ProfileServiceCriterion.getByProfileIdAndServiceId(100,
				100);
		ProfileService profileService = mProfileServiceDAO.get(criterion);

		assertThat(profileService, notNullValue());
	}



	@Test
	public void testHasServiceByServiceIds() throws Exception {
		Criterion<ProfileService, Boolean> criterion = ProfileServiceCriterion
				.hasServiceByServiceIds(new Long[] { 100L });
		Boolean result = mProfileServiceDAO.get(criterion);

		assertThat(result, is(true));
	}



	@Test
	public void testHasByProfileIdAndServiceIdAndEnabled() throws Exception {
		Criterion<ProfileService, Boolean> criterion = ProfileServiceCriterion.hasByProfileIdAndServiceIdAndEnabled(
				100L, 100L, Enabled.Y);
		Boolean result = mProfileServiceDAO.get(criterion);

		assertThat(result, is(true));
	}

}
