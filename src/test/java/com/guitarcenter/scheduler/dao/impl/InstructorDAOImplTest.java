package com.guitarcenter.scheduler.dao.impl;

import com.guitarcenter.scheduler.dao.InstructorDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.InstructorCriterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.enums.Enabled;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.junit.Ignore;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;
import java.util.TimeZone;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.junit.Assert.assertThat;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring/test-context.xml"})
public class InstructorDAOImplTest {

    @Autowired
    @Qualifier("instructorDAO")
    private InstructorDAO mInstructorDAO;


    @Test
    public void testFindByActivity() throws Exception {
        Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByActivityId(104);
        List<Instructor> list = mInstructorDAO.search(criterion, DAOHelper.FETCH_PERSON);
        assertThat(list, notNullValue());
        assertThat(list.size(), is(3));
        assertThat(list.get(0).getPerson().getFirstName(), notNullValue());
    }


    @Test
    public void testFindByLocationId() throws Exception {
        {
            Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByLocation(100);
            List<Instructor> list = mInstructorDAO.search(criterion, DAOHelper.FETCH_PERSON);
            assertThat(list, notNullValue());
            assertThat(list.size(), is(3));
            assertThat(list.get(0).getPerson().getFirstName(), notNullValue());
        }
        {
            Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByLocation(100, Enabled.Y);
            List<Instructor> list = mInstructorDAO.search(criterion, DAOHelper.FETCH_PERSON);
            assertThat(list, notNullValue());
            assertThat(list.size(), is(2));
            assertThat(list.get(0).getPerson().getFirstName(), notNullValue());
        }
        {
            Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByLocation(100, Enabled.N);
            List<Instructor> list = mInstructorDAO.search(criterion, DAOHelper.FETCH_PERSON);
            assertThat(list, notNullValue());
            assertThat(list.size(), is(1));
            assertThat(list.get(0).getPerson().getFirstName(), notNullValue());
        }
    }


    @Test
    public void testFindByLocationIdAndActivityId() throws Exception {
        {
            Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByLocationIdAndActivityId(101, 104,
                    Enabled.Y);
            List<Instructor> list = mInstructorDAO.search(criterion, DAOHelper.FETCH_PERSON);
            assertThat(list, notNullValue());
            assertThat(list.size(), is(0));
        }
        {
            Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByLocationIdAndActivityId(101, 104,
                    Enabled.N);
            List<Instructor> list = mInstructorDAO.search(criterion, DAOHelper.FETCH_PERSON);
            assertThat(list, notNullValue());
            assertThat(list.size(), is(1));
            assertThat(list.get(0).getPerson().getFirstName(), notNullValue());
        }
    }


    @Test
    public void testFindByLocationIdAndActivityIds() throws Exception {
        Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByLocationIdAndActivityIds(100, 105L,
                106L);
        List<Instructor> list = mInstructorDAO.search(criterion, DAOHelper.FETCH_PERSON);
        assertThat(list, notNullValue());
        assertThat(list.size(), is(1));
        assertThat(list.get(0).getPerson().getFirstName(), notNullValue());
    }


    @Test
    public void testFindByInstructorIds() throws Exception {
        {
            Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByInstructorIds(100L);
            List<Instructor> list = mInstructorDAO.search(criterion);

            assertThat(list, notNullValue());
            assertThat(list.size(), is(1));
        }
        {
            Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByInstructorIds(100L, 101L);
            List<Instructor> list = mInstructorDAO.search(criterion);

            assertThat(list, notNullValue());
            assertThat(list.size(), is(2));
        }
        {
            Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByInstructorIds();
            List<Instructor> list = mInstructorDAO.search(criterion);

            assertThat(list, notNullValue());
            assertThat(list.size(), is(0));
        }
    }


    @Test
    public void testHasAppointmentByInstructorAndDayOfWeek() throws Exception {
        Criterion<Instructor, Boolean> criterion = InstructorCriterion.hasAppointmentByInstructorAndDayOfWeek(100L, 2);
        Boolean result = mInstructorDAO.get(criterion);

        assertThat(result, is(false));

    }


    @Test
    public void testCheckAppointmentDateTimeByInstructorAndDateTime() throws Exception {
        Long instructor = 100L;
        DateTime startTime = new DateTime(2013, 11, 19, 11, 0, 1, 1);
        DateTime endTime = new DateTime(2013, 11, 19, 14, 0, 59, 1);
        int dayOfWeek = startTime.getDayOfWeek();
        Criterion<Instructor, Boolean> criterion = InstructorCriterion.checkAppointmentDateTimeByInstructorAndDateTime(instructor, startTime.toDate(), endTime.toDate(), dayOfWeek);
        Boolean result = mInstructorDAO.get(criterion);

        assertThat(result, is(true));

    }

    @Test
    @Ignore //TODO:Jose Ignore this test temporarily until find about timezone solution
    public void testFindByProfileIdAndActivityIdAndDateTime() throws Exception {
        long profileId = 100;
        long activityId = 105;
        DateTime startDate = new DateTime(2013, 12, 10, 9, 15, 0, 1);
        DateTime endDate = new DateTime(2013, 12, 10, 12, 15, 59, 1);
        Long pExcludeAppointmentId = null;
        System.out.println(startDate.toDate());
        System.out.println(endDate.toDate());
        Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByProfileIdAndActivityIdAndDateTime(profileId, activityId, startDate.toDate(), endDate.toDate(), pExcludeAppointmentId);
        List<Instructor> list = mInstructorDAO.search(criterion);

        assertThat(list.size(), is(1));

    }
}
