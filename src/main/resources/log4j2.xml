<?xml version="1.0" encoding="UTF-8"?>
        <Configuration status="WARN" monitorInterval="30">
            <Appenders>
                <!-- Console Appender for Development/Debugging -->
                <Console name="Console" target="SYSTEM_OUT">
                    <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
                </Console>

                <!-- File Appender for Production Logging -->
                <RollingFile name="RollingFile"
                             fileName="${sys:catalina.base}/logs/scheduler.log"
                             filePattern="${sys:catalina.base}/logs/scheduler-%d{yyyy-MM-dd}.log.gz">
                    <PatternLayout>
                        <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</pattern>
                    </PatternLayout>
                    <Policies>
                        <!-- Rotate logs daily and when they exceed 50MB -->
                        <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                        <SizeBasedTriggeringPolicy size="50MB"/>
                    </Policies>
                </RollingFile>
            </Appenders>

            <Loggers>
                <!-- Root logger (Production Level) -->
                <Root level="INFO">
                    <AppenderRef ref="RollingFile"/>
                    <AppenderRef ref="Console"/>
                </Root>

                <!-- Enable Debug Logging for Spring and Hibernate in Production as needed -->
                <Logger name="org.springframework" level="WARN" additivity="false">
                    <AppenderRef ref="RollingFile"/>
                    <AppenderRef ref="Console"/>
                </Logger>

                <Logger name="org.hibernate" level="WARN" additivity="false">
                    <AppenderRef ref="RollingFile"/>
                    <AppenderRef ref="Console"/>
                </Logger>

                <!-- Uncomment to log SQL and SQL Binding for Hibernate -->
                <!--
                <Logger name="org.hibernate.SQL" level="DEBUG" additivity="false">
                    <AppenderRef ref="RollingFile"/>
                </Logger>
                <Logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="TRACE" additivity="false">
                    <AppenderRef ref="RollingFile"/>
                </Logger>
                -->

                <!-- Specific Logger for the Scheduler Module -->
                <Logger name="com.guitarcenter.scheduler" level="INFO" additivity="false">
                    <AppenderRef ref="RollingFile"/>
                    <AppenderRef ref="Console"/>
                </Logger>

                <!-- Spring Security Logging (Optional) -->
                <Logger name="org.springframework.security" level="WARN" additivity="false">
                    <AppenderRef ref="RollingFile"/>
                    <AppenderRef ref="Console"/>
                </Logger>

                <!-- Spring Web Logging (Optional) -->
                <Logger name="org.springframework.web" level="ERROR" additivity="false">
                    <AppenderRef ref="RollingFile"/>
                    <AppenderRef ref="Console"/>
                </Logger>
            </Loggers>
        </Configuration>