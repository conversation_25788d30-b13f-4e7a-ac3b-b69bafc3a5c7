#spring.profiles.active=prod
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true
server.servlet.context-path=/lessons-api/v1
server.port=8080

# Database Configuration
jdbc.schedulerDB.driverClassName=
jdbc.schedulerDB.url=
jdbc.schedulerDB.username=
jdbc.schedulerDB.password=

# Mail Session Configuration
mail.session.name=mail/session
mail.session.auth=Container
mail.smtp.host=prod-smtp.guitarcenter.com
mail.smtp.port=25
mail.debug=false