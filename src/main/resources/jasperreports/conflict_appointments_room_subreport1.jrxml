<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.0.3.final using JasperReports Library version 6.0.3  -->
<!-- 2015-09-02T17:20:47 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="conflict_appointments_room_subreport1" pageWidth="555" pageHeight="802" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="63f80bd3-74ed-49da-8184-8f7675b3ecc7">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="rowBgStyle" mode="Opaque" fill="Solid" pattern="" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style mode="Opaque" backcolor="#D9DADB"/>
		</conditionalStyle>
	</style>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="activityType" class="java.lang.String">
		<fieldDescription><![CDATA[activityType]]></fieldDescription>
	</field>
	<field name="cancelled" class="java.lang.String">
		<fieldDescription><![CDATA[cancelled]]></fieldDescription>
	</field>
	<field name="class" class="java.lang.Class">
		<fieldDescription><![CDATA[class]]></fieldDescription>
	</field>
	<field name="customerName" class="java.lang.String">
		<fieldDescription><![CDATA[customerName]]></fieldDescription>
	</field>
	<field name="duration" class="java.lang.String">
		<fieldDescription><![CDATA[duration]]></fieldDescription>
	</field>
	<field name="endDate" class="java.lang.String">
		<fieldDescription><![CDATA[endDate]]></fieldDescription>
	</field>
	<field name="endTime" class="java.lang.String">
		<fieldDescription><![CDATA[endTime]]></fieldDescription>
	</field>
	<field name="instructorId" class="java.lang.Long">
		<fieldDescription><![CDATA[instructorId]]></fieldDescription>
	</field>
	<field name="roomName" class="java.lang.String">
		<fieldDescription><![CDATA[roomName]]></fieldDescription>
	</field>
	<field name="queryEndDate" class="java.lang.String">
		<fieldDescription><![CDATA[queryEndDate]]></fieldDescription>
	</field>
	<field name="queryStartDate" class="java.lang.String">
		<fieldDescription><![CDATA[queryStartDate]]></fieldDescription>
	</field>
	<field name="startDate" class="java.lang.String">
		<fieldDescription><![CDATA[startDate]]></fieldDescription>
	</field>
	<field name="startTime" class="java.lang.String">
		<fieldDescription><![CDATA[startTime]]></fieldDescription>
	</field>
	<field name="timeFrame" class="java.lang.String">
		<fieldDescription><![CDATA[timeFrame]]></fieldDescription>
	</field>
	<columnHeader>
		<band height="21" splitType="Stretch">
			<staticText>
				<reportElement x="15" y="1" width="100" height="20" backcolor="#D9DADB" uuid="e5c2b06b-01f5-42b7-985b-4c2b7db765bd">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" isPdfEmbedded="false"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Room Name]]></text>
			</staticText>
			<staticText>
				<reportElement x="115" y="1" width="70" height="20" backcolor="#D9DADB" uuid="836e1120-**************-e24478671790">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="185" y="1" width="110" height="20" backcolor="#D9DADB" uuid="c1fcd1ed-ad6d-4fe2-8f42-992f35fb1a7b">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Time Frame]]></text>
			</staticText>
			<staticText>
				<reportElement x="295" y="1" width="70" height="20" backcolor="#D9DADB" uuid="59e56e87-8401-4ed5-b7ae-d2dc6cfbfaaf">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Duration]]></text>
			</staticText>
			<staticText>
				<reportElement x="365" y="1" width="75" height="20" backcolor="#D9DADB" uuid="c5a899d5-c077-4470-9f56-83b3c6ca5c50">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Activity]]></text>
			</staticText>
			<staticText>
				<reportElement x="440" y="1" width="100" height="20" backcolor="#D9DADB" uuid="44a32b2b-d9dd-4eea-8205-4f7f522bebd7">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Customer Name]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="28" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" x="15" y="0" width="100" height="28" uuid="04f0a309-81c8-4681-ac56-22c7a4bcfa8e">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="false"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{roomName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="115" y="0" width="70" height="28" uuid="01cca361-80a8-4038-a3d7-179d9e6f34ff">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{startDate}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="185" y="0" width="110" height="28" uuid="4083dcdc-2c78-4ca7-947f-e030ac3d16e2">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{timeFrame}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="295" y="0" width="70" height="28" uuid="69edda9a-911c-45bf-996f-6e00779be38e">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{duration}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="365" y="0" width="75" height="28" uuid="58632c25-5816-4b3c-a1ab-7a2fd53d1f13">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{activityType}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="440" y="0" width="100" height="28" uuid="fb9a1204-c457-4414-8cdc-6b71c8b29930">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{customerName}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
