<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.0.3.final using JasperReports Library version 6.0.3  -->
<!-- 2015-10-08T15:58:16 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="instructor_outside_appointments_report" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="f8f45a09-0087-4310-8d07-bd360eb21ae7">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D:\\reportfile\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="TO_DATE" class="java.lang.String"/>
	<parameter name="FROM_DATE" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="currentDate" class="java.lang.String">
		<fieldDescription><![CDATA[currentDate]]></fieldDescription>
	</field>
	<field name="instructorInfo" class="java.util.List">
		<fieldDescription><![CDATA[instructorInfo]]></fieldDescription>
	</field>
	<field name="instructorInfoDTO" class="com.guitarcenter.scheduler.dto.InstructorInfoDTO">
		<fieldDescription><![CDATA[instructorInfoDTO]]></fieldDescription>
	</field>
	<field name="instructorReports" class="java.util.List">
		<fieldDescription><![CDATA[instructorReports]]></fieldDescription>
	</field>
	<group name="currentDateGroup">
		<groupExpression><![CDATA[$F{currentDate}]]></groupExpression>
	</group>
	<group name="instructorInfoGroup" isStartNewPage="true">
		<groupExpression><![CDATA[$F{instructorInfoDTO}]]></groupExpression>
		<groupHeader>
			<band splitType="Stretch"/>
		</groupHeader>
		<groupFooter>
			<band height="33"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="75" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="17" width="555" height="40" uuid="0e4cc49b-65d5-4394-9c82-4c53dd85e9ec"/>
				<textElement textAlignment="Center">
					<font size="28" isBold="true"/>
				</textElement>
				<text><![CDATA[Appointments Outside Time Range Report]]></text>
			</staticText>
			<staticText>
				<reportElement x="184" y="45" width="31" height="22" backcolor="#D9DADB" uuid="5d331d64-9623-40a2-bbe2-8996bf6fd71b"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" isStrikeThrough="false"/>
				</textElement>
				<text><![CDATA[From:]]></text>
			</staticText>
			<staticText>
				<reportElement x="287" y="45" width="20" height="22" backcolor="#D9DADB" uuid="1ea0a8db-a599-4b04-8f87-e7784f7a4687"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[To:]]></text>
			</staticText>
			<textField>
				<reportElement x="215" y="45" width="87" height="22" uuid="0bafc9f3-9552-4302-a106-66f640f0abba"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{FROM_DATE}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="307" y="45" width="100" height="22" uuid="a8be6547-3bc6-40ce-9fe1-75c6021dca9c"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{TO_DATE}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="33" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport>
				<reportElement x="0" y="20" width="555" height="13" uuid="41fb4888-e512-4b32-b067-1e54f3b686fd">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource((Collection)$F{instructorReports})]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "instructor_outside_appointments_report_subreport1.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="12" splitType="Stretch"/>
	</detail>
</jasperReport>
