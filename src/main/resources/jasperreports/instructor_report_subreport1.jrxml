<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.8.0.final using JasperReports Library version 6.8.0-2ed8dfabb690ff337a5797129f2cd92902b0c87b  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="instructor_report_subreport1" pageWidth="734" pageHeight="752" whenNoDataType="AllSectionsNoDetail" columnWidth="734" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="f4407222-3d15-4bc9-8617-ee2ce90076ff">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="479"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="521"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="rowBgStyle" mode="Opaque" fill="Solid" pattern="" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style mode="Opaque" backcolor="#D9DADB"/>
		</conditionalStyle>
	</style>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="activityType" class="java.lang.String">
		<fieldDescription><![CDATA[activityType]]></fieldDescription>
	</field>
	<field name="roomName" class="java.lang.String">
		<fieldDescription><![CDATA[roomName]]></fieldDescription>
	</field>
	<field name="attendees" class="java.lang.Integer">
		<fieldDescription><![CDATA[attendees]]></fieldDescription>
	</field>
	<field name="cancelled" class="java.lang.String">
		<fieldDescription><![CDATA[cancelled]]></fieldDescription>
	</field>
<!--	<field name="class" class="java.lang.Class">
		<fieldDescription><![CDATA[class]]></fieldDescription>
	</field>-->
	<field name="customerName" class="java.lang.String">
		<fieldDescription><![CDATA[customerName]]></fieldDescription>
	</field>
	<field name="duration" class="java.lang.String">
		<fieldDescription><![CDATA[duration]]></fieldDescription>
	</field>
	<field name="endDate" class="java.lang.String">
		<fieldDescription><![CDATA[endDate]]></fieldDescription>
	</field>
	<field name="endTime" class="java.lang.String">
		<fieldDescription><![CDATA[endTime]]></fieldDescription>
	</field>
	<field name="in" class="java.lang.String">
		<fieldDescription><![CDATA[in]]></fieldDescription>
	</field>
	<field name="instructorId" class="java.lang.Long">
		<fieldDescription><![CDATA[instructorId]]></fieldDescription>
	</field>
	<field name="instructorName" class="java.lang.String">
		<fieldDescription><![CDATA[instructorName]]></fieldDescription>
	</field>
	<field name="noShow" class="java.lang.String">
		<fieldDescription><![CDATA[noShow]]></fieldDescription>
	</field>
	<field name="out" class="java.lang.String">
		<fieldDescription><![CDATA[out]]></fieldDescription>
	</field>
	<field name="queryEndDate" class="java.lang.String">
		<fieldDescription><![CDATA[queryEndDate]]></fieldDescription>
	</field>
	<field name="queryStartDate" class="java.lang.String">
		<fieldDescription><![CDATA[queryStartDate]]></fieldDescription>
	</field>
	<field name="show" class="java.lang.String">
		<fieldDescription><![CDATA[show]]></fieldDescription>
	</field>
	<field name="Show/No Show/Cancel" class="java.lang.String">
		<fieldDescription><![CDATA[signitureBlock]]></fieldDescription>
	</field>
	<field name="startDate" class="java.lang.String">
		<fieldDescription><![CDATA[startDate]]></fieldDescription>
	</field>
	<field name="startTime" class="java.lang.String">
		<fieldDescription><![CDATA[startTime]]></fieldDescription>
	</field>
	<field name="timeFrame" class="java.lang.String">
		<fieldDescription><![CDATA[timeFrame]]></fieldDescription>
	</field>
	<field name="external_id" class="java.lang.String"/>
	<columnHeader>
		<band height="32" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<staticText>
				<reportElement x="384" y="0" width="46" height="31" backcolor="#D9DADB" uuid="517f178d-7b14-46c6-8cc9-522f6d4e0b2b">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[ Activity]]></text>
			</staticText>
			<staticText>
				<reportElement x="310" y="0" width="74" height="31" backcolor="#D9DADB" uuid="517f178d-7b14-46c6-8cc9-522f6d4e0b2b">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[ Room Name]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="70" y="0" width="90" height="32" backcolor="#D9DADB" uuid="449596b4-820a-48b3-8a8e-af573a884bf8">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" isPdfEmbedded="false"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[ Instructor Name]]></text>
			</staticText>
			<staticText>
				<reportElement x="210" y="0" width="50" height="31" backcolor="#D9DADB" uuid="cb70b2ac-ec10-4365-a242-6cee19b6c750">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[ Time Frame]]></text>
			</staticText>
			<staticText>
				<reportElement x="430" y="0" width="60" height="31" backcolor="#D9DADB" uuid="774056d1-56ca-4460-a731-3622ca2ecf35">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[ Customer Name]]></text>
			</staticText>
			<staticText>
				<reportElement x="260" y="0" width="50" height="31" backcolor="#D9DADB" uuid="f7a4ea16-fd68-4e72-8817-de0491a17b4c">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[ Duration]]></text>
			</staticText>
			<staticText>
				<reportElement x="160" y="0" width="50" height="31" backcolor="#D9DADB" uuid="a8ae4038-99e6-4b6a-87df-b273c1d8fcd3">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="490" y="0" width="82" height="32" backcolor="#D9DADB" uuid="a304e3a2-ad35-444e-a997-7a17bb73fe15">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[Show/NoShow /Cancel]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="70" height="32" uuid="0911cbae-e3fc-415d-8d7e-fdcb70ccfc55">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[Instructor #]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="48" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" x="70" y="0" width="90" height="48" uuid="101e59f4-6664-4716-a75f-ec6ef4092fc9">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="false"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{instructorName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="210" y="0" width="50" height="48" uuid="7ee71c73-090f-4173-826c-96713acf68f3">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{timeFrame}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement style="rowBgStyle" x="490" y="0" width="82" height="48" uuid="92331f1f-f926-45dd-a2eb-f5258fd0a109">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField>
				<reportElement style="rowBgStyle" x="430" y="0" width="60" height="48" uuid="d3ae408a-6ba9-4f97-81a1-00fbb153b1fe">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{customerName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="383" y="0" width="47" height="48" uuid="a92b004b-9e28-430b-a6e2-ce82616bd3b2">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{activityType}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="310" y="0" width="74" height="48" uuid="a92b004b-9e28-430b-a6e2-ce82616bd3b2">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{roomName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="260" y="0" width="50" height="48" uuid="8cb389f5-1811-416f-92f6-2aa8791637f1">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{duration}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="160" y="0" width="50" height="48" uuid="70a0ce6c-3ae7-4e53-b0bc-23b0f9d34641">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{startDate}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="0" y="0" width="70" height="48" uuid="558f2075-a3fe-44a1-b81d-8c6f5f833723">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="false"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{external_id}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="12">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="572" height="12" backcolor="#706B6B" uuid="c1b5e277-e16f-4360-8d41-83769d477d13">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</pageFooter>
</jasperReport>
