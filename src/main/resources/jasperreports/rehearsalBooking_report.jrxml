<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report3" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="3cfa426e-f3f7-4167-b5c8-e75e9f987b02">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<subDataset name="Table Dataset 1" uuid="84352f9f-70bd-46a6-9457-1f97e6b6e860"/>
	<subDataset name="Table Dataset 2" uuid="fab48ff9-077e-4ed5-ba6b-65010e301a93"/>
	<subDataset name="Table Dataset 3" uuid="4d8a08ca-2687-4af7-81b7-64b44f28350a"/>
	<subDataset name="Table Dataset 4" uuid="d319a6fd-8845-4547-ad8a-3d1dbf61519b"/>
	<subDataset name="dataset1" uuid="4f8e6b73-b7d0-45ff-8b77-bc65384ddad6"/>
	<parameter name="FROM_DATE" class="java.lang.String"/>
	<parameter name="TO_DATE" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D:\\WorkSpace\\scheduler\\src\\main\\resources\\jasperreports\\"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="currentDate" class="java.lang.String">
		<fieldDescription><![CDATA[currentDate]]></fieldDescription>
	</field>
	<field name="list" class="java.util.List">
		<fieldDescription><![CDATA[list]]></fieldDescription>
	</field>
	<group name="rehearsal group" isStartNewPage="true">
		<groupExpression><![CDATA[$F{currentDate}]]></groupExpression>
		<groupHeader>
			<band height="23">
				<textField>
					<reportElement uuid="9b37d895-8e4b-4bac-aa9a-d672362f3369" x="0" y="3" width="122" height="20"/>
					<textElement/>
					<textFieldExpression><![CDATA[$F{currentDate}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="1"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="80" splitType="Stretch">
			<staticText>
				<reportElement uuid="6329e75d-0092-4361-955c-5d4f179bda94" x="0" y="0" width="555" height="79"/>
				<textElement textAlignment="Center">
					<font size="28"/>
				</textElement>
				<text><![CDATA[Rehearsal Booking Report]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="4268eb37-5747-4729-a89b-f5a95f244106" x="144" y="53" width="33" height="20" backcolor="#D9DADB"/>
				<textElement textAlignment="Center"/>
				<text><![CDATA[From]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="98cba292-8647-42a3-ac7f-8bf5e0ab56b2" x="277" y="53" width="30" height="20" backcolor="#D9DADB"/>
				<textElement textAlignment="Center"/>
				<text><![CDATA[To]]></text>
			</staticText>
			<textField>
				<reportElement uuid="8eba1312-3f55-44dd-af93-79d3c07f0ac3" x="177" y="53" width="100" height="20"/>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$P{FROM_DATE}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="69ebb0d5-9273-4f1f-8388-3204d8e3b394" x="307" y="53" width="100" height="20"/>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$P{TO_DATE}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="17">
			<subreport>
				<reportElement uuid="be9e91cb-9db0-4712-9258-f4225653b011" x="0" y="0" width="555" height="17"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource((Collection)$F{list})]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "rehearsalBooking_report_subreport1.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
