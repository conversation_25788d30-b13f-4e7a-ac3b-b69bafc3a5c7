<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.0.3.final using JasperReports Library version 6.0.3  -->
<!-- 2015-09-02T16:28:18 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="conflict_appointments_room_report" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="700f530e-e173-4e97-b50e-ae39bdd6050e">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D:\\reportfile\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="TO_DATE" class="java.lang.String"/>
	<parameter name="FROM_DATE" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="currentDate" class="java.lang.String">
		<fieldDescription><![CDATA[currentDate]]></fieldDescription>
	</field>
	<field name="instructorInfo" class="java.util.List">
		<fieldDescription><![CDATA[instructorInfo]]></fieldDescription>
	</field>
	<field name="instructorInfoDTO" class="com.guitarcenter.scheduler.dto.InstructorInfoDTO">
		<fieldDescription><![CDATA[instructorInfoDTO]]></fieldDescription>
	</field>
	<field name="instructorReports" class="java.util.List">
		<fieldDescription><![CDATA[instructorReports]]></fieldDescription>
	</field>
	<group name="currentDateGroup">
		<groupExpression><![CDATA[$F{currentDate}]]></groupExpression>
	</group>
	<group name="instructorInfoGroup" isStartNewPage="true">
		<groupExpression><![CDATA[$F{instructorInfoDTO}]]></groupExpression>
		<groupHeader>
			<band splitType="Stretch"/>
		</groupHeader>
		<groupFooter>
			<band height="33"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="75" splitType="Stretch">
			<property name="local_mesure_unitheight" value="pixel"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<staticText>
				<reportElement x="0" y="17" width="555" height="40" uuid="4d602e1b-1cf4-46e9-8f1f-bb61c761ada6"/>
				<textElement textAlignment="Center">
					<font size="28" isBold="true"/>
				</textElement>
				<text><![CDATA[Conflicting Appointments By Room]]></text>
			</staticText>
			<staticText>
				<reportElement x="184" y="45" width="31" height="22" backcolor="#D9DADB" uuid="7529354a-9415-4223-884d-f65e3b279803"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" isStrikeThrough="false"/>
				</textElement>
				<text><![CDATA[From:]]></text>
			</staticText>
			<staticText>
				<reportElement x="287" y="45" width="20" height="22" backcolor="#D9DADB" uuid="14b6fd00-8ac2-43e9-aedb-45ec8a318259"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[To:]]></text>
			</staticText>
			<textField>
				<reportElement x="215" y="45" width="87" height="22" uuid="aed3be46-108f-48fe-84f8-5edaf764f007"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{FROM_DATE}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="307" y="45" width="100" height="22" uuid="7acdf8bc-1585-4848-adfb-6372fc792819"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{TO_DATE}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="33" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport>
				<reportElement x="0" y="20" width="555" height="13" uuid="629be19b-f113-442e-9b2b-46102c723b94">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource((Collection)$F{instructorReports})]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "conflict_appointments_room_subreport1.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="12" splitType="Stretch"/>
	</detail>
</jasperReport>
