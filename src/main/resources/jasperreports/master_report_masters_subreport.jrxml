<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.8.0.final using JasperReports Library version 6.8.0-2ed8dfabb690ff337a5797129f2cd92902b0c87b  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="master_report_masters_subreport" pageWidth="572" pageHeight="752" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="ce917728-3251-4e56-806c-24b337665562">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="rowBgStyle" mode="Opaque" fill="Solid" pattern="" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style mode="Opaque" backcolor="#D9DADB"/>
		</conditionalStyle>
	</style>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="duration" class="java.lang.String">
		<fieldDescription><![CDATA[duration]]></fieldDescription>
	</field>
	<field name="notes" class="java.lang.String">
		<fieldDescription><![CDATA[notes]]></fieldDescription>
	</field>
	<field name="startDateStr" class="java.lang.String">
		<fieldDescription><![CDATA[startDateStr]]></fieldDescription>
	</field>
	<field name="customerName" class="java.lang.String">
		<fieldDescription><![CDATA[customerName]]></fieldDescription>
	</field>
	<field name="endDateStr" class="java.lang.String">
		<fieldDescription><![CDATA[endDateStr]]></fieldDescription>
	</field>
	<field name="activityType" class="java.lang.String">
		<fieldDescription><![CDATA[activityType]]></fieldDescription>
	</field>
	<field name="endTime" class="java.lang.String">
		<fieldDescription><![CDATA[endTime]]></fieldDescription>
	</field>
	<field name="queryStartDate" class="java.lang.String">
		<fieldDescription><![CDATA[queryStartDate]]></fieldDescription>
	</field>
	<field name="queryEndDate" class="java.lang.String">
		<fieldDescription><![CDATA[queryEndDate]]></fieldDescription>
	</field>
	<field name="instructorName" class="java.lang.String">
		<fieldDescription><![CDATA[instructorName]]></fieldDescription>
	</field>
	<field name="profileRoomName" class="java.lang.String">
		<fieldDescription><![CDATA[profileRoomName]]></fieldDescription>
	</field>
	<field name="startTime" class="java.lang.String">
		<fieldDescription><![CDATA[startTime]]></fieldDescription>
	</field>
	<columnHeader>
		<band height="82" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="0" y="-3" width="549" height="37" forecolor="#FFFFFF" backcolor="#666666" uuid="c339dce2-5c2c-46de-a65c-a0b987b97d92">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle" markup="none">
					<font size="22" isBold="true"/>
				</textElement>
				<text><![CDATA[Daily Master Schedule]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="34" width="40" height="47" backcolor="#D9DADB" uuid="de579636-2eec-4859-8f22-352043b61298"/>
				<box leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Time]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="40" y="34" width="50" height="47" backcolor="#D9DADB" uuid="4fda385f-2a2c-4691-924c-a12198d7ec3f"/>
				<box leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Duration]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="90" y="34" width="70" height="47" backcolor="#D9DADB" uuid="3e2f1066-dde9-426f-8b72-4e41f6d0fe85"/>
				<box leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Activity Type]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="160" y="34" width="90" height="47" backcolor="#D9DADB" uuid="dea9c747-5e97-44f1-b149-448a31575184"/>
				<box leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Instructor Name]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="250" y="34" width="70" height="47" backcolor="#D9DADB" uuid="dea9c747-5e97-44f1-b149-448a31575184"/>
				<box leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" isStrikeThrough="false"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Room Name]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="320" y="34" width="30" height="47" backcolor="#D9DADB" uuid="cc52eda3-7217-4c74-9d37-3bbc4c1ffbf0"/>
				<box leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[In]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="350" y="34" width="30" height="47" backcolor="#D9DADB" uuid="c4dca5ea-10f2-49c0-ad92-1c64469e6fd2"/>
				<box leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Out]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="380" y="34" width="90" height="47" backcolor="#D9DADB" uuid="611db4db-0b03-4333-8e2e-8417ab0941f9"/>
				<box leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Customer Name]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="470" y="34" width="78" height="47" backcolor="#D9DADB" uuid="98d33b37-270f-46c0-909d-2286fc3cd686">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Notes]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="48" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.spreadsheet.SpreadsheetLayout"/>
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" stretchType="RelativeToTallestObject" x="0" y="0" width="40" height="48" uuid="4016c16b-8d46-4b68-acda-d4df5e8c88d2"/>
				<box topPadding="5" leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{startTime}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" stretchType="RelativeToTallestObject" x="40" y="0" width="50" height="48" uuid="d7a29611-3414-4a44-8dc4-74148f510372"/>
				<box topPadding="5" leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{duration}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" stretchType="RelativeToTallestObject" x="90" y="0" width="70" height="48" uuid="ed14fdb7-87fd-4902-9f51-e32be54a9afc"/>
				<box topPadding="5" leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{activityType}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" stretchType="RelativeToTallestObject" x="160" y="0" width="90" height="48" uuid="57f00a74-5b11-4ec7-b835-16d83f6c56f4"/>
				<box topPadding="5" leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{instructorName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" stretchType="RelativeToTallestObject" x="250" y="0" width="70" height="48" uuid="57f00a74-5b11-4ec7-b835-16d83f6c56f4"/>
				<box topPadding="5" leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profileRoomName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement style="rowBgStyle" stretchType="RelativeToTallestObject" x="320" y="0" width="30" height="48" uuid="7318e3b3-488f-498c-b752-913022799cc1"/>
				<box>
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement style="rowBgStyle" stretchType="RelativeToTallestObject" x="350" y="0" width="30" height="48" uuid="b6604d4d-1c61-432c-94e0-db1d325d1b78"/>
				<box>
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" stretchType="RelativeToTallestObject" x="380" y="0" width="90" height="48" uuid="fd9fc12f-8e5f-4acd-9a20-dd2864eccd6d"/>
				<box topPadding="5" leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{customerName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" stretchType="RelativeToTallestObject" x="470" y="0" width="78" height="48" uuid="df696eac-8a92-44f4-a147-996318a3cd89">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="5" leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{notes}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="28">
			<staticText>
				<reportElement mode="Opaque" x="-1" y="0" width="550" height="10" backcolor="#666666" uuid="6d157bb7-974c-4059-ba85-691fef589355">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</pageFooter>
</jasperReport>
