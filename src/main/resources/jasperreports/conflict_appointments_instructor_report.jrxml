<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.0.3.final using JasperReports Library version 6.0.3  -->
<!-- 2015-10-08T16:24:54 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="conflict_appointments_instructor_report" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="6c546de2-9ba7-4a18-a640-5cee46398241">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D:\\reportfile\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="TO_DATE" class="java.lang.String"/>
	<parameter name="FROM_DATE" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="currentDate" class="java.lang.String">
		<fieldDescription><![CDATA[currentDate]]></fieldDescription>
	</field>
	<field name="instructorInfo" class="java.util.List">
		<fieldDescription><![CDATA[instructorInfo]]></fieldDescription>
	</field>
	<field name="instructorInfoDTO" class="com.guitarcenter.scheduler.dto.InstructorInfoDTO">
		<fieldDescription><![CDATA[instructorInfoDTO]]></fieldDescription>
	</field>
	<field name="instructorReports" class="java.util.List">
		<fieldDescription><![CDATA[instructorReports]]></fieldDescription>
	</field>
	<group name="currentDateGroup">
		<groupExpression><![CDATA[$F{currentDate}]]></groupExpression>
	</group>
	<group name="instructorInfoGroup" isStartNewPage="true">
		<groupExpression><![CDATA[$F{instructorInfoDTO}]]></groupExpression>
		<groupHeader>
			<band splitType="Stretch"/>
		</groupHeader>
		<groupFooter>
			<band height="33"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="75" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="17" width="555" height="40" uuid="a56e835c-578e-4b58-aff5-4658648998b7"/>
				<textElement textAlignment="Center">
					<font size="28" isBold="true"/>
				</textElement>
				<text><![CDATA[Conflicting Appointments By Instructor]]></text>
			</staticText>
			<staticText>
				<reportElement x="184" y="45" width="31" height="22" backcolor="#D9DADB" uuid="4e51567a-2b4e-476e-a4b9-5be54b81f83e"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" isStrikeThrough="false"/>
				</textElement>
				<text><![CDATA[From:]]></text>
			</staticText>
			<staticText>
				<reportElement x="287" y="45" width="20" height="22" backcolor="#D9DADB" uuid="2311e9fc-31b7-44f0-b998-64dd83826f22"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[To:]]></text>
			</staticText>
			<textField>
				<reportElement x="215" y="45" width="87" height="22" uuid="8bfb44d8-b224-40e8-b38d-278ba2437b5c"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{FROM_DATE}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="307" y="45" width="100" height="22" uuid="5deedb3a-508c-463e-99fb-41cbf570791f"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{TO_DATE}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="33" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport>
				<reportElement x="0" y="20" width="555" height="13" uuid="ffdff377-7f8b-4a0d-b45e-12f5dd67f01e">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource((Collection)$F{instructorReports})]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "conflict_appointments_instructor_subreport1.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="12" splitType="Stretch"/>
	</detail>
</jasperReport>
