<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.8.0.final using JasperReports Library version 6.8.0-2ed8dfabb690ff337a5797129f2cd92902b0c87b  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="master_report_employee_subreport" pageWidth="572" pageHeight="752" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="e1b55edc-62ae-45ff-808f-c4dd5d7f8f2a">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="rowBgStyle" mode="Opaque" fill="Solid" pattern="" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style mode="Opaque" backcolor="#D9DADB"/>
		</conditionalStyle>
	</style>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="currentDate" class="java.util.Date">
		<fieldDescription><![CDATA[currentDate]]></fieldDescription>
	</field>
	<field name="endTime" class="java.lang.String">
		<fieldDescription><![CDATA[endTime]]></fieldDescription>
	</field>
	<field name="firstName" class="java.lang.String">
		<fieldDescription><![CDATA[firstName]]></fieldDescription>
	</field>
	<field name="fullName" class="java.lang.String">
		<fieldDescription><![CDATA[fullName]]></fieldDescription>
	</field>
	<field name="instructorId" class="java.lang.Long">
		<fieldDescription><![CDATA[instructorId]]></fieldDescription>
	</field>
	<field name="lastName" class="java.lang.String">
		<fieldDescription><![CDATA[lastName]]></fieldDescription>
	</field>
	<field name="maximumTime" class="java.util.Date">
		<fieldDescription><![CDATA[maximumTime]]></fieldDescription>
	</field>
	<field name="minimumTime" class="java.util.Date">
		<fieldDescription><![CDATA[minimumTime]]></fieldDescription>
	</field>
	<field name="scheduleTime" class="java.lang.String">
		<fieldDescription><![CDATA[scheduleTime]]></fieldDescription>
	</field>
	<field name="startDateStr" class="java.lang.String">
		<fieldDescription><![CDATA[startDateStr]]></fieldDescription>
	</field>
	<field name="startTime" class="java.lang.String">
		<fieldDescription><![CDATA[startTime]]></fieldDescription>
	</field>
	<columnHeader>
		<band height="61">
			<staticText>
				<reportElement mode="Opaque" x="0" y="2" width="550" height="34" forecolor="#FFFFFF" backcolor="#666666" uuid="2f7de105-2b25-410a-84fa-73c1976e3eb2">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle" markup="none">
					<font size="22" isBold="true"/>
				</textElement>
				<text><![CDATA[Employee Schedule]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="178" y="36" width="110" height="25" backcolor="#D9DADB" uuid="0dd2bd1e-a124-45d7-b0b5-a8f3c69f2669"/>
				<box leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Schedule]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="288" y="36" width="261" height="25" backcolor="#D9DADB" uuid="ecbc8958-cf62-4250-975f-792878b9e159">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="36" width="178" height="25" backcolor="#D9DADB" uuid="b1107b31-4526-4970-8d0c-4d6c8ec87427"/>
				<box leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Employee Name]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="35" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" x="0" y="0" width="178" height="35" uuid="f3bc7546-dd65-4ade-ba21-e1e2ab665682"/>
				<box topPadding="5" leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{fullName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" x="178" y="0" width="110" height="35" uuid="cfeffe7c-a285-4d08-a91f-dc72894e79ba"/>
				<box topPadding="5" leftPadding="3">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{scheduleTime}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement style="rowBgStyle" x="288" y="0" width="261" height="35" uuid="1fb0811b-6db2-4664-9e7f-ae8c8e1b8783">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</detail>
</jasperReport>
