<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.2.0.final using JasperReports Library version 6.2.0  -->
<!-- 2016-08-18T21:57:25 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="active_students_report" pageWidth="710" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="670" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4d722c75-d383-4a07-9f75-************">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D:\\reportfile\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="FROM_DATE" class="java.lang.String"/>
	<parameter name="TO_DATE" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="currentDate" class="java.lang.String">
		<fieldDescription><![CDATA[currentDate]]></fieldDescription>
	</field>
	<field name="activeStudentsDTO" class="com.guitarcenter.scheduler.dao.criterion.dto.ActiveStudentsDTO">
		<fieldDescription><![CDATA[activeStudentsDTO]]></fieldDescription>
	</field>
	<field name="instructorInfoDTO" class="com.guitarcenter.scheduler.dto.InstructorInfoDTO">
		<fieldDescription><![CDATA[instructorInfoDTO]]></fieldDescription>
	</field>
	<field name="activeStudentsReports" class="java.util.List">
		<fieldDescription><![CDATA[activeStudentsReports]]></fieldDescription>
	</field>
	<field name="instructorReports" class="java.util.List">
		<fieldDescription><![CDATA[instructorReports]]></fieldDescription>
	</field>
	<group name="activeStudentsDTO" isStartNewPage="true">
		<groupExpression><![CDATA[$F{activeStudentsDTO}]]></groupExpression>
		<groupHeader>
			<band splitType="Stretch">
				<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="50"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="85" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<staticText>
				<reportElement x="0" y="17" width="555" height="40" uuid="65701c59-957d-46dd-b0e0-de25ba653fe6">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" rotation="None">
					<font size="28" isBold="true"/>
				</textElement>
				<text><![CDATA[Active Students Report]]></text>
			</staticText>
			<staticText>
				<reportElement key="" x="184" y="45" width="31" height="22" uuid="ebe35cc5-0776-4b0c-97e0-8a6175f099e3">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[From:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement x="215" y="45" width="87" height="22" uuid="324b2ddc-8703-4182-acb1-b3e970bba1e1">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{FROM_DATE}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="287" y="45" width="20" height="22" uuid="f70db44b-3491-4bba-8f30-faa5a8227046">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[To:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement x="307" y="45" width="100" height="22" uuid="2c8d3684-f58b-46b4-bc11-c2359e99d3e8">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{TO_DATE}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="49" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport>
				<reportElement x="0" y="1" width="675" height="30" uuid="a9cdff25-5f3e-4559-b2ee-2ce618d3b764">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource((Collection)$F{instructorReports})]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "active_students_report_subreport1.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
