<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.1.0.final using JasperReports Library version 6.1.0  -->
<!-- 2015-06-04T15:23:01 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="instructro_report" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="bed04340-e8e7-40a5-8087-a71ee8b57593">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D:\\reportfile\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="TO_DATE" class="java.lang.String"/>
	<parameter name="FROM_DATE" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="currentDate" class="java.lang.String">
		<fieldDescription><![CDATA[currentDate]]></fieldDescription>
	</field>
	<field name="instructorInfo" class="java.util.List">
		<fieldDescription><![CDATA[instructorInfo]]></fieldDescription>
	</field>
	<field name="instructorInfoDTO" class="com.guitarcenter.scheduler.dto.InstructorInfoDTO">
		<fieldDescription><![CDATA[instructorInfoDTO]]></fieldDescription>
	</field>
	<field name="instructorReports" class="java.util.List">
		<fieldDescription><![CDATA[instructorReports]]></fieldDescription>
	</field>
	<group name="currentDateGroup">
		<groupExpression><![CDATA[$F{currentDate}]]></groupExpression>
	</group>
	<group name="instructorInfoGroup" isStartNewPage="true">
		<groupExpression><![CDATA[$F{instructorInfoDTO}]]></groupExpression>
		<groupHeader>
			<band splitType="Stretch"/>
		</groupHeader>
		<groupFooter>
			<band height="33"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="75" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="17" width="555" height="40" uuid="f14efd4c-49be-468b-8cdd-9c26d3f3235c"/>
				<textElement textAlignment="Center">
					<font size="28" isBold="true"/>
				</textElement>
				<text><![CDATA[Instructor Open Appointments Report]]></text>
			</staticText>
			<staticText>
				<reportElement x="184" y="45" width="31" height="22" backcolor="#D9DADB" uuid="29b30fdb-5b0f-4224-af3d-f46062c11d43"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" isStrikeThrough="false"/>
				</textElement>
				<text><![CDATA[From:]]></text>
			</staticText>
			<staticText>
				<reportElement x="287" y="45" width="20" height="22" backcolor="#D9DADB" uuid="da3816fd-67ac-4b22-8218-45a96d1b3bcf"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[To:]]></text>
			</staticText>
			<textField>
				<reportElement x="215" y="45" width="87" height="22" uuid="cfee31fd-7f8b-4ad6-b1bd-aaaa4b7891f0"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{FROM_DATE}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="307" y="45" width="100" height="22" uuid="73eb62d4-3845-449e-b624-c7e864f1deeb"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{TO_DATE}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="33" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport>
				<reportElement x="0" y="20" width="555" height="13" uuid="6dc360bc-35be-4895-8e9c-1d6561e488c7">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource((Collection)$F{instructorReports})]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "instructor_open_appointments_report_subreport1.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="12" splitType="Stretch"/>
	</detail>
</jasperReport>
