<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rehearsalSchedule_report_subreport1" pageWidth="572" pageHeight="752" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="934d0690-a08a-465f-a4a6-17d34523d5e1">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="style1" mode="Opaque" fill="Solid" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style mode="Opaque" backcolor="#D9DADB" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false"/>
		</conditionalStyle>
	</style>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="activityType" class="java.lang.String">
		<fieldDescription><![CDATA[activityType]]></fieldDescription>
	</field>
	<field name="bandName" class="java.lang.String">
		<fieldDescription><![CDATA[bandName]]></fieldDescription>
	</field>
	<field name="class" class="java.lang.Class">
		<fieldDescription><![CDATA[class]]></fieldDescription>
	</field>
	<field name="customerName" class="java.lang.String">
		<fieldDescription><![CDATA[customerName]]></fieldDescription>
	</field>
	<field name="duration" class="java.lang.String">
		<fieldDescription><![CDATA[duration]]></fieldDescription>
	</field>
	<field name="endDateStr" class="java.lang.String">
		<fieldDescription><![CDATA[endDateStr]]></fieldDescription>
	</field>
	<field name="queryEndDate" class="java.lang.String">
		<fieldDescription><![CDATA[queryEndDate]]></fieldDescription>
	</field>
	<field name="queryStartDate" class="java.lang.String">
		<fieldDescription><![CDATA[queryStartDate]]></fieldDescription>
	</field>
	<field name="roomName" class="java.lang.String">
		<fieldDescription><![CDATA[roomName]]></fieldDescription>
	</field>
	<field name="startDateStr" class="java.lang.String">
		<fieldDescription><![CDATA[startDateStr]]></fieldDescription>
	</field>
	<field name="startTime" class="java.lang.String">
		<fieldDescription><![CDATA[startTime]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="79" splitType="Stretch">
			<staticText>
				<reportElement uuid="355f6f7f-ea99-4966-a99f-6f0dc040a148" style="style1" x="438" y="47" width="117" height="31" backcolor="#D9DADB"/>
				<box>
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Band]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="9410f91f-7efc-4f90-848f-62c44abfe2be" style="style1" x="0" y="47" width="79" height="31" backcolor="#D9DADB"/>
				<box>
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Time]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="beef23b6-228f-43e5-a3c9-77cd67d1edec" style="style1" x="144" y="47" width="118" height="31" backcolor="#D9DADB"/>
				<box>
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Activity Type]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="cccc8094-02b9-4e9d-8a5b-d06d4a1a19f8" style="style1" x="338" y="47" width="100" height="31" backcolor="#D9DADB"/>
				<box>
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Customer]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="f20228b4-cf0a-4e0c-a963-c2486ac33083" style="style1" x="79" y="47" width="65" height="31" backcolor="#D9DADB"/>
				<box>
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Duration]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="e8ba1a8a-091f-4c9c-9461-98945bbc418f" style="style1" x="262" y="47" width="76" height="31" backcolor="#D9DADB"/>
				<box>
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Room]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="5f9d7fbc-255a-4136-a968-34b9e1d65560" mode="Opaque" x="0" y="0" width="555" height="47" forecolor="#FFFFFF" backcolor="#666666"/>
				<box>
					<pen lineColor="#FFFFFF"/>
					<topPen lineColor="#FFFFFF"/>
					<leftPen lineColor="#FFFFFF"/>
					<bottomPen lineColor="#FFFFFF"/>
					<rightPen lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font size="20"/>
				</textElement>
				<text><![CDATA[Daily Rehearsal Schedule ]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="32" splitType="Stretch">
			<textField>
				<reportElement uuid="fd113e01-4764-4ff7-a08f-466936ebfc3b" style="style1" mode="Opaque" x="0" y="0" width="79" height="31"/>
				<box>
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{startTime}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="718d024e-3e74-4f28-8493-70690b2348bb" style="style1" x="144" y="0" width="118" height="31"/>
				<box leftPadding="2">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{activityType}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="90e0b6bb-81f5-46b5-8db1-172ad554d3ac" style="style1" x="438" y="0" width="117" height="31"/>
				<box leftPadding="2">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{bandName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="7381f657-4f21-4114-9714-707ed34a9770" style="style1" x="262" y="0" width="76" height="31"/>
				<box leftPadding="2">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{roomName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2cf42115-0420-420e-9d22-48b9ed8e5a30" style="style1" x="79" y="0" width="65" height="31"/>
				<box leftPadding="2">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{duration}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="59459b89-4461-457a-bd0d-b4acb149cbb8" style="style1" x="338" y="0" width="100" height="31"/>
				<box leftPadding="2">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{customerName}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
