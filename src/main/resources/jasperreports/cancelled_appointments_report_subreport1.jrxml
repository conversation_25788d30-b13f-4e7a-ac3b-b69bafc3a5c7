<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.6.0.final using JasperReports Library version 6.6.0  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="cancelled_appointments_report_subreport1" pageWidth="826" pageHeight="752" whenNoDataType="AllSectionsNoDetail" columnWidth="826" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="f4407222-3d15-4bc9-8617-ee2ce90076ff">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="rowBgStyle" mode="Opaque" fill="Solid" pattern="" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style mode="Opaque" backcolor="#D9DADB"/>
		</conditionalStyle>
	</style>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="time" class="java.lang.String">
		<fieldDescription><![CDATA[time]]></fieldDescription>
	</field>
	<field name="endTime" class="java.lang.String">
		<fieldDescription><![CDATA[endTime]]></fieldDescription>
	</field>
	<field name="notes" class="java.lang.String">
		<fieldDescription><![CDATA[notes]]></fieldDescription>
	</field>
	<field name="instructorName" class="java.lang.String">
		<fieldDescription><![CDATA[instructorName]]></fieldDescription>
	</field>
	<field name="activityType" class="java.lang.String">
		<fieldDescription><![CDATA[activityType]]></fieldDescription>
	</field>
	<field name="queryStartDate" class="java.lang.String">
		<fieldDescription><![CDATA[queryStartDate]]></fieldDescription>
	</field>
	<field name="customerName" class="java.lang.String">
		<fieldDescription><![CDATA[customerName]]></fieldDescription>
	</field>
	<field name="startDateStr" class="java.lang.String">
		<fieldDescription><![CDATA[startDateStr]]></fieldDescription>
	</field>
	<field name="endDateStr" class="java.lang.String">
		<fieldDescription><![CDATA[endDateStr]]></fieldDescription>
	</field>
	<field name="startTime" class="java.lang.String">
		<fieldDescription><![CDATA[startTime]]></fieldDescription>
	</field>
	<field name="queryEndDate" class="java.lang.String">
		<fieldDescription><![CDATA[queryEndDate]]></fieldDescription>
	</field>
	<field name="cancelledTimestamp" class="java.lang.String">
		<fieldDescription><![CDATA[cancelledTimestamp]]></fieldDescription>
	</field>
	<field name="cancelledTime" class="java.lang.String">
		<fieldDescription><![CDATA[cancelledTime]]></fieldDescription>
	</field>
	<field name="cancelledUser" class="java.lang.String">
		<fieldDescription><![CDATA[cancelledUser]]></fieldDescription>
	</field>
	<field name="cancelledReason" class="java.lang.String">
		<fieldDescription><![CDATA[cancelledReason]]></fieldDescription>
	</field>
	<columnHeader>
		<band height="21" splitType="Stretch">
			<staticText>
				<reportElement x="188" y="1" width="80" height="20" backcolor="#D9DADB" uuid="517f178d-7b14-46c6-8cc9-522f6d4e0b2b">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[ Activity Type]]></text>
			</staticText>
			<staticText>
				<reportElement x="74" y="0" width="114" height="21" backcolor="#D9DADB" uuid="449596b4-820a-48b3-8a8e-af573a884bf8">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[ Time]]></text>
			</staticText>
			<staticText>
				<reportElement x="715" y="0" width="111" height="21" backcolor="#D9DADB" uuid="a304e3a2-ad35-444e-a997-7a17bb73fe15">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Cancelled Reason]]></text>
			</staticText>
			<staticText>
				<reportElement x="268" y="1" width="90" height="20" backcolor="#D9DADB" uuid="f7a4ea16-fd68-4e72-8817-de0491a17b4c">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[ Instructor Name]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="74" height="21" backcolor="#D9DADB" uuid="62c333a1-6b03-40dd-87a1-159814d1c19e">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="534" y="0" width="106" height="21" backcolor="#D9DADB" uuid="0655ff61-5a94-4897-a4b8-5daabd938dfa">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Cancelled Timestamp]]></text>
			</staticText>
			<staticText>
				<reportElement x="640" y="0" width="75" height="21" backcolor="#D9DADB" uuid="2fe516fe-a593-4ac3-bee0-8295dcbb33f3">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Notes]]></text>
			</staticText>
			<staticText>
				<reportElement x="449" y="0" width="85" height="21" backcolor="#D9DADB" uuid="6b154564-ebfb-40ba-abdb-a4b06021dac2">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[  Cancelled User]]></text>
			</staticText>
			<staticText>
				<reportElement x="358" y="0" width="91" height="21" backcolor="#D9DADB" uuid="b93f9b1e-90cd-4a59-a3fe-df1173bd9fb7">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[ Customer Name]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="32" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" x="74" y="0" width="116" height="29" uuid="101e59f4-6664-4716-a75f-ec6ef4092fc9">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box topPadding="5" leftPadding="2">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="false"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{time}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="268" y="0" width="90" height="29" uuid="8cb389f5-1811-416f-92f6-2aa8791637f1">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box leftPadding="2">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{instructorName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="188" y="0" width="80" height="29" uuid="25c3cf71-cf7a-4979-b9de-43964d63f453">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box leftPadding="2">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{activityType}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="715" y="0" width="111" height="29" uuid="939b2a19-047e-46cd-bca3-5b1da655ea2e">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box leftPadding="2">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cancelledReason}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="0" y="0" width="74" height="29" uuid="c872eeea-d23e-4601-9636-b81f6dfdffdb">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{startDateStr}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement style="rowBgStyle" x="449" y="0" width="85" height="29" uuid="6fc32813-a864-4003-aaee-356bfaf2d5df">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<paragraph leftIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cancelledUser}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement style="rowBgStyle" x="534" y="0" width="107" height="29" uuid="f8308750-4e5e-4d8e-99b9-b7c2d38fd95d">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<paragraph leftIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cancelledTime}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement style="rowBgStyle" x="640" y="0" width="75" height="29" uuid="37ec96a8-d80b-4e4f-90a3-172613b962cd">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<paragraph leftIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{notes}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="358" y="0" width="91" height="29" uuid="3b2e242a-4b8b-425a-b36e-cb5c8c5e1a36">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box leftPadding="2">
					<pen lineWidth="1.0" lineColor="#D0D0D0"/>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{customerName}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
