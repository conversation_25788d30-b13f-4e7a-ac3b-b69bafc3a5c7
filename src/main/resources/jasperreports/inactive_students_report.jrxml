<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.2.0.final using JasperReports Library version 6.2.0  -->
<!-- 2016-10-07T03:53:30 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="inactive_students_report" pageWidth="625" pageHeight="842" columnWidth="585" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="8107d31c-e024-46b1-be31-8ff9bc1740e9">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D:\\reportfile\\"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="inActiveStudentsDTO" class="com.guitarcenter.scheduler.dao.criterion.dto.InActiveStudentsDTO">
		<fieldDescription><![CDATA[inActiveStudentsDTO]]></fieldDescription>
	</field>
	<field name="instructorInfoDTO" class="com.guitarcenter.scheduler.dto.InstructorInfoDTO">
		<fieldDescription><![CDATA[instructorInfoDTO]]></fieldDescription>
	</field>
	<field name="inActiveStudentsReports" class="java.util.List">
		<fieldDescription><![CDATA[inActiveStudentsReports]]></fieldDescription>
	</field>
	<field name="instructorReports" class="java.util.List">
		<fieldDescription><![CDATA[instructorReports]]></fieldDescription>
	</field>
	<group name="inActiveStudentsDTO">
		<groupFooter>
			<band height="50"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="79" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="17" width="555" height="40" uuid="b7b8f603-6f05-48f9-8bed-28fd6458725d">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="28" isBold="true"/>
				</textElement>
				<text><![CDATA[InActive Students Report]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="49" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport>
				<reportElement x="0" y="1" width="580" height="30" uuid="aff8f732-bffb-4b51-8f86-d218dbf39311">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource((Collection)$F{instructorReports})]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "inactive_students_report_subreport1.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
