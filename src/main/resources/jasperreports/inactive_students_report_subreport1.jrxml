<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.2.0.final using JasperReports Library version 6.2.0  -->
<!-- 2016-10-14T01:43:48 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="inactive_students_report_subreport1" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="580cc7bd-a10a-4815-aaf5-c841a8a6c5e6">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<style name="rowBgStyle" fill="Solid">
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style mode="Opaque" backcolor="#D9DADB" fill="Solid"/>
		</conditionalStyle>
	</style>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="customerName" class="java.lang.String">
		<fieldDescription><![CDATA[customerName]]></fieldDescription>
	</field>
	<field name="customerEmail" class="java.lang.String">
		<fieldDescription><![CDATA[customerEmail]]></fieldDescription>
	</field>
	<field name="customerPhone" class="java.lang.String">
		<fieldDescription><![CDATA[customerPhone]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="20" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<staticText>
				<reportElement x="0" y="0" width="120" height="20" uuid="b015ad1b-b7fb-453a-80a6-e326aeca0e11">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="0"/>
				</textElement>
				<text><![CDATA[Student Name]]></text>
			</staticText>
			<staticText>
				<reportElement x="120" y="0" width="200" height="20" uuid="8b8c6990-7ddf-4cb2-85cd-d5798222e498">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Student Email]]></text>
			</staticText>
			<staticText>
				<reportElement x="320" y="0" width="200" height="20" uuid="c4be016c-4012-4e9d-87cf-6fc532d52c7d">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Student Phone]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="28" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" mode="Opaque" x="0" y="0" width="120" height="28" uuid="33485db6-e841-4a5c-a099-93a35c3c503e">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{customerName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement style="rowBgStyle" mode="Opaque" x="120" y="0" width="200" height="28" uuid="4f26dcf1-d813-43ef-9512-c575c9abebe6">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{customerEmail}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement style="rowBgStyle" x="320" y="0" width="200" height="28" uuid="d4410cf3-e8ae-40cd-8826-f10ff34b7dca">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{customerPhone}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
