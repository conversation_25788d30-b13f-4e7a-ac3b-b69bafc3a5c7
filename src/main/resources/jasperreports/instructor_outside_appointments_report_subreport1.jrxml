<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.0.3.final using JasperReports Library version 6.0.3  -->
<!-- 2015-10-08T16:21:28 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="instructor_outside_appointments_report_subreport1" pageWidth="555" pageHeight="802" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="fc5c5b93-0736-467a-9cf3-f9370720917e">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="rowBgStyle" mode="Opaque" fill="Solid" pattern="" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style mode="Opaque" backcolor="#D9DADB"/>
		</conditionalStyle>
	</style>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="activityType" class="java.lang.String">
		<fieldDescription><![CDATA[activityType]]></fieldDescription>
	</field>
	<field name="class" class="java.lang.Class">
		<fieldDescription><![CDATA[class]]></fieldDescription>
	</field>
	<field name="customerName" class="java.lang.String">
		<fieldDescription><![CDATA[customerName]]></fieldDescription>
	</field>
	<field name="duration" class="java.lang.String">
		<fieldDescription><![CDATA[duration]]></fieldDescription>
	</field>
	<field name="endDate" class="java.lang.String">
		<fieldDescription><![CDATA[endDate]]></fieldDescription>
	</field>
	<field name="endTime" class="java.lang.String">
		<fieldDescription><![CDATA[endTime]]></fieldDescription>
	</field>
	<field name="instructorName" class="java.lang.String">
		<fieldDescription><![CDATA[instructorName]]></fieldDescription>
	</field>
	<field name="startDate" class="java.lang.String">
		<fieldDescription><![CDATA[startDate]]></fieldDescription>
	</field>
	<field name="startTime" class="java.lang.String">
		<fieldDescription><![CDATA[startTime]]></fieldDescription>
	</field>
	<field name="timeFrame" class="java.lang.String">
		<fieldDescription><![CDATA[timeFrame]]></fieldDescription>
	</field>
	<field name="roomName" class="java.lang.String">
		<fieldDescription><![CDATA[roomName]]></fieldDescription>
	</field>
	<columnHeader>
		<band height="21" splitType="Stretch">
			<property name="local_mesure_unitheight" value="pixel"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<staticText>
				<reportElement x="0" y="1" width="100" height="20" backcolor="#D9DADB" uuid="26296cc8-2eb9-4afd-ad74-59e0cca7840d">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" isPdfEmbedded="false"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Instructor Name]]></text>
			</staticText>
			<staticText>
				<reportElement x="100" y="1" width="60" height="20" backcolor="#D9DADB" uuid="8084deac-96dd-4ee6-be3b-5dd4be695678">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="160" y="1" width="65" height="20" backcolor="#D9DADB" uuid="a9ea8a97-4684-40ac-afa5-df7dedb003da">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Time Frame]]></text>
			</staticText>
			<staticText>
				<reportElement x="225" y="1" width="60" height="20" backcolor="#D9DADB" uuid="123dfd2b-8802-409f-a439-26adf5a09dcb"/>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Duration]]></text>
			</staticText>
			<staticText>
				<reportElement x="285" y="1" width="75" height="20" backcolor="#D9DADB" uuid="ab0195c3-a2b4-42b4-8f09-522c27b2fe8a">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Activity]]></text>
			</staticText>
			<staticText>
				<reportElement x="360" y="1" width="100" height="20" backcolor="#D9DADB" uuid="937bb198-face-4d48-8d74-80c6639373d4">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Customer Name]]></text>
			</staticText>
			<staticText>
				<reportElement x="460" y="1" width="95" height="20" backcolor="#D9DADB" uuid="ae11e095-45ae-41b3-a0a4-4ad95b184c5a">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Room Name]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="28" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" x="0" y="0" width="100" height="28" uuid="d620803e-bfd7-4d39-bab6-31c3cca554d7">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="false"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{instructorName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="100" y="0" width="60" height="28" uuid="a7d8d2ba-49b5-438f-be3f-b1004259e926">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{startDate}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="160" y="0" width="65" height="28" uuid="802efbe1-67c7-4e70-a301-1e179ca280f7">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{timeFrame}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="225" y="0" width="60" height="28" uuid="c4f00b93-87e4-4611-9453-6fa7ace3ef68"/>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{duration}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="285" y="0" width="75" height="28" uuid="21be3f76-e715-4b38-a5d6-b080e62aae55">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{activityType}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="360" y="0" width="100" height="28" uuid="c98b3729-0183-4f5a-9107-7facba2c2643">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{customerName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="460" y="0" width="95" height="28" uuid="718d4626-3692-40eb-b842-3e1c95573c79">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{roomName}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
