<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.8.0.final using JasperReports Library version 6.8.0-2ed8dfabb690ff337a5797129f2cd92902b0c87b  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="master_report" pageWidth="595" pageHeight="825" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="ecfac94a-051c-47c7-927e-b774ffb104b1">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="EMPLOYEE_LIST" class="java.util.List">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D:\\reportfile\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="FROM_DATE" class="java.lang.String"/>
	<parameter name="TO_DATE" class="java.lang.String"/>
	<parameter name="MASTER_LIST" class="java.util.List">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="currentDate" class="java.lang.String">
		<fieldDescription><![CDATA[currentDate]]></fieldDescription>
	</field>
	<field name="employees" class="java.util.List">
		<fieldDescription><![CDATA[employees]]></fieldDescription>
	</field>
	<field name="masters" class="java.util.List">
		<fieldDescription><![CDATA[masters]]></fieldDescription>
	</field>
	<group name="masterReportGroup" isStartNewPage="true">
		<groupExpression><![CDATA[$F{currentDate}]]></groupExpression>
		<groupHeader>
			<band height="30">
				<textField>
					<reportElement x="3" y="10" width="116" height="20" uuid="08f1a866-de57-4404-bf71-d6c958ef9e94"/>
					<textElement textAlignment="Left" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$F{currentDate}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="28"/>
		</groupFooter>
	</group>
	<title>
		<band height="68" splitType="Stretch">
			<staticText>
				<reportElement x="103" y="3" width="331" height="40" uuid="af06cdb1-86ff-4a06-88b0-5986bc34e7da"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="28" isBold="true"/>
				</textElement>
				<text><![CDATA[Master Schedule Report]]></text>
			</staticText>
			<staticText>
				<reportElement x="272" y="43" width="18" height="22" backcolor="#D9DADB" uuid="db3b1c55-ad95-4567-8e53-68df6862d8ca"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[To:]]></text>
			</staticText>
			<staticText>
				<reportElement x="151" y="43" width="31" height="22" backcolor="#D9DADB" uuid="dd841abc-959e-4994-8dbe-d1c78b93009f"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[From:]]></text>
			</staticText>
			<textField>
				<reportElement x="182" y="43" width="90" height="22" uuid="98f22e54-bb79-488a-a72b-54b5c2db4a9c"/>
				<box leftPadding="5">
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{FROM_DATE}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="290" y="43" width="96" height="22" uuid="a9880f83-fdfa-49f1-8e68-7f0fc8b11e55"/>
				<box leftPadding="5">
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{TO_DATE}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="20">
			<subreport isUsingCache="true">
				<reportElement x="0" y="0" width="555" height="11" uuid="89a52258-002d-4a5a-83eb-453052b724c4"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource((Collection)$F{employees})]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "master_report_employee_subreport.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="13">
			<subreport>
				<reportElement x="0" y="0" width="555" height="13" uuid="33e61527-3564-470f-80c6-4e983535df38"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource((Collection)$F{masters})]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "master_report_masters_subreport.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
