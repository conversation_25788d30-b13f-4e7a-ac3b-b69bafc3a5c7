<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.6.0.final using JasperReports Library version 6.6.0  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="cancelled_appointments_report" pageWidth="886" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="846" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="bed04340-e8e7-40a5-8087-a71ee8b57593">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D:\\reportfile\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="TO_DATE" class="java.lang.String"/>
	<parameter name="FROM_DATE" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="date1" class="java.lang.String">
		<fieldDescription><![CDATA[date1]]></fieldDescription>
	</field>
	<field name="list" class="java.util.List">
		<fieldDescription><![CDATA[list]]></fieldDescription>
	</field>
	<group name="cancelledAppointmentsInfoGroup" isStartNewPage="true">
		<groupExpression><![CDATA[$F{date1}]]></groupExpression>
		<groupHeader>
			<band height="19" splitType="Stretch">
				<textField>
					<reportElement x="0" y="2" width="92" height="17" uuid="5fec99b6-fc1e-4db6-b9e2-4d38049ab49f"/>
					<textFieldExpression><![CDATA[$F{date1}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="33"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="75" splitType="Stretch">
			<staticText>
				<reportElement x="51" y="23" width="452" height="40" uuid="82b0fdb6-195d-4a00-beee-25e325bd74ae"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="28" isBold="true"/>
				</textElement>
				<text><![CDATA[Cancelled Appointments]]></text>
			</staticText>
			<elementGroup>
				<staticText>
					<reportElement x="156" y="53" width="31" height="22" backcolor="#D9DADB" uuid="56271236-2e3a-4b02-ae40-16a4bcbbcd82"/>
					<box>
						<pen lineColor="#D0D0D0"/>
						<topPen lineColor="#D0D0D0"/>
						<leftPen lineColor="#D0D0D0"/>
						<bottomPen lineColor="#D0D0D0"/>
						<rightPen lineColor="#D0D0D0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true" isStrikeThrough="false"/>
					</textElement>
					<text><![CDATA[From:]]></text>
				</staticText>
				<staticText>
					<reportElement x="274" y="53" width="28" height="22" backcolor="#D9DADB" uuid="de81c6a8-3302-465f-a180-0f245591fb8f"/>
					<box>
						<pen lineColor="#D0D0D0"/>
						<topPen lineColor="#D0D0D0"/>
						<leftPen lineColor="#D0D0D0"/>
						<bottomPen lineColor="#D0D0D0"/>
						<rightPen lineColor="#D0D0D0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[To:]]></text>
				</staticText>
			</elementGroup>
			<textField>
				<reportElement x="187" y="53" width="87" height="22" uuid="dd16799e-a882-4180-9a81-2ccd6f655406"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{FROM_DATE}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="302" y="53" width="100" height="22" uuid="3a02ac2d-7ec3-46af-9a62-47af8a82b963"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{TO_DATE}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="13">
			<subreport>
				<reportElement x="0" y="0" width="846" height="13" uuid="dd8efc95-a9d3-4999-b226-c4df7ef055fa">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "cancelled_appointments_report_title_subreport.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="13">
			<subreport>
				<reportElement x="0" y="0" width="846" height="13" uuid="6dc360bc-35be-4895-8e9c-1d6561e488c7">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource((Collection)$F{list})]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "cancelled_appointments_report_subreport1.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
