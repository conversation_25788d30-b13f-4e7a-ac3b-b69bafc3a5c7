<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.1.0.final using JasperReports Library version 6.1.0  -->
<!-- 2015-06-03T12:15:40 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Blank_A4" pageWidth="555" pageHeight="802" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="bdc23243-b86c-42b3-9241-07ffcb559103">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="rowBgStyle" mode="Opaque" fill="Solid" pattern="" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style mode="Opaque" backcolor="#D9DADB"/>
		</conditionalStyle>
	</style>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="activityType" class="java.lang.String">
		<fieldDescription><![CDATA[activityType]]></fieldDescription>
	</field>
	<field name="cancelled" class="java.lang.String">
		<fieldDescription><![CDATA[cancelled]]></fieldDescription>
	</field>
<!--	<field name="class" class="java.lang.Class">
		<fieldDescription><![CDATA[class]]></fieldDescription>
	</field>-->
	<field name="customerName" class="java.lang.String">
		<fieldDescription><![CDATA[customerName]]></fieldDescription>
	</field>
	<field name="duration" class="java.lang.String">
		<fieldDescription><![CDATA[duration]]></fieldDescription>
	</field>
	<field name="endDate" class="java.lang.String">
		<fieldDescription><![CDATA[endDate]]></fieldDescription>
	</field>
	<field name="endTime" class="java.lang.String">
		<fieldDescription><![CDATA[endTime]]></fieldDescription>
	</field>
	<field name="instructorId" class="java.lang.Long">
		<fieldDescription><![CDATA[instructorId]]></fieldDescription>
	</field>
	<field name="instructorName" class="java.lang.String">
		<fieldDescription><![CDATA[instructorName]]></fieldDescription>
	</field>
	<field name="queryEndDate" class="java.lang.String">
		<fieldDescription><![CDATA[queryEndDate]]></fieldDescription>
	</field>
	<field name="queryStartDate" class="java.lang.String">
		<fieldDescription><![CDATA[queryStartDate]]></fieldDescription>
	</field>
	<field name="startDate" class="java.lang.String">
		<fieldDescription><![CDATA[startDate]]></fieldDescription>
	</field>
	<field name="startTime" class="java.lang.String">
		<fieldDescription><![CDATA[startTime]]></fieldDescription>
	</field>
	<field name="timeFrame" class="java.lang.String">
		<fieldDescription><![CDATA[timeFrame]]></fieldDescription>
	</field>
	<field name="roomName" class="java.lang.String">
		<fieldDescription><![CDATA[roomName]]></fieldDescription>
	</field>
	<columnHeader>
		<band height="21" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="1" width="100" height="20" backcolor="#D9DADB" uuid="ca88dbd9-f3b7-4667-9ff8-3ca2f9c19c70">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" isPdfEmbedded="false"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Instructor Name]]></text>
			</staticText>
			<staticText>
				<reportElement x="100" y="1" width="60" height="20" backcolor="#D9DADB" uuid="0a2673a5-6eab-40f3-8c89-de6903f349f4">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="160" y="1" width="65" height="20" backcolor="#D9DADB" uuid="f32cecbf-dde3-448d-afac-8588b77970ad">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Time Frame]]></text>
			</staticText>
			<staticText>
				<reportElement x="225" y="1" width="60" height="20" backcolor="#D9DADB" uuid="f3e9f9a3-2c38-4e5c-8558-f9541f90b1da"/>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Duration]]></text>
			</staticText>
			<staticText>
				<reportElement x="285" y="1" width="75" height="20" backcolor="#D9DADB" uuid="6113549e-faee-40c7-a978-cde4c6b5645f">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Activity]]></text>
			</staticText>
			<staticText>
				<reportElement x="360" y="1" width="100" height="20" backcolor="#D9DADB" uuid="61a4705b-e695-4a2b-97fb-2f69eb9cd441">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Customer Name]]></text>
			</staticText>
			<staticText>
				<reportElement x="460" y="1" width="95" height="20" backcolor="#D9DADB" uuid="cd4f5f8c-b819-48e3-b262-60a95f80a198">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Room Name]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="28" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" x="0" y="0" width="100" height="28" uuid="303dba6c-3ad8-42ab-87e6-c96a4b59c3cf">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="false"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{instructorName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="100" y="0" width="60" height="28" uuid="da92076a-74f2-44b6-bc5e-36d7a2c080a3">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{startDate}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="160" y="0" width="65" height="28" uuid="bbc8f54a-c479-4803-aee1-59c613aad718">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{timeFrame}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="225" y="0" width="60" height="28" uuid="16b2a65c-872b-4c85-b1e1-ac881d2230e1"/>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{duration}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="285" y="0" width="75" height="28" uuid="46db1f91-f99f-4479-b52f-443fc085b476">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{activityType}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="360" y="0" width="100" height="28" uuid="b62c9b8a-20c6-4674-939c-7ccab1bfbaa2">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{customerName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="rowBgStyle" x="460" y="0" width="95" height="28" uuid="5dbbea1b-baea-4af0-9e94-ce3ccf5b1340">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#BFBFBF"/>
					<topPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<leftPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<bottomPen lineWidth="1.0" lineColor="#BFBFBF"/>
					<rightPen lineWidth="1.0" lineColor="#BFBFBF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{roomName}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
