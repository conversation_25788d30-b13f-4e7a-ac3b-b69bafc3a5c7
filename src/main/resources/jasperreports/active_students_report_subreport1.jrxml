<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.2.0.final using JasperReports Library version 6.2.0  -->
<!-- 2016-08-18T21:49:56 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="active_students_report_subreport1" pageWidth="675" pageHeight="802" columnWidth="675" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="e3281a22-e29d-4b5e-978f-bce220fa794a">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<style name="rowBgStyle" fill="Solid">
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style mode="Opaque" backcolor="#D9DADB" fill="Solid"/>
		</conditionalStyle>
	</style>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="customerName" class="java.lang.String">
		<fieldDescription><![CDATA[customerName]]></fieldDescription>
	</field>
	<field name="activityType" class="java.lang.String">
		<fieldDescription><![CDATA[activityType]]></fieldDescription>
	</field>
	<field name="cancelled" class="java.lang.String">
		<fieldDescription><![CDATA[cancelled]]></fieldDescription>
	</field>
	<field name="duration" class="java.lang.String">
		<fieldDescription><![CDATA[duration]]></fieldDescription>
	</field>
	<field name="customerEmail" class="java.lang.String">
		<fieldDescription><![CDATA[customerEmail]]></fieldDescription>
	</field>
	<field name="startDate" class="java.lang.String">
		<fieldDescription><![CDATA[startDate]]></fieldDescription>
	</field>
	<field name="instructorName" class="java.lang.String">
		<fieldDescription><![CDATA[instructorName]]></fieldDescription>
	</field>
	<field name="timeFrame" class="java.lang.String">
		<fieldDescription><![CDATA[timeFrame]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="20" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<staticText>
				<reportElement x="0" y="0" width="90" height="20" uuid="d1c478cd-068d-4313-a865-13279f1f130f">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="0"/>
				</textElement>
				<text><![CDATA[Student Name]]></text>
			</staticText>
			<staticText>
				<reportElement x="90" y="0" width="90" height="20" uuid="35c091d3-c993-48dd-8e18-e039914dab93">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Instructor Name]]></text>
			</staticText>
			<staticText>
				<reportElement x="180" y="0" width="80" height="20" uuid="5ae6db4a-62cb-4acc-9b43-a10f45e2b47b">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="260" y="0" width="65" height="20" uuid="efd9b730-e762-45cb-9190-1e90a396cb1c">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Time Frame]]></text>
			</staticText>
			<staticText>
				<reportElement x="325" y="0" width="75" height="20" uuid="5980681e-a718-451f-952d-2af7ce897bc9">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<text><![CDATA[Duration]]></text>
			</staticText>
			<staticText>
				<reportElement x="400" y="0" width="75" height="20" uuid="d6f48cf4-121e-4f7f-8790-a913a9944660">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Activity Type]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="475" y="0" width="200" height="20" uuid="0512fcd4-8b62-4fb7-ae29-ecd1326f916b">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Student Email]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="28" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" mode="Opaque" x="0" y="0" width="90" height="28" uuid="33c26d9b-57b5-4e4f-b7e5-d883acca1c03">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{customerName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" mode="Opaque" x="90" y="0" width="90" height="28" uuid="07a4c9b5-f3d0-4c17-b3d2-bd8d4f87a6cf">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{instructorName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" mode="Opaque" x="180" y="0" width="80" height="28" uuid="126968a2-2a51-4bf3-b90b-e686a8f46789">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{startDate}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" mode="Opaque" x="260" y="0" width="65" height="28" uuid="83920630-44ec-4417-ac5c-e5d4040cce0a">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{timeFrame}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" mode="Opaque" x="325" y="0" width="75" height="28" uuid="12b37687-0d50-48bb-8e5f-4397b4c31cb8">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font isBold="false"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{duration}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement style="rowBgStyle" mode="Opaque" x="400" y="0" width="75" height="28" uuid="7fa6fdaa-5168-4ae3-84c3-8a81952264dd">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{activityType}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement style="rowBgStyle" mode="Opaque" x="475" y="0" width="200" height="28" uuid="a177a284-a62b-4508-81c5-8a009b1ffcf1">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<leftPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<bottomPen lineWidth="1.0" lineColor="#D0D0D0"/>
					<rightPen lineWidth="1.0" lineColor="#D0D0D0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{customerEmail}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
