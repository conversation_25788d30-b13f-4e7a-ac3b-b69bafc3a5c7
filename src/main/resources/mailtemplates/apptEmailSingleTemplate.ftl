<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<style type="text/css">
	body
	{
		border: thin solid gray;
		width: 750px;
	}
	p
	{
		font-family: "Arial";
	}
</style>
</head>
<body>
<img src ="${emailHeaderImgUrl?default('')}"/>
<br/>
<br/>
&nbsp;&nbsp;${customerName?default('')},
<br/>
<br/>
<#switch serviceType?default('')>

	<#case "modifyRehearsal">
	<#assign isRecurring=0>
           &nbsp;&nbsp;Your rehearsal has been modified. The details of your new appointment are shown below.<br/>
           <#break>
	<#case "modifyRehearsalNotAll">
	<#assign isRecurring=0>
           &nbsp;&nbsp;The following rehearsal has been modified. Any other scheduled rehearsals are not affected.<br/>
           &nbsp;&nbsp;The details of your new appointment are shown below.<br/>
           <#break>

	<#case "modifyLesson">
	<#assign isRecurring=0>
           &nbsp;&nbsp;Your lesson at Guitar Center has been modified. The details of your new appointment<br/>
           &nbsp;&nbsp;are shown below.<br/>
           <#break>
	<#case "modifyLessonNotAll">
	<#assign isRecurring=0>
           &nbsp;&nbsp;The following lesson at Guitar Center has been modified.Other scheduled<br/>
		   &nbsp;&nbsp;lessons are not affected. The details of your new appointment are listed below.<br/>
           <#break>

	<#case "cancelRehearsal">
	<#assign isRecurring=0>
           &nbsp;&nbsp;The following rehearsal has been cancelled. Please contact your local Guitar Center Studios<br/>
		   &nbsp;&nbsp;when you're ready to reschedule.<br/>
           <#break>
	<#case "cancelRehearsalNotAll">
	<#assign isRecurring=0>
           &nbsp;&nbsp;The following rehearsal has been cancelled. Any other scheduled rehearsals are not<br/>
		   &nbsp;&nbsp;affected.<br/>
           <#break>

	<#case "cancelLesson">


	<#assign isRecurring=0>
           &nbsp;&nbsp;  The following lesson has been <b>cancelled</b>. None of your other scheduled lessons are  affected.<br>
           &nbsp;&nbsp;  Please contact us to reschedule this lesson at your earliest convenience.<br/>
           <#break>
	<#case "cancelLessonNotAll">
		   &nbsp;&nbsp;  The following lesson has been <b>cancelled</b>. None of your other scheduled lessons are  affected.<br>
           &nbsp;&nbsp;  Please contact us to reschedule this lesson at your earliest convenience.<br/>
		  <#break>
	<#default>
	<#assign isRecurring=0>
			&nbsp;&nbsp;Your rehearsal is coming up at Guitar Center Studios. The details of your appointment are<br/>
			&nbsp;&nbsp;shown below.<br/>
</#switch>
<br/>
<br/>
&nbsp;&nbsp;Location: ${location?default('')}<br/>

&nbsp;&nbsp;Date: ${date?default('')}<br/>

&nbsp;&nbsp;Time: ${time?default('')}<br/>
&nbsp;&nbsp;Instructor: ${instructor?default('')}<br/>
&nbsp;&nbsp;Duration: ${duration?default('')}<br/>
<#if serviceType == "modifyLesson" ||serviceType == "modifyLessonNotAll"|| serviceType == "cancelLesson" ||serviceType == "cancelLessonNotAll" >
<#if serviceType == "modifyLesson" ||serviceType == "modifyLessonNotAll" >
&nbsp;&nbsp;Room Name: ${roomName?default('')}<br/>
</#if>
&nbsp;&nbsp;Lesson Type: ${activityName?default('')}<br/>
  </#if>
&nbsp;&nbsp;Updated by: ${updatedBy?default('')}<br/>
<br/>
<br/>
&nbsp;&nbsp;If you scheduled or updated online lesson(s), you will receive link soon.<br/>
&nbsp;&nbsp;See you soon!<br/>
&nbsp;&nbsp;-Guitar Center<br/>



</body>
</html>