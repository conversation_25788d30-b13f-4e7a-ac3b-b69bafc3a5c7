<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<style type="text/css">
	body
	{ 
		border: thin solid gray;
		width: 750px;
	}	
</style>
</head>
<body>
<img src ="${emailHeaderImgUrl}"/>

<#if successStatus>
<br/>
<br/>
<br/>
<br/>
     &nbsp;&nbsp;The ${jobName} has run successfully<br/>   
<br/>

<br/>
	<br/>	
		&nbsp;&nbsp; Mentioned below are the appointment series that could not be updated along with the reason:
	<br/>		
	<br/>

	<table style="border: 1px solid black;">
	<thead>
		<tr style="border: 1px solid black;">
			<th style="border: 1px solid black;">Issue</th>
			<th style="border: 1px solid black;">Appointment Series Id</th>
			
		</tr>
	</thead>
	
	<tbody>
		<#list appointmentsFailedToUpdate?keys as key>
		<tr style="border: 1px solid black;">
		  <td style="border: 1px solid black;">${key}</td>
		  <td style="border: 1px solid black;">${appointmentsFailedToUpdate[key]}</td>
		</tr>			
		</#list>		
		
		
	</tbody>	
	</table>

<#else>

<br/>
<br/>
<br/>
<br/>
     &nbsp;&nbsp;The ${jobName} has failed with the following exception<br/>   
<br/>
<br/>
<br/>
<br/>
<br/>
<br/>
${exception}
<br/>
<br/>
<br/>
<br/>
</#if>
<br/>
&nbsp;&nbsp;-Guitar Center<br/>
</body>
</html>