<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Guitar Center Lessons</title>
    <style>
        /* -------------------------------------
            GLOBAL RESETS
        ------------------------------------- */
        img {
            border: none;
            -ms-interpolation-mode: bicubic;
            max-width: 100%; }

        body {
            background-color: #fff;
            color: #000;
            font-family: 'Montserrat',helvetica,arial,sans-serif;
            -webkit-font-smoothing: antialiased;
            font-size: 14px;
            line-height: 1.4;
            margin: 0;
            padding: 0;
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%; }

        table {
            border-collapse: separate;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
            width: 100%; }
        table td {
            font-size: 14px;
            vertical-align: top; }

        /* -------------------------------------
            BODY & CONTAINER
        ------------------------------------- */

        .body {
            background-color: #fff;
            width: 100%; }

        /* Set a max-width, and make it display as block so it will automatically stretch to that width, but will also shrink down on a phone or something */
        .container {
            display: block;
            margin: 0 auto !important;
            /* makes it centered */
            max-width: 580px;
            width: 580px; }

        /* This should also be a block element, so that it will fill 100% of the .container */
        .content {
            box-sizing: border-box;
            display: block;
            margin: 0 auto;
            max-width: 580px;

        }

        /* -------------------------------------
            HEADER, FOOTER, MAIN
        ------------------------------------- */
        .main {
            background: #ffffff;
            border-radius: 3px;
            width: 100%; }

        .wrapper {
            box-sizing: border-box;
            padding: 15px 10px; }

        .content-block {
            padding-bottom: 10px;
            padding-top: 10px;
        }

        .footer {
            clear: both;
            margin-top: 10px;
            text-align: left;
            width: 100%; }
        .footer td,
        .footer p,
        .footer span,
        .footer a {
            font-size: 12px;
            text-align: left; }

        /* -------------------------------------
            TYPOGRAPHY
        ------------------------------------- */
        h1,
        h2,
        h3,
        h4 {
            color: #000000;
            font-family: sans-serif;
            font-weight: 400;
            line-height: 1.4;
            margin: 0;
            margin-bottom: 30px; }

        h1 {
            font-size: 35px;
            font-weight: 300;
            text-align: center;
            text-transform: capitalize; }

        p,
        ul,
        ol {
            font-weight: normal;
            margin: 0;
            margin-bottom: 15px; }
        p li,
        ul li,
        ol li {
            list-style-position: inside;
            margin-left: 5px; }

        a {
            color: #3498db;
            text-decoration: underline; }

        /* -------------------------------------
            OTHER STYLES THAT MIGHT BE USEFUL
        ------------------------------------- */
        .last {
            margin-bottom: 0; }

        .first {
            margin-top: 0; }

        .align-center {
            text-align: center; }

        .align-right {
            text-align: right; }

        .align-left {
            text-align: left; }

        .clear {
            clear: both; }

        .mt0 {
            margin-top: 0; }

        .mb0 {
            margin-bottom: 0; }

        .preheader {
            color: transparent;
            display: none;
            height: 0;
            max-height: 0;
            max-width: 0;
            opacity: 0;
            overflow: hidden;
            mso-hide: all;
            visibility: hidden;
            width: 0; }

        hr {
            border: 0;
            border-bottom: 1px solid #000;
            margin: 20px 0; }

        /* -------------------------------------
            RESPONSIVE AND MOBILE FRIENDLY STYLES
        ------------------------------------- */
        @media only screen and (max-width: 750px) {
            table[class=body] h1 {
                font-size: 28px !important;
                margin-bottom: 10px !important; }
            table[class=body] p,
            table[class=body] ul,
            table[class=body] ol,
            table[class=body] td,
            table[class=body] span,
            table[class=body] a {
                font-size: 16px !important; }
            table[class=body] .wrapper,
            table[class=body] .article {
                padding: 10px !important; }
            table[class=body] .content {
                padding: 0 !important; }
            table[class=body] .container {
                padding: 0 !important;
                width: 100% !important; }
            table[class=body] .main {
                border-left-width: 0 !important;
                border-radius: 0 !important;
                border-right-width: 0 !important; }
            table[class=body] .btn table {
                width: 100% !important; }
            table[class=body] .btn a {
                width: 100% !important; }
            table[class=body] .img-responsive {
                height: auto !important;
                max-width: 100% !important;
                width: auto !important; }}

        /* -------------------------------------
            PRESERVE THESE STYLES IN THE HEAD
        ------------------------------------- */
        @media all {
            .ExternalClass {
                width: 100%; }
            .ExternalClass,
            .ExternalClass p,
            .ExternalClass span,
            .ExternalClass font,
            .ExternalClass td,
            .ExternalClass div {
                line-height: 100%; }
            .apple-link a {
                color: inherit !important;
                font-family: inherit !important;
                font-size: inherit !important;
                font-weight: inherit !important;
                line-height: inherit !important;
                text-decoration: none !important; }
        }

    </style>
</head>
<body class="">
<table border="0" cellpadding="0" cellspacing="0" class="body">
    <tr>
        <td class="container">
            <div class="content">
    <tr>
        <td style="background-color: #ffffff;">
            <img src="https://media.guitarcenter.com/is/image/MMGS7/Lessons-Email-Header-100?hei=100" alt="Guitar Center Lessons" style="width: 100%; max-width: 750px; height: auto; font-family: sans-serif; font-size: 15px; line-height: 15px; display: block;" class="g-img" border="0">
        </td>
    </tr>
    <table class="main">

        <!-- START MAIN CONTENT AREA -->
        <tr>
            <td class="wrapper">
                <table border="0" cellpadding="0" cellspacing="0">
                    <tr>
                        <td>
                            <p>This is a system generated mail. Please do not reply to this email for any schedule related topics.</p>
                            <br/>
                            <p>${customerName},</p>
                            <p>Thank you for scheduling a lesson at Guitar Center Lessons. The details of your appointment are shown below.</p>
                            <br>
                            <table border="0" cellpadding="0" cellspacing="0">
                                <tbody>
                                <tr>
                                    <td align="left">
                                        <table border="0" cellpadding="0" cellspacing="0">
                                            <tbody>
                                            <tr>
                                                <td>
                                                    <ul style="padding: 0; margin: 0; list-style-type: none;">
                                                        <li style="margin:0 0 5px 0;" class="list-item-first"><span style="font-weight:bold">Location : ${location} <a href="${locationMapUrl}"> (map) </a>  </span> </li>
                                                        <li style="margin:0 0 5px 0;"><span style="font-weight:bold">Date : </span> ${date} - ${endTime}</li>
                                                        <li style="margin:0 0 5px 0;" class="list-item-last"><span style="font-weight:bold">Duration : </span>${duration}</li>
                                                        <li style="margin:0 0 0 0;" class="list-item-last"><span style="font-weight:bold">Instructor Name : </span>${instructorFullName}</li>
                                                    </ul>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <br>
                            <br>
                            <p style="margin-bottom: 5px;">See you soon!</p>
                            <p style="margin-bottom: 5px;">-Guitar Center</p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <!-- END MAIN CONTENT AREA -->
    </table>

    <!-- START FOOTER -->
    <div class="footer">
        <table style="background-color:#ffffff" width="600" cellspacing="0" cellpadding="0" border="0" align="left">
            <tbody>
            <tr>
                <td style="padding:10px 10px 10px 10px;font-family:Helvetica,Arial,sans-serif;font-size:11px;color:#333333;line-height:1.4;text-decoration:none" align="center">© 2020 <span class="il">Guitar</span> <span class="il">Center</span>, Inc., <span></span>P.O. Box 5111, Thousand Oaks, CA&nbsp;&nbsp;91359-5111, USA.<span></span> Call us at <span style="color:#333332">************</span> from <br>
                    6 a.m. to 8 p.m. PT, <span></span>Monday through Friday, and from 7 a.m. to 7 p.m. PT, <span></span> Saturday and Sunday.</td>
            </tr>
            </tbody>
        </table>
    </div>
    <!-- END FOOTER -->

    <!-- END CENTERED WHITE CONTAINER -->
    </div>
    </td>
    <td>&nbsp;</td>
    </tr>
</table>

</body>
</html>
