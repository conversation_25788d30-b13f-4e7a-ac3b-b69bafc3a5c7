<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<style type="text/css">
	body
	{ 
		border: thin solid gray;
		width: 750px;
	}	
</style>
</head>
<body>
<img src ="${emailHeaderImgUrl}"/>

<#if successStatus>
<br/>
<br/>
<br/>
<br/>
     &nbsp;&nbsp;The ${jobName} has run successfully
     <#if successEmptyStatus>
     and no records found.
      <br/>   
	<br/>
     <#else>
     
     <br/>   
<br/>

<br/>
	<br/>	
		&nbsp;&nbsp; Below are the stores and instructors personal email id respectively
	<br/>		
	<br/>

	<table style="border: 1px solid black;">
	<thead>
		<tr style="border: 1px solid black;">
			<th style="border: 1px solid black;">Store </th>
			<th style="border: 1px solid black;">Instructor(s) personal email id</th>
			
		</tr>
	</thead>
	
	<tbody>
		<#list successMap as appDetails>
		<tr style="border: 1px solid black;">
		  <td style="border: 1px solid black;">${appDetails.locationNameList}</td>
		  <td style="border: 1px solid black;">
				<#list appDetails.instructorMailingList as mail>	
					   	${mail}	<br/><br/>					
				</#list>
			</td>
		</tr>			
		</#list>		
		
		
	</tbody>	
	</table>
</#if>
<#else>

<br/>
<br/>
<br/>
<br/>
     &nbsp;&nbsp;The ${jobName} has failed with the following exception<br/>   
<br/>
<br/>
<br/>
<br/>
<br/>
<br/>
${exception}
<br/>
<br/>
<br/>
<br/>
</#if>
<br/>
&nbsp;&nbsp;-Guitar Center<br/>
</body>
</html>