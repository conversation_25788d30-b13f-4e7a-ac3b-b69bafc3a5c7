# Parameters for integration tests
#  - note that some properties related to remote access are blank until a suitable
#    server is available for use (AAXIS old DEV server is being shutdown).
#
#  Tests that use RemoteDataIntegrationServiceImpl should use an assume-type
#  clause to not fail under this scenario.
#  copy files to classpath to avoid error--Austin 2020.11.24
crm.hostname=
crm.username=
crm.privateKeyPath=
crm.knownHostsPath=
crm.locationTriggerPath=
crm.locationFilePathPrefix=
crm.employeeTriggerPath=
crm.employeeFilePathPrefix=

crm.original=
crm.encrypt=
crm.decrypt=
crm.publicKeyFile=
crm.secretKeyFile=
crm.passphrase=
crm.fileExtn=
crm.sftpPGPdir=
#crm.passphrase = pietro
pos.serverUrl=ssl://tib-ems-qarelease.domestic.guitarcenter.com:3223,ssl://tib-ems-qarelease.domestic.guitarcenter.com:3223
pos.username=Svc_GCS-Dev
pos.password=2A*Aierp5FJ/*mD'DFJw?5#=g
pos.queue=GCI.GC.LESSONS.PRV.CUSTOMER.INBOUND
pos.sslidentity=${catalina.base}/conf/GCQARELEMS11.client.identity.p12
pos.sslpassword=qarelease