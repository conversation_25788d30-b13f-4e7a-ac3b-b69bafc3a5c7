#spring.profiles.active=dev
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true
server.servlet.context-path=/lessons-api/v1
server.port=8080

#spring.security.user.name=admin
#spring.security.user.password=secret


# Database Configuration
jdbc.schedulerDB.driverClassName=oracle.jdbc.OracleDriver
jdbc.schedulerDB.url=*******************************************************************************
jdbc.schedulerDB.username=gcstudio
jdbc.schedulerDB.password=MGU86#nJA4ZbK4H



# Mail Session Configuration
mail.session.name=mail/session
mail.session.auth=Container
mail.smtp.host=phxcas.guitarcenter.com
mail.smtp.port=25
mail.debug=false

email.header.img.url=https://media.guitarcenter.com/is/image/MMGS7/Lessons-Email-Header-135?hei=135

API_KEY=123456789