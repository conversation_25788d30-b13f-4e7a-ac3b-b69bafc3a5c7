module.exports = function(grunt) {
	require('jit-grunt')(grunt);
	grunt.initConfig({
		pkg : grunt.file.readJSON('package.json'),
		coffee:{},
		less: {
			development: {
				options: {
					compress: true,
					yuicompress: true,
					optimization: 2
				},
				files: {
					"css/gcs.min.css": "css/less/gcs.less" // destination file and source file
				}
			}
		},
		watch: {
			styles: {
				files: ['css/less/**/*.less'], // which files to watch
				tasks: ['less'],
				options: {
					nospawn: true
				}
			}
		},
		concat: {},
		cssmin: {
			options: {
				banner: '/*Compressed css at' + Date() + '*/\n',
				report: 'gzip'
			},
			combine: {
				files: {
					'css/gcs.min.css': ['css/gcs.css']
				}
			}

		},
		uglify: {
			my_target: {
				files: {
//					'js/vendor/libs/typeahead.min.js': ['js/vendor/libs/typeahead.js']
					'js/vendor/libs/pikaday.min.js': ['js/vendor/libs/pikaday.js']
					// 'js/utils/eventdeploy.min.js': ['js/utils/eventdeploy.js'],
//					'js/utils/hash.min.js': ['js/utils/hash.js'],
					// 'js/vendor/libs/moment.min.js': ['js/vendor/libs/moment.js'],
					// 'js-build/app/modules/calendar.min.js': ['js/app/modules/calendar.js'],
					// 'js-build/app/modules/filter.min.js': ['js/app/modules/filter.js'],
					// 'js-build/app/scripts/schedule.min.js': ['js/app/scripts/schedule.js'],
					// 'js-build/utils/eventdeploy.min.js': ['js/utils/eventdeploy.js'],
				}
			}
		},
		jshint: {
			options: {

			},
			all: ['js/app/modules/*.js']
		}
	});

	grunt.loadNpmTasks('grunt-contrib-uglify');
	grunt.loadNpmTasks('grunt-contrib-cssmin');
	grunt.loadNpmTasks('grunt-contrib-watch');
	grunt.loadNpmTasks('grunt-contrib-jshint');
	grunt.loadNpmTasks('grunt-contrib-less');
	grunt.registerTask('default', ['less', 'watch']);

}