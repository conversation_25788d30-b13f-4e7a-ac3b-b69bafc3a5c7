package com.guitarcenter.scheduler.webservice.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

import org.joda.time.DateTime;
import org.joda.time.LocalTime;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.AvailabilityUtil;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dto.StudioHoursDTO;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.webservice.dto.InstructorAvailableHoursDTO;


/**
* Class which has all utility methods used by Lesson Service
* <AUTHOR>
*
*/

public class WebServiceUtil {
	

	
/**
 *Util Method to format availability hours of Studio
 * @param availability
 * @return List<StudioHoursDTO>
 */
public static List<StudioHoursDTO> getAvailabilityEditHourShowList(Availability availability){
	  	  
		  
	  	List<StudioHoursDTO> studioHours  = new ArrayList<StudioHoursDTO>();
	  
	  	StudioHoursDTO studioHoursDTO = null;		  
					
		
		String mondayStart ="";
		String mondayEnd = "";
		if(availability.getMondayStartTime() != null && availability.getMondayEndTime() != null){						
			
			mondayStart = AvailabilityUtil.format(availability.getMondayStartTime());
			mondayEnd = AvailabilityUtil.format(availability.getMondayEndTime());	
			
			studioHoursDTO = new StudioHoursDTO();
			
			studioHoursDTO.setDayName(AppConstants.STUDIO_HOURS_WEEKDAY_CHOSE[0]);
			studioHoursDTO.setHours(mondayStart + "-"  + mondayEnd );
			studioHours.add(studioHoursDTO);
			
		}
		
				
		
		String tuesdayStart = "";
		String tuesdayEnd = "" ;
		if(availability.getTuesdayEndTime() != null && availability.getTuesdayStartTime() != null){
			tuesdayStart = AvailabilityUtil.format(availability.getTuesdayStartTime());
			tuesdayEnd = AvailabilityUtil.format(availability.getTuesdayEndTime());
			
			studioHoursDTO = new StudioHoursDTO();
			
			studioHoursDTO.setDayName(AppConstants.STUDIO_HOURS_WEEKDAY_CHOSE[1]);
			studioHoursDTO.setHours(tuesdayStart + "-"  + tuesdayEnd );
			studioHours.add(studioHoursDTO);
			
		}
		String wednesdayStart = "";
		String wednesdayEnd = "";
		if(availability.getWednesdayEndTime() != null && availability.getWednesdayStartTime() != null){
			 wednesdayStart = AvailabilityUtil.format(availability.getWednesdayStartTime());
			 wednesdayEnd = AvailabilityUtil.format(availability.getWednesdayEndTime());
			 
			 studioHoursDTO = new StudioHoursDTO();
			 
			 studioHoursDTO.setDayName(AppConstants.STUDIO_HOURS_WEEKDAY_CHOSE[2]);
			studioHoursDTO.setHours(wednesdayStart + "-"  + wednesdayEnd );
			studioHours.add(studioHoursDTO);
			 
		}
		String thursdayStart = "";
		String thursdayEnd  = "";
		if(availability.getThursdayStartTime() != null && availability.getThursdayEndTime() != null){
			thursdayStart = AvailabilityUtil.format(availability.getThursdayStartTime());
			thursdayEnd = AvailabilityUtil.format(availability.getThursdayEndTime());
			
			studioHoursDTO = new StudioHoursDTO();
			
			 studioHoursDTO.setDayName(AppConstants.STUDIO_HOURS_WEEKDAY_CHOSE[3]);
			studioHoursDTO.setHours(thursdayStart + "-"  + thursdayEnd );
			studioHours.add(studioHoursDTO);
			 
			
		}
		String fridayStart = "";
		String fridayEnd = "";
		if(availability.getFridayEndTime() != null && availability.getFridayStartTime() != null){
			fridayStart = AvailabilityUtil.format(availability.getFridayStartTime());
			fridayEnd = AvailabilityUtil.format(availability.getFridayEndTime());
			
			studioHoursDTO = new StudioHoursDTO();
			
			studioHoursDTO.setDayName(AppConstants.STUDIO_HOURS_WEEKDAY_CHOSE[4]);
			studioHoursDTO.setHours(fridayStart + "-"  + fridayEnd );
			studioHours.add(studioHoursDTO);
			
		}
		String saturdayStart = "";
		String saturdayEnd = "";
		if(availability.getSaturdayEndTime() != null && availability.getSaturdayStartTime() != null){
			saturdayStart = AvailabilityUtil.format(availability.getSaturdayStartTime());
			saturdayEnd = AvailabilityUtil.format(availability.getSaturdayEndTime());
			
			studioHoursDTO = new StudioHoursDTO();
			
			studioHoursDTO.setDayName(AppConstants.STUDIO_HOURS_WEEKDAY_CHOSE[5]);
			studioHoursDTO.setHours(saturdayStart + "-"  + saturdayEnd );
			studioHours.add(studioHoursDTO);
		}
					
		String sundayStart = "";
		String sundayEnd = "";
		if(availability.getSundayStartTime() != null && availability.getSundayEndTime() != null){
			sundayStart = AvailabilityUtil.format(availability.getSundayStartTime());
			sundayEnd = AvailabilityUtil.format(availability.getSundayEndTime());
			
			studioHoursDTO = new StudioHoursDTO();
			
			studioHoursDTO.setDayName(AppConstants.STUDIO_HOURS_WEEKDAY_CHOSE[6]);
			studioHoursDTO.setHours(sundayStart + "-"  + sundayEnd );
			studioHours.add(studioHoursDTO);
			
		}
		
		return studioHours;
	}
	
	
	
	//Added for Phase2_LES-27 Changes - Method to get the available hours for the instructor	
	/**
	 * Method to get the open slots for instructors
	 * @param startTime
	 * @param endTime
	 * @param instructorAvailableHoursDTOList
	 * 
	 * @return List<InstructorAvailableHoursDTO> 
	 * @throws ParseException
	 */
	public static List<InstructorAvailableHoursDTO> getAvailableSlots(DateTime startTime,DateTime endTime, 
						List <InstructorAvailableHoursDTO>  instructorAvailableHoursDTOList ) throws ParseException{
		
		
		
			List<InstructorAvailableHoursDTO> freeSlotsList = new ArrayList<InstructorAvailableHoursDTO>();
			
			InstructorAvailableHoursDTO freeSlot= null;
			
			
			LocalTime startLocalTime = new LocalTime(startTime.getHourOfDay(),startTime.getMinuteOfHour());
					
					
			LocalTime endLocalTime =   new LocalTime(endTime.getHourOfDay(),endTime.getMinuteOfHour());
				
			
			LocalTime newStartime  = startLocalTime;
		
			
			
			if(null != instructorAvailableHoursDTOList  && instructorAvailableHoursDTOList.size() > 0)
			{	
				
				Collections.sort(instructorAvailableHoursDTOList);
				
			     for(InstructorAvailableHoursDTO instructorAvailableHoursDTO : instructorAvailableHoursDTOList)
			     {	    	 
			    	
			    	 LocalTime appointmentEndDate = instructorAvailableHoursDTO.getAppointmentEndTime();
			    	  
			    	 LocalTime appointmentStartDate =instructorAvailableHoursDTO.getAppointmentStartTime();
			    	 
			    	 		if(newStartime.isBefore(appointmentStartDate))
	
			    			{
			    			 
			    			 	freeSlot=  new InstructorAvailableHoursDTO(newStartime,appointmentStartDate);
			    			 	freeSlotsList.add(freeSlot);
			    			}		    		 
			    		 			
			    
			    	 newStartime  = appointmentEndDate;
			  			    	 
			     }
			     
			     if(newStartime.isBefore(endLocalTime))
			     {
			    	 freeSlot=  new InstructorAvailableHoursDTO(newStartime,endLocalTime);
			    	 
			    	 freeSlotsList.add(freeSlot);
			     }
			     
			}
			else				
			{
				
				
				freeSlot=  new InstructorAvailableHoursDTO(startLocalTime,endLocalTime);
		    	 
		    	 freeSlotsList.add(freeSlot);
		     }
				
			
		     
		     return freeSlotsList;
		
	}

	/**
	 * Method to get the open slots for instructors
	 * @param startTime
	 * @param endTime
	 * @param instructorAvailableHoursDTOList
	 * 
	 * @return List<InstructorAvailableHoursDTO> 
	 * @throws ParseException
	 */
	public static List<InstructorAvailableHoursDTO> getAvailableSlotsInsAVL(DateTime startTime,DateTime endTime, 
						List <InstructorAvailableHoursDTO>  instructorAvailableHoursDTOList ) throws ParseException{
		
		
		
			List<InstructorAvailableHoursDTO> freeSlotsList = new ArrayList<InstructorAvailableHoursDTO>();
			
			InstructorAvailableHoursDTO freeSlot= null;
			
			
			LocalTime startLocalTime = new LocalTime(startTime.getHourOfDay(),startTime.getMinuteOfHour());
					
					
			LocalTime endLocalTime =   new LocalTime(endTime.getHourOfDay(),endTime.getMinuteOfHour());
				
			
			LocalTime newStartime  = startLocalTime;
		
			
			
			if(null != instructorAvailableHoursDTOList  && instructorAvailableHoursDTOList.size() > 0)
			{	
				
				Collections.sort(instructorAvailableHoursDTOList);
				
			     for(InstructorAvailableHoursDTO instructorAvailableHoursDTO : instructorAvailableHoursDTOList)
			     {	    	 
			    	
			    	 LocalTime appointmentEndDate = instructorAvailableHoursDTO.getAppointmentEndTime();
			    	  
			    	 LocalTime appointmentStartDate =instructorAvailableHoursDTO.getAppointmentStartTime();
			    	 
			    	 		if(newStartime.isBefore(appointmentStartDate))
	
			    			{
			    			 
			    			 	freeSlot=  new InstructorAvailableHoursDTO(newStartime,appointmentStartDate);
			    			 	freeSlotsList.add(freeSlot);
			    			}		    		 
			    		 			
			    
			    	 newStartime  = appointmentEndDate;
			  			    	 
			     }
			     
			     if(newStartime.isBefore(endLocalTime))
			     {
			    	 freeSlot=  new InstructorAvailableHoursDTO(newStartime,endLocalTime);
			    	 
			    	 freeSlotsList.add(freeSlot);
			     }
			     
			}
			else				
			{
				
				
				freeSlot=  new InstructorAvailableHoursDTO(startLocalTime,endLocalTime);
		    	 
		    	 freeSlotsList.add(freeSlot);
		     }
				
			
		     
		     return freeSlotsList;
		
	}

	
	/**
	 * Method to block the open slots for instructors on Off days
	 * @param startTime
	 * @param endTime
	 * @param profileTimeoffStartTime
	 * @param profileTimeoffEndTime
	 * @param instructorAvailableHoursDTOList
	 * @param instructorInfoDTO 
	 * 
	 * @return List<InstructorAvailableHoursDTO> 
	 * @throws ParseException
	 */
	public static List<InstructorAvailableHoursDTO> getAvailableSlotsOnProfileTimeOff(DateTime startTime,DateTime endTime, 
						List <InstructorAvailableHoursDTO>  instructorAvailableHoursDTOList,LocalTime profileTimeoffStartTime, LocalTime profileTimeoffEndTime ) throws ParseException{
		
		List<InstructorAvailableHoursDTO> freeSlotsList = new ArrayList<InstructorAvailableHoursDTO>();
		InstructorAvailableHoursDTO freeSlot = null;
		LocalTime startLocalTime = new LocalTime(startTime.getHourOfDay(), startTime.getMinuteOfHour());
		LocalTime endLocalTime = new LocalTime(endTime.getHourOfDay(), endTime.getMinuteOfHour());
     
			if(startLocalTime.isAfter(profileTimeoffStartTime.plusMinutes(-1)) && endLocalTime.isBefore(profileTimeoffEndTime.plusMinutes(1))){
				return  freeSlotsList;
			}
			   
			profileTimeoffStartTime =    profileTimeoffStartTime.plusMinutes(-1);
			profileTimeoffEndTime = profileTimeoffEndTime.plusMinutes(1);
			LocalTime newStartime  = startLocalTime;
 
			if(null != instructorAvailableHoursDTOList  && instructorAvailableHoursDTOList.size() > 0)
			{	
				
				Collections.sort(instructorAvailableHoursDTOList);
				
			     for(InstructorAvailableHoursDTO instructorAvailableHoursDTO : instructorAvailableHoursDTOList)
			     {	    	 
			    	
			    	 LocalTime appointmentEndDate = instructorAvailableHoursDTO.getAppointmentEndTime();
			    	  
			    	 LocalTime appointmentStartDate =instructorAvailableHoursDTO.getAppointmentStartTime();
			    	 
			    	 if(newStartime.isBefore(appointmentStartDate) &&  ((newStartime.isBefore(profileTimeoffStartTime) && appointmentStartDate.isBefore(profileTimeoffStartTime) ) ||
			    			 (newStartime.isAfter(profileTimeoffEndTime) && appointmentStartDate.isAfter((profileTimeoffEndTime)) ))){
			    		    freeSlot=  new InstructorAvailableHoursDTO(newStartime,appointmentStartDate);
		    			 	freeSlotsList.add(freeSlot);
			    	 	}else if(newStartime.isBefore(appointmentStartDate) && (newStartime.isBefore(profileTimeoffStartTime) && profileTimeoffEndTime.isAfter(appointmentStartDate))){
			    				freeSlot=  new InstructorAvailableHoursDTO(newStartime,profileTimeoffStartTime.plusMinutes(1));
			    			 	freeSlotsList.add(freeSlot);
			    			}else if (newStartime.isBefore(appointmentStartDate) &&  appointmentStartDate.isAfter(profileTimeoffEndTime) 
			    					&& (newStartime.equals(profileTimeoffStartTime) || newStartime.isBefore(profileTimeoffEndTime))){
			    				freeSlot=  new InstructorAvailableHoursDTO(profileTimeoffEndTime.plusMinutes(-1),appointmentStartDate);
			    			 	freeSlotsList.add(freeSlot);
			    			}
			    		 newStartime  = appointmentEndDate;
			     }
			     
			     if(newStartime.isBefore(endLocalTime)   && ((newStartime.isBefore(profileTimeoffStartTime) && endLocalTime.isBefore(profileTimeoffStartTime) ) ||
			    			 (newStartime.isAfter(profileTimeoffEndTime) && endLocalTime.isAfter((profileTimeoffEndTime)) )) ){
			    	 freeSlot =  new InstructorAvailableHoursDTO(newStartime,endLocalTime);
			    	 freeSlotsList.add(freeSlot);
			     }else if(newStartime.isBefore(endLocalTime) && newStartime.isAfter(profileTimeoffStartTime) && endLocalTime.isAfter(profileTimeoffEndTime) && newStartime.isBefore(profileTimeoffStartTime)){
			    	 freeSlot =  new InstructorAvailableHoursDTO(profileTimeoffEndTime.plusMinutes(-1),endLocalTime);
			    	 freeSlotsList.add(freeSlot);
			     }else if(newStartime.isBefore(endLocalTime) && newStartime.isAfter(profileTimeoffStartTime) && endLocalTime.isAfter(profileTimeoffEndTime)){
			    	 freeSlot =  new InstructorAvailableHoursDTO(profileTimeoffEndTime.plusMinutes(-1),endLocalTime);
			    	 freeSlotsList.add(freeSlot);
			     }
			}
			else				
			{
				    if((startLocalTime.isBefore(endLocalTime) && (endLocalTime.isBefore(profileTimeoffEndTime) 
		    			 && endLocalTime.isBefore(profileTimeoffStartTime) || (startLocalTime.isAfter(profileTimeoffStartTime) && startLocalTime.isAfter((profileTimeoffEndTime)) ))) ){
				    	freeSlot=  new InstructorAvailableHoursDTO(startLocalTime,endLocalTime);
				    	freeSlotsList.add(freeSlot);
		    	     }else if( (startLocalTime.isBefore(endLocalTime) && profileTimeoffEndTime.isAfter(endLocalTime))){
		    				freeSlot=  new InstructorAvailableHoursDTO(startLocalTime,profileTimeoffStartTime.plusMinutes(1));
		   		    	 	freeSlotsList.add(freeSlot);
		    		}else if(startLocalTime.isBefore(endLocalTime) && startLocalTime.isAfter(profileTimeoffStartTime) 
		    				&& startLocalTime.isBefore(profileTimeoffEndTime) && endLocalTime.isAfter(profileTimeoffEndTime)){
		    			freeSlot=  new InstructorAvailableHoursDTO(profileTimeoffEndTime.plusMinutes(-1),endLocalTime);
		    			freeSlotsList.add(freeSlot);
		    		}
		    	     else if ( startLocalTime.isBefore(endLocalTime) &&
		    				profileTimeoffStartTime.isAfter(startLocalTime) && profileTimeoffStartTime.isBefore(endLocalTime) && profileTimeoffEndTime.isAfter(startLocalTime) && profileTimeoffEndTime.isBefore(endLocalTime)
		    				){
		    			freeSlot=  new InstructorAvailableHoursDTO(startLocalTime,profileTimeoffStartTime.plusMinutes(1));
		    			freeSlotsList.add(freeSlot);
		    			freeSlot=  new InstructorAvailableHoursDTO(profileTimeoffEndTime.plusMinutes(-1),endLocalTime);
		    			freeSlotsList.add(freeSlot);
		    		}
			 }
				
		     return freeSlotsList;
		
	}
	
	/**
	 * Method to block the open slots for instructors on Off days
	 * @param startTime
	 * @param endTime
	 * @param profileTimeoffStartTime
	 * @param profileTimeoffEndTime
	 * @param instructorAvailableHoursDTOList
	 * @param instructorInfoDTO 
	 * 
	 * @return List<InstructorAvailableHoursDTO> 
	 * @throws ParseException
	 */
	public static List<InstructorAvailableHoursDTO> getAvailableSlotsOnProfileTimeOffInsAVL(DateTime startTime,DateTime endTime, 
						List <InstructorAvailableHoursDTO>  instructorAvailableHoursDTOList,LocalTime profileTimeoffStartTime, LocalTime profileTimeoffEndTime ) throws ParseException{
		
		List<InstructorAvailableHoursDTO> freeSlotsList = new ArrayList<InstructorAvailableHoursDTO>();
		InstructorAvailableHoursDTO freeSlot = null;
		LocalTime startLocalTime = new LocalTime(startTime.getHourOfDay(), startTime.getMinuteOfHour());
		LocalTime endLocalTime = new LocalTime(endTime.getHourOfDay(), endTime.getMinuteOfHour());
     
			if(startLocalTime.isAfter(profileTimeoffStartTime.plusMinutes(-1)) && endLocalTime.isBefore(profileTimeoffEndTime.plusMinutes(1))){
				return  freeSlotsList;
			}
			   
			profileTimeoffStartTime =    profileTimeoffStartTime.plusMinutes(-1);
			profileTimeoffEndTime = profileTimeoffEndTime.plusMinutes(1);
			LocalTime newStartime  = startLocalTime;
 
			if(null != instructorAvailableHoursDTOList  && instructorAvailableHoursDTOList.size() > 0)
			{	
				
				Collections.sort(instructorAvailableHoursDTOList);
				
			     for(InstructorAvailableHoursDTO instructorAvailableHoursDTO : instructorAvailableHoursDTOList)
			     {	    	 
			    	
			    	 LocalTime appointmentEndDate = instructorAvailableHoursDTO.getAppointmentEndTime();
			    	  
			    	 LocalTime appointmentStartDate =instructorAvailableHoursDTO.getAppointmentStartTime();
			    	 
			    	 if(newStartime.isBefore(appointmentStartDate) &&  ((newStartime.isBefore(profileTimeoffStartTime) && appointmentStartDate.isBefore(profileTimeoffStartTime) ) ||
			    			 (newStartime.isAfter(profileTimeoffEndTime) && appointmentStartDate.isAfter((profileTimeoffEndTime)) ))){
			    		    freeSlot=  new InstructorAvailableHoursDTO(newStartime,appointmentStartDate);
		    			 	freeSlotsList.add(freeSlot);
			    	 	}else if(newStartime.isBefore(appointmentStartDate) && (newStartime.isBefore(profileTimeoffStartTime) && profileTimeoffEndTime.isAfter(appointmentStartDate))){
			    				freeSlot=  new InstructorAvailableHoursDTO(newStartime,profileTimeoffStartTime.plusMinutes(1));
			    			 	freeSlotsList.add(freeSlot);
			    			}else if (newStartime.isBefore(appointmentStartDate) &&  appointmentStartDate.isAfter(profileTimeoffEndTime) 
			    					&& (newStartime.equals(profileTimeoffStartTime) || newStartime.isBefore(profileTimeoffEndTime))){
			    				freeSlot=  new InstructorAvailableHoursDTO(profileTimeoffEndTime.plusMinutes(-1),appointmentStartDate);
			    			 	freeSlotsList.add(freeSlot);
			    			}
			    		 newStartime  = appointmentEndDate;
			     }
			     
			     if(newStartime.isBefore(endLocalTime)   && ((newStartime.isBefore(profileTimeoffStartTime) && endLocalTime.isBefore(profileTimeoffStartTime) ) ||
			    			 (newStartime.isAfter(profileTimeoffEndTime) && endLocalTime.isAfter((profileTimeoffEndTime)) )) ){
			    	 freeSlot =  new InstructorAvailableHoursDTO(newStartime,endLocalTime);
			    	 freeSlotsList.add(freeSlot);
			     }else if(newStartime.isBefore(endLocalTime) && newStartime.isAfter(profileTimeoffStartTime) && endLocalTime.isAfter(profileTimeoffEndTime) && newStartime.isBefore(profileTimeoffStartTime)){
			    	 freeSlot =  new InstructorAvailableHoursDTO(profileTimeoffEndTime.plusMinutes(-1),endLocalTime);
			    	 freeSlotsList.add(freeSlot);
			     }else if(newStartime.isBefore(endLocalTime) && newStartime.isAfter(profileTimeoffStartTime) && endLocalTime.isAfter(profileTimeoffEndTime)){
			    	 freeSlot =  new InstructorAvailableHoursDTO(profileTimeoffEndTime.plusMinutes(-1),endLocalTime);
			    	 freeSlotsList.add(freeSlot);
			     }
			}
			else				
			{
				    if((startLocalTime.isBefore(endLocalTime) && (endLocalTime.isBefore(profileTimeoffEndTime) 
		    			 && endLocalTime.isBefore(profileTimeoffStartTime) || (startLocalTime.isAfter(profileTimeoffStartTime) && startLocalTime.isAfter((profileTimeoffEndTime)) ))) ){
				    	freeSlot=  new InstructorAvailableHoursDTO(startLocalTime,endLocalTime);
				    	freeSlotsList.add(freeSlot);
		    	     }else if( (startLocalTime.isBefore(endLocalTime) && profileTimeoffEndTime.isAfter(endLocalTime))){
		    				freeSlot=  new InstructorAvailableHoursDTO(startLocalTime,profileTimeoffStartTime.plusMinutes(1));
		   		    	 	freeSlotsList.add(freeSlot);
		    		}else if(startLocalTime.isBefore(endLocalTime) && startLocalTime.isAfter(profileTimeoffStartTime) 
		    				&& startLocalTime.isBefore(profileTimeoffEndTime) && endLocalTime.isAfter(profileTimeoffEndTime)){
		    			freeSlot=  new InstructorAvailableHoursDTO(profileTimeoffEndTime.plusMinutes(-1),endLocalTime);
		    			freeSlotsList.add(freeSlot);
		    		}
		    	     else if ( startLocalTime.isBefore(endLocalTime) &&
		    				profileTimeoffStartTime.isAfter(startLocalTime) && profileTimeoffStartTime.isBefore(endLocalTime) && profileTimeoffEndTime.isAfter(startLocalTime) && profileTimeoffEndTime.isBefore(endLocalTime)
		    				){
		    			freeSlot=  new InstructorAvailableHoursDTO(startLocalTime,profileTimeoffStartTime.plusMinutes(1));
		    			freeSlotsList.add(freeSlot);
		    			freeSlot=  new InstructorAvailableHoursDTO(profileTimeoffEndTime.plusMinutes(-1),endLocalTime);
		    			freeSlotsList.add(freeSlot);
		    		}
			 }
				
		     return freeSlotsList;
		
	}
		
	 public static List<InstructorAvailableHoursDTO> getAvailableSlotsInsAVLDayForce(DateTime startTime,DateTime endTime, 
					List <InstructorAvailableHoursDTO>  instructorAvailableHoursDTOList ) throws ParseException{
		
		
		
		List<InstructorAvailableHoursDTO> freeSlotsList = new ArrayList<InstructorAvailableHoursDTO>();
		
		
		InstructorAvailableHoursDTO freeSlot= null;
		
		InstructorAvailableHoursDTO instructorAvailableHoursminMaxDTO = DateTimeUtil.getMinAndMaxTimes(instructorAvailableHoursDTOList);
		
		
		//LocalTime startLocalTime = new LocalTime(startTime.getHourOfDay(),startTime.getMinuteOfHour());
		LocalTime startLocalTime = instructorAvailableHoursminMaxDTO.getAppointmentStartTime();
				
				
		//LocalTime endLocalTime =   new LocalTime(endTime.getHourOfDay(),endTime.getMinuteOfHour());
		LocalTime endLocalTime = instructorAvailableHoursminMaxDTO.getAppointmentEndTime();
			
		
		LocalTime newStartime  = startLocalTime;
		
		
		
		if(null != instructorAvailableHoursDTOList  && instructorAvailableHoursDTOList.size() > 0)
		{	
			
			Collections.sort(instructorAvailableHoursDTOList);
			
		     for(InstructorAvailableHoursDTO instructorAvailableHoursDTO : instructorAvailableHoursDTOList)
		     {	    	 
		    	
		    	 LocalTime appointmentEndDate = instructorAvailableHoursDTO.getAppointmentEndTime();
		    	  
		    	 LocalTime appointmentStartDate =instructorAvailableHoursDTO.getAppointmentStartTime();
		    	 
		    	 		if(newStartime.isBefore(appointmentStartDate))
		
		    			{
		    			 
		    			 	freeSlot=  new InstructorAvailableHoursDTO(newStartime,appointmentStartDate);
		    			 	freeSlotsList.add(freeSlot);
		    			}		    		 
		    		 			
		    
		    	 newStartime  = appointmentEndDate;
		  			    	 
		     }
		     
		     if(newStartime.isBefore(endLocalTime))
		     {
		    	 freeSlot=  new InstructorAvailableHoursDTO(newStartime,endLocalTime);
		    	 
		    	 freeSlotsList.add(freeSlot);
		     }
		     
		}
		else				
		{
			
			
			freeSlot=  new InstructorAvailableHoursDTO(startLocalTime,endLocalTime);
			 
			 freeSlotsList.add(freeSlot);
		 }
			
		
		 
		 return freeSlotsList;
		
		}
	
 
    public static String formatTimeTo12Hr (String time) throws Exception {
        try {
               SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
               Date dateObj = sdf.parse(time);
               return new SimpleDateFormat("hh:mm a").format(dateObj);
        } catch (Exception e) {
               throw e;
        }      
}
    
    public static String formatTimeTo12HrInsAVL (String time) throws Exception {
        try {
               SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
               Date dateObj = sdf.parse(time);
               return new SimpleDateFormat("hh:mm a").format(dateObj);
        } catch (Exception e) {
               throw e;
        }      
}

    
    
 public static  String   getCurTmForTZ(String timeZoneStr)  // pass the location profile time zone like like America/New_York
    {
			TimeZone tz =  TimeZone.getTimeZone(timeZoneStr);
			Calendar c = 	Calendar.getInstance(tz);			    
			String curTmForTZ = c.get(Calendar.HOUR_OF_DAY)+":"+c.get(Calendar.MINUTE)+":"+c.get(Calendar.SECOND);                   
            return curTmForTZ;
    }

 
 public static  String   getCurDateForTZ(String timeZoneStr)  // pass the location profile time zone like like America/New_York
 {
			TimeZone tz =  TimeZone.getTimeZone(timeZoneStr);
			Calendar c = 	Calendar.getInstance(tz);		
			int month = c.get(Calendar.MONTH) + 1;
			String curTmForTZ = c.get(Calendar.YEAR)+"-"+month+"-"+c.get(Calendar.DAY_OF_MONTH);
			
         return curTmForTZ;
 }
 
 //GSSP-163 changes
 public static String convertToJson(Object result) throws JsonProcessingException
 {
	  ObjectMapper mapper = new ObjectMapper();
	  String jsonInString = mapper.writeValueAsString(result);
	  
	  return jsonInString;
 }
	

}
