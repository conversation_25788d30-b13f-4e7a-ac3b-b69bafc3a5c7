package com.guitarcenter.scheduler.webservice;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.CustomerDAO;
import com.guitarcenter.scheduler.dao.criterion.AppointmentCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.CustomerCriterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.ServiceLogger;
import com.guitarcenter.scheduler.service.CustomerEmailService;
import com.guitarcenter.scheduler.webservice.constants.WebServiceConstants;
import com.guitarcenter.scheduler.webservice.dto.CustLessonSeriesDTO;
import com.guitarcenter.scheduler.webservice.dto.CustLessonSeriesResponseDTO;
import com.guitarcenter.scheduler.webservice.dto.CustomerInformationDTO;
import com.guitarcenter.scheduler.webservice.dto.LessonSeries;
import com.guitarcenter.scheduler.webservice.dto.LessonSeriesServiceInputDTO;


@RestController
public class LessonValidationController {
	
	@Autowired
	private CustomerDAO	customerDAO;
		

	@Autowired
	private CustomerEmailService customerEmailService;
	
	private static final Logger		LOG					        = LoggerFactory.getLogger("schedulerlog");
	
	
	@Autowired
	private AppointmentDAO	appointmentDAO;
	
	
 	/**
		 * Service method used for Instructor Fetch Service
		 *  
	  	 * @param InstructorServiceDTO  instructorServiceDTO
	  	 * @return InstructorServiceResponseDTO
 	 * @throws JsonProcessingException 
	  	 * 
		 * 
		 */
		 
		  @RequestMapping(value="/ValidateAndGetLessonSeries",
				  method=RequestMethod.POST, headers = {"content-type=application/x-www-form-urlencoded"})  
		  
		 
		  public CustLessonSeriesResponseDTO validateAndGetLessonSeries(LessonSeriesServiceInputDTO  lessonSeriesServiceInputDTO) throws JsonProcessingException 
		  {		
			  CustLessonSeriesResponseDTO custLessonSeriesResponseDTO = new CustLessonSeriesResponseDTO();
			  
			  CustLessonSeriesDTO custLessonSeriesDTO = null;
			  
			  List<CustLessonSeriesDTO>  custLessonSeries = new ArrayList<CustLessonSeriesDTO>();
			  
			  String custIDs = lessonSeriesServiceInputDTO.getCustId() , lastNames = lessonSeriesServiceInputDTO.getLastName(),
					  phoneNumbers = lessonSeriesServiceInputDTO.getPhone();
			 
			  String custId = "", lastName = "", phoneNumber = ""; 
			  
			 try
			 {
					  Criterion<Appointment,LessonSeries> lessonDetailsCriterion =  null;
					  Criterion<Customer, List<CustomerInformationDTO>> customerDetailsCriterion = null;
					  
					  List<String> custIDList = Arrays.asList(custIDs.split("_"));
					  List<String> phoneIDList = Arrays.asList(lastNames.split("_"));
					  List<String> lastNameList = Arrays.asList(phoneNumbers.split("_"));
					  
					  List<CustomerInformationDTO>  customerList = null , customerDetailsList = null;
					  
					  List<Long> customerIDList = null;
					  
					  List<String> externalIDList = new ArrayList<String>();
					  
					  List<LessonSeries> lessonSeriesList =  null;
					  
					  String email = lessonSeriesServiceInputDTO.getEmail();
					  
					//GSSP-208 changes
					  ServiceLogger serviceLogger = new ServiceLogger();
					   String successString;
					
					serviceLogger.setServiceName("ValidateAndGetLessonSeries");
					  
					
					 
					  //Iterating over the memberID/badgeNumber passed for the Request
					  for(int i=0 ; i < custIDList.size() ; i++)
					  {
						  
						  custId = custIDList.get(i);
						  lastName = phoneIDList.get(i);
						  phoneNumber = lastNameList.get(i);
						  
						  //Validate memberID/badge number against last Name and phone Number
						  customerList = validateBadgeNumberOrExternalID(custId,lastName,phoneNumber,email);
						  
						  
						  //If Valid Customer
						if(null != customerList && customerList.size() > 0)
						{
								customerIDList = new ArrayList<Long>(); 
											
								//Get the list of customer ID and member ID associated with the customer ID
								for(CustomerInformationDTO customer : customerList)
								  {
									  customerIDList.add(new Long(customer.getCustomerId()));
									  externalIDList.add(customer.getExternalId());
									  
								  }
								
								if(customerIDList.size() > 0 )
								{	
									
								  
									    long[] customerArray = customerIDList.stream().mapToLong(j->j).toArray();
										  
										Long[] customerID  =  ArrayUtils.toObject (customerArray);
										
										
										
										//Get details of All Customers
										 customerDetailsCriterion = CustomerCriterion
													.getCustomerDetails(customerID);
										  
										  customerDetailsList = customerDAO.get(customerDetailsCriterion, DAOHelper.FETCH_APPOINTMENT_SERIES);
										  
									
									  //Get the customer information and appointment series information for valid customers
									  for(CustomerInformationDTO customerDetails : customerDetailsList)
									  {
										  
										  lessonDetailsCriterion = AppointmentCriterion
													.findAppointmentSeriesByCustomerId(new Long(customerDetails.getCustomerId()));
										  
										  lessonSeriesList = appointmentDAO.search(lessonDetailsCriterion, DAOHelper.FETCH_APPOINTMENT_SERIES);
										  
										  if(lessonSeriesList.size() == 0)
											  lessonSeriesList = null;
										  
										  
										  custLessonSeriesDTO = new CustLessonSeriesDTO(
												  custId , customerDetails.getExternalId() ,customerDetails.getBadgeNumber(),
													  customerDetails.getFirstName(),customerDetails.getLastName(),"Valid",lessonSeriesList);								  
									
										
										  custLessonSeries.add(custLessonSeriesDTO);	
										  
		
									  }	  
								  
								}  
						
						}
						else
						{
							 custLessonSeriesDTO = new CustLessonSeriesDTO(
									 custId, null ,null,
									  null,null,"Invalid",null);
									  
							  custLessonSeries.add(custLessonSeriesDTO);	
						}
						  
		
					  }
					  
					  			  
					  custLessonSeriesResponseDTO.setCustLessonSeries(custLessonSeries);
					  
					  customerEmailService.checkEmail(externalIDList,lessonSeriesServiceInputDTO.getEmail());
					  //GSSP-208 CHANGES
					  
					  List<LessonSeries> lessonSeriesDelimitedList = null;
					  
					  
					  StringBuffer appointmentSeriesDeLimited = new StringBuffer();
					  
					  String appointmentSeriesLog  ="";
					
					  for(CustLessonSeriesDTO custLessonSeriesDTOs : custLessonSeries)
					  {
							  if(null != custLessonSeriesDTOs.getSeries() && custLessonSeriesDTOs.getSeries().size() > 0)
							  {
								  lessonSeriesDelimitedList = custLessonSeriesDTOs.getSeries();
								  for(LessonSeries lessonSeries : lessonSeriesDelimitedList)
								  {
									  appointmentSeriesDeLimited.append(lessonSeries.getSeriesId());
									  appointmentSeriesDeLimited.append("_");
								  }
								  
								 
							  }			  
					  }
					  
					  if(appointmentSeriesDeLimited.length() > 0)
					  {
						  appointmentSeriesLog =  appointmentSeriesDeLimited.substring(0,appointmentSeriesDeLimited.length()-1);
						  
					  }
					  
					  serviceLogger.setAppointmentSeriesId(appointmentSeriesLog);
					  serviceLogger.setCustomerMemberId(custIDs);
					  Criterion<Appointment,String>  appointmentCriterion =
							    AppointmentCriterion.logService(serviceLogger);
					 
					  successString = appointmentDAO.get(appointmentCriterion);
			 }
			 catch(Exception e)
			 {
				 LOG.error("Error during getLessons Web Service " ,e);
				 custLessonSeriesResponseDTO.setCustLessonSeries(null);
				 custLessonSeriesResponseDTO.setErrorKey(WebServiceConstants.lessonServiceError);
				 custLessonSeriesResponseDTO.setErrorMessage(WebServiceConstants.lessonServiceErrorDescription);
				 
				 
			 }
	
			  return custLessonSeriesResponseDTO;
	 }
	
		  
		  
				  
		  private  List<CustomerInformationDTO> validateBadgeNumberOrExternalID(String badgeNumber, String lastName, String phoneNumber,String email)
		  {
			  
			  List<CustomerInformationDTO>  customerList = null;
			  
			  
			  if(null != email)
			  {	  
				  Criterion<Customer, List<CustomerInformationDTO>> criterion = CustomerCriterion
							.validateWithBadgeNumber(badgeNumber, lastName,phoneNumber);
				  
				  customerList = customerDAO.get(criterion, DAOHelper.FETCH_APPOINTMENT_SERIES);	
				  
			  }	  
			
			  if(null != customerList && customerList.size() > 0)
			  {
				  
				  return customerList;
			  }
			  else
			  {
				  Criterion<Customer, List<CustomerInformationDTO>> externalIDcriterion = CustomerCriterion
							.validateWithExternalID(badgeNumber, lastName,phoneNumber);
				  
				  customerList = customerDAO.get(externalIDcriterion, DAOHelper.FETCH_APPOINTMENT_SERIES);
				
			  }
			
			  
				return customerList;
		  }
		 }
