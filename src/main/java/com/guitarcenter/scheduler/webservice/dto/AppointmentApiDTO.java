package com.guitarcenter.scheduler.webservice.dto;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.AppointmentSeries;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.Service;
import com.guitarcenter.scheduler.model.enums.IsRecurring;

public class AppointmentApiDTO {

    private String appointmentId;
    private String startTime;
    private String endTime;
    private String instructorId;
    private String startDate;
    private String endDate;
    private String custExternalID;
    private String day;
    private String instructorName;
    private String curCustName;
    private String curCustExternalID;
    private String activityName;
    private String activityId;
    private String serviceId;
    public String getAppointmentId() {
        return appointmentId;
    }
    public void setAppointmentId(String appointmentId) {
        this.appointmentId = appointmentId;
    }
    public String getStartTime() {
        return startTime;
    }
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }
    public String getEndTime() {
        return endTime;
    }
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
    public String getInstructorId() {
        return instructorId;
    }
    public void setInstructorId(String instructorId) {
        this.instructorId = instructorId;
    }
    public String getStartDate() {
        return startDate;
    }
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }
    public String getEndDate() {
        return endDate;
    }
    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }
    public String getCustExternalID() {
        return custExternalID;
    }
    public void setCustExternalID(String custExternalID) {
        this.custExternalID = custExternalID;
    }
    public String getDay() {
        return day;
    }
    public void setDay(String day) {
        this.day = day;
    }
    public String getInstructorName() {
        return instructorName;
    }
    public void setInstructorName(String instructorName) {
        this.instructorName = instructorName;
    }
    public String getCurCustName() {
        return curCustName;
    }
    public void setCurCustName(String curCustName) {
        this.curCustName = curCustName;
    }
    public String getCurCustExternalID() {
        return curCustExternalID;
    }
    public void setCurCustExternalID(String curCustExternalID) {
        this.curCustExternalID = curCustExternalID;
    }
    public String getActivityName() {
        return activityName;
    }
    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }
    public String getActivityId() {
        return activityId;
    }
    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }
    public String getServiceId() {
        return serviceId;
    }
    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }
    @Override
    public String toString() {
        return "AppointmentApiDTO [appointmentId=" + appointmentId + ", startTime=" + startTime + ", endTime=" + endTime
                + ", instructorId=" + instructorId + ", startDate=" + startDate + ", endDate=" + endDate
                + ", custExternalID=" + custExternalID + ", day=" + day + ", instructorName=" + instructorName
                + ", curCustName=" + curCustName + ", curCustExternalID=" + curCustExternalID + ", activityName="
                + activityName + ", activityId=" + activityId + ", serviceId=" + serviceId + "]";
    }



}
