package com.guitarcenter.scheduler.webservice.dto;



public class LocationProfileInfoDTO {
	
	  private long profileID;
	  private String storeName;
	  private String phoneNumber;
	  private String seriesStartDate;	   
	  private long appointmentSeriesID;
	  private long locationID;
	  private long activityID;
	  private String appointmentDate;
	  private long duration;
	  private Long instructorId;
	  private String startTime;
	  private String endTime;
	  
	  private String seriesEndDate;
	  
	  private String isRecurring;
	  
	  
	  public String getIsRecurring() {
		return isRecurring;
	}



	public void setIsRecurring(String isRecurring) {
		this.isRecurring = isRecurring;
	}



	public String getSeriesEndDate() {
		return seriesEndDate;
	}



	public void setSeriesEndDate(String seriesEndDate) {
		this.seriesEndDate = seriesEndDate;
	}



	public String getStartTime() {
		return startTime;
	}



	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}



	public String getEndTime() {
		return endTime;
	}



	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}



	public String getAppointmentDate() {
		return appointmentDate;
	}



	public void setAppointmentDate(String appointmentDate) {
		this.appointmentDate = appointmentDate;
	}



	public long getDuration() {
		return duration;
	}



	public void setDuration(long duration) {
		this.duration = duration;
	}

	public LocationProfileInfoDTO(){
	}


	public LocationProfileInfoDTO(long profileID, String storeName,String phoneNumber, String seriesStartDate,
			  long appointmentSeriesID,long locationID,long activityID, String appointmentDate,long duration,String startTime, String endTime,String seriesEndDate,
			  String isRecurring)
		{
	        this.profileID = profileID;
	       
	        this.storeName = storeName;
			this.phoneNumber = phoneNumber;            
			this.seriesStartDate = seriesStartDate ;    
	        this.appointmentSeriesID = appointmentSeriesID;
	        this.locationID = locationID;
			this.activityID = activityID;
			this.appointmentDate = appointmentDate;
			this.duration = duration/3600 ; 
			this.startTime = startTime ; 
			this.endTime = endTime; 
			this.seriesEndDate = seriesEndDate;
			this.isRecurring = isRecurring;
	        
	    }
	  
	  
	  
	  public LocationProfileInfoDTO(long profileID, String storeName,String phoneNumber, String seriesStartDate)
		{
	        this.profileID = profileID;	       
	        this.storeName = storeName;
			this.phoneNumber = phoneNumber;            
			this.seriesStartDate = seriesStartDate ;    
	      
	        
	    }
	  
	  
	public long getProfileID() {
		return profileID;
	}
	public void setProfileID(long profileID) {
		this.profileID = profileID;
	}
	public String getStoreName() {
		return storeName;
	}
	public void setStoreName(String storeName) {
		this.storeName = storeName;
	}
	public String getPhoneNumber() {
		return phoneNumber;
	}
	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}
	public String getSeriesStartDate() {
		return seriesStartDate;
	}
	public void setSeriesStartDate(String seriesStartDate) {
		this.seriesStartDate = seriesStartDate;
	}
	public long getAppointmentSeriesID() {
		return appointmentSeriesID;
	}
	public void setAppointmentSeriesID(long appointmentSeriesID) {
		this.appointmentSeriesID = appointmentSeriesID;
	}
	public long getLocationID() {
		return locationID;
	}
	public void setLocationID(long locationID) {
		this.locationID = locationID;
	}
	   
	   
	public long getActivityID() {
		return activityID;
	}
	public void setActivityID(long locationID) {
		this.activityID = activityID;
	}



	public Long getInstructorId() {
		return instructorId;
	}



	public void setInstructorId(Long instructorId) {
		this.instructorId = instructorId;
	}



	@Override
	public String toString() {
		return "LocationProfileInfoDTO [profileID=" + profileID + ", storeName=" + storeName + ", phoneNumber="
				+ phoneNumber + ", seriesStartDate=" + seriesStartDate + ", appointmentSeriesID=" + appointmentSeriesID
				+ ", locationID=" + locationID + ", activityID=" + activityID + ", appointmentDate=" + appointmentDate
				+ ", duration=" + duration + ", startTime=" + startTime + ", endTime=" + endTime + ", seriesEndDate="
				+ seriesEndDate + ", isRecurring=" + isRecurring + "]";
	}
	
	

}
