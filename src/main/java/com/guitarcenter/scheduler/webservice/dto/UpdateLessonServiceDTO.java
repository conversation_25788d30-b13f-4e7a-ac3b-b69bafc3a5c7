package com.guitarcenter.scheduler.webservice.dto;

public class UpdateLessonServiceDTO {
	
	private String aptId;
	private String startTime;
	private String endTime;
	private Long insId;
	private String date;
	private String custMemberID;
	private String day;
	private String instructorName;
	private String curCustName;
	private String curCustMemberID;
	
	
	
	public String getInstructorName() {
		return instructorName;
	}

	public void setInstructorName(String instructorName) {
		this.instructorName = instructorName;
	}

	public String getDay() {
		return day;
	}

	public void setDay(String day) {
		this.day = day;
	}

	public String getCustMemberID() {
		return custMemberID;
	}

	public void setCustMemberID(String custMemberID) {
		this.custMemberID = custMemberID;
	}

	public String getAptId() {
		return aptId;
	}

	public void setAptId(String aptId) {
		this.aptId = aptId;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public Long getInsId() {
		return insId;
	}

	public void setInsId(Long insId) {
		this.insId = insId;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public String getCurCustName() {
		return curCustName;
	}

	public void setCurCustName(String curCustName) {
		this.curCustName = curCustName;
	}

	public String getCurCustMemberID() {
		return curCustMemberID;
	}

	public void setCurCustMemberID(String curCustMemberID) {
		this.curCustMemberID = curCustMemberID;
	}


}
