package com.guitarcenter.scheduler.webservice.dto;

import java.util.List;

import com.guitarcenter.scheduler.dto.StudioHoursDTO;

public class LessonServiceDTO {
	
	

    private String responseStartDate;
    private String responseEndDate;

    private String name;
    private String custMemberId;
    private String seriesStartDate;
    private String store;
    private String phoneNumber;
    
    
    private List<StudioHoursDTO> storeHours;
    
    private List<Lesson>		lessons;	
    
    //Changes made for GSSP-250
    
    private List<CancelReasonDTO> cancelReasons;
    
    
    
   	
	public List<CancelReasonDTO> getCancelReasons() {
		return cancelReasons;
	}


	public void setCancelReasons(List<CancelReasonDTO> cancelReasons) {
		this.cancelReasons = cancelReasons;
	}


	public String getResponseStartDate() {
		return responseStartDate;
	}


	public void setResponseStartDate(String responseStartDate) {
		this.responseStartDate = responseStartDate;
	}


	public String getResponseEndDate() {
		return responseEndDate;
	}


	public void setResponseEndDate(String responseEndDate) {
		this.responseEndDate = responseEndDate;
	}


	public String getName() {
		return name;
	}


	public void setName(String name) {
		this.name = name;
	}

	
	public String getCustMemberId() {
		return custMemberId;
	}


	public void setCustMemberId(String custMemberId) {
		this.custMemberId = custMemberId;
	}


	public void setLessons(List<Lesson> lessons) {
		this.lessons = lessons;
	}


	public String getSeriesStartDate() {
		return seriesStartDate;
	}


	public void setSeriesStartDate(String seriesStartDate) {
		this.seriesStartDate = seriesStartDate;
	}


	public String getStore() {
		return store;
	}


	public void setStore(String store) {
		this.store = store;
	}


	public String getPhoneNumber() {
		return phoneNumber;
	}


	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}
	
	public List<StudioHoursDTO> getStoreHours() {
		return storeHours;
	}


	public void setStoreHours(List<StudioHoursDTO> storeHours) {
		this.storeHours = storeHours;
	}


	public List<Lesson> getLessons() {
		return lessons;
	}


	public void setLesson(List<Lesson> lessons) {
		this.lessons = lessons;
	}


	
	
}
