package com.guitarcenter.scheduler.webservice.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
public class AppointmentStatusServiceResponseDTO {

	    private String customerEmail;
	    private List<AppointmentStatusDTO> apptStatusList;
		private String status;
		
		public String getCustomerEmail() {
			return customerEmail;
		}
		public void setCustomerEmail(String customerEmail) {
			this.customerEmail = customerEmail;
		}
		public List<AppointmentStatusDTO> getApptStatusList() {
			return apptStatusList;
		}
		public void setApptStatusList(List<AppointmentStatusDTO> apptStatusList) {
			this.apptStatusList = apptStatusList;
		}
		public String getStatus() {
			return status;
		}
		public void setStatus(String status) {
			this.status = status;
		}	    
		
}
