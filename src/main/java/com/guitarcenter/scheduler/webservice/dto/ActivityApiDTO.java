package com.guitarcenter.scheduler.webservice.dto;
 


public class ActivityApiDTO implements Comparable<ActivityApiDTO>{
	private Long activityId;
	private String activityName;
	private String serviceName;
	private Long serviceId;
	private Boolean enable;
	private String maximunAttendees;
	private String minimunAttendees;
	private String minimumDuration;
	private String maxmumDuration;
	private String requiresInstructor;
	private Long version;
	
	/**
	 * Field used to identify if the activity is selected in filter list
	 */
	private String isSelectedActivity;

	/**
	
	  * ActivityApiDTO. 
	  * <p>Title: </p>
	  * <p>Description: </p>
	  */
	public ActivityApiDTO() {
		super();
	}

	/**
	
	  * ActivityApiDTO. 
	  * <p>Title: </p>
	  * <p>Description: </p>
	  * @param activityId
	  * @param activityName
	  */
	public ActivityApiDTO(Long activityId, String activityName) {
		super();
		this.activityId = activityId;
		this.activityName = activityName;
	}

	public Long getActivityId() {
		return activityId;
	}

	public void setActivityId(Long activityId) {
		this.activityId = activityId;
	}

	public String getActivityName() {
		return activityName;
	}

	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}

	public String getServiceName() {
		return serviceName;
	}

	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}

	public Long getServiceId() {
		return serviceId;
	}

	public void setServiceId(Long serviceId) {
		this.serviceId = serviceId;
	}

	public Boolean getEnable() {
		return enable;
	}

	public void setEnable(Boolean enable) {
		this.enable = enable;
	}

	public String getMaximunAttendees() {
		return maximunAttendees;
	}

	public void setMaximunAttendees(String maximunAttendees) {
		this.maximunAttendees = maximunAttendees;
	}

	public String getMinimunAttendees() {
		return minimunAttendees;
	}

	public void setMinimunAttendees(String minimunAttendees) {
		this.minimunAttendees = minimunAttendees;
	}

	public String getMinimumDuration() {
		return minimumDuration;
	}

	public void setMinimumDuration(String minimumDuration) {
		this.minimumDuration = minimumDuration;
	}

	public String getRequiresInstructor() {
		return requiresInstructor;
	}

	public void setRequiresInstructor(String requiresInstructor) {
		this.requiresInstructor = requiresInstructor;
	}

	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}


	public String getMaxmumDuration() {
		return maxmumDuration;
	}

	public void setMaxmumDuration(String maxmumDuration) {
		this.maxmumDuration = maxmumDuration;
	}


    @Override
    public int compareTo(ActivityApiDTO o) {
        return (int) (activityId - o.getActivityId());
    }

	/**
	 * @return the isSelectedActivity
	 */
	public String getIsSelectedActivity() {
		return isSelectedActivity;
	}

	/**
	 * @param isSelectedActivity the isSelectedActivity to set
	 */
	public void setIsSelectedActivity(String isSelectedActivity) {
		this.isSelectedActivity = isSelectedActivity;
	}

	@Override
	public String toString() {
		return "{activityId=" + activityId + ", activityName=" + activityName + ", serviceName="
				+ serviceName + ", serviceId=" + serviceId + ", enable=" + enable + ", maximunAttendees="
				+ maximunAttendees + ", minimunAttendees=" + minimunAttendees + ", minimumDuration=" + minimumDuration
				+ ", maxmumDuration=" + maxmumDuration + ", requiresInstructor=" + requiresInstructor + ", version="
				+ version + ", isSelectedActivity=" + isSelectedActivity + "}";
	}
    
	
}
