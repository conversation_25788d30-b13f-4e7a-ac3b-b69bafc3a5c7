package com.guitarcenter.scheduler.webservice.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
public class CustomerAppointmentDetailsResultDTO {
	
	
	
	private Long appointmentId;
    private String location;
    private String instructorExternalId;
    private String instructorName;
    private String dateOfAppointment;
    private String timeFrame;
    private String duration;
    private String activityName;
    private String customerExternalId;
    private String customerName;
    private String customerEmail;
    private String customerPhone;
	private String instructorInternalId;
	private String activityId;
	private String serviceId;

    
    
	public Long getAppointmentId() {
		return appointmentId;
	}
	public void setAppointmentId(Long appointmentId) {
		this.appointmentId = appointmentId;
	}
	public String getLocation() {
		return location;
	}
	public void setLocation(String location) {
		this.location = location;
	}
	public String getInstructorExternalId() {
		return instructorExternalId;
	}
	public void setInstructorExternalId(String instructorExternalId) {
		this.instructorExternalId = instructorExternalId;
	}
	public String getInstructorName() {
		return instructorName;
	}
	public void setInstructorName(String instructorName) {
		this.instructorName = instructorName;
	}
	public String getDateOfAppointment() {
		return dateOfAppointment;
	}
	public void setDateOfAppointment(String dateOfAppointment) {
		this.dateOfAppointment = dateOfAppointment;
	}
	public String getTimeFrame() {
		return timeFrame;
	}
	public void setTimeFrame(String timeFrame) {
		this.timeFrame = timeFrame;
	}
	public String getDuration() {
		return duration;
	}
	public void setDuration(String duration) {
		this.duration = duration;
	}
	public String getActivityName() {
		return activityName;
	}
	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}
	public String getCustomerExternalId() {
		return customerExternalId;
	}
	public void setCustomerExternalId(String customerExternalId) {
		this.customerExternalId = customerExternalId;
	}
	public String getCustomerName() {
		return customerName;
	}
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
	public String getCustomerEmail() {
		return customerEmail;
	}
	public void setCustomerEmail(String customerEmail) {
		this.customerEmail = customerEmail;
	}
	public String getCustomerPhone() {
		return customerPhone;
	}
	public void setCustomerPhone(String customerPhone) {
		this.customerPhone = customerPhone;
	}
	public String getInstructorInternalId() {
		return instructorInternalId;
	}
	public void setInstructorInternalId(String instructorInternalId) {
		this.instructorInternalId = instructorInternalId;
	}
	public String getActivityId() {
		return activityId;
	}
	public void setActivityId(String activityId) {
		this.activityId = activityId;
	}
	public String getServiceId() {
		return serviceId;
	}
	public void setServiceId(String serviceId) {
		this.serviceId = serviceId;
	}
	public CustomerAppointmentDetailsResultDTO(Long appointmentId, String location, String instructorExternalId,
			String instructorName, String dateOfAppointment, String timeFrame, String duration, String activityName,
			String customerExternalId, String customerName, String customerEmail, String customerPhone,
			String instructorInternalId, String activityId, String serviceId) {
		super();
		this.appointmentId = appointmentId;
		this.location = location;
		this.instructorExternalId = instructorExternalId;
		this.instructorName = instructorName;
		this.dateOfAppointment = dateOfAppointment;
		this.timeFrame = timeFrame;
		this.duration = duration;
		this.activityName = activityName;
		this.customerExternalId = customerExternalId;
		this.customerName = customerName;
		this.customerEmail = customerEmail;
		this.customerPhone = customerPhone;
		this.instructorInternalId = instructorInternalId;
		this.activityId = activityId;
		this.serviceId = serviceId;
	}

	
	
	


	//toString method
	@Override
	public String toString() {
		return "CustomerAppointmentDetailsResultDTO [appointmentId=" + appointmentId + ", location=" + location
				+ ", instructorExternalId=" + instructorExternalId + ", instructorName=" + instructorName
				+ ", dateOfAppointment=" + dateOfAppointment + ", timeFrame=" + timeFrame + ", duration=" + duration
				+ ", activityName=" + activityName + ", customerExternalId=" + customerExternalId + ", customerName="
				+ customerName + ", customerEmail=" + customerEmail + ", customerPhone=" + customerPhone
				+ ", instructorInternalId=" + instructorInternalId + ", activityId=" + activityId + ", serviceId="
				+ serviceId + "]";
	}


	public CustomerAppointmentDetailsResultDTO() {
		super();
	}
	
	
    
    
    
 

}
