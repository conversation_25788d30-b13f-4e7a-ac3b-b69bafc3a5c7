package com.guitarcenter.scheduler.webservice.dto;

import java.util.List;

import com.guitarcenter.scheduler.dto.StudioHoursDTO;

public class InstructorServiceResponseDTO {


    private String name;
    private String custMemberID;
    private String seriesStartDate;
    private String seriesEndDate;
    private String seriesID;
    
    public String getSeriesID() {
		return seriesID;
	}

	public void setSeriesID(String seriesID) {
		this.seriesID = seriesID;
	}

	public String getSeriesEndDate() {
		return seriesEndDate;
	}

	public void setSeriesEndDate(String seriesEndDate) {
		this.seriesEndDate = seriesEndDate;
	}

	private String store;
    private String phoneNumber;
    

    
    private CurrentLesson		currentLesson;	
    
    public String getCustMemberID() {
		return custMemberID;
	}

	public void setCustMemberID(String custMemberID) {
		this.custMemberID = custMemberID;
	}

	private List<Lessons>   lessons;

	

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	

	public String getSeriesStartDate() {
		return seriesStartDate;
	}

	public void setSeriesStartDate(String seriesStartDate) {
		this.seriesStartDate = seriesStartDate;
	}

	public String getStore() {
		return store;
	}

	public void setStore(String store) {
		this.store = store;
	}

	public String getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public CurrentLesson getCurrentLesson() {
		return currentLesson;
	}

	public void setCurrentLesson(CurrentLesson currentLesson) {
		this.currentLesson = currentLesson;
	}

	public List<Lessons> getLessons() {
		return lessons;
	}

	public void setLessons(List<Lessons> lessons) {
		this.lessons = lessons;
	}
    
    
}
