package com.guitarcenter.scheduler.webservice.dto;

import org.joda.time.DateTime;
import org.joda.time.LocalTime;


public class InstructorAvailableHoursDTO implements Comparable<InstructorAvailableHoursDTO>{
	
	private LocalTime appointmentStartTime;
	
	private LocalTime appointmentEndTime;
	
	private DateTime appointmentStartDate;
	
	private DateTime appointmentEndDate;
	
	private Long actvityID;
	
	private String type;
	
	
	public InstructorAvailableHoursDTO(LocalTime appointmentStartTime, LocalTime appointmentEndTime)
	{
		
        this.appointmentStartTime = appointmentStartTime;
        this.appointmentEndTime = appointmentEndTime;
          
        
    }
	
	
	public InstructorAvailableHoursDTO(LocalTime appointmentStartTime, LocalTime appointmentEndTime,DateTime appointmentStartDate, 
			DateTime appointmentEndDate)
	{
		
        this.appointmentStartTime = appointmentStartTime;
        this.appointmentEndTime = appointmentEndTime;
        this.setAppointmentStartDate(appointmentStartDate);
        this.setAppointmentEndDate(appointmentEndDate);
          
        
    }

	public LocalTime getAppointmentStartTime() {
		return appointmentStartTime;
	}

	public void setAppointmentStartTime(LocalTime appointmentStartTime) {
		this.appointmentStartTime = appointmentStartTime;
	}

	public LocalTime getAppointmentEndTime() {
		return appointmentEndTime;
	}

	public void setAppointmentEndTime(LocalTime appointmentEndTime) {
		this.appointmentEndTime = appointmentEndTime;
	}

	
	@Override
	public int compareTo(InstructorAvailableHoursDTO o) {
		if ((o.getAppointmentStartTime()).
        		isBefore(this.appointmentStartTime))
        {
            return 1;
        }else{
            return -1;
        }
	}


	public DateTime getAppointmentStartDate() {
		return appointmentStartDate;
	}


	public void setAppointmentStartDate(DateTime appointmentStartDate) {
		this.appointmentStartDate = appointmentStartDate;
	}


	public DateTime getAppointmentEndDate() {
		return appointmentEndDate;
	}


	public void setAppointmentEndDate(DateTime appointmentEndDate) {
		this.appointmentEndDate = appointmentEndDate;
	}


	public Long getActvityID() {
		return actvityID;
	}


	public void setActvityID(Long actvityID) {
		this.actvityID = actvityID;
	}


	public String getType() {
		return type;
	}


	public void setType(String type) {
		this.type = type;
	}

 

}
