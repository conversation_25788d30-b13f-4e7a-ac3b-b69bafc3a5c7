package com.guitarcenter.scheduler.webservice.dto;

public class LessonCancelServiceDTO {
	
	private String custMemberID;
	private String aptId;
	private String date;
	private String startTime;
	private String endTime;
	private String day;
	private String curCustName;
	private String curCustMemberID;
	
	
	//Changes made for GSSP-270
	private String  cancelReasonCode;

	
	public String getCancelReasonCode() {
		return cancelReasonCode;
	}
	public void setCancelReasonCode(String cancelReasonCode) {
		this.cancelReasonCode = cancelReasonCode;
	}
	
	//End of Changes made for GSSP-250
	
	
	public String getCurCustName() {
		return curCustName;
	}
	public void setCurCustName(String curCustName) {
		this.curCustName = curCustName;
	}
	public String getCurCustMemberID() {
		return curCustMemberID;
	}
	public void setCurCustMemberID(String curCustMemberID) {
		this.curCustMemberID = curCustMemberID;
	}
	public String getCustMemberID() {
		return custMemberID;
	}
	public void setCustMemberID(String custMemberID) {
		this.custMemberID = custMemberID;
	}
	public String getAptId() {
		return aptId;
	}
	public void setAptId(String aptId) {
		this.aptId = aptId;
	}
	public String getDate() {
		return date;
	}
	public void setDate(String date) {
		this.date = date;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getDay() {
		return day;
	}
	public void setDay(String day) {
		this.day = day;
	}
		
}
