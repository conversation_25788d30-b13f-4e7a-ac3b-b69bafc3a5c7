package com.guitarcenter.scheduler.webservice.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

public class InstructorACTServiceResponseDTO {


	
	
	private String instruments;
   
   
    
    @JsonIgnore
    private List<Lessons>   lessons;
    
	
	public String getInstruments() {
		return instruments;
	}
	public void setInstruments(String instruments) {
		this.instruments = instruments;
	}
		

	//@JsonProperty("activity")
	@JsonIgnoreProperties("lessons")
	public List<Lessons> getLessons() {
		return lessons;
	}
	public void setLessons(List<Lessons> lessons) {
		this.lessons = lessons;
	}
	@Override
	public String toString() {
		return "InstructorAVLServiceResponseDTO [   instruments=" + instruments + ",  lessons="
				+ lessons + "]";
	}
	
	
	
     
 
    
}
