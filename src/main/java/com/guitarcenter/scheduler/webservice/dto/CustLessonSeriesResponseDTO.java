package com.guitarcenter.scheduler.webservice.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
public class CustLessonSeriesResponseDTO {
	
	private List<CustLessonSeriesDTO>  custLessonSeries;
	
	private String errorKey;
	
	private String errorMessage;
	
	 private String  seriesId;
	

	public List<CustLessonSeriesDTO> getCustLessonSeries() {
		return custLessonSeries;
	}

	public String getSeries() {
		return seriesId;
	}

	public void setSeries(String seriesId) {
		this.seriesId = seriesId;
	}

	public void setCustLessonSeries(List<CustLessonSeriesDTO> custLessonSeries) {
		this.custLessonSeries = custLessonSeries;
	}

	public String getErrorKey() {
		return errorKey;
	}

	public void setErrorKey(String errorKey) {
		this.errorKey = errorKey;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}
	
	
	
	
	

}
