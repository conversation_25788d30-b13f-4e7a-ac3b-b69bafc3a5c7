package com.guitarcenter.scheduler.webservice.dto;

import org.apache.commons.lang3.StringUtils;


public class Lesson {
	
	
	private Long				id;	
	private String				name;	
	private String			    type;
	private String				startTime;	
	private String 				endTime;
	private String				isEditable;
	private String				date;
	

	public Lesson(Long id, String type,String startTime, String firstName,String lastName,String endTime,String date,
			String isEditable)
	{
        this.id = id;
        this.name  =StringUtils.isBlank(firstName)?"":firstName + " " + (StringUtils.isBlank(lastName) ? "":lastName);
        this.type = type;
		this.startTime = startTime;            
		this.endTime = endTime ;    
        this.isEditable = isEditable;
        this.date = date;
        
    }
	
	public String getDate() {
		return date;
	}
	public void setDate(String date) {
		this.date = date;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	
	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getIsEditable() {
		return isEditable;
	}
	public void setIsEditable(String isEditable) {
		this.isEditable = isEditable;
	}
	
	
	

}
