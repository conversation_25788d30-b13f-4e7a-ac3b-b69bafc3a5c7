package com.guitarcenter.scheduler.webservice.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonRawValue;

public class InstructorAVLServiceResponseDTO {


	private String lastupdateddate;
	private String storeNumber;
	private String name;
    private String firstname;
	private String lastname;
	private String id;
	private String email;
	private String phoneNumber;
	//---Added Instructor Mode Changes Starts
	private String InstructorMode;
	@JsonRawValue
	private String instruments;
	@JsonRawValue
    private String availability;
    private String storeaddress1;
    private String storeaddress2;
    private String storecity;
    private String storestate;
    private String storezip;
    
    private String instructorStatus;
    
    private String instructorId;
    private String activityFull;
    
    private String storeDistrict;
 	private String storeRegion;
 	private String storeLocation;
    
    @JsonIgnore
    private List<Lessons>   lessons;
    
	public String getLastupdateddate() {
		return lastupdateddate;
	}
	public void setLastupdateddate(String lastupdateddate) {
		this.lastupdateddate = lastupdateddate;
	}
	public String getStoreNumber() {
		return storeNumber;
	}
	public void setStoreNumber(String storeNumber) {
		this.storeNumber = storeNumber;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getFirstname() {
		return firstname;
	}
	public void setFirstname(String firstname) {
		this.firstname = firstname;
	}
	public String getLastname() {
		return lastname;
	}
	public void setLastname(String lastname) {
		this.lastname = lastname;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getPhoneNumber() {
		return phoneNumber;
	}
	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}
	
	@JsonRawValue
	public String getInstructorMode() {
		return InstructorMode;
	}
	public void setInstructorMode(String instructorMode) {
		InstructorMode = instructorMode;
	}
	@JsonRawValue
	public String getInstruments() {
		return instruments;
	}
	public void setInstruments(String instruments) {
		this.instruments = instruments;
	}
	@JsonRawValue
	public String getAvailability() {
		return availability;
	}
	public void setAvailability(String availability) {
		this.availability = availability;
	}
	
	public String getStoreaddress1() {
		return storeaddress1;
	}
	public void setStoreaddress1(String storeaddress1) {
		this.storeaddress1 = storeaddress1;
	}
	public String getStoreaddress2() {
		return storeaddress2;
	}
	public void setStoreaddress2(String storeaddress2) {
		this.storeaddress2 = storeaddress2;
	}
	public String getStorecity() {
		return storecity;
	}
	public void setStorecity(String storecity) {
		this.storecity = storecity;
	}
	public String getStorestate() {
		return storestate;
	}
	public void setStorestate(String storestate) {
		this.storestate = storestate;
	}
	public String getStorezip() {
		return storezip;
	}
	public void setStorezip(String storezip) {
		this.storezip = storezip;
	}
	
	public String getInstructorStatus() {
		return instructorStatus;
	}
	public void setInstructorStatus(String instructorStatus) {
		this.instructorStatus = instructorStatus;
	}
	//@JsonProperty("availability")
	@JsonIgnoreProperties("lessons")
	public List<Lessons> getLessons() {
		return lessons;
	}
	public void setLessons(List<Lessons> lessons) {
		this.lessons = lessons;
	}
	
	
	
	
	public String getInstructorId() {
		return instructorId;
	}
	public void setInstructorId(String instructorId) {
		this.instructorId = instructorId;
	}
	
	@JsonRawValue
	public String getActivityFull() {
		return activityFull;
	}
	public void setActivityFull(String activityFull) {
		this.activityFull = activityFull;
	}
	public String getStoreDistrict() {
		return storeDistrict;
	}
	public void setStoreDistrict(String storeDistrict) {
		this.storeDistrict = storeDistrict;
	}
	public String getStoreRegion() {
		return storeRegion;
	}
	public void setStoreRegion(String storeRegion) {
		this.storeRegion = storeRegion;
	}
	public String getStoreLocation() {
		return storeLocation;
	}
	public void setStoreLocation(String storeLocation) {
		this.storeLocation = storeLocation;
	}
	@Override
	public String toString() {
		return "InstructorAVLServiceResponseDTO [lastupdateddate=" + lastupdateddate + ", storeNumber=" + storeNumber
				+ ", name=" + name + ", firstname=" + firstname + ", lastname=" + lastname + ", id=" + id + ", email="
				+ email + ", phoneNumber=" + phoneNumber + ", InstructorMode=" + InstructorMode + ", instruments="
				+ instruments + ", availability=" + availability + ", storeaddress1=" + storeaddress1
				+ ", storeaddress2=" + storeaddress2 + ", storecity=" + storecity + ", storestate=" + storestate
				+ ", storezip=" + storezip + ", instructorStatus=" + instructorStatus + ", instructorId=" + instructorId
				+ ", activityFull=" + activityFull + ", storeDistrict=" + storeDistrict + ", storeRegion=" + storeRegion
				+ ", storeLocation=" + storeLocation + ", lessons=" + lessons + "]";
	}
	
 
    
}
