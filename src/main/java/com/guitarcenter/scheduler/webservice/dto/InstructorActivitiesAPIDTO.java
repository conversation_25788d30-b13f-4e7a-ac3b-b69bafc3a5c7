package com.guitarcenter.scheduler.webservice.dto;

import java.util.HashSet;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonRawValue;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.ServiceMode;

public class InstructorActivitiesAPIDTO {
	
	private String lastupdateddate;
	private String storeNumber;
	private String name;
    private String firstname;
	private String lastname;
	private String id;
	private String email;
	private String phoneNumber;
	@JsonIgnore
	private String instruments;
    private String storeaddress1;
    private String storeaddress2;
    private String storecity;
    private String storestate;
    private String storezip;
    private Set<ActivityDTO>		activities			= new HashSet<ActivityDTO>(0);
	private String		serviceMode;;
    
    
	public String getLastupdateddate() {
		return lastupdateddate;
	}
	public void setLastupdateddate(String lastupdateddate) {
		this.lastupdateddate = lastupdateddate;
	}
	public String getStoreNumber() {
		return storeNumber;
	}
	public void setStoreNumber(String storeNumber) {
		this.storeNumber = storeNumber;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getFirstname() {
		return firstname;
	}
	public void setFirstname(String firstname) {
		this.firstname = firstname;
	}
	public String getLastname() {
		return lastname;
	}
	public void setLastname(String lastname) {
		this.lastname = lastname;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getPhoneNumber() {
		return phoneNumber;
	}
	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}
	public String getInstruments() {
		return instruments;
	}
	public void setInstruments(String instruments) {
		this.instruments = instruments;
	}
	
	public String getStoreaddress1() {
		return storeaddress1;
	}
	public void setStoreaddress1(String storeaddress1) {
		this.storeaddress1 = storeaddress1;
	}
	public String getStoreaddress2() {
		return storeaddress2;
	}
	public void setStoreaddress2(String storeaddress2) {
		this.storeaddress2 = storeaddress2;
	}
	public String getStorecity() {
		return storecity;
	}
	public void setStorecity(String storecity) {
		this.storecity = storecity;
	}
	public String getStorestate() {
		return storestate;
	}
	public void setStorestate(String storestate) {
		this.storestate = storestate;
	}
	public String getStorezip() {
		return storezip;
	}
	public void setStorezip(String storezip) {
		this.storezip = storezip;
	}
	public Set<ActivityDTO> getActivities() {
		return activities;
	}
	public void setActivities(Set<ActivityDTO> activities) {
		this.activities = activities;
	}
	@JsonRawValue
	public String getServiceMode() {
		return serviceMode;
	}
	public void setServiceMode(String serviceMode) {
		this.serviceMode = serviceMode;
	}
 

}
