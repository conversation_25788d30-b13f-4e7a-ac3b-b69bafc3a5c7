package com.guitarcenter.scheduler.webservice.dto;

import java.util.Comparator;

import org.joda.time.LocalTime;
import org.joda.time.format.DateTimeFormat;


public class Times{
	
	
	private Long				insId;	
	private String				insName;
	private String	 			startTime;	
	private String 			endTime;
	
	
	public Times(String startTime, String endTime,String insName, Long insId)
	{
		
        this.startTime = startTime;
        this.endTime = endTime;
        this.insId  = insId;
        this.insName = insName;
          
        
    }
	
	public Long getInsId() {
		return insId;
	}
	public void setInsId(Long insId) {
		this.insId = insId;
	}
	public String getInsName() {
		return insName;
	}
	public void setInsName(String insName) {
		this.insName = insName;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}


	//Added for LES-149 
	   public static Comparator<Times> startTimeasLTComparator 
	    = new Comparator<Times>() {
		   
		   				public int compare(Times stTime1, Times stTime2) {

	                             LocalTime startTime1 = DateTimeFormat.forPattern("hh:mm a").parseLocalTime(stTime1.getStartTime());
	                            LocalTime startTime2 = DateTimeFormat.forPattern("hh:mm a").parseLocalTime(stTime2.getStartTime());

	                              if(startTime1.isBefore(startTime2)){
	                                                            return -1;
	                              }
	                                             else{
	                                            	
	                                            	 if(startTime1.isEqual(startTime2)){
	                                                      
	                                            		 return 0;
	                                            	 }	
	                                            	 
	                                            	 else{
	                                            		 return 1;
	                                            	 }
	                                            		 
	                              }
		   				}
	};

	   
	//Added for LES-149  for Instructor availability
	   public static Comparator<Times> startTimeasLTMComparator 
	    = new Comparator<Times>() {
		   
		   				public int compare(Times stTime1, Times stTime2) {

	                             LocalTime startTime1 = DateTimeFormat.forPattern("HH:mm").parseLocalTime(stTime1.getStartTime());
	                            LocalTime startTime2 = DateTimeFormat.forPattern("HH:mm").parseLocalTime(stTime2.getStartTime());

	                              if(startTime1.isBefore(startTime2)){
	                                                            return -1;
	                              }
	                                             else{
	                                            	
	                                            	 if(startTime1.isEqual(startTime2)){
	                                                      
	                                            		 return 0;
	                                            	 }	
	                                            	 
	                                            	 else{
	                                            		 return 1;
	                                            	 }
	                                            		 
	                              }
		   				}
	};

	   	
	
	
	
}
