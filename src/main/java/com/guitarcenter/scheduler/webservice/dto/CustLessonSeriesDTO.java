package com.guitarcenter.scheduler.webservice.dto;

import java.util.List;

public class CustLessonSeriesDTO {
		
	
	 private String custID ;
	 private String custMemberId ; 
	 private String badgeId;
	 private String custFirstName;	 
	 private String custLastName ;
	 private String seriesId ;
	private String	status;
	
	
	public CustLessonSeriesDTO(String custID, String custMemberId,String badgeId , String custFirstName, String custLastName,String status, List<LessonSeries> series )
	{
		
        this.custID = custID;
        this.custMemberId = custMemberId;
        this.badgeId = badgeId;
        this.custFirstName = custFirstName;
        this.custLastName = custLastName;
        this.status = status;
        this.series = series;
          
        
    }
	 
	 
	 private List<LessonSeries>  series;
	 
	 
	public String getCustID() {
		return custID;
	}
	public void setCustID(String custID) {
		this.custID = custID;
	}
	public String getCustMemberId() {
		return custMemberId;
	}
	public void setCustMemberId(String custMemberId) {
		this.custMemberId = custMemberId;
	}
	public String getBadgeId() {
		return badgeId;
	}
	public void setBadgeId(String badgeId) {
		this.badgeId = badgeId;
	}
	public String getCustFirstName() {
		return custFirstName;
	}
	public void setCustFirstName(String custFirstName) {
		this.custFirstName = custFirstName;
	}
	public String getCustLastName() {
		return custLastName;
	}
	public void setCustLastName(String custLastName) {
		this.custLastName = custLastName;
	}
	
	
	 public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public List<LessonSeries> getSeries() {
			return series;
		}
		public void setSeries(List<LessonSeries> series) {
			this.series = series;
		}
		public String getSeriesId() {
			return seriesId;
		}
		public void setSeriesId(String seriesId) {
			this.seriesId = seriesId;
		}

}
