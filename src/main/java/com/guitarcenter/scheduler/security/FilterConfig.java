package com.guitarcenter.scheduler.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

@Configuration
public class FilterConfig {

    @Bean
    public FilterRegistrationBean<ApiKeyAuthFilter> apiKeyFilter(ApiKeyAuthFilter apiKeyAuthFilter) {
        FilterRegistrationBean<ApiKeyAuthFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(apiKeyAuthFilter);

        // Secure "/lessons-api/*" but exclude "/lessons-api/v1/"
        registrationBean.addUrlPatterns("/lessons-api/*");

        // Exclude "/lessons-api/v1/" by setting a custom filter condition
        registrationBean.setFilter(new ApiKeyAuthFilter() {
            @Override
            public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
                    throws IOException, ServletException {
                HttpServletRequest httpRequest = (HttpServletRequest) request;
                String requestUri = httpRequest.getRequestURI();

                // Skip filtering for "/lessons-api/v1/"
                if (requestUri.startsWith("/lessons-api/v1/")) {
                    chain.doFilter(request, response);
                    return;
                }

                super.doFilter(request, response, chain);
            }
        });

        registrationBean.setOrder(1); // Ensure filter runs early
        return registrationBean;
    }
}


