/*
package com.guitarcenter.scheduler.security;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.service.LocationManagerService;
import com.guitarcenter.scheduler.service.SiteService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.List;

*/
/**
 * Override UsernamePasswordAuthenticationFilter to inject a GCSS token during
 * authentication.
 *
 * <AUTHOR> Emes <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 *//*

public class GCSSAuthenticationFilter extends UsernamePasswordAuthenticationFilter implements AppConstants {

    Logger log = LoggerFactory.getLogger(GCSSAuthenticationFilter.class);

    private final LocationManagerService locationManagerService;
    private final SiteService siteService;
    private String filterProcessUrl;

    public GCSSAuthenticationFilter(String filterProcessUrl, LocationManagerService locationManagerService, SiteService siteService) {
        this.filterProcessUrl = filterProcessUrl;
        this.locationManagerService = locationManagerService;
        this.siteService = siteService;
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response)
            throws AuthenticationException {
        if (log.isDebugEnabled()) {
            log.debug("attempting to authenticate {} in domain {}",
                    request.getParameter(SPRING_SECURITY_FORM_USERNAME_KEY),
                    request.getParameter(DOMAIN_STRING));
        }
        String domain = request.getParameter(DOMAIN_STRING);
        if (StringUtils.isBlank(domain)) {
            throw new BadCredentialsException("A domain is required for GCSS authentication");
        }
        GCSSUsernamePasswordAuthenticationToken authToken =
                new GCSSUsernamePasswordAuthenticationToken(request.getParameter(SPRING_SECURITY_FORM_USERNAME_KEY),
                        request.getParameter(SPRING_SECURITY_FORM_PASSWORD_KEY));
        authToken.setDomain(domain);
        authToken.setSite(getSite(request));
        authToken.setLocation(getLocation(request, authToken.getSite()));
        setDetails(request, authToken);
        Authentication authentication =
                super.getAuthenticationManager().authenticate(authToken);
        if (log.isDebugEnabled()) {
            log.debug("returning authentication instance {}", authentication);
        }
        return authentication;
    }

    @Override
    protected void successfulAuthentication(HttpServletRequest request, HttpServletResponse response,
                                            FilterChain chain, Authentication authResult) throws IOException, ServletException {
        // Update the SecurityContext with the authenticated user
        SecurityContextHolder.getContext().setAuthentication(authResult);

        // Call the parent method to complete the filter chain
        super.successfulAuthentication(request, response, chain, authResult);
    }

    private Site getSite(HttpServletRequest request) {
        String siteIdentifier = "GCS";
        List<Site> sites = siteService.findSitesByExternalId(siteIdentifier);
        if (sites.size() > 1) {
            throw new BadCredentialsException("A single site is required for authentication");
        }
        if (sites.isEmpty()) {
            throw new BadCredentialsException("A site could not be found for authentication");
        }
        return sites.get(0);
    }

    private Location getLocation(HttpServletRequest request, Site site) {
        String store = request.getParameter(STORE_STRING);
        if (StringUtils.isNotBlank(store)) {
            List<Location> locations = locationManagerService.findByExternalId(site.getSiteId(), store);
            if (locations.size() == 1) {
                return locations.get(0);
            }
        }
        return null;
    }

    @Override
    protected boolean requiresAuthentication(HttpServletRequest request, HttpServletResponse response) {
        String uri = request.getRequestURI();
        int pathParamIndex = uri.indexOf(59);
        if (pathParamIndex > 0) {
            uri = uri.substring(0, pathParamIndex);
        }
        return "".equals(request.getContextPath()) ? uri.endsWith(this.filterProcessUrl) : uri.endsWith(request.getContextPath() + this.filterProcessUrl);
    }
}
*/
