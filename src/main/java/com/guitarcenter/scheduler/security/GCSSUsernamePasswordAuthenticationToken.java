/*
package com.guitarcenter.scheduler.security;

import java.util.Collection;

import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.Site;

*/
/**
 * Extends UsernamePasswordAuthenticationToken to add an additional parameter
 * for authentication: domain
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 *//*


public class GCSSUsernamePasswordAuthenticationToken extends
        UsernamePasswordAuthenticationToken
{
    private static final long serialVersionUID = -7634345152847672203L;
    
    */
/**
     * The domain of authentication
     *//*

    private String domain;
    
    */
/**
     * The location; may be null
     *//*

    private Location location;
    
    */
/**
     * The site
     *//*

    private Site site;
    
    */
/**
     * Construct an instance based on the supplied username and password
     * 
     * @param username Username being authenticated
     * @param password Password to use for authentication
     *//*

    public GCSSUsernamePasswordAuthenticationToken(Object username,
                                                   Object password) {
        super(username, password);
    }
    
    */
/**
     * Construct an instance based on the supplied username, password and
     * a collection of authorities. Usually called post-authentication by
     * the authentication manager.
     * 
     * @param username Username being authenticated
     * @param password Password to use for authentication
     * @param authorities GrantedAuthorities a collection of authorities to
     *                    associate with the token
     *//*

    public GCSSUsernamePasswordAuthenticationToken(Object username,
                                                   Object password,
                                                   Collection<? extends GrantedAuthority> authorities) {
        super(username, password, authorities);
    }
    
    */
/**
     * Returns the domain that this token is associated with.
     * 
     * @return String containing the domain
     *//*

    public String getDomain() {
        return domain;
    }
    
    */
/**
     * Sets the domain that this token is associated with.
     * 
     * @param domain String containing the domain
     *//*

    protected void setDomain(String domain) {
        this.domain = domain;
    }
    
    */
/**
     * Returns the location that this token is associated with.
     * 
     * @return String containing the location identifier
     *//*

    public Location getLocation() {
        return location;
    }
    
    */
/**
     * Sets the location that this token is associated with.
     * 
     * @param location the Location to associate with this token
     *//*

    protected void setLocation(Location location) {
        this.location = location;
    }
    
    */
/**
     * Returns the site that is associated with this token.
     * 
     * @return Site 
     *//*

    public Site getSite() {
        return site;
    }
    
    */
/**
     * Sets the Site that this token is associated with.
     * 
     * @param site an instance of Site
     *//*

    protected void setSite(Site site) {
        this.site = site;
    }
}
*/
