package com.guitarcenter.scheduler.security;

import java.io.IOException;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletResponse;

public class CORSFilter implements Filter {

	  @Override
	  public void init(FilterConfig filterConfig) throws ServletException {

	  }

	  @Override
	  public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
	      HttpServletResponse httpResponse = (HttpServletResponse) response;
	      httpResponse.addHeader("Access-Control-Allow-Origin", "*");
	      httpResponse.addHeader("Access-Control-Allow-Methods", "POST, GET, PUT, UPDATE, OPTIONS");
	     // httpResponse.setHeader("Access-Control-Allow-Headers", "X-Requested-With, X-Auth-Token");  
	      chain.doFilter(request, response);
	  }

	  @Override
	  public void destroy() {

	  }
	}
