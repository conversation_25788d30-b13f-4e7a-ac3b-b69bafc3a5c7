package com.guitarcenter.scheduler.jobs;

import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;

import com.guitarcenter.scheduler.config.SpringContextHelper;
import com.guitarcenter.scheduler.service.CRMAppointmentDataFileService;
import jakarta.persistence.Transient;

import org.joda.time.DateTime;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.quartz.StatefulJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.guitarcenter.scheduler.integration.service.EmployeeIntegrationService;
import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;
import com.guitarcenter.scheduler.integration.service.LocationIntegrationService;
import com.guitarcenter.scheduler.service.AppointmentEmailService;
import com.guitarcenter.scheduler.service.JobNotificationEmailService;

/**
 * Implements a job to retrieve and process EDW files.
 * 
 * Note that Quartz job beans cannot be autowired to Spring beans, so the
 * implementation of service will be passed at run-time.
 *
 * <AUTHOR> Emes <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

public class EDWFileJob extends QuartzJobBean implements StatefulJob, Serializable {
    @Transient
    Logger log = LoggerFactory.getLogger(EDWFileJob.class);
    
    private static final long serialVersionUID = -8750509713905705092L;
    
    /**
     * The method that will be called by Quartz when the trigger fires.
     * 
     * @see @see org.springframework.scheduling.quartz.QuartzJobBean#executeInternal(org.quartz.JobExecutionContext)
     * @param jec
     * @throws JobExecutionException
     */
    @SuppressWarnings("unused")
	@Override
    protected void executeInternal(JobExecutionContext jec)
        throws JobExecutionException
    {
    	
    	//Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure 
    	JobNotificationEmailService jobNotificationEmailService = null;
    	    	
        if (log.isDebugEnabled()) {
            log.debug("trigger fired; retrieve and process EDW files. jec = {}",
                      jec);
        }
        
        try {
            LocationIntegrationService locationIntegrationService = null;
           /* locationIntegrationService = (LocationIntegrationService)
                    jec.getScheduler().getContext().get("locationIntegrationService");
*/
			locationIntegrationService = SpringContextHelper.getBean(LocationIntegrationService.class);


			//Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure
       	 /*	jobNotificationEmailService = (JobNotificationEmailService) jec.getScheduler().getContext()
       					.get("jobNotificationEmailService");*/

			jobNotificationEmailService = SpringContextHelper.getBean(JobNotificationEmailService.class);
            if (locationIntegrationService != null) {
                if (log.isDebugEnabled()) {
                    log.debug("about to call locationIntegrationService.scheduledUpdate()");
                }
                locationIntegrationService.scheduledUpdate();
            } else {
                log.warn("locationIntegrationService is null");
            }
        } catch (IntegrationServiceException ise) {
            log.warn("Caught an IntegrationServiceException retrieving or " +
                     "processing EDW employee file", ise);
            //Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure
			if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				ise.printStackTrace(new PrintWriter(errors));		
				jobNotificationEmailService.sendEmailforJobFailureNotification("EDW Job",errors.toString());
			}
			else
			{
				   log.warn("jobNotificationEmailService is null");
			}
			//End of Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure
            
        } catch (Exception se) {
        	
        	  //Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure
			if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				se.printStackTrace(new PrintWriter(errors));	
				jobNotificationEmailService.sendEmailforJobFailureNotification("EDW Job",errors.toString());
			}
			else
			{
				   log.warn("jobNotificationEmailService is null");
			}
			//End of Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure
            
            log.warn("Caught a SchedulerException", se);
        }
        try {
            EmployeeIntegrationService employeeIntegrationService = null;
           /* employeeIntegrationService = (EmployeeIntegrationService)
                    jec.getScheduler().getContext().get("employeeIntegrationService");*/

			employeeIntegrationService = SpringContextHelper.getBean(EmployeeIntegrationService.class);


            if (employeeIntegrationService != null) {
                if (log.isDebugEnabled()) {
                    log.debug("about to call employeeIntegrationService.scheduledUpdate()");
                }
                employeeIntegrationService.scheduledUpdate();
            } else {
                log.warn("employeeIntegrationService is null");
            }
            
        	//Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure 
			if (log.isDebugEnabled()) {
				log.debug("job completed");
			}
						
			if(jobNotificationEmailService != null)
			{				
				jobNotificationEmailService.sendEmailForJobNotification("EDW Job");
			}
			else
			{
				   log.warn("jobNotificationEmailService is null");
			}
			//End of Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure
						
        } catch (IntegrationServiceException ise) {
            log.warn("Caught an IntegrationServiceException retrieving or " +
                     "processing EDW employee file", ise);
            
            //Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure
			if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				ise.printStackTrace(new PrintWriter(errors));				
				jobNotificationEmailService.sendEmailforJobFailureNotification("EDW Job",errors.toString());
			}
			else
			{
				   log.warn("jobNotificationEmailService is null");
			}
			//End of Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure
        } catch (Exception se) {
            log.warn("Caught a SchedulerException", se);
            
            //Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure
			if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				se.printStackTrace(new PrintWriter(errors));				
				jobNotificationEmailService.sendEmailforJobFailureNotification("EDW Job",errors.toString());
			}
			else
			{
				   log.warn("jobNotificationEmailService is null");
			}
			//End of Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure
        }
        
        //Changes made for GSSP-230
        try
        {
        	 AppointmentEmailService appointmentEmailService = null;
        	 
        	 appointmentEmailService = (AppointmentEmailService)
                     jec.getScheduler().getContext().get("appointmentEmailService");

			appointmentEmailService = SpringContextHelper.getBean(AppointmentEmailService.class);
        	 DateTime today = new DateTime();
		     int dayOfWeek = today.getDayOfWeek();
		  
		     if(dayOfWeek == 1 && appointmentEmailService != null)
		     {
		    	 appointmentEmailService.sendTerminatedEmployeeEmail();
			  }
       
     
        }
        
        catch (SchedulerException se) {
            log.warn("Caught a SchedulerException", se);           
        	
			
        }  
    }    
    
}
