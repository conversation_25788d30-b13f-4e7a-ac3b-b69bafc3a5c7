package com.guitarcenter.scheduler.jobs;

import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;

import com.guitarcenter.scheduler.config.SpringContextHelper;
import com.guitarcenter.scheduler.integration.service.InstructorPersonalDetailsIntegrationService;
import jakarta.persistence.Transient;
import org.joda.time.DateTime;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.quartz.StatefulJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.criterion.dto.CRMAppointmentDataFileDTO;
import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;
import com.guitarcenter.scheduler.service.CRMAppointmentDataFileService;
import com.guitarcenter.scheduler.service.JobNotificationEmailService;

/**
 * Implements a job bean for rCRM Appointment data File transferring.
 *
 */

public class CRMAppointmentDataFileJob extends QuartzJobBean implements StatefulJob, Serializable {
    
	@Transient
    Logger log = LoggerFactory.getLogger(CRMAppointmentDataFileJob.class);
    
    private static final long serialVersionUID = 4L;

     /**
     * The method that will be called by Quartz when the trigger fires.
     * 
     * @see @see org.springframework.scheduling.quartz.QuartzJobBean#executeInternal(org.quartz.JobExecutionContext)
     * @param jec
     * @throws JobExecutionException
     */
    @SuppressWarnings("unused")
	@Override
    protected void executeInternal(JobExecutionContext jec)
            throws JobExecutionException {

		JobNotificationEmailService jobNotificationEmailService = null;
		Date currentDate = DateTime.now().toDate();
		CRMAppointmentDataFileService cRMAppointmentDataFileService = null;
		Map<String, CRMAppointmentDataFileDTO> cRMAppointmentDataList = null;
		
		if (log.isDebugEnabled()) {
			log.debug("trigger fired; retrieve and process CRMAppointmentDataFile. jec = {}", jec);
			
		}
		try {
		
		    cRMAppointmentDataFileService = (CRMAppointmentDataFileService)jec.getScheduler().getContext().get("cRMAppointmentDataFileService");
			/*jobNotificationEmailService = (JobNotificationEmailService) jec.getScheduler().getContext()
					.get("jobNotificationEmailService");*/

			cRMAppointmentDataFileService = SpringContextHelper.getBean(CRMAppointmentDataFileService.class);
			jobNotificationEmailService = SpringContextHelper.getBean(JobNotificationEmailService.class);
			log.error("dailySubscriptionReportService is  "+cRMAppointmentDataFileService);
			log.error("jobNotificationEmailService is  "+jobNotificationEmailService);

			if (cRMAppointmentDataFileService != null) {
				//CRM-338 ::Before one-date changed as per job trigger time changed to 12:30 AM PST.
				//Need to get all the date changed yestarday and till time job triggered
				Calendar cal = Calendar.getInstance();
				cal.setTime(new Date());
				cal.add(Calendar.DATE, -1);
				
				String dateInString =new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_HIPHEN).format(cal.getTime());
				cRMAppointmentDataList = cRMAppointmentDataFileService.scheduedTask(dateInString);
 				 try {
					 cRMAppointmentDataFileService.encryptAndTransferCRMApppointmentData(cRMAppointmentDataList);
				}catch (IntegrationServiceException e) {
						log.warn("Caught at encryptAndTransferCRMApppointmentData IntegrationServiceException transfer", e);
					}
 				catch (Exception e) {
						log.warn("Caught at encryptAndTransferCRMApppointmentData Exception transfer", e);
					}
				if (log.isDebugEnabled()) {
					log.debug("CRMAppointmentDataFileJob   CRM Job completed");
				}
				
				if(jobNotificationEmailService != null)
				{							
					  jobNotificationEmailService.sendEmailForCRMAppointmentDataFile("CRM Appointment Data File Job", "");
				}
				else
				{
					   log.warn("CRM jobNotificationEmailService is null");
				}

			} else {
				log.warn("In CRMAppointmentDataFileJob class cRMAppointmentDataFileService is null");
				throw new SchedulerException();
			}
	
		} catch (SchedulerException se) {
			//Code changes made for CRMI-338 Notification for Lesson Scheduler Data Email  Success and Failure
			if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				se.printStackTrace(new PrintWriter(errors));
				jobNotificationEmailService.sendEmailforJobFailureNotification("CRM Appointment Data File Job",errors.toString());
			}
			else
			{
				   log.warn("In CRMAppointmentDataFileJob.java ,CRM jobNotificationEmailService is null");
			}
			//End of Code changes made for CRMI-338 Notification for Lesson Scheduler Data Job Success and Failure
			log.warn("In CRMAppointmentDataFileJob.java at Caught a SchedulerException", se);
			
		}			
     }
 
     
}
