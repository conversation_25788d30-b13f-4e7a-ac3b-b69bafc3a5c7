package com.guitarcenter.scheduler.jobs;

import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.util.TimeZone;

import com.guitarcenter.scheduler.config.SpringContextHelper;
import com.guitarcenter.scheduler.service.CRMAppointmentDataFileService;
import jakarta.persistence.Transient;

import org.quartz.CronTrigger;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.quartz.StatefulJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.guitarcenter.scheduler.service.InstructorActivityAndAvailabitityService;
import com.guitarcenter.scheduler.service.JobNotificationEmailService;
//Created for GSSP-298
public class InstructorActivityAndAvailabilityJob extends QuartzJobBean implements StatefulJob, Serializable{

	@Transient
    Logger log = LoggerFactory.getLogger(InstructorActivityAndAvailabilityJob.class);
    
    private static final long serialVersionUID = -5123766345613082109L;
    @SuppressWarnings("unused")
	@Override
    protected void executeInternal(JobExecutionContext jec)
            throws JobExecutionException {
    	
    	
    	JobNotificationEmailService jobNotificationEmailService = null;
        if (log.isDebugEnabled()) {
            log.debug("about to execute instructorActivityAndAvailability, jec = {}", jec);
        }
        InstructorActivityAndAvailabitityService instructorActivityAndAvailabilityService = null;
        
        TimeZone tz =  ((CronTrigger)jec.getTrigger()).getTimeZone();        
        String timezoneID =  tz.getID();      
               
        try {        	    	   
        	/*instructorActivityAndAvailabilityService = (InstructorActivityAndAvailabitityService)
                jec.getScheduler().getContext().get("instructorActivityAndAvailabitityService");*/
            
        	
           /* jobNotificationEmailService = (JobNotificationEmailService) jec.getScheduler().getContext()
					.get("jobNotificationEmailService");*/
			instructorActivityAndAvailabilityService = SpringContextHelper.getBean(InstructorActivityAndAvailabitityService.class);
			jobNotificationEmailService = SpringContextHelper.getBean(JobNotificationEmailService.class);

			  if (instructorActivityAndAvailabilityService != null) {		  
				  instructorActivityAndAvailabilityService.generateInstructorAcitivityAndAvailabilityReport();
                
            	if (log.isDebugEnabled()) {
					log.debug("job completed");
				}							
				
				if(jobNotificationEmailService != null)
				{					
					jobNotificationEmailService.sendEmailForJobNotification("Instructor Activity and Availability Report" );
				}
				else
				{
					   log.warn("jobNotificationEmailService is null");
				}			
                
                
            } else {
                log.warn("Unable to send Instructor Activity and Availability Report due to jobNotificationEmailService null value");
            }
        } catch (SchedulerException se) {
            log.warn("Caught a SchedulerException", se);
            
        	if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				se.printStackTrace(new PrintWriter(errors));
				jobNotificationEmailService.sendEmailforJobFailureNotification("Instructor Activity and Availability Report  ",errors.toString());
			}
			else
			{
				   log.warn("jobNotificationEmailService is null");
			}
			
        }        
        catch(Exception e)
        {
        	
        }
    }

}
