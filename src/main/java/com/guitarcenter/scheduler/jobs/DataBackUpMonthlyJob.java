package com.guitarcenter.scheduler.jobs;

import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.util.Date;
import java.util.Map;

import com.guitarcenter.scheduler.config.SpringContextHelper;
import com.guitarcenter.scheduler.service.CRMAppointmentDataFileService;
import com.guitarcenter.scheduler.service.ReportService;
import jakarta.persistence.Transient;
import org.joda.time.DateTime;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.quartz.StatefulJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;
import com.guitarcenter.scheduler.service.DataBackUpMonthlyService;
import com.guitarcenter.scheduler.service.JobNotificationEmailService;

/**
 * Implements a job bean for rCRM Appointment data File transferring.
 *
 */

public class DataBackUpMonthlyJob extends QuartzJobBean implements StatefulJob, Serializable {
    
	@Transient
    Logger log = LoggerFactory.getLogger(DataBackUpMonthlyJob.class);
    
    private static final long serialVersionUID = 4L;

     /**
     * The method that will be called by Quartz when the trigger fires.
     * 
     * @see @see org.springframework.scheduling.quartz.QuartzJobBean#executeInternal(org.quartz.JobExecutionContext)
     * @param jec
     * @throws JobExecutionException
     */
    @SuppressWarnings("unused")
	@Override
    protected void executeInternal(JobExecutionContext jec)
            throws JobExecutionException {
		JobNotificationEmailService jobNotificationEmailService = null;
		Date currentDate = DateTime.now().toDate();
		DataBackUpMonthlyService dataBackUpMonthlyService = null;
		
		if (log.isDebugEnabled()) {
			log.debug("trigger fired; retrieve and process DataBackUp. jec = {}", jec);
			
		}
		try {
		
		/*	dataBackUpMonthlyService = (DataBackUpMonthlyService)jec.getScheduler().getContext().get("dataBackUpMonthlyService");*/
			/*jobNotificationEmailService = (JobNotificationEmailService) jec.getScheduler().getContext()
					.get("jobNotificationEmailService");*/
			dataBackUpMonthlyService = SpringContextHelper.getBean(DataBackUpMonthlyService.class);
			jobNotificationEmailService = SpringContextHelper.getBean(JobNotificationEmailService.class);
			if (dataBackUpMonthlyService != null) {

				Map<String,String> mdata = dataBackUpMonthlyService.scheduedTask();
				
				String backup = "table " + mdata.get("APPTMNT_BKP_TBL_CREATED") + " has been created\n" +
						"The table " + mdata.get("APPTMNT_BKP_TBL_DROP")  + " has been dropped\n" +
						"The table " + mdata.get("APPTMNT_CUST_BKP_TBL_CREATED") + " has been created\n" +
						"The table "+mdata.get("APPTMNT_CUST_BKP_TBL_DROP") + " has been dropped\n" + 
						"Data Recovery Monthly Job "; 
				
				 log.warn("In DataBackUpMonthlyJob class DataBackUpMonthlyService is null",backup);
				
				  backup = "Backup Tables " + mdata.get("APPTMNT_BKP_TBL_CREATED") + " and  " + mdata.get("APPTMNT_CUST_BKP_TBL_CREATED") + " has been created, \n <br/>&nbsp;&nbsp;" +
						"" +  "Data Back-up Monthly Job ";
				
				if (log.isDebugEnabled()) {
					log.debug("DataBackUpMonthlyJob   DataBackUp Job completed");
				}
				
				if(jobNotificationEmailService != null)
				{							
					 jobNotificationEmailService.sendEmailForCRMAppointmentDataFile(backup, "");
				}
				else
				{
					   log.warn("DataBackUp jobNotificationEmailService is null");
				}

			} else {
				log.warn("In DataBackUpMonthlyJob class DataBackUpMonthlyService is null");
				throw new SchedulerException();
			}
	
		} catch (SchedulerException se) {
			//Code changes made for GCSS-277 Notification for Lesson Scheduler Data Email  Success and Failure
			if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				se.printStackTrace(new PrintWriter(errors));
				 jobNotificationEmailService.sendEmailforJobFailureNotification("Data Back-Up Monthly Job",errors.toString());
			}
			else
			{
				   log.warn("In DataBackUpMonthlyJob.java ,DataBackUpMonthly jobNotificationEmailService is null");
			}
			//End of Code changes made for GCSS-277 Notification for Lesson Scheduler Data Job Success and Failure
			log.warn("In DataBackUpMonthlyJob.java at Caught a SchedulerException", se);
			
		}	

     }
 
     
}
