package com.guitarcenter.scheduler.jobs;

import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.util.TimeZone;

import com.guitarcenter.scheduler.config.SpringContextHelper;
import com.guitarcenter.scheduler.service.CRMAppointmentDataFileService;
import jakarta.persistence.Transient;

import org.quartz.CronTrigger;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.quartz.StatefulJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.guitarcenter.scheduler.service.InstructorActivitiesService;
import com.guitarcenter.scheduler.service.JobNotificationEmailService;

/**
 * Runs Instructor activities related job 
 * 
 */
public class InstructorActivitiesEmailJob extends QuartzJobBean implements StatefulJob, Serializable{
	//GSSP-240 - Sends e-mail to store user with attachment
	
    @Transient
    Logger log = LoggerFactory.getLogger(InstructorActivitiesEmailJob.class);
    
    private static final long serialVersionUID = -5123766345613082108L;
    
    
    /**
     * The method that will be called by Quartz when the trigger fires.
     * 
     * @see @see org.springframework.scheduling.quartz.QuartzJobBean#executeInternal(org.quartz.JobExecutionContext)
     * @param jec
     * @throws JobExecutionException
     */
    @SuppressWarnings("unused")
	@Override
    protected void executeInternal(JobExecutionContext jec)
            throws JobExecutionException {
    	
    	
    	JobNotificationEmailService jobNotificationEmailService = null;
    	   			
        if (log.isDebugEnabled()) {
            log.debug("about to execute instructorActivitiesEmailJob, jec = {}", jec);
        }
		InstructorActivitiesService instructorActivitiesService = null;
        
        TimeZone tz =  ((CronTrigger)jec.getTrigger()).getTimeZone();        
        String timezoneID =  tz.getID();      
               
        try {        	    	   
        	/*instructorActivitiesService = (InstructorActivitiesService)
                jec.getScheduler().getContext().get("instructorActivitiesService");
            
          
            jobNotificationEmailService = (JobNotificationEmailService) jec.getScheduler().getContext()
					.get("jobNotificationEmailService");*/

			instructorActivitiesService = SpringContextHelper.getBean(InstructorActivitiesService.class);
			jobNotificationEmailService = SpringContextHelper.getBean(JobNotificationEmailService.class);

			  if (instructorActivitiesService != null) {
				  				  
				  instructorActivitiesService.generateInstructorActivitiesReport();
                
            	if (log.isDebugEnabled()) {
					log.debug("job completed");
				}							
				
				if(jobNotificationEmailService != null)
				{					
					jobNotificationEmailService.sendEmailForJobNotification("Weekly Instructor activities report " );
				}
				else
				{
					   log.warn("jobNotificationEmailService is null");
				}			
                
                
            } else {
                log.warn("Unable to send weekly Instructor Activities report due to instructorActivitiesService null value");
            }
        } catch (SchedulerException se) {
            log.warn("Caught a SchedulerException", se);
            
        	if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				se.printStackTrace(new PrintWriter(errors));
				jobNotificationEmailService.sendEmailforJobFailureNotification("Weekly Instructor activities report ",errors.toString());
			}
			else
			{
				   log.warn("jobNotificationEmailService is null");
			}
			
        }        
        catch(Exception e)
        {
        	
        }
    }

}
