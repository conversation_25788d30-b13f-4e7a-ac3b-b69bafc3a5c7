package com.guitarcenter.scheduler.jobs;

import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.util.Date;
import java.util.Map;

import com.guitarcenter.scheduler.config.SpringContextHelper;
import com.guitarcenter.scheduler.service.CRMAppointmentDataFileService;
import jakarta.persistence.Transient;

import org.joda.time.DateTime;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.quartz.StatefulJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.guitarcenter.scheduler.service.JobNotificationEmailService;
import com.guitarcenter.scheduler.service.LackingAppointmentService;

public class LackingAppointmentJob extends QuartzJobBean implements StatefulJob, Serializable {

	@Transient
	Logger						log					= LoggerFactory.getLogger(LackingAppointmentJob.class);

	private static final long	serialVersionUID	= 8777593275564566461L;


	@SuppressWarnings("unused")
	@Override
	protected void executeInternal(JobExecutionContext jec) throws JobExecutionException {
		
		//Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure 
		JobNotificationEmailService jobNotificationEmailService = null;
	
		if (log.isDebugEnabled()) {
			log.debug("trigger fired; retrieve and process lack appointment. jec = {}", jec);
		}
		try {
							
				LackingAppointmentService lackingAppointmentService = (LackingAppointmentService) jec.getScheduler().getContext()
						.get("lackingAppointmentService");				
			
				//Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure
				/*jobNotificationEmailService = (JobNotificationEmailService) jec.getScheduler().getContext()
					.get("jobNotificationEmailService");*/
			lackingAppointmentService = SpringContextHelper.getBean(LackingAppointmentService.class);
			jobNotificationEmailService = SpringContextHelper.getBean(JobNotificationEmailService.class);
										
				Date currentDate = DateTime.now().toDate();
				if (lackingAppointmentService != null) {
						if (log.isDebugEnabled()) {
							log.debug("about to call lackingAppointmentService.scheduedTask({})", currentDate);
						}
						Map<String,String> appointmentsFailedToUpdate= lackingAppointmentService.scheduedTask(currentDate); //GSSP-268 lack Appointment job issue			
								
						//Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure 
						if (log.isDebugEnabled()) {
							log.debug("job completed");
						}		
										
						if(jobNotificationEmailService != null)
						{							
							jobNotificationEmailService.sendEmailForLackAppointmentWithData("lack appointment Job", appointmentsFailedToUpdate); //lack Appointment job issue
						}
						else
						{
							   log.warn("jobNotificationEmailService is null");
						}
						//End of Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure 
						
				
			} else {
				log.warn("lackingAppointmentService is null");
			}
		} catch (SchedulerException se) {
			
			//Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure
			if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				se.printStackTrace(new PrintWriter(errors));
				jobNotificationEmailService.sendEmailforJobFailureNotification("Lack Appointment Job",errors.toString());
			}
			else
			{
				   log.warn("jobNotificationEmailService is null");
			}
			//End of Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure
			
			log.warn("Caught a SchedulerException", se);
		}
		
	}
}
