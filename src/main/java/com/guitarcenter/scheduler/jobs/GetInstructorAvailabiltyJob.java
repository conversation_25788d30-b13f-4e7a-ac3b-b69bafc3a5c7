package com.guitarcenter.scheduler.jobs;

import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.guitarcenter.scheduler.config.SpringContextHelper;
import com.guitarcenter.scheduler.service.CRMAppointmentDataFileService;
import jakarta.persistence.Transient;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.quartz.StatefulJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.guitarcenter.scheduler.elesticsearch.InstructorAvailablityElasticUploader;
import com.guitarcenter.scheduler.service.GetInstructorAvailabiltyService;
import com.guitarcenter.scheduler.service.JobNotificationEmailService;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceResponseDTO;

/**
 * Implements a job bean for rCRM Appointment data File transferring.
 *
 */

public class GetInstructorAvailabiltyJob extends QuartzJobBean implements StatefulJob, Serializable {
    
	@Transient
    Logger log = LoggerFactory.getLogger(GetInstructorAvailabiltyJob.class);
    
    private static final long serialVersionUID = 4L;

     
     /**
     * The method that will be called by Quartz when the trigger fires.
     * 
     * @see @see org.springframework.scheduling.quartz.QuartzJobBean#executeInternal(org.quartz.JobExecutionContext)
     * @param jec
     * @throws JobExecutionException
     */
    @SuppressWarnings("unused")
	@Override
    protected void executeInternal(JobExecutionContext jec)
            throws JobExecutionException {
    	log.error("Get Instructor    Availabilty    start");
		JobNotificationEmailService jobNotificationEmailService = null;
		GetInstructorAvailabiltyService  getInstructorAvailabiltyService = null;
		if (log.isDebugEnabled()) {
			log.debug("trigger fired; retrieve and process GetInstructorAvailability. jec = {}", jec);
			
		}
		
		try {
		
			//getInstructorAvailabiltyService = (GetInstructorAvailabiltyService)jec.getScheduler().getContext().get("getInstructorAvailabiltyService");
			/*jobNotificationEmailService = (JobNotificationEmailService) jec.getScheduler().getContext()
					.get("jobNotificationEmailService");*/

			getInstructorAvailabiltyService = SpringContextHelper.getBean(GetInstructorAvailabiltyService.class);
			jobNotificationEmailService = SpringContextHelper.getBean(JobNotificationEmailService.class);
			List<InstructorAVLServiceResponseDTO> result = new ArrayList<InstructorAVLServiceResponseDTO>();
			
			if (getInstructorAvailabiltyService != null) {
				 try
				  {
 
					  result = getInstructorAvailabiltyService.getInstructorAvailabilitySlots();
					  String env = getInstructorAvailabiltyService.getEnvironmentName();
					  if (log.isDebugEnabled()) {
							log.debug("trigger fired; retrieve and Job GetInstructorAvailabiltys result", result);
							
						}
					  log.error("GetInstructorAvailabilty  result fetching done");
					  InstructorAvailablityElasticUploader iaeu = new InstructorAvailablityElasticUploader();
					  
					  Map<String, String> elasticConnect =  getInstructorAvailabiltyService.getPropertiesDataForElasticLoader();
					   /* ObjectMapper Obj = new ObjectMapper();
				        String jsonStr = Obj.writeValueAsString(result);*/
						/*
						 * String path = "D:/a.txt";
						 * System.out.println("The JSON out put is  result-->"+jsonStr); Files.write(
						 * Paths.get(path), jsonStr.getBytes());
						 */
					  log.error("GetInstructorAvailabilty  elasticInsertOrUpdate open");
					   if(result!= null && result.size()> 0) iaeu.elasticInsertOrUpdate(result,env,elasticConnect,"GetInstructorAvailabilty");
					   log.error("GetInstructorAvailabilty  elasticInsertOrUpdate completed");
 
				  }
				  catch(Exception e)
				  {
					  log.error("Error during fetching of data in new instructor Availabilitiy api Web Service", e);
					  throw new SchedulerException();
					  
				  }						 
				
				if(jobNotificationEmailService != null)
				{		
					 if(result!= null && result.size()== 0){
						 jobNotificationEmailService.sendEmailForCRMAppointmentDataFile("Get Instructor Availability Job with no records ", "");
					 }else{
						 jobNotificationEmailService.sendEmailForCRMAppointmentDataFile("Get Instructor Availability Job", "");
					 }
				}
				else
				{
					   log.warn("getInstructorAvailabiltyJob jobNotificationEmailService is null");
				}

			} else {
				log.warn("In GetInstructorAvailability Job   Service is null");
				throw new SchedulerException();
			}
	
		} catch (SchedulerException se) {
			//Code changes made for CRMI-338 Notification for Lesson Scheduler Data Email  Success and Failure
			if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				se.printStackTrace(new PrintWriter(errors));
				jobNotificationEmailService.sendEmailforJobFailureNotification("Get Instructor Availability Job",errors.toString());
			}
			else
			{
				   log.warn("In getInstructorAvailabilityJob.java ,Cancel Hold Appointments jobNotificationEmailService is null");
			}
			//End of Code changes made for CRMI-338 Notification for Lesson Scheduler Data Job Success and Failure
			log.warn("In GetInstructorAvailabilityJob.java at Caught a SchedulerException", se);
			
		}
		log.error("Get Instructor    Availabilty    ends");
     }
 
     
}
