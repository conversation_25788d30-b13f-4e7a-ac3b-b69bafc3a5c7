package com.guitarcenter.scheduler.jobs;

import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.guitarcenter.scheduler.config.SpringContextHelper;
import com.guitarcenter.scheduler.service.CRMAppointmentDataFileService;
import jakarta.persistence.Transient;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.quartz.StatefulJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.guitarcenter.scheduler.service.CancelHoldAppointmentsService;
import com.guitarcenter.scheduler.service.JobNotificationEmailService;

/**
 * Implements a job bean for rCRM Appointment data File transferring.
 *
 */

public class CancelHoldAppointmentsJob extends QuartzJobBean implements StatefulJob, Serializable {
    
	@Transient
    Logger log = LoggerFactory.getLogger(CancelHoldAppointmentsJob.class);
    
    private static final long serialVersionUID = 4L;

     /**
     * The method that will be called by Quartz when the trigger fires.
     * 
     * @see @see org.springframework.scheduling.quartz.QuartzJobBean#executeInternal(org.quartz.JobExecutionContext)
     * @param jec
     * @throws JobExecutionException
     */
    @SuppressWarnings("unused")
	@Override
    protected void executeInternal(JobExecutionContext jec)
            throws JobExecutionException {

		JobNotificationEmailService jobNotificationEmailService = null;
		CancelHoldAppointmentsService  cancelHoldAppointmentsService = null;
		if (log.isDebugEnabled()) {
			log.debug("trigger fired; retrieve and process CancelHoldAppointments. jec = {}", jec);
			
		}
		try {
		
			/*cancelHoldAppointmentsService = (CancelHoldAppointmentsService)jec.getScheduler().getContext().get("cancelHoldAppointmentsService");*/
			/*jobNotificationEmailService = (JobNotificationEmailService) jec.getScheduler().getContext()
					.get("jobNotificationEmailService");*/

			cancelHoldAppointmentsService = SpringContextHelper.getBean(CancelHoldAppointmentsService.class);
			jobNotificationEmailService = SpringContextHelper.getBean(JobNotificationEmailService.class);
			if (cancelHoldAppointmentsService != null) {
			 
				
				try {
					List<String> customerEmailList = new ArrayList<String>();
					Date currentDateTime =  new Date();
					Date dateBefore = new Date(currentDateTime.getTime() - 1 * 24 * 3600 * 1000 );
					cancelHoldAppointmentsService.findHoldAppointments(dateBefore);
				} catch (Exception e) {
					throw new SchedulerException();
					
				}
				if(jobNotificationEmailService != null)
				{							
					  jobNotificationEmailService.sendEmailForCRMAppointmentDataFile("Cancel Hold Appointments Job", "");
				}
				else
				{
					   log.warn("Cancel Hold Appointments Service is null");
				}

			} else {
				log.warn("In CancelHoldAppointmentsJob class CancelHoldAppointmentsService is null");
				throw new SchedulerException();
			}
	
		} catch (SchedulerException se) {
			//Code changes made for CRMI-338 Notification for Lesson Scheduler Data Email  Success and Failure
			if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				se.printStackTrace(new PrintWriter(errors));
				jobNotificationEmailService.sendEmailforJobFailureNotification("Cancel Hold Appointments Job",errors.toString());
			}
			else
			{
				   log.warn("In cancelHoldAppointmentsJob.java ,Cancel Hold Appointments jobNotificationEmailService is null");
			}
			//End of Code changes made for CRMI-338 Notification for Lesson Scheduler Data Job Success and Failure
			log.warn("In CancelHoldAppointmentsJob.java at Caught a SchedulerException", se);
			
		}			
     }
    
}
