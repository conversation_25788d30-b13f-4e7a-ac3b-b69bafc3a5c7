/**
 * @Title: TimeoffDAO.java
 * @Package com.guitarcenter.scheduler.dao
 * @Description: 
 * Copyright: Copyright (c) 2014 
 * Company:
 * 
 * <AUTHOR>
 * @date Mar 10, 2014 4:05:17 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.dao;

import java.util.List;

import com.guitarcenter.scheduler.dto.TimeoffDateDTO;
import com.guitarcenter.scheduler.model.Timeoff;

/**
 * @ClassName: TimeoffDAO
 * @Description: 
 * <AUTHOR>
 * @date Mar 10, 2014 4:05:17 PM
 *
 */
public interface TimeoffDAO extends AbstractDAO<Timeoff> {
	public List<Timeoff> getTimeoffByInstructorId(long instructorId);
	public List<Timeoff> getDisplayTimeoffByInstructorId(long instructorId);
	public boolean checkTimeoffByTime(String startDate, String startTime, String endTime, long instructorId);
	public boolean checkTimeoffByRecurringTime(List<String> startDate, String startTime, String endTime, long instructorId);
	public List<Timeoff> getTimeoffByDateInstructorId(long instructorId, String date);
	public List<TimeoffDateDTO> getTimeoffDateDTOByAvailabilityTime(String date, long locationId);
}
