package com.guitarcenter.scheduler.dao;

import java.util.List;

import com.guitarcenter.scheduler.model.Onetime;
import com.guitarcenter.scheduler.model.InstoreAvailability;
import com.guitarcenter.scheduler.model.Person;

public interface InstoreAvailabilityDAO  extends AbstractDAO<InstoreAvailability> {
	
	public List<InstoreAvailability> getInstoreAvailabilityByInstructorId(long instructorId);
	public List<InstoreAvailability> getDisplayInstoreAvailabilityeByInstructorId(long instructorId);
	public boolean checkInstoreAvailabilityByProfileId(String startDate, String startTime, String endTime, long profileId);
	public boolean checkInstoreAvailabilityByTime(String startDate, String startTime,String endDate, String endTime, long instructorId);
	public List<Onetime> getInstoreAvailabilityByTime(String startDate, String startTime, String endTime, long instructorId);
	public void saveOrUpdate(InstoreAvailability pT,Person pUpdatedBy);
	
}
