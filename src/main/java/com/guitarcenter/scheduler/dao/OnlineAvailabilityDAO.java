package com.guitarcenter.scheduler.dao;

import java.util.List;

import com.guitarcenter.scheduler.model.Onetime;
import com.guitarcenter.scheduler.model.OnlineAvailability;
import com.guitarcenter.scheduler.model.Person;

public interface OnlineAvailabilityDAO  extends AbstractDAO<OnlineAvailability> {
	
	public List<OnlineAvailability> getOnlineAvailabilityByInstructorId(long instructorId);
	public List<OnlineAvailability> getDisplayOnlineAvailabilityeByInstructorId(long instructorId);
	public boolean checkOnlineAvailabilityByProfileId(String startDate, String startTime, String endTime, long profileId);
	public boolean checkOnlineAvailabilityByTime(String startDate, String startTime,String endDate, String endTime, long instructorId);
	public List<Onetime> getOnlineAvailabilityByTime(String startDate, String startTime, String endTime, long instructorId);
	public void saveOrUpdate(OnlineAvailability pT,Person pUpdatedBy);
	
}
