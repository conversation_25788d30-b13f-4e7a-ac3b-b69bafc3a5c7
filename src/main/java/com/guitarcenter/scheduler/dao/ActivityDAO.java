package com.guitarcenter.scheduler.dao;

import java.util.List;

import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.model.Activity;

public interface ActivityDAO extends AbstractDAO<Activity> {
	public Activity getActivityByAppointmentTime(long activityId, String startDate, String startTime, String endTime, long profileId);
	public Activity getActivityByAppointmentRecurringTime(long activityId, String startDate, String endDate, String startTime, String endTime, long profileId);
	public List<ActivityDTO> getActivityDTOByProfileRoom(long profileId, long roomId);
	public void deleteInstructorAvtivityByActivityId(long activityId) throws Exception;
	public void deleteProfileAvtivityByActivityId(long activityId) throws Exception;
	public void deleteRoomTemplateAvtivityByActivityId(long activityId) throws Exception;
	public void deleteRoomAvtivityByActivityId(long activityId) throws Exception;
	public void deleteInstructorAvtivityByServiceId(long serviceId) throws Exception;
	public void deleteProfileAvtivityByServiceId(long serviceId) throws Exception;
	public void deleteRoomTemplateAvtivityByServiceId(long serviceId) throws Exception;
	public void deleteRoomAvtivityByServiceId(long serviceId) throws Exception;
}
