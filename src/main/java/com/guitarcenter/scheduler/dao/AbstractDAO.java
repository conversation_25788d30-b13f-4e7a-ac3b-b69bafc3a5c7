package com.guitarcenter.scheduler.dao;

import java.util.List;

import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.Person;

public interface AbstractDAO<T> {

	long save(T t, Person updatedBy);

	long save(T t, long updatedById);

	void update(T t, Person updatedBy);

	void update(T t, long updatedById);

	T get(long id);

	void delete(T t);

	T merge(T t);

	<E> List<E> search(Criterion<T, E> criterion);

	List<T> search(T example);

	List<T> searchAll();

	T get(long id, int fetchMode);

	T merge(T t, int fetchMode);

	<E> List<E> search(Criterion<T, E> criterion, int fetchMode);

	List<T> search(T example, int fetchMode);

	List<T> searchAll(int fetchMode);

	<E> E get(Criterion<T, E> criterion, int fetchMode);

	<E> E get(Criterion<T, E> criterion);

	T get(T example);

	T get(T example, int fetchMode);
}
