package com.guitarcenter.scheduler.dao.util;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.security.NoSuchProviderException;
import java.security.SecureRandom;
import java.security.Security;
import java.util.Date;
import java.util.Iterator;
import java.util.Map;

import jakarta.persistence.Transient;

import org.bouncycastle.bcpg.ArmoredOutputStream;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openpgp.PGPCompressedData;
import org.bouncycastle.openpgp.PGPCompressedDataGenerator;
import org.bouncycastle.openpgp.PGPEncryptedData;
import org.bouncycastle.openpgp.PGPEncryptedDataGenerator;
import org.bouncycastle.openpgp.PGPEncryptedDataList;
import org.bouncycastle.openpgp.PGPException;
import org.bouncycastle.openpgp.PGPLiteralData;
import org.bouncycastle.openpgp.PGPLiteralDataGenerator;
import org.bouncycastle.openpgp.PGPObjectFactory;
import org.bouncycastle.openpgp.PGPOnePassSignatureList;
import org.bouncycastle.openpgp.PGPPrivateKey;
import org.bouncycastle.openpgp.PGPPublicKey;
import org.bouncycastle.openpgp.PGPPublicKeyEncryptedData;
import org.bouncycastle.openpgp.PGPPublicKeyRing;
import org.bouncycastle.openpgp.PGPPublicKeyRingCollection;
import org.bouncycastle.openpgp.PGPSecretKey;
import org.bouncycastle.openpgp.PGPSecretKeyRingCollection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.dao.criterion.dto.CRMAppointmentDataFileDTO;
import com.guitarcenter.scheduler.jobs.CRMAppointmentDataFileJob;

public class PGPUtils {

	@Transient
	static Logger logPGP = LoggerFactory.getLogger(CRMAppointmentDataFileJob.class);
	
	@SuppressWarnings("unchecked")
	public static PGPPublicKey readPublicKey(InputStream in) throws IOException, PGPException {
		in = org.bouncycastle.openpgp.PGPUtil.getDecoderStream(in);

		
		
		PGPPublicKeyRingCollection pgpPub = new PGPPublicKeyRingCollection(in);

		//
		// we just loop through the collection till we find a key suitable for
		// encryption, in the real
		// world you would probably want to be a bit smarter about this.
		//
		PGPPublicKey key = null;

		//
		// iterate through the key rings.
		//
		Iterator<PGPPublicKeyRing> rIt = pgpPub.getKeyRings();

		while (key == null && rIt.hasNext()) {
			PGPPublicKeyRing kRing = rIt.next();
			Iterator<PGPPublicKey> kIt = kRing.getPublicKeys();
			while (key == null && kIt.hasNext()) {
				PGPPublicKey k = kIt.next();

				if (k.isEncryptionKey()) {
					key = k;
				}
			}
		}

		if (key == null) {
			throw new IllegalArgumentException("Can't find encryption key in key ring.");
		}

		return key;
	}

	/**
	 * Load a secret key ring collection from keyIn and find the secret key
	 * corresponding to keyID if it exists.
	 *
	 * @param keyIn
	 *            input stream representing a key ring collection.
	 * @param keyID
	 *            keyID we want.
	 * @param pass
	 *            passphrase to decrypt secret key with.
	 * @return
	 * @throws IOException
	 * @throws PGPException
	 * @throws NoSuchProviderException
	 */
	private static PGPPrivateKey findSecretKey(InputStream keyIn, long keyID, char[] pass)
			throws IOException, PGPException, NoSuchProviderException {
		PGPSecretKeyRingCollection pgpSec = new PGPSecretKeyRingCollection(
				org.bouncycastle.openpgp.PGPUtil.getDecoderStream(keyIn));

		PGPSecretKey pgpSecKey = pgpSec.getSecretKey(keyID);

		if (pgpSecKey == null) {
			return null;
		}

		return pgpSecKey.extractPrivateKey(pass, "BC");
	}

	/**
	 * decrypt the passed in message stream
	 * @throws IOException 
	 * @throws PGPException 
	 * @throws NoSuchProviderException 
	 */
	@SuppressWarnings("unchecked")
	public static void decryptFile(InputStream in, OutputStream out, InputStream keyIn, char[] passwd) throws IOException, NoSuchProviderException, PGPException {
		Security.addProvider(new BouncyCastleProvider());

		in = org.bouncycastle.openpgp.PGPUtil.getDecoderStream(in);
		PGPObjectFactory pgpF = new PGPObjectFactory(in);
		PGPEncryptedDataList enc;

		Object o = pgpF.nextObject();
		//
		// the first object might be a PGP marker packet.
		//
		if (o instanceof PGPEncryptedDataList) {
			enc = (PGPEncryptedDataList) o;
		} else {
			enc = (PGPEncryptedDataList) pgpF.nextObject();
		}

		//
		// find the secret key
		//
		Iterator<PGPPublicKeyEncryptedData> it = enc.getEncryptedDataObjects();
		PGPPrivateKey sKey = null;
		PGPPublicKeyEncryptedData pbe = null;

		while (sKey == null && it.hasNext()) {
			pbe = it.next();

			sKey = findSecretKey(keyIn, pbe.getKeyID(), passwd);
		}

		if (sKey == null) {
			throw new IllegalArgumentException("Secret key for message not found.");
		}

		@SuppressWarnings("deprecation")
		InputStream clear = pbe.getDataStream(sKey, "BC");

		PGPObjectFactory plainFact = new PGPObjectFactory(clear);

		Object message = plainFact.nextObject();

		if (message instanceof PGPCompressedData) {
			PGPCompressedData cData = (PGPCompressedData) message;
			PGPObjectFactory pgpFact = new PGPObjectFactory(cData.getDataStream());

			message = pgpFact.nextObject();
		}

		if (message instanceof PGPLiteralData) {
			PGPLiteralData ld = (PGPLiteralData) message;

			InputStream unc = ld.getInputStream();
			int ch;

			while ((ch = unc.read()) >= 0) {
				out.write(ch);
			}
		} else if (message instanceof PGPOnePassSignatureList) {
			throw new PGPException("Encrypted message contains a signed message - not literal data.");
		} else {
			throw new PGPException("Message is not a simple encrypted file - type unknown.");
		}

		if (pbe.isIntegrityProtected()) {
			if (!pbe.verify()) {
				throw new PGPException("Message failed integrity check");
			}
		}
	}

	@SuppressWarnings("deprecation")
	public static void encryptFile(OutputStream out, Map<String, CRMAppointmentDataFileDTO> cRMAppointmentDataMap, PGPPublicKey encKey, boolean armor,
			boolean withIntegrityCheck) throws IOException, NoSuchProviderException, PGPException {
 
		Security.addProvider(new BouncyCastleProvider());

		if (armor) {
			out = new ArmoredOutputStream(out);
		}

		ByteArrayOutputStream bOut = new ByteArrayOutputStream();

		PGPCompressedDataGenerator comData = new PGPCompressedDataGenerator(PGPCompressedData.ZIP);

		 writeFileToLiteralDataPGP(comData.open(bOut), PGPLiteralData.BINARY,
				 cRMAppointmentDataMap);

		comData.close();

		@SuppressWarnings("deprecation")
		PGPEncryptedDataGenerator cPk = new PGPEncryptedDataGenerator(PGPEncryptedData.CAST5, withIntegrityCheck,
				new SecureRandom(), "BC");

		cPk.addMethod(encKey);

		byte[] bytes = bOut.toByteArray();

		OutputStream cOut = cPk.open(out, bytes.length);

		cOut.write(bytes);

		cOut.close();

		out.close();
	}
	 
	public static void writeFileToLiteralDataPGP(OutputStream out, char fileType,
			Map<String, CRMAppointmentDataFileDTO> cRMAppointmentDataMap) throws IOException {

		PGPLiteralDataGenerator lData = new PGPLiteralDataGenerator();
		OutputStream pOut = lData.open(out, fileType, "pgpEncrypt", 99999999999L, new Date());
		pipeFileContentsPGP(cRMAppointmentDataMap, pOut, 4096);
	}

	private static void pipeFileContentsPGP(Map<String, CRMAppointmentDataFileDTO> cRMAppointmentDataMap, OutputStream pOut,
			int bufSize) throws IOException {

		cRMAppointmentDataMap.forEach((k, v) -> {
			try {
				pOut.write(v.toString().getBytes("UTF-8"));
				//pOut.write("\n".getBytes());
			} catch (IOException ie) {
				logPGP.warn("Caught at encrypt GPG Exception ", ie);
			}

		});

		pOut.close();

	}

	public static String getRemoveNewLineAndTilde(Object inputObj)
	{
		if(inputObj ==null)return "";
		String outputStr = inputObj.toString();
		outputStr = outputStr.replaceAll("[~|\n]", " ");
		return outputStr;
		 
	}
	
	public static String setNullToEmpty(Object inputObj)
	{
		if(inputObj ==null) return "";
		String outputStr = inputObj.toString();
		return outputStr;
		 
	}
	
	public static String setComletedStatusForEmpty(Object inputObj)
	{
		if(inputObj ==null) return "Pending Instructor Response";
		String outputStr = inputObj.toString();
		return outputStr;
		 
	}
	
	public static String setNullToEmptyAndNewLineToSpace(Object inputObj)
	{
		if(inputObj ==null) return "";
		String outputStr = inputObj.toString();
		outputStr = outputStr.replaceAll("[\n]", " ").toString();
		return outputStr;
		 
	}
	
	public static String hasNotes(Object inputObj)
	{
		if(inputObj != null)
		{
		    return "Has Notes";
		}else{ 
			return "No Notes"; 
		}
	}
	
	public static String hasRemarks(Object inputObj)
	{
		if(inputObj != null)
		{
		    return "Has Remarks";
		}else{ 
			return "No Remarks"; 
		}
	}

}