package com.guitarcenter.scheduler.dao.util;

/**
 * Created by jose<PERSON><PERSON> on 12/23/13
 */
public class EntryKey {

    private Long instructorId;
    private Long profileId;

    public EntryKey(Long instructorId, Long profileId) {
        this.instructorId = instructorId;
        this.profileId = profileId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof EntryKey)) return false;

        EntryKey entry = (EntryKey) o;

        if (instructorId == null && entry.instructorId != null) return false;
        if (profileId == null && entry.profileId != null) return false;
        if (instructorId != null && !instructorId.equals(entry.instructorId)) return false;
        if (profileId != null && !profileId.equals(entry.profileId)) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = 0;
        if (instructorId != null) {
            result = 31 * result + instructorId.hashCode();
        }
        if (profileId != null) {
            result = 31 * result + profileId.hashCode();
        }
        return result;
    }
}
