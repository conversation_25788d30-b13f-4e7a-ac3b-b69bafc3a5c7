/**
 * @Title: OnetimeDAO.java
 * @Package com.guitarcenter.scheduler.dao
 * @Description: 
 * Copyright: Copyright (c) 2014 
 * Company:
 * 
 * <AUTHOR>
 * @date May 29, 2014 1:59:50 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.dao;

import java.util.List;

import com.guitarcenter.scheduler.model.Onetime;

/**
 * @ClassName: OnetimeDAO
 * @Description: 
 * <AUTHOR>
 * @date May 29, 2014 1:59:51 PM
 *
 */
public interface OnetimeDAO  extends AbstractDAO<Onetime> {
	public List<Onetime> getOnetimeByInstructorId(long instructorId);
	public List<Onetime> getDisplayOnetimeByInstructorId(long instructorId);
	public boolean checkOnetimeAvailabilityByProfileId(String startDate, String startTime, String endTime, long profileId);
	public boolean checkOnetimeByTime(String startDate, String startTime,String endDate, String endTime, long instructorId);
	public List<Onetime> getOnetimeByTime(String startDate, String startTime, String endTime, long instructorId);
}
