package com.guitarcenter.scheduler.dao;

import java.util.List;

import com.guitarcenter.scheduler.dto.ServiceDTO;
import com.guitarcenter.scheduler.model.Service;
import com.guitarcenter.scheduler.model.enums.Enabled;

public interface ServiceDAO extends AbstractDAO<Service> {
	public List<Service> getServiceListBySiteId(long siteId);
	public List<ServiceDTO> getServiceDTOByProfileRoom(long profileId, long roomId);
	public void deleteProfileServiceByServiceId(long serviceId) throws Exception;
	public void deleteRoomTemplateServiceByServiceId(long serviceId) throws Exception;
	public void deleteRoomServiceByServiceId(long serviceId) throws Exception;

	public List<Service> findByProfileIdAndRoomIdAndEnabledHib(  long pProfileId,   long pRoomId,   Enabled pEnabled);
}
