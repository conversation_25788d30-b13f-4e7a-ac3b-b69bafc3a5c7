package com.guitarcenter.scheduler.dao;

import java.util.Date;
import java.util.List;
import com.guitarcenter.scheduler.dto.ProfileTimeOffDTO;
import com.guitarcenter.scheduler.model.ProfileTimeoff;
 

public interface ProfileTimeoffDAO extends AbstractDAO<ProfileTimeoff> {
	public List<ProfileTimeoff> getTimeoffByProfileId(long profileId,Date startDate,Date endDate) throws Exception;
	public List<ProfileTimeoff> getTimeoffByProfileIdInsAVL(long profileId,Date startDate,Date endDate) throws Exception;
	public List<ProfileTimeoff> getUpcomingTimeOffByProfileId(long profileId,Date startDate) throws Exception;
	public void saveOrUpdateProfileTimeOff(ProfileTimeoff profileTimeoff);
	public List<ProfileTimeoff> getDisplayProfileTimeoffById(long profileId);
	public ProfileTimeoff getProfileTimeoffByProfileTimeoffId(long profileTimeoffId);
	public  ProfileTimeOffDTO getAppointmentTimeForProfile(long profileId,String profileTimeOffDate);
	public List<ProfileTimeOffDTO> getAppointmentTimeListForProfileAndDate(long profileId,String profileTimeOffDate);
	public ProfileTimeOffDTO verifySingleAppointmentWithProfileTimeOff(long profileId,String profileTimeOffDate);
	public ProfileTimeOffDTO getProfileTimeOffIdbyDate(long profileId,String profileTimeOffDate);

}