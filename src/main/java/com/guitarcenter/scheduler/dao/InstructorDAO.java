package com.guitarcenter.scheduler.dao;

import java.util.List;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Person;

public interface InstructorDAO extends AbstractDAO<Instructor> {
	public Instructor getInstructorByTime(long instructorId, String startDate, String startTime, String endTime);
	public Instructor checkInstructorByAppointmentTime(long instructorId, String startDate, String startTime, String endTime);
	public Instructor checkInstructorByAppointmentRecurringTime(long instructorId, String startDate, String endDate, String startTime, String endTime);
	public boolean checkUpdateInstructorByAppointmentTime(long instructorId, String startDate, String startTime, String endTime, String excludeAppointmentIdParam);
	public boolean checkUpdateInstructorByAppointmentRecurringTime(long instructorId, String startDate, String endDate, String startTime, String endTime, String excludeAppointmentIdParam);
	public boolean checkInstructorByProfileActivityId(long instructorId, long activityId, long profileId);
	public Instructor checkInstructorByAppointmentTime(long instructorId, String startDate, String endDate, String startTime, String endTime);
	//---GSSP Instructor Mode update changes
	public void saveOrUpdateInstructor(Instructor pT,Person pUpdatedBy);
	public List<Instructor> getInstructorsAffectRecently(String updateDate);
	public List<Instructor> getFullInstructorsAffectRecently(String updateDate);
	
}
