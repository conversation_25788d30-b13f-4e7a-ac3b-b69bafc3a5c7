package com.guitarcenter.scheduler.dao;

import java.util.Date;
import java.util.List;

import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.AppointmentCancelReason;
import com.guitarcenter.scheduler.model.AppointmentTransactions;

public interface AppointmentDAO extends AbstractDAO<Appointment> {
	public boolean checkAppointmentByRoomActivity(long roomId, long activityId, long profileId);
	public boolean checkAppointmentByRoom(long roomId, long profileId);
	public boolean checkAppointmentByProfile(long profileId);
	public boolean checkStartTime(String startDate, String startTime);
	public boolean checkAppointmentByRoom(long roomId);
	public boolean checkAppointmentByRoomTemplateIdActivity(long roomTemplateId, long activityId);
	public boolean checkAppointmentByActivityId(long activityId);
	public boolean checkAppointmentByServiceId(long serviceId);
	public boolean checkAppointmentByProfileIdServiceId(long profileId, long serviceId);
	public boolean checkAppointmentByProfileIdActivityId(long profileId, long activityId);
	
	//Changes made for GSSP-250
	public List<AppointmentCancelReason> getCancelReason(String isRecurringId);
	public List<Appointment> findHoldAppointments(Date currentDateTime);
	public List<Appointment> findAppointmentbyId(Long appointmentId);

	public  List<AppointmentTransactions> findHoldAppointmentIdByTransactionId(String transcationId);
	public void saveOrUpdate(Appointment pT);
	//Changes made for GSSP-278
	/*public List<AppointmentCancelReason> getCancelReasonCode(long cancleReasonId);*/
}
