package com.guitarcenter.scheduler.dao;
 
import com.guitarcenter.scheduler.dto.CustomerDetailDTO;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.ParentDetails;

public interface CustomerDAO extends AbstractDAO<Customer> {
	public boolean checkCustomerByAppointmentTime(long customerId, String startDate, String startTime, String endTime, long profileId);
	public boolean checkCustomerByAppointmentRecurringTime(long customerId, String startDate, String endDate, String startTime, String endTime, long profileId);
	public boolean checkUpdateCustomerByAppointmentTime(long customerId, String startDate, String startTime, String endTime, long profileId, String excludeAppointmentIdParam);
	public boolean checkUpdateCustomerByAppointmentRecurringTime(long customerId, String startDate, String endDate, String startTime, String endTime, long profileId, String excludeAppointmentIdParam);
	public ParentDetails getParentIdFromCustomerTable(Long customerId);
	public CustomerDetailDTO  getCustomerDetailsById(Long customerId);
	public void saveOrUpdateInstructor(Customer ct);
	
	public Customer getCustomerById(Long customerId);
}
