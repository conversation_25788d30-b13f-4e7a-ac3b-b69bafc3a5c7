package com.guitarcenter.scheduler.dao;

import java.util.List;

import com.guitarcenter.scheduler.model.Room;

public interface RoomDA<PERSON> extends AbstractDAO<Room> {
	public Room getRoomByAppointmentTime(long roomId, String startDate, String startTime, String endTime, long profileId);
	public Room getRoomByAppointmentRecurringTime(long roomId, String startDate, String endDate, String startTime, String endTime, long profileId);
	public List<Room> getSplitRoomsByParentId(long roomId);
	public List<Room> getSplitRoomByTime(long roomId, String startDate, String startTime, String endTime);
	public List<Room> getSplitRoomByRecurringTime(long roomId, String startDate, String endDate, String startTime, String endTime);
	public boolean getUpdateRoomByAppointmentTime(long roomId, String startDate, String startTime, String endTime, long profileId, String excludeAppointmentIdParam);
	public boolean getUpdateRoomByAppointmentRecurringTime(long roomId, String startDate, String endDate, String startTime, String endTime, long profileId, String excludeAppointmentIdParam);
}
