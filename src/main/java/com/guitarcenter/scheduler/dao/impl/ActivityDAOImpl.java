package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SERVICE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.ActivityDAO;
import com.guitarcenter.scheduler.dao.criterion.ActivityCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.enums.Canceled;
import com.guitarcenter.scheduler.model.enums.Enabled;

@Repository("activityDAO")
public class ActivityDAOImpl extends AbstractDAOImpl<Activity> implements ActivityDAO {

	@PersistenceContext
	private EntityManager entityManager;

	public ActivityDAOImpl() {
		super(Activity.class);
	}

	@Override
	protected void updateAuditor(Activity activity, Person updatedBy) {
		activity.setUpdated(new Date());
		activity.setUpdatedBy(updatedBy);
	}

	@Override
	protected void fetchOne(Activity activity, int fetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, fetchMode, activity.getUpdatedBy());
		addFetchCriteria(FETCH_SERVICE, fetchMode, activity.getService());
		addFetchCriteria(FETCH_SITE, fetchMode, activity.getSite());
	}

	@Override
	protected void fetchMany(Activity activity, int fetchMode) {
		// If needed, implement logic to fetch associated collections
	}

	@Override
	protected Criterion<Activity, Activity> getCriterionInstance() {
		return ActivityCriterion.getInstance();
	}

	@Override
	public Activity getActivityByAppointmentTime(long activityId, String startDate,
												 String startTime, String endTime, long profileId) {
		TypedQuery<Activity> query = entityManager.createNamedQuery("activity.getActivityByAppointmentTime", Activity.class);
		query.setParameter(1, startDate + " " + startTime);
		query.setParameter(2, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(3, startDate + " " + endTime);
		query.setParameter(4, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(5, startDate + " " + startTime);
		query.setParameter(6, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(7, startDate + " " + endTime);
		query.setParameter(8, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(9, startDate + " " + startTime);
		query.setParameter(10, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(11, startDate + " " + endTime);
		query.setParameter(12, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(13, profileId);
		query.setParameter(14, Canceled.N.name());
		query.setParameter(15, activityId);


		List<Activity> resultList = query.getResultList();
		return resultList.isEmpty() ? null : resultList.get(0);
	}

	@Override
	public Activity getActivityByAppointmentRecurringTime(long activityId, String startDate, String endDate,
														  String startTime, String endTime, long profileId) {
		TypedQuery<Activity> query = entityManager.createNamedQuery("activity.getActivityByAppointmentRecurringTime", Activity.class);
		query.setParameter(1, startDate);
		query.setParameter(2, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(3, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(4, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(5, startDate);
		query.setParameter(6, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(7, endDate);
		query.setParameter(8, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(9, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(10, startTime);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(12, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(13, endTime);
		query.setParameter(14, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(15, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(16, startTime);
		query.setParameter(17, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(18, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(19, endTime);
		query.setParameter(20, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(21, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(22, startTime);
		query.setParameter(23, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(24, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(25, endTime);
		query.setParameter(26, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(27, profileId);
		query.setParameter(28, Canceled.N.name());
		query.setParameter(29, activityId);


		List<Activity> resultList = query.getResultList();
		return resultList.isEmpty() ? null : resultList.get(0);
	}

	@Override
	public List<ActivityDTO> getActivityDTOByProfileRoom(long profileId, long roomId) {
		StringBuilder sb = new StringBuilder("SELECT ra.activity_id, a.activity_name ")
				.append("FROM room_activities ra ")
				.append("JOIN profile_activity pa ON ra.activity_id = pa.activity_id ")
				.append("JOIN activity a ON ra.activity_id = a.activity_id ")
				.append("WHERE ra.room_id = :roomId ")
				.append("AND pa.profile_id = :profileId ")
				.append("AND pa.enabled = :enabled");

		Query query = entityManager.createNativeQuery(sb.toString());
		query.setParameter("roomId", roomId);
		query.setParameter("profileId", profileId);
		query.setParameter("enabled", Enabled.Y.toString());

		@SuppressWarnings("unchecked")
		List<Object[]> resultList = query.getResultList();
		List<ActivityDTO> dtoList = new ArrayList<>();
		for (Object[] row : resultList) {
			ActivityDTO dto = new ActivityDTO(Long.parseLong(row[0].toString()), row[1].toString());
			dtoList.add(dto);
		}
		return dtoList;
	}

	@Override
	public void deleteInstructorAvtivityByActivityId(long activityId) throws Exception {
		String sql = "DELETE FROM instructor_activities WHERE activity_id = :activityId";
		executeNativeUpdateQuery(sql, "activityId", activityId);
	}

	@Override
	public void deleteProfileAvtivityByActivityId(long activityId) throws Exception {
		String sql = "DELETE FROM profile_activity WHERE activity_id = :activityId";
		executeNativeUpdateQuery(sql, "activityId", activityId);
	}

	@Override
	public void deleteRoomTemplateAvtivityByActivityId(long activityId) throws Exception {
		String sql = "DELETE FROM room_template_activities WHERE activity_id = :activityId";
		executeNativeUpdateQuery(sql, "activityId", activityId);
	}

	@Override
	public void deleteRoomAvtivityByActivityId(long activityId) throws Exception {
		String sql = "DELETE FROM room_activities WHERE activity_id = :activityId";
		executeNativeUpdateQuery(sql, "activityId", activityId);
	}

	@Override
	public void deleteInstructorAvtivityByServiceId(long serviceId) throws Exception {
		String sql = "DELETE FROM instructor_activities i WHERE EXISTS (SELECT * FROM activity a WHERE a.activity_id = i.activity_id AND a.service_id = :serviceId)";
		executeNativeUpdateQuery(sql, "serviceId", serviceId);
	}

	@Override
	public void deleteProfileAvtivityByServiceId(long serviceId) throws Exception {
		String sql = "DELETE FROM profile_activity p WHERE EXISTS (SELECT * FROM activity a WHERE a.activity_id = p.activity_id AND a.service_id = :serviceId)";
		executeNativeUpdateQuery(sql, "serviceId", serviceId);
	}

	@Override
	public void deleteRoomTemplateAvtivityByServiceId(long serviceId) throws Exception {
		String sql = "DELETE FROM room_template_activities r WHERE EXISTS (SELECT * FROM activity a WHERE a.activity_id = r.activity_id AND a.service_id = :serviceId)";
		executeNativeUpdateQuery(sql, "serviceId", serviceId);
	}

	@Override
	public void deleteRoomAvtivityByServiceId(long serviceId) throws Exception {
		String sql = "DELETE FROM room_activities r WHERE EXISTS (SELECT * FROM activity a WHERE a.activity_id = r.activity_id AND a.service_id = :serviceId)";
		executeNativeUpdateQuery(sql, "serviceId", serviceId);
	}

	private void executeNativeUpdateQuery(String sql, String paramName, long paramValue) throws Exception {
		Query query = entityManager.createNativeQuery(sql);
		query.setParameter(paramName, paramValue);
		try {
			query.executeUpdate();
		} catch (Exception e) {
			throw new Exception(e.getMessage());
		}
	}
}
