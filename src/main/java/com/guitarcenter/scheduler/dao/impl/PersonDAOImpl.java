package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;

//import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.PersonDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.PersonCriterion;
import com.guitarcenter.scheduler.model.Person;

@Repository("personDAO")
public class PersonDAOImpl extends AbstractDAOImpl<Person> implements PersonDAO {

	public PersonDAOImpl() {
		super(Person.class);
	}



	@Override
	protected void updateAuditor(Person pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(Person pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
	}


	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) {
	 * addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy",
	 * FetchMode.JOIN, pCriteria); }
	 */



	@Override
	protected void fetchMany(Person pResult, int pFetchMode) {
	}



	@Override
	protected Criterion<Person, Person> getCriterionInstance() {
		return PersonCriterion.getInstance();
	}
}
