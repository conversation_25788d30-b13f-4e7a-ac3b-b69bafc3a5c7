package com.guitarcenter.scheduler.dao.impl;

import java.util.List;

import com.guitarcenter.scheduler.model.*;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.criteria.*;
//import org.hibernate.Criteria;
import org.hibernate.Session;
//import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.InstoreAvailabilityDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Repository("instoreAvailabilityDAO")
public class InstoreAvailabilityDAOImpl extends AbstractDAOImpl<InstoreAvailability> implements InstoreAvailabilityDAO {

	@PersistenceContext
	private EntityManager entityManager;
 
	public InstoreAvailabilityDAOImpl() {
		super(InstoreAvailability.class);
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public List<InstoreAvailability> getInstoreAvailabilityByInstructorId(long instructorId) {
		// Use EntityManager instead of Session
		CriteriaBuilder builder = entityManager.getCriteriaBuilder();
		CriteriaQuery<InstoreAvailability> query = builder.createQuery(InstoreAvailability.class);
		Root<InstoreAvailability> root = query.from(InstoreAvailability.class);

		// Join with Instructor entity
		Join<InstoreAvailability, Instructor> instructorJoin = root.join("instructor");

		// Create predicate for instructorId
		Predicate instructorIdPredicate = builder.equal(instructorJoin.get("instructorId"), instructorId);
		query.select(root).where(instructorIdPredicate);

		// Execute the query using EntityManager
		return entityManager.createQuery(query).getResultList();
	}



	@Override
	public List<InstoreAvailability> getDisplayInstoreAvailabilityeByInstructorId(long instructorId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public boolean checkInstoreAvailabilityByProfileId(String startDate, String startTime, String endTime,
			long profileId) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public boolean checkInstoreAvailabilityByTime(String startDate, String startTime, String endDate, String endTime,
			long instructorId) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public List<Onetime> getInstoreAvailabilityByTime(String startDate, String startTime, String endTime,
			long instructorId) {
		// TODO Auto-generated method stub
		return null;
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void saveOrUpdate(InstoreAvailability pT, Person pUpdatedBy) {
		// Update auditor information
		updateAuditor(pT, pUpdatedBy);

		// Use EntityManager instead of Session
		if (pT.getInstoreAvailabilityId() == null) {
			entityManager.persist(pT); // Save if new entity (id is null)
		} else {
			entityManager.merge(pT); // Update if entity already exists (id is not null)
		}
	}

	@Override
	protected void updateAuditor(InstoreAvailability pT, Person pUpdatedBy) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchOne(InstoreAvailability pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) { //
	 * TODO Auto-generated method stub
	 * 
	 * }
	 */

	@Override
	protected void fetchMany(InstoreAvailability pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected Criterion<InstoreAvailability, InstoreAvailability> getCriterionInstance() {
		// TODO Auto-generated method stub
		return null;
	}

	 

}
