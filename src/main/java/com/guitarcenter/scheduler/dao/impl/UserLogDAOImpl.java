package com.guitarcenter.scheduler.dao.impl;

//import org.hibernate.Criteria;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.UserLogDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.UserLog;

@Repository("userLogDAO")
public class UserLogDAOImpl extends AbstractDAOImpl<UserLog> implements UserLogDAO {



	public UserLogDAOImpl() {
		super(UserLog.class);
	}

	@Override
	protected void updateAuditor(UserLog pT, Person pUpdatedBy) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchOne(UserLog pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) { //
	 * TODO Auto-generated method stub
	 * 
	 * }
	 */

	@Override
	protected void fetchMany(User<PERSON>og pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected Criterion<UserLog, UserLog> getCriterionInstance() {
		// TODO Auto-generated method stub
		return null;
	}

	

 
}
