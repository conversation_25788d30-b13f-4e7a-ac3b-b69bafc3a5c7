package com.guitarcenter.scheduler.dao.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import com.guitarcenter.scheduler.dao.AppointmentLogDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.util.HibernateAwareObjectMapper;
import com.guitarcenter.scheduler.model.AppointmentLog;
import com.guitarcenter.scheduler.model.Person;

@Repository("appointmentLogDAO")
public class AppointmentLogDAOImpl extends AbstractDAOImpl<AppointmentLog> implements AppointmentLogDAO {

	private static final Logger LOGGER = LoggerFactory.getLogger(AbstractDAOImpl.class);

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	@Qualifier("JSONMapper")
	private HibernateAwareObjectMapper mObjectMapper;

	public AppointmentLogDAOImpl() {
		super(AppointmentLog.class);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void saveEntity(AppointmentLog pT, Person pUpdatedBy) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pT.getClass().getSimpleName(),
					mObjectMapper.toJSON(pT), pUpdatedBy };
			LOGGER.debug("{}.save({} pT, Person pUpdatedBy) : entry, pT is [{}] pUpdatedBy is [{}]", objects);
		}

		updateAuditor(pT, pUpdatedBy); // Update auditing fields
		entityManager.persist(pT); // Persist the entity using JPA

		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pT.getClass().getSimpleName() };
			LOGGER.debug("{}.save({} pT, Person pUpdatedBy) : exit", objects);
		}
	}

	@Override
	protected void updateAuditor(AppointmentLog pT, Person pUpdatedBy) {
	//	pT.setUpdatedBy(pUpdatedBy); // Assuming AppointmentLog has an updatedBy field
	//	pT.setUpdatedDate(new java.util.Date()); // Assuming AppointmentLog has an updatedDate field
	}

	@Override
	protected void fetchOne(AppointmentLog pResult, int pFetchMode) {
		// Implement fetch logic if needed for lazy-loaded fields
	}

	@Override
	protected void fetchMany(AppointmentLog pResult, int pFetchMode) {
		// Implement fetch logic if needed for collections
	}

	@Override
	protected Criterion<AppointmentLog, AppointmentLog> getCriterionInstance() {
		// Return the appropriate criterion instance if used
		return null; // This can be implemented when necessary
	}
}
