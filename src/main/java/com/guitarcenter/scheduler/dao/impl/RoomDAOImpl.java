package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION_PROFILE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_MORE_ACTIVITIES;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_MORE_SERVICES;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_PARENT_ROOM;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROOM_NUMBER;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROOM_SIZE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROOM_TEMPLATE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROOM_TYPE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

//import org.hibernate.Criteria;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import org.hibernate.FetchMode;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.RoomDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.RoomCriterion;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.enums.Canceled;

@Repository("roomDAO")
public class RoomDAOImpl extends AbstractDAOImpl<Room> implements RoomDAO {

	@PersistenceContext
	private EntityManager entityManager;

	public RoomDAOImpl() {
		super(Room.class);
	}



	@Override
	protected void updateAuditor(Room pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(Room pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_LOCATION_PROFILE, pFetchMode, pResult.getLocationProfile());
		addFetchCriteria(FETCH_PARENT_ROOM, pFetchMode, pResult.getParentRoom());
		addFetchCriteria(FETCH_ROOM_NUMBER, pFetchMode, pResult.getRoomNumber());
		addFetchCriteria(FETCH_ROOM_SIZE, pFetchMode, pResult.getRoomSize());
		addFetchCriteria(FETCH_ROOM_TEMPLATE, pFetchMode, pResult.getRoomTemplate());
		addFetchCriteria(FETCH_ROOM_TYPE, pFetchMode, pResult.getRoomType());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}



	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) {
	 * addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy",
	 * FetchMode.JOIN, pCriteria); addFetchCriteria(FETCH_LOCATION_PROFILE,
	 * pFetchMode, "locationProfile", FetchMode.JOIN, pCriteria);
	 * addFetchCriteria(FETCH_PARENT_ROOM, pFetchMode, "parentRoom", FetchMode.JOIN,
	 * pCriteria); addFetchCriteria(FETCH_ROOM_NUMBER, pFetchMode, "roomNumber",
	 * FetchMode.JOIN, pCriteria); addFetchCriteria(FETCH_ROOM_SIZE, pFetchMode,
	 * "roomSize", FetchMode.JOIN, pCriteria); addFetchCriteria(FETCH_ROOM_TEMPLATE,
	 * pFetchMode, "roomTemplate", FetchMode.JOIN, pCriteria);
	 * addFetchCriteria(FETCH_ROOM_TYPE, pFetchMode, "roomType", FetchMode.JOIN,
	 * pCriteria); addFetchCriteria(FETCH_SITE, pFetchMode, "site", FetchMode.JOIN,
	 * pCriteria); }
	 */



	@Override
	protected void fetchMany(Room pResult, int pFetchMode) {
		addFetchCriteria(FETCH_MORE_SERVICES, pFetchMode, pResult.getServices());
		addFetchCriteria(FETCH_MORE_ACTIVITIES, pFetchMode, pResult.getActivities());
	}



	@Override
	protected Criterion<Room, Room> getCriterionInstance() {
		return RoomCriterion.getInstance();
	}

	@Override
	public Room getRoomByAppointmentTime(long roomId, String startDate, String startTime, String endTime, long profileId) {
		// Inject the EntityManager


		// Create the query using named query defined in the entity
		Query query = entityManager.createNamedQuery("room.getRoomByAppointmentTime");

		// Set the parameters
		query.setParameter(1, startDate + " " + startTime);
		query.setParameter(2, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(3, startDate + " " + endTime);
		query.setParameter(4, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(5, startDate + " " + startTime);
		query.setParameter(6, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(7, startDate + " " + endTime);
		query.setParameter(8, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(9, startDate + " " + startTime);
		query.setParameter(10, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(11, startDate + " " + endTime);
		query.setParameter(12, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		//BigDecimal profileId1 = BigDecimal.valueOf(profileId);
		query.setParameter(13, profileId);
		query.setParameter(14, Canceled.N.name());
		query.setParameter(15, Canceled.H.name());
		//BigDecimal roomId1 = BigDecimal.valueOf(roomId);
		query.setParameter(16, roomId);



		// Execute the query and return the result list
		@SuppressWarnings("unchecked")
		List<Room> rooms = query.getResultList();

		return rooms.isEmpty() ? null : rooms.get(0);
	}



	/*
	  * <p>Title: getRoomByAppointmentRecurringTime</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @return
	  * @see com.guitarcenter.scheduler.dao.RoomDAO#getRoomByAppointmentRecurringTime(long, java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	  */
	@Override
	public Room getRoomByAppointmentRecurringTime(long roomId, String startDate,
			String endDate, String startTime, String endTime, long profileId) {
		//Session session = super.getSessionFactory().getCurrentSession();
		Query query = entityManager.createNamedQuery("room.getRoomByAppointmentRecurringTime");
		query.setParameter(1, startDate);
		query.setParameter(2, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(3, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(4, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(5, startDate);
		query.setParameter(6, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(7, endDate);
		query.setParameter(8, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(9, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(10, startTime);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(12, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(13, endTime);
		query.setParameter(14, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(15, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(16, startTime);
		query.setParameter(17, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(18, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(19, endTime);
		query.setParameter(20, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(21, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(22, startTime);
		query.setParameter(23, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(24, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(25, endTime);
		query.setParameter(26, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		BigDecimal profileId1 = BigDecimal.valueOf(profileId);
		query.setParameter(27, profileId1);
// String canceleds = Canceled.H+","+Canceled.N;
// query.setParameter(28, canceleds);
		query.setParameter(28, Canceled.N.name());
		query.setParameter(29, Canceled.H.name());
		BigDecimal roomId1 = BigDecimal.valueOf(roomId);
		query.setParameter(30, roomId1);

		@SuppressWarnings("unchecked")
        List<Room> l = query.getResultList();
		return l.size()==0?null:l.get(0);
	}
	
	/**
	  * <p>Title: getSplitRoomsByParentId</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.RoomDAO#getSplitRoomsByParentId(long)
	  */
	@SuppressWarnings("unchecked")
    @Override
	public List<Room> getSplitRoomsByParentId(long roomId) {
	//	Session session = super.getSessionFactory().getCurrentSession();
/*		Query query = entityManager.createQuery("from Room where parentRoom.roomId = ?");
		query.setParameter(0, roomId);
		return query.getResultList();*/
// parentRoom.roomId 5055
		String sql = "SELECT * FROM Room WHERE PARENT_ID = :roomId";
		List<Room> instructors = entityManager.createNativeQuery(sql, Room.class)
				.setParameter("roomId", roomId)
				.getResultList();
		return instructors;
	}



	/**
	  * <p>Title: getSplitRoomByTime</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @param startDate
	  * @param startTime
	  * @param endTime
	  * @return
	  * @see com.guitarcenter.scheduler.dao.RoomDAO#getSplitRoomByTime(long, java.lang.String, java.lang.String, java.lang.String)
	  */
	@Override
	public List<Room> getSplitRoomByTime(long roomId, String startDate,
			String startTime, String endTime) {
	//	Session session = super.getSessionFactory().getCurrentSession();
		Query query = entityManager.createNamedQuery("room.getSplitRoomByTime");
		query.setParameter(0, startDate+" "+startTime);
		query.setParameter(1, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(2, startDate+" "+endTime);
		query.setParameter(3, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(4, startDate+" "+startTime);
		query.setParameter(5, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(6, startDate+" "+endTime);
		query.setParameter(7, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(8, startDate+" "+startTime);
		query.setParameter(9, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(10, startDate+" "+endTime);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(12, roomId);
		@SuppressWarnings("unchecked")
        List<Room> l = query.getResultList();
		return l;
	}



	/**
	  * <p>Title: getSplitRoomByRecurringTime</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @return
	  * @see com.guitarcenter.scheduler.dao.RoomDAO#getSplitRoomByRecurringTime(long, java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	  */
	@Override
	public List<Room> getSplitRoomByRecurringTime(long roomId,
			String startDate, String endDate, String startTime, String endTime) {
		//Session session = super.getSessionFactory().getCurrentSession();
		Query query = entityManager.createNamedQuery("room.getSplitRoomByRecurringTime");
		query.setParameter(0, startDate);
		query.setParameter(1, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(2, startDate);
		query.setParameter(3, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(4, endDate);
		query.setParameter(5, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(6, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(7, startTime);
		query.setParameter(8, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(9, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(10, endTime);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(12, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(13, startTime);
		query.setParameter(14, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(15, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(16, endTime);
		query.setParameter(17, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(18, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(19, startTime);
		query.setParameter(20, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(21, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(22, endTime);
		query.setParameter(23, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(24, roomId);
		@SuppressWarnings("unchecked")
        List<Room> l = query.getResultList();
		return l;
	}



	/*@Override
	public boolean getUpdateRoomByAppointmentTime(long roomId, String startDate,
			String startTime, String endTime, long profileId,
			String excludeAppointmentIdParam) {
		//Session session = super.getSessionFactory().getCurrentSession();
		String queryString = entityManager.createNamedQuery("room.getUpdateRoomByAppointmentTime").toString();
		String replaceParameter = SystemUtil.REPLACE_CONDITION.replace("&PARAMETER", excludeAppointmentIdParam);
		String sql = excludeAppointmentIdParam.length()==0?queryString.replace("&REPLACECONDITION", ""):queryString.replace("&REPLACECONDITION", replaceParameter);
		Query query = entityManager.createNativeQuery(sql);
		query.setParameter(0, startDate+" "+startTime);
		query.setParameter(1, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(2, startDate+" "+endTime);
		query.setParameter(3, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(4, startDate+" "+startTime);
		query.setParameter(5, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(6, startDate+" "+endTime);
		query.setParameter(7, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(8, startDate+" "+startTime);
		query.setParameter(9, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(10, startDate+" "+endTime);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(12, profileId);
		query.setParameter(13, Canceled.N);
		query.setParameter(14, Canceled.H);
		query.setParameter(15, roomId);
		Object o = query.getSingleResult();
		return Boolean.parseBoolean(o.toString());
	}*/

	@Override
	public boolean getUpdateRoomByAppointmentTime(long roomId, String startDate,
												  String startTime, String endTime, long profileId,
												  String excludeAppointmentIdParam) {
	//	EntityManager entityManager = // obtain EntityManager instance
				String queryString = entityManager.createNamedQuery("Room.getUpdateRoomByAppointmentTime").unwrap(org.hibernate.query.NativeQuery.class).getQueryString();

		// Replace `&REPLACECONDITION` in the query if necessary
		String replaceParameter = SystemUtil.REPLACE_CONDITION.replace("&PARAMETER", excludeAppointmentIdParam);
		String sql = excludeAppointmentIdParam.isEmpty() ? queryString.replace("&REPLACECONDITION", "") : queryString.replace("&REPLACECONDITION", replaceParameter);

		Query query = entityManager.createNativeQuery(sql, Boolean.class);
		query.setParameter(1, startDate + " " + startTime);
		query.setParameter(2, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(3, startDate + " " + endTime);
		query.setParameter(4, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(5, startDate + " " + startTime);
		query.setParameter(6, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(7, startDate + " " + endTime);
		query.setParameter(8, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(9, startDate + " " + startTime);
		query.setParameter(10, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(11, startDate + " " + endTime);
		query.setParameter(12, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(13, profileId);
		query.setParameter(14, Canceled.N.toString());
		query.setParameter(15, Canceled.H.toString());
		query.setParameter(16, roomId);

		Boolean result = (Boolean) query.getSingleResult();
		return result != null && result;
	}



	@Override
	public boolean getUpdateRoomByAppointmentRecurringTime(long roomId,
			String startDate, String endDate, String startTime, String endTime,
			long profileId, String excludeAppointmentIdParam) {
	//	Session session = super.getSessionFactory().getCurrentSession();

		Session session = entityManager.unwrap(Session.class);

		String queryString = session.getNamedQuery("room.getUpdateRoomByAppointmentRecurringTime").getQueryString();

		//String queryString = entityManager.createNamedQuery("room.getUpdateRoomByAppointmentRecurringTime").toString();
		String replaceParameter = SystemUtil.REPLACE_CONDITION.replace("&PARAMETER", excludeAppointmentIdParam);
		String sql = excludeAppointmentIdParam.length()==0?queryString.replace("&REPLACECONDITION", ""):queryString.replace("&REPLACECONDITION", replaceParameter);
		Query query = entityManager.createNativeQuery(sql);
		query.setParameter(1, startDate);
		query.setParameter(2, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(3, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(4, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(5, startDate);
		query.setParameter(6, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(7, endDate);
		query.setParameter(8, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(9, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(10, startTime);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(12, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(13, endTime);
		query.setParameter(14, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(15, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(16, startTime);
		query.setParameter(17, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(18, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(19, endTime);
		query.setParameter(20, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(21, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(22, startTime);
		query.setParameter(23, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(24, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(25, endTime);
		query.setParameter(26, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(27, profileId);
		query.setParameter(28, Canceled.N.name());
		query.setParameter(29, Canceled.H.name());
		query.setParameter(30, roomId);

		Object o = query.getSingleResult();
		return Boolean.parseBoolean(o.toString());
	}
}
