package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROLE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;
import java.util.List;

//import org.hibernate.Criteria;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.PersonRoleDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.PersonRoleCriterion;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.PersonRole;

@Repository("personRoleDAO")
public class PersonRoleDAOImpl extends AbstractDAOImpl<PersonRole> implements PersonRoleDAO {

	public PersonRoleDAOImpl() {
		super(PersonRole.class);
	}



	@Override
	protected void updateAuditor(PersonRole pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(PersonRole pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_LOCATION, pFetchMode, pResult.getLocation());
		addFetchCriteria(FETCH_PERSON, pFetchMode, pResult.getPerson());
		addFetchCriteria(FETCH_ROLE, pFetchMode, pResult.getRole());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}



	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) { throw
	 * new UnsupportedOperationException(); }
	 */



	@Override
	protected void fetchMany(PersonRole pResult, int pFetchMode) {
	}



	@Override
	protected Criterion<PersonRole, PersonRole> getCriterionInstance() {
		return PersonRoleCriterion.getInstance();
	}



	@Override
	public List<PersonRole> search(PersonRole pExample) {
		throw new UnsupportedOperationException();
	}



	@Override
	public List<PersonRole> search(PersonRole pExample, int pFetchMode) {
		throw new UnsupportedOperationException();
	}
}
