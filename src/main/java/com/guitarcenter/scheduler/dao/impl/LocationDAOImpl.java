package com.guitarcenter.scheduler.dao.impl;

import com.guitarcenter.scheduler.dao.LocationDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.LocationCriterion;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.Person;
import org.springframework.stereotype.Repository;

import java.util.Date;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.*;

@Repository("locationDAO")
public class LocationDAOImpl extends AbstractDAOImpl<Location> implements LocationDAO {

	public LocationDAOImpl() {
		super(Location.class);
	}



	@Override
	protected void updateAuditor(Location pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(Location pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_LOCATION_PROFILE, pFetchMode, pResult.getLocationProfile());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}



	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) {
	 * addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy",
	 * FetchMode.JOIN, pCriteria); addFetchCriteria(FETCH_LOCATION_PROFILE,
	 * pFetchMode, "locationProfile", FetchMode.JOIN, pCriteria);
	 * addFetchCriteria(FETCH_SITE, pFetchMode, "site", FetchMode.JOIN, pCriteria);
	 * }
	 */



	@Override
	protected void fetchMany(Location pResult, int pFetchMode) {
	}



	@Override
	protected Criterion<Location, Location> getCriterionInstance() {
		return LocationCriterion.getInstance();
	}
}
