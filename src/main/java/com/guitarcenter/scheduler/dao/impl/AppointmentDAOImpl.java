package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.guitarcenter.scheduler.model.enums.Canceled;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.criterion.AppointmentCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.*;

@Repository("appointmentDAO")
public class AppointmentDAOImpl extends AbstractDAOImpl<Appointment> implements AppointmentDAO {

	@PersistenceContext
	private EntityManager entityManager;

	public AppointmentDAOImpl() {
		super(Appointment.class);
	}

	@Override
	protected void updateAuditor(Appointment appointment, Person updatedBy) {
		appointment.setUpdated(new Date());
		appointment.setUpdatedBy(updatedBy);
	}

	@Override
	protected void fetchOne(Appointment appointment, int fetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, fetchMode, appointment.getUpdatedBy());
		addFetchCriteria(FETCH_ACTIVITY, fetchMode, appointment.getActivity());
		addFetchCriteria(FETCH_APPOINTMENT_SERIES, fetchMode, appointment.getAppointmentSeries());
		addFetchCriteria(FETCH_INSTRUCTOR, fetchMode, appointment.getInstructor());
		addFetchCriteria(FETCH_LOCATION_PROFILE, fetchMode, appointment.getLocationProfile());
		addFetchCriteria(FETCH_ROOM, fetchMode, appointment.getRoom());
		addFetchCriteria(FETCH_SITE, fetchMode, appointment.getSite());
	}

	@Override
	protected void fetchMany(Appointment appointment, int fetchMode) {
		addFetchCriteria(FETCH_MORE_CUSTOMERS, fetchMode, appointment.getCustomers());
	}

	@Override
	protected Criterion<Appointment, Appointment> getCriterionInstance() {
		return AppointmentCriterion.getInstance();
	}

	@Override
	public boolean checkAppointmentByRoomActivity(long roomId, long activityId, long profileId) {
		//TypedQuery<Long> query = entityManager.createNamedQuery("appointment.getAppointmentByRoomActivityProfile", Long.class);
		//5055
		TypedQuery<BigDecimal> query = entityManager.createNamedQuery("appointment.getAppointmentByRoomActivityProfile", BigDecimal.class);
		query.setParameter(1, SystemUtil.VALIDATION_SCOPE_MONTH);
		query.setParameter(2, roomId);
		query.setParameter(3, activityId);
		query.setParameter(4, profileId);
		query.setParameter(5, 'N');
		query.setParameter(6, 'H');
		return query.getResultList().isEmpty();
	}

	@Override
	public boolean checkAppointmentByRoom(long roomId, long profileId) {
		TypedQuery<BigDecimal> query = entityManager.createNamedQuery("appointment.getAppointmentByRoom", BigDecimal.class);
		query.setParameter(1, SystemUtil.VALIDATION_SCOPE_MONTH);
		query.setParameter(2, roomId);
		query.setParameter(3, profileId);
		query.setParameter(4, 'N');
		query.setParameter(5, 'H');
		return query.getResultList().isEmpty();
	}

	@Override
	public boolean checkAppointmentByProfile(long profileId) {
		TypedQuery<Long> query = entityManager.createNamedQuery("appointment.getAppointmentByProfile", Long.class);
		query.setParameter(1, SystemUtil.VALIDATION_SCOPE_MONTH);
		query.setParameter(2, profileId);
		query.setParameter(3, Canceled.N);
		query.setParameter(4, Canceled.H);
		return query.getResultList().isEmpty();
	}

	@Override
	public boolean checkStartTime(String startDate, String startTime) {
		// 5055 at
		Query query = entityManager.createNamedQuery("appointment.checkStartTime");
		query.setParameter(1, startDate + " " + startTime);
		query.setParameter(2, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		boolean flg  = (boolean) query.getSingleResult();
		return flg;
	}

	@Override
	public boolean checkAppointmentByRoom(long roomId) {
		TypedQuery<Appointment> query = entityManager.createQuery("FROM Appointment WHERE room.roomId = :roomId AND canceled IN ('N', 'H')", Appointment.class);
		query.setParameter("roomId", roomId);
		return query.getResultList().isEmpty();
	}

	@Override
	public boolean checkAppointmentByRoomTemplateIdActivity(long roomTemplateId, long activityId) {
		TypedQuery<Long> query = entityManager.createNamedQuery("appointment.getAppointmentByRoomTemplateActivity", Long.class);
		query.setParameter(1, SystemUtil.VALIDATION_SCOPE_MONTH);
		query.setParameter(2, roomTemplateId);
		query.setParameter(3, activityId);
		return query.getResultList().isEmpty();
	}

	@Override
	public boolean checkAppointmentByActivityId(long activityId) {
		TypedQuery<Appointment> query = entityManager.createQuery("FROM Appointment WHERE activity.activityId = :activityId", Appointment.class);
		query.setParameter("activityId", activityId);
		return !query.getResultList().isEmpty();
	}

	@Override
	public boolean checkAppointmentByServiceId(long serviceId) {
		// Create a native query
		Query query = entityManager.createNativeQuery(
				"SELECT 1 FROM appointment a, activity at WHERE a.activity_id = at.activity_id AND at.service_id = :serviceId"
		);

		// Set parameters
		query.setParameter("serviceId", serviceId);

		// Execute query and return result
		List<Object[]> resultList = query.getResultList(); // Expecting Object[] because it is a native query returning multiple columns
		return !resultList.isEmpty(); // Check if there are any results
	}

	@Override
	public boolean checkAppointmentByProfileIdServiceId(long profileId, long serviceId) {
		// Create a native query
		Query query = entityManager.createNativeQuery(
				"SELECT 1 FROM appointment a, activity at WHERE a.activity_id = at.activity_id AND at.service_id = :serviceId AND a.profile_id = :profileId"
		);

		// Set parameters
		query.setParameter("serviceId", serviceId);
		query.setParameter("profileId", profileId);

		// Execute query and return result
		List<Object[]> resultList = query.getResultList();
		return !resultList.isEmpty(); // Check if there are any results
	}

	@Override
	public boolean checkAppointmentByProfileIdActivityId(long profileId, long activityId) {
		// Create a native query
		Query query = entityManager.createNativeQuery(
				"SELECT 1 FROM appointment a WHERE a.activity_id = :activityId AND a.profile_id = :profileId"
		);

		// Set parameters
		query.setParameter("activityId", activityId);
		query.setParameter("profileId", profileId);

		// Execute query and return result
		List<Object[]> resultList = query.getResultList();
		return !resultList.isEmpty();
	}


	@Override
	public List<AppointmentCancelReason> getCancelReason(String isRecuring) {
		TypedQuery<AppointmentCancelReason> query = entityManager.createQuery("FROM AppointmentCancelReason WHERE isRecuring IN (:isRecuring, 'B') AND enabled = 'Y'", AppointmentCancelReason.class);
		query.setParameter("isRecuring", isRecuring);
		return query.getResultList();
	}

	@Override
	@Transactional
	public List<Appointment> findAppointmentbyId(Long appointmentId) {
		CriteriaBuilder builder = entityManager.getCriteriaBuilder();
		CriteriaQuery<Appointment> query = builder.createQuery(Appointment.class);
		Root<Appointment> root = query.from(Appointment.class);
		query.select(root).where(builder.equal(root.get("appointmentId"), appointmentId));
		return entityManager.createQuery(query).getResultList();
	}

	@Override
	@Transactional
	public List<Appointment> findHoldAppointments(Date startTime) {
		CriteriaBuilder builder = entityManager.getCriteriaBuilder();
		CriteriaQuery<Appointment> query = builder.createQuery(Appointment.class);
		Root<Appointment> root = query.from(Appointment.class);

		Predicate canceledPredicate = builder.equal(root.get("canceled"), Canceled.H);
		Predicate timePredicate = builder.lessThan(root.get("createTime"), startTime);
		query.select(root).where(builder.and(canceledPredicate, timePredicate));

		return entityManager.createQuery(query).getResultList();
	}

	@Override
	@Transactional
	public List<AppointmentTransactions> findHoldAppointmentIdByTransactionId(String transactionId) {
		CriteriaBuilder builder = entityManager.getCriteriaBuilder();
		CriteriaQuery<AppointmentTransactions> query = builder.createQuery(AppointmentTransactions.class);
		Root<AppointmentTransactions> root = query.from(AppointmentTransactions.class);
		query.select(root).where(builder.equal(root.get("transactionId"), transactionId));

		return entityManager.createQuery(query).getResultList();
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void saveOrUpdate(Appointment appointment) {
		entityManager.merge(appointment);
	}
}
