package com.guitarcenter.scheduler.dao.impl;

//import org.hibernate.Criteria;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.AppointmentCancelDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.AppointmentCancel;
import com.guitarcenter.scheduler.model.Person;

@Repository("appointmentCancelDAO")
public class AppointmentCancelDAOImpl extends AbstractDAOImpl<AppointmentCancel> implements AppointmentCancelDAO {

	public AppointmentCancelDAOImpl() {
		super(AppointmentCancel.class);
	}

	@Override
	protected void updateAuditor(AppointmentCancel pT, Person pUpdatedBy) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchOne(AppointmentCancel pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) { //
	 * TODO Auto-generated method stub
	 * 
	 * }
	 */

	@Override
	protected void fetchMany(AppointmentCancel pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected Criterion<AppointmentCancel, AppointmentCancel> getCriterionInstance() {
		// TODO Auto-generated method stub
		return null;
	}

	
}
