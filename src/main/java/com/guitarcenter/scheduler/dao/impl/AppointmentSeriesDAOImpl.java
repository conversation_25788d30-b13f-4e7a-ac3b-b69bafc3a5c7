package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ACTIVITY;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION_PROFILE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_MORE_CUSTOMERS;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;

//import org.hibernate.Criteria;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.hibernate.FetchMode;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.AppointmentSeriesDAO;
import com.guitarcenter.scheduler.dao.criterion.AppointmentSeriesCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.AppointmentSeries;
import com.guitarcenter.scheduler.model.InstructorAppointmentStatus;
import com.guitarcenter.scheduler.model.Person;

@Repository("appointmentSeriesDAO")
public class AppointmentSeriesDAOImpl extends AbstractDAOImpl<AppointmentSeries> implements AppointmentSeriesDAO {


	@PersistenceContext
	private EntityManager entityManager;

	public AppointmentSeriesDAOImpl() {
		super(AppointmentSeries.class);
	}



	@Override
	protected void updateAuditor(AppointmentSeries pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(AppointmentSeries pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_ACTIVITY, pFetchMode, pResult.getActivity());
		addFetchCriteria(FETCH_LOCATION_PROFILE, pFetchMode, pResult.getLocationProfile());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}



	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) {
	 * addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy",
	 * FetchMode.JOIN, pCriteria); addFetchCriteria(FETCH_ACTIVITY, pFetchMode,
	 * "activity", FetchMode.JOIN, pCriteria);
	 * addFetchCriteria(FETCH_LOCATION_PROFILE, pFetchMode, "locationProfile",
	 * FetchMode.JOIN, pCriteria); addFetchCriteria(FETCH_SITE, pFetchMode, "site",
	 * FetchMode.JOIN, pCriteria); }
	 */



	@Override
	protected void fetchMany(AppointmentSeries pResult, int pFetchMode) {
		addFetchCriteria(FETCH_MORE_CUSTOMERS, pFetchMode, pResult.getCustomers());
	}



	@Override
	protected Criterion<AppointmentSeries, AppointmentSeries> getCriterionInstance() {
		return AppointmentSeriesCriterion.getInstance();
	}
	
	/*@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void saveOrUpdate(AppointmentSeries pT) {

		Session session = getSessionFactory().getCurrentSession();
		session.saveOrUpdate(pT);
 
	}*/

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void saveOrUpdate(AppointmentSeries pT) {
		if (pT.getAppointmentSeriesId() == null) { // Assuming the entity uses 'id' to determine persistence state
			entityManager.persist(pT); // Persist new entity
		} else {
			entityManager.merge(pT); // Merge existing entity
		}
	}
}
