package com.guitarcenter.scheduler.dao.impl;

import java.util.List;


import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.hibernate.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.elasticsearch.core.query.Criteria;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.guitarcenter.scheduler.dao.AbstractDAO;
import com.guitarcenter.scheduler.dao.criterion.AbstractCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.util.HibernateAwareObjectMapper;
import com.guitarcenter.scheduler.model.Person;

abstract class AbstractDAOImpl<T> implements AbstractDAO<T> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractDAOImpl.class);

    @PersistenceContext
    private EntityManager entityManager;

    private final Class<T> entityClass;

   // private final Class<T>	mTClass;

    @Autowired
    @Qualifier("JSONMapper")
    private HibernateAwareObjectMapper objectMapper;

    protected AbstractDAOImpl(final Class<T> entityClass) {
        this.entityClass = entityClass;
    }

    protected EntityManager getEntityManager() {
        return entityManager;
    }

    protected Class<T> getEntityClass() {
        return entityClass;
    }

   /* public Class<T> getTClass() {
        return mTClass;
    }*/

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public long save(T entity, Person updatedBy) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("{}.save() : entry, entity is [{}], updatedBy is [{}]", this.getClass().getSimpleName(), objectMapper.toJSON(entity), updatedBy);
        }
        updateAuditor(entity, updatedBy);
        entityManager.persist(entity);
        entityManager.flush(); // Force persistence
        long id = (Long) entityManager.getEntityManagerFactory().getPersistenceUnitUtil().getIdentifier(entity);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("{}.save() : exit", this.getClass().getSimpleName());
        }
        return id;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public long save(T entity, long updatedById) {
        Person updatedBy = new Person();
        updatedBy.setPersonId(updatedById);
        return save(entity, updatedBy);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void update(T entity, Person updatedBy) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("{}.update() : entry, entity is [{}], updatedBy is [{}]", this.getClass().getSimpleName(), objectMapper.toJSON(entity), updatedBy);
        }
        updateAuditor(entity, updatedBy);
        entityManager.merge(entity);
        LOGGER.debug("{}.update() : exit", this.getClass().getSimpleName());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void update(T entity, long updatedById) {
        Person updatedBy = new Person();
        updatedBy.setPersonId(updatedById);
        update(entity, updatedBy);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public T get(long id) {
        return entityManager.find(getEntityClass(), id);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void delete(T entity) {
        entityManager.remove(entityManager.contains(entity) ? entity : entityManager.merge(entity));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public T merge(T entity) {
        return entityManager.merge(entity);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public <E> E get(Criterion<T, E> criterion) {
        return criterion.get(entityManager);
    }

  //  @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public T get1(T example) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<T> cq = cb.createQuery(getEntityClass());
        Root<T> root = cq.from(getEntityClass());

        // Build the query based on the example entity
        cq.where(cb.equal(root, example));

        TypedQuery<T> query = entityManager.createQuery(cq);
        return query.getSingleResult();
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public T get(T pExample) {
        if (LOGGER.isDebugEnabled()) {
            Object[] objects = { this.getClass().getSimpleName(), pExample.getClass().getSimpleName(),
                    objectMapper.toJSON(pExample) };
            LOGGER.debug("{}.get({} pExample) : entry, pExample is [{}]", objects);
        }
        Session session = entityManager.unwrap(Session.class);
      //  Session session = entityManager().getCurrentSession();
        CriteriaBuilder criteriaBuilder = session.getCriteriaBuilder();
        CriteriaQuery<T> criteriaQuery = criteriaBuilder.createQuery(getEntityClass());
        Root<T> root = criteriaQuery.from(getEntityClass());
        criteriaQuery.select(root).where(criteriaBuilder.equal(root, pExample));
       /* Root<T> root = criteriaQuery.from(getEntityClass());

        // Create an Example predicate for matching fields of the entity
        Predicate predicate = criteriaBuilder.equal(root, pExample);
        criteriaQuery.where(predicate);*/

        // Execute the query
        T result = session.createQuery(criteriaQuery).getSingleResult();


       /* Root<T> root = criteriaQuery.from(getEntityClass());
        criteriaQuery.add(Example.create(pExample));
        T t = (T) criteriaQuery.uniqueResult();*/
        if (LOGGER.isDebugEnabled()) {
            Object[] objects = { this.getClass().getSimpleName(), pExample.getClass().getSimpleName(), result };
            LOGGER.debug("{}.get({} pExample) : returning [{}]", objects);
        }
        return result;
    }

    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED)
    public T get2(T pExample) {
        if (LOGGER.isDebugEnabled()) {
            Object[] objects = { this.getClass().getSimpleName(), pExample.getClass().getSimpleName(),
                    objectMapper.toJSON(pExample) };
            LOGGER.debug("{}.get({} pExample) : entry, pExample is [{}]", objects);
        }

        // Obtain CriteriaBuilder from EntityManager
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();

        // Create a CriteriaQuery object for the entity class
        CriteriaQuery<T> criteriaQuery = criteriaBuilder.createQuery((Class<T>) pExample.getClass());

        // Set up the root for the query
        Root<T> root = criteriaQuery.from((Class<T>) pExample.getClass());

        // Create an Example predicate for matching fields of the entity
        Predicate predicate = criteriaBuilder.equal(root, pExample);
        criteriaQuery.where(predicate);

        // Execute the query
        T result = entityManager.createQuery(criteriaQuery).getSingleResult();

        if (LOGGER.isDebugEnabled()) {
            Object[] objects = { this.getClass().getSimpleName(), pExample.getClass().getSimpleName(), result };
            LOGGER.debug("{}.get({} pExample) : returning [{}]", objects);
        }

        return result;
    }




    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public <E> List<E> search(Criterion<T, E> criterion) {
        return criterion.search(entityManager);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public List<T> searchAll() {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<T> cq = cb.createQuery(getEntityClass());
        cq.from(getEntityClass());
        TypedQuery<T> query = entityManager.createQuery(cq);
        return query.getResultList();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public List<T> search(T example) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<T> cq = cb.createQuery(getEntityClass());
        Root<T> root = cq.from(getEntityClass());

        Predicate predicate = buildPredicateFromExample(cb, root, example);
        cq.where(predicate);

        TypedQuery<T> query = entityManager.createQuery(cq);
        return query.getResultList();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public T get(long id, int fetchMode) {
        T result = entityManager.find(getEntityClass(), id);
        if (result != null && fetchMode > 0) {
            fetchOne(result, fetchMode);
            fetchMany(result, fetchMode);
        }
        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public T merge(T entity, int fetchMode) {
        T mergedEntity = entityManager.merge(entity);
        if (mergedEntity != null && fetchMode > 0) {
            fetchOne(mergedEntity, fetchMode);
            fetchMany(mergedEntity, fetchMode);
        }
        return mergedEntity;
    }

  /*  @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public <E> E get(Criterion<T, E> criterion, int fetchMode) {
        E entity = criterion.get(entityManager, fetchMode);
        if (entity != null && fetchMode > 0) {
            if (entity instanceof T) {
                fetchMany((T) entity, fetchMode);
            }
        }
        return entity;
    }*/

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public <E> E get(Criterion<T, E> criterion, int fetchMode) {
        E entity = criterion.get(entityManager, fetchMode);
        if (entity != null && fetchMode > 0) {
            if (getEntityClass().isAssignableFrom(entity.getClass())) {
                fetchMany((T) entity, fetchMode); // Safe cast
            }
        }
        return entity;
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    @SuppressWarnings("unchecked")
    public <E> List<E> search(Criterion<T, E> criterion, int fetchMode) {
        List<E> results = criterion.search(entityManager, fetchMode);
        if (!results.isEmpty() && fetchMode > 0) {
            if (getEntityClass().isAssignableFrom(results.get(0).getClass())) {
                for (T entity : (List<T>) results) {  // Safe cast
                    fetchMany(entity, fetchMode);
                }
            }
        }
        return results;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public List<T> searchAll(int fetchMode) {
        Criterion<T, T> criterion = getCriterionInstance();
        List<T> results = criterion.searchAll(entityManager, fetchMode);
        if (!results.isEmpty() && fetchMode > 0) {
            for (T entity : results) {
                fetchMany(entity, fetchMode);
            }
        }
        return results;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public List<T> search(T example, int fetchMode) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<T> cq = cb.createQuery(getEntityClass());
        Root<T> root = cq.from(getEntityClass());

        Predicate predicate = buildPredicateFromExample(cb, root, example);
        cq.where(predicate);

        TypedQuery<T> query = entityManager.createQuery(cq);
        List<T> results = query.getResultList();

        if (!results.isEmpty() && fetchMode > 0) {
            for (T entity : results) {
                fetchMany(entity, fetchMode);
            }
        }
        return results;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public T get(T example, int fetchMode) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<T> cq = cb.createQuery(getEntityClass());
        Root<T> root = cq.from(getEntityClass());

        Predicate predicate = buildPredicateFromExample(cb, root, example);
        cq.where(predicate);

        TypedQuery<T> query = entityManager.createQuery(cq);
        T result = query.getSingleResult();

        if (result != null && fetchMode > 0) {
            fetchMany(result, fetchMode);
        }
        return result;
    }

    private Predicate buildPredicateFromExample(CriteriaBuilder cb, Root<T> root, T example) {
        Predicate predicate = cb.conjunction();
        try {
            for (var field : example.getClass().getDeclaredFields()) {
                field.setAccessible(true);
                Object value = field.get(example);
                if (value != null) {
                    predicate = cb.and(predicate, cb.equal(root.get(field.getName()), value));
                }
            }
        } catch (IllegalAccessException e) {
            LOGGER.error("Error building predicate from example", e);
        }
        return predicate;
    }

    protected abstract void updateAuditor(T entity, Person updatedBy);

    protected abstract void fetchOne(T result, int fetchMode);

    protected abstract void fetchMany(T result, int fetchMode);

    protected abstract Criterion<T, T> getCriterionInstance();
}
