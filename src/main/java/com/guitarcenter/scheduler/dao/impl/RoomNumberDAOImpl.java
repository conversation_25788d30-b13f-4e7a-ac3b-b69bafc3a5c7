package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;
import java.util.List;

//import org.hibernate.Criteria;
import com.guitarcenter.scheduler.model.RoomSize;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import org.hibernate.FetchMode;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.RoomNumberDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.RoomNumberCriterion;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.RoomNumber;

@Repository("roomNumberDAO")
public class RoomNumberDAOImpl extends AbstractDAOImpl<RoomNumber> implements RoomNumberDAO {

	@PersistenceContext
	private EntityManager entityManager;

	public RoomNumberDAOImpl() {
		super(RoomNumber.class);
	}



	@Override
	protected void updateAuditor(RoomNumber pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(RoomNumber pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}


	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) {
	 * addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy",
	 * FetchMode.JOIN, pCriteria); addFetchCriteria(FETCH_SITE, pFetchMode, "site",
	 * FetchMode.JOIN, pCriteria); }
	 */



	@Override
	protected void fetchMany(RoomNumber pResult, int pFetchMode) {
	}



	@Override
	protected Criterion<RoomNumber, RoomNumber> getCriterionInstance() {
		return RoomNumberCriterion.getInstance();
	}



	/**
	 * 
	  * <p>Title: getRoomNumberBySiteId</p>
	  * <p>Description: </p>
	  * @param siteId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.RoomNumberDAO#getRoomNumberBySiteId(long)
	 */
	@SuppressWarnings("unchecked")
    @Override
	public List<RoomNumber> getRoomNumberBySiteId(long siteId) {

		String hql = "from RoomNumber where site.siteId = :siteId";
		List<RoomNumber>  rs= null;
			TypedQuery<RoomNumber> query = entityManager.createQuery(hql, RoomNumber.class);
			query.setParameter("siteId", siteId);
			rs = query.getResultList();
		return rs;
	}
}
