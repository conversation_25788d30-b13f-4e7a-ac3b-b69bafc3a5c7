package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.*;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

//import javax.persistence.criteria.JoinType;
//import javax.persistence.criteria.Root;

////import org.hibernate.Criteria;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import org.hibernate.FetchMode;
import org.hibernate.Session;
//import org.hibernate.criterion.CriteriaQuery;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.Query;
import org.hibernate.type.StandardBasicTypes;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.InstructorDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.InstructorCriterion;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.enums.Canceled;
import com.guitarcenter.scheduler.model.enums.Enabled;

@Repository("instructorDAO")
public class InstructorDAOImpl extends AbstractDAOImpl<Instructor> implements InstructorDAO {

    @PersistenceContext
    private EntityManager entityManager;

    public InstructorDAOImpl() {
        super(Instructor.class);
    }

    @Override
    protected void updateAuditor(Instructor pT, Person pUpdatedBy) {
        pT.setUpdated(new Date());
        pT.setUpdatedBy(pUpdatedBy);
    }

    @Override
    protected Criterion<Instructor, Instructor> getCriterionInstance() {
        return InstructorCriterion.getInstance();
    }

    @Override
    public Instructor getInstructorByTime(long instructorId, String startDate, String startTime, String endTime) {
        TypedQuery<Instructor> query = entityManager.createNamedQuery("instructor.checkInstructorTime", Instructor.class);
        query.setParameter(1, startDate);
        query.setParameter(2, DateTimeUtil.DATE_PATTERN_SLASH);
        //steve 20130912 the date format only check HH24:MI
        query.setParameter(3, startTime);
        query.setParameter(4, DateTimeUtil.TIME_FORMAT_HH24_MI);
        query.setParameter(5, DateTimeUtil.TIME_FORMAT_HH24_MI);
        query.setParameter(6, DateTimeUtil.TIME_FORMAT_HH24_MI);
        query.setParameter(7, DateTimeUtil.TIME_FORMAT_HH24_MI);
        query.setParameter(8, DateTimeUtil.TIME_FORMAT_HH24_MI);
        query.setParameter(9, endTime);
        query.setParameter(10, DateTimeUtil.TIME_FORMAT_HH24_MI);
        query.setParameter(11, DateTimeUtil.TIME_FORMAT_HH24_MI);
        query.setParameter(12, DateTimeUtil.TIME_FORMAT_HH24_MI);
        query.setParameter(13, DateTimeUtil.TIME_FORMAT_HH24_MI);
        query.setParameter(14, DateTimeUtil.TIME_FORMAT_HH24_MI);
        query.setParameter(15, instructorId);
        List<Instructor> list = query.getResultList();
        return list.isEmpty() ? null : list.get(0);
    }

    @Override
    public Instructor checkInstructorByAppointmentTime(long instructorId, String startDate, String startTime, String endTime) {
        TypedQuery<Instructor> query = entityManager.createNamedQuery("instructor.checkInstructorAppointmentTime", Instructor.class);
        query.setParameter(1, startDate + " " + startTime);
        query.setParameter(2, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(3, startDate + " " + endTime);
        query.setParameter(4, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(5, startDate + " " + startTime);
        query.setParameter(6, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(7, startDate + " " + endTime);
        query.setParameter(8, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(9, startDate + " " + startTime);
        query.setParameter(10, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(11, startDate + " " + endTime);
        query.setParameter(12, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(13, Canceled.N.name());
        query.setParameter(14, Canceled.H.name());
        query.setParameter(15, instructorId);

        List<Instructor> list = query.getResultList();
        return list.isEmpty() ? null : list.get(0);
    }




    @Override
    public Instructor checkInstructorByAppointmentRecurringTime(
            long instructorId, String startDate, String endDate, String startTime, String endTime) {


            TypedQuery<Instructor> query = entityManager.createNamedQuery("instructor.checkInstructorAppointmentRecurringTime", Instructor.class);

        query.setParameter(1, startDate);
        query.setParameter(2, DateTimeUtil.DATE_PATTERN_SLASH);
        query.setParameter(3, DateTimeUtil.DATE_PATTERN_SLASH);
        query.setParameter(4, DateTimeUtil.DATE_PATTERN_SLASH);
        query.setParameter(5, startDate);
        query.setParameter(6, DateTimeUtil.DATE_PATTERN_SLASH);
        query.setParameter(7, endDate);
        query.setParameter(8, DateTimeUtil.DATE_PATTERN_SLASH);
        query.setParameter(9, DateTimeUtil.DATE_PATTERN_SLASH);
        query.setParameter(10, startTime);
        query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(12, DateTimeUtil.DATE_PATTERN_SLASH);
        query.setParameter(13, endTime);
        query.setParameter(14, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(15, DateTimeUtil.DATE_PATTERN_SLASH);
        query.setParameter(16, startTime);
        query.setParameter(17, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(18, DateTimeUtil.DATE_PATTERN_SLASH);
        query.setParameter(19, endTime);
        query.setParameter(20, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(21, DateTimeUtil.DATE_PATTERN_SLASH);
        query.setParameter(22, startTime);
        query.setParameter(23, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(24, DateTimeUtil.DATE_PATTERN_SLASH);
        query.setParameter(25, endTime);
        query.setParameter(26, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(27, Canceled.N.name());
        query.setParameter(28, Canceled.H.name());
        query.setParameter(29, instructorId);

        List<Instructor> list = query.getResultList();
        return list.isEmpty() ? null : list.get(0);
    }

  /*  @Override
    public boolean checkUpdateInstructorByAppointmentTime(
            long instructorId, String startDate, String startTime, String endTime, String excludeAppointmentIdParam) {

        // Fetch the named query and dynamically handle the replacement of &REPLACECONDITION
        String queryString = entityManager.createNamedQuery("instructor.checkUpdateInstructorAppointmentTime", String.class).getSingleResult();

        // Dynamically replace the &REPLACECONDITION with the proper condition
        String replaceParameter = SystemUtil.REPLACE_CONDITION.replace("&PARAMETER", excludeAppointmentIdParam);
        String sql = excludeAppointmentIdParam.isEmpty()
                ? queryString.replace("&REPLACECONDITION", "")
                : queryString.replace("&REPLACECONDITION", replaceParameter);

        // Create the query and set named parameters
        TypedQuery<Boolean> query = entityManager.createQuery(sql, Boolean.class);
        query.setParameter("startRange", startDate + " " + startTime);
        query.setParameter("startRangeFormat", DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter("endRange", startDate + " " + endTime);
        query.setParameter("endRangeFormat", DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter("startTime", startDate + " " + startTime);
        query.setParameter("startTimeFormat", DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter("endTime", startDate + " " + endTime);
        query.setParameter("endTimeFormat", DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter("startTimeInRange", startDate + " " + startTime);
        query.setParameter("startTimeInRangeFormat", DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter("endTimeInRange", startDate + " " + endTime);
        query.setParameter("endTimeInRangeFormat", DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
      //  query.setParameter("canceledStatusN", Canceled.N.toString());
      //  query.setParameter("canceledStatusH", Canceled.H.toString());
        query.setParameter("instructorId", instructorId);

        // Return the result of the query
        return query.getSingleResult();
    }*/

    @Override
    public boolean checkUpdateInstructorByAppointmentTime(long instructorId, String startDate, String startTime, String endTime, String excludeAppointmentIdParam) {
      //  EntityManager entityManager = // obtain EntityManager instance
                String queryString = entityManager.createNamedQuery("Instructor.checkUpdateInstructorAppointmentTime").unwrap(org.hibernate.query.NativeQuery.class).getQueryString();

        String replaceParameter = SystemUtil.REPLACE_CONDITION.replace("&PARAMETER", excludeAppointmentIdParam);
        String sql = excludeAppointmentIdParam.isEmpty() ? queryString.replace("&REPLACECONDITION", "") : queryString.replace("&REPLACECONDITION", replaceParameter);

        Query query = (Query) entityManager.createNativeQuery(sql, Boolean.class);
        query.setParameter(1, startDate + " " + startTime);
        query.setParameter(2, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(3, startDate + " " + endTime);
        query.setParameter(4, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(5, startDate + " " + startTime);
        query.setParameter(6, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(7, startDate + " " + endTime);
        query.setParameter(8, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(9, startDate + " " + startTime);
        query.setParameter(10, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(11, startDate + " " + endTime);
        query.setParameter(12, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(13, Canceled.N.toString());
        query.setParameter(14, Canceled.H.toString());
        query.setParameter(15, instructorId);

        Boolean result = (Boolean) query.getSingleResult();
        return result != null && result;
    }




    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public boolean checkUpdateInstructorByAppointmentRecurringTime(
            long instructorId, String startDate, String endDate, String startTime, String endTime, String excludeAppointmentIdParam) {

        Session session = entityManager.unwrap(Session.class);

        // Fetch the named query string
        String queryString = session.getNamedQuery("instructor.checkUpdateInstructorAppointmentRecurringTime").getQueryString();

        // Dynamically replace the &REPLACECONDITION
        String replaceParameter = SystemUtil.REPLACE_CONDITION.replace("&PARAMETER", excludeAppointmentIdParam);
        String sql = excludeAppointmentIdParam.isEmpty()
                ? queryString.replace("&REPLACECONDITION", "")
                : queryString.replace("&REPLACECONDITION", replaceParameter);

        // Create the query using EntityManager
        jakarta.persistence.Query query = entityManager.createNativeQuery(sql);

        // Set the parameters for the query
        query.setParameter(1, startDate);  // TO_DATE(?1, ?2)
        query.setParameter(2, DateTimeUtil.DATE_PATTERN_SLASH);  // Date format

        query.setParameter(3, DateTimeUtil.DATE_PATTERN_SLASH);  // Date format in TO_CHAR
        query.setParameter(4, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);  // Time format in TO_TIMESTAMP_TZ

        query.setParameter(5, startDate);  // Start date for comparison
        query.setParameter(6, DateTimeUtil.DATE_PATTERN_SLASH);  // Start date format
        query.setParameter(7, endDate);  // End date for comparison
        query.setParameter(8, DateTimeUtil.DATE_PATTERN_SLASH);  // End date format

        query.setParameter(9, DateTimeUtil.DATE_PATTERN_SLASH);  // Repeated date pattern
        query.setParameter(10, startTime);  // Start time
        query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);  // Time format

        query.setParameter(12, DateTimeUtil.DATE_PATTERN_SLASH);  // Another date pattern
        query.setParameter(13, endTime);  // End time
        query.setParameter(14, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);  // Time format for end time

        query.setParameter(15, DateTimeUtil.DATE_PATTERN_SLASH);  // Another date format
        query.setParameter(16, startTime);  // Another start time
        query.setParameter(17, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);  // Time format for the second start time

        query.setParameter(18, DateTimeUtil.DATE_PATTERN_SLASH);  // Date format
        query.setParameter(19, endTime);  // Another end time
        query.setParameter(20, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);  // Time format for the second end time

        query.setParameter(21, DateTimeUtil.DATE_PATTERN_SLASH);  // Another date format
        query.setParameter(22, startTime);  // Third start time
        query.setParameter(23, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);  // Time format for the third start time

        query.setParameter(24, DateTimeUtil.DATE_PATTERN_SLASH);  // Date format
        query.setParameter(25, endTime);  // Third end time
        query.setParameter(26, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);  // Time format for the third end time

        // Set canceled status
        query.setParameter(27, Canceled.N.toString());  // "N" status
        query.setParameter(28, Canceled.H.toString());  // "H" status

        // Set the instructor ID
        query.setParameter(29, instructorId);

        Object result = query.getSingleResult();
        // Return the result of the query
        return Boolean.parseBoolean(result.toString());
    }






    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public boolean checkInstructorByProfileActivityId(long instructorId, long activityId, long profileId) {
        // Create a TypedQuery using the named query from the EntityManager
        TypedQuery<Boolean> query = entityManager.createNamedQuery("instructor.checkInstructorByProfileActivityId", Boolean.class);

        // Set the parameters for the query
        // BigDecimal roomId1 = BigDecimal.valueOf(roomId);
        query.setParameter(1, instructorId);
        query.setParameter(2, activityId);
        query.setParameter(3, profileId);
        query.setParameter(4, Enabled.Y.name());

        // Execute the query and fetch the result
        Boolean result = query.getSingleResult();

        // Return the result
        return result != null ? result : false;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public Instructor checkInstructorByAppointmentTime(
            long instructorId, String startDate, String endDate, String startTime, String endTime) {

        // Create a TypedQuery using the named query and EntityManager
        TypedQuery<Instructor> query = entityManager.createNamedQuery("instructor.checkInstructorAppointmentTime", Instructor.class);

        // Set parameters for the query
        query.setParameter(1, startDate + " " + startTime);
        query.setParameter(2, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(3, endDate + " " + endTime);
        query.setParameter(4, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(5, startDate + " " + startTime);
        query.setParameter(6, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(7, endDate + " " + endTime);
        query.setParameter(8, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(9, startDate + " " + startTime);
        query.setParameter(10, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(11, endDate + " " + endTime);
        query.setParameter(12, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
        query.setParameter(13, Canceled.N.name());
        query.setParameter(14, Canceled.H.name());
        query.setParameter(15, instructorId);

        // Fetch the results and return the first if available
        List<Instructor> resultList = query.getResultList();
        return resultList.isEmpty() ? null : resultList.get(0);
    }


    //---GSSP Instructor Mode update changes
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void saveOrUpdateInstructor(Instructor pT, Person pUpdatedBy) {
        // Update the audit fields before persisting the entity
        updateAuditor(pT, pUpdatedBy);

        // Use EntityManager for persistence
        if (pT.getInstructorId() == null) {
            // If the instructor ID is null, it's a new entity, so persist it
            entityManager.persist(pT);
        } else {
            // Otherwise, it's an existing entity, so merge the changes
            entityManager.merge(pT);
        }
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public List<Instructor> getInstructorsAffectRecently(String updateDate) {
        // Calculate dates
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DATE, -1);
        Date yesterday01 = c.getTime();
        c.add(Calendar.DATE, 31);
        Date after_30day = c.getTime();
        DateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
        String yesterday = dateFormatter.format(yesterday01);
        String after30day = dateFormatter.format(after_30day);

        // Construct the query
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT DISTINCT i.instructorId FROM Instructor i ");
        sb.append("WHERE (EXISTS (SELECT a.instructorId FROM Appointment a WHERE ");
        sb.append("a.startTime <= :after30day AND a.startTime >= :yesterday AND a.updated >= :updateDate)) ");
        sb.append("OR EXISTS (SELECT av.instructorId FROM Availability av WHERE ");
        sb.append("av.updated >= :updateDate) ");
        sb.append("OR i.updated >= :updateDate");

        // Create a TypedQuery with EntityManager
        TypedQuery<Long> query = entityManager.createQuery(sb.toString(), Long.class);
        query.setParameter("after30day", after30day);
        query.setParameter("yesterday", yesterday);
        query.setParameter("updateDate", updateDate);

        // Execute the query and construct the list of instructors
        List<Long> instructorIds = query.getResultList();
        List<Instructor> instructorList = new ArrayList<>();
        for (Long id : instructorIds) {
            Instructor instructor = entityManager.find(Instructor.class, id);
            if (instructor != null) {
                instructorList.add(instructor);
            }
        }

        return instructorList;
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public List<Instructor> getFullInstructorsAffectRecently(String updateDate) {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DATE, -1);
        Date yesterday01 = c.getTime();
        c.add(Calendar.DATE, 31);
        Date after_30day = c.getTime();
        DateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
        String yesterday = dateFormatter.format(yesterday01);
        String after30day = dateFormatter.format(after_30day);

        StringBuilder sb = new StringBuilder();
        sb.append("SELECT DISTINCT i.instructorId FROM Instructor i ");
        sb.append("WHERE (EXISTS (SELECT a.instructorId FROM Appointment a WHERE ");
        sb.append("a.startTime <= :after30day AND a.startTime >= :yesterday AND a.updated >= :updateDate)) ");
        sb.append("OR EXISTS (SELECT av.instructorId FROM Availability av WHERE ");
        sb.append("av.updated >= :updateDate) ");
        sb.append("OR i.updated >= :updateDate");

        // Create TypedQuery with EntityManager
        TypedQuery<Long> query = entityManager.createQuery(sb.toString(), Long.class);
        query.setParameter("after30day", after30day);
        query.setParameter("yesterday", yesterday);
        query.setParameter("updateDate", updateDate);

        List<Long> instructorIds = query.getResultList();
        List<Instructor> instructorList = new ArrayList<>();
        for (Long id : instructorIds) {
            Instructor instructor = entityManager.find(Instructor.class, id);
            if (instructor != null) {
                instructorList.add(instructor);
            }
        }

        return instructorList;
    }



    @Override
	protected void fetchOne(Instructor pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_AVAILABILITY, pFetchMode, pResult.getAvailability());
		addFetchCriteria(FETCH_LOCATION, pFetchMode, pResult.getLocation());
		addFetchCriteria(FETCH_PERSON, pFetchMode, pResult.getPerson());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}

	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) {
	 * 
	 * }
	 */


    public  boolean shouldFetch(int fetchType, int pFetchMode) {
	    // Implement your logic to determine if the fetchType should be fetched based on pFetchMode.
	    // Example:
	    return (pFetchMode & fetchType) != 0;
	}


	@Override
	protected void fetchMany(Instructor pResult, int pFetchMode) {
		addFetchCriteria(FETCH_MORE_ACTIVITIES, pFetchMode, pResult.getActivities());
	}

	//@Override
	protected void fetchOne(Query<Instructor> pQuery, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

}
