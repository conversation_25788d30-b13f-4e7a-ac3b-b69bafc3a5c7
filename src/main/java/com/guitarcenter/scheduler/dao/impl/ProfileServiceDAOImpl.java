package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION_PROFILE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SERVICE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;

//import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.ProfileServiceDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.ProfileServiceCriterion;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileService;

@Repository("profileServiceDAO")
public class ProfileServiceDAOImpl extends AbstractDAOImpl<ProfileService> implements ProfileServiceDAO {

	public ProfileServiceDAOImpl() {
		super(ProfileService.class);
	}



	@Override
	protected void updateAuditor(ProfileService pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(ProfileService pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
		addFetchCriteria(FETCH_LOCATION_PROFILE, pFetchMode, pResult.getLocationProfile());
		addFetchCriteria(FETCH_SERVICE, pFetchMode, pResult.getService());

	}



	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) {
	 * addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy",
	 * FetchMode.JOIN, pCriteria); addFetchCriteria(FETCH_SITE, pFetchMode, "site",
	 * FetchMode.JOIN, pCriteria); addFetchCriteria(FETCH_LOCATION_PROFILE,
	 * pFetchMode, "locationProfile", FetchMode.JOIN, pCriteria);
	 * addFetchCriteria(FETCH_SERVICE, pFetchMode, "service", FetchMode.JOIN,
	 * pCriteria); }
	 */



	@Override
	protected void fetchMany(ProfileService pResult, int pFetchMode) {
	}



	@Override
	protected Criterion<ProfileService, ProfileService> getCriterionInstance() {
		return ProfileServiceCriterion.getInstance();
	}
}
