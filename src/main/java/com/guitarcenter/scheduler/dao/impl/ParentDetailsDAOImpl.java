package com.guitarcenter.scheduler.dao.impl;

import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

//import org.hibernate.Criteria;
//import org.hibernate.SQLQuery;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import org.hibernate.Session;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.ParentDetailsDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.ParentDetails;
import com.guitarcenter.scheduler.model.Person;

@Repository("parentDetailsDAO")
public class ParentDetailsDAOImpl extends AbstractDAOImpl<ParentDetails> implements ParentDetailsDAO {

	@PersistenceContext
	private EntityManager entityManager;

	public ParentDetailsDAOImpl() {
		super(ParentDetails.class);
	}

	@Override
	protected void updateAuditor(ParentDetails pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}


	@Override
	protected void fetchOne(ParentDetails pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) { //
	 * TODO Auto-generated method stub
	 * 
	 * }
	 */

	@Override
	protected void fetchMany(ParentDetails pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected Criterion<ParentDetails, ParentDetails> getCriterionInstance() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public ParentDetails getParentDetailsFromCustomerTable(Long customerId) {
		String sql = "SELECT c.PARENT_id AS PARENT_id, pd.full_name AS full_name, pd.version AS version " +
				"FROM PARENT_DETAILS pd, customer c WHERE pd.PARENT_id = c.PARENT_id AND c.customer_id = :customerId";

		// Use EntityManager to create a native query
		Query query = entityManager.createNativeQuery(sql);

		// Set the parameter for the query
		query.setParameter("customerId", customerId);

		// Execute the query and get the result list
		@SuppressWarnings("unchecked")
		List<Object[]> list = query.getResultList();

		// Create the ParentDetails object to populate
		ParentDetails pd = new ParentDetails();

		// Check if the result list is not null and not empty
		if (list != null && !list.isEmpty()) {
			Object[] row = list.get(0);

			// Populate ParentDetails object with retrieved data
			if (row[0] != null) pd.setParentId(Long.valueOf(row[0].toString()));
			if (row[1] != null) pd.setFullName(row[1].toString());
			if (row[2] != null) pd.setVersion(Long.valueOf(row[2].toString()));
		}

		return pd;
	}


}
