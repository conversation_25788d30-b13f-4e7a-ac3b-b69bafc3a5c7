/**
 * @Title: TimeoffDAOImpl.java
 * @Package com.guitarcenter.scheduler.dao.impl
 * @Description: TODO
 * Copyright: Copyright (c) 2014 
 * Company:
 * 
 * <AUTHOR>
 * @date Mar 10, 2014 4:06:04 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_INSTRUCTOR;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

//import org.hibernate.Criteria;
import com.guitarcenter.scheduler.model.Onetime;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import org.hibernate.FetchMode;
//import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.query.NativeQuery;
import org.hibernate.type.StandardBasicTypes;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.TimeoffDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.TimeoffCriterion;
import com.guitarcenter.scheduler.dto.TimeoffDateDTO;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Timeoff;

/**
 * @ClassName: TimeoffDAOImpl
 * @Description: TODO
 * <AUTHOR>
 * @date Mar 10, 2014 4:06:04 PM
 *
 */
@Repository("timeoffDAO")
public class TimeoffDAOImpl extends AbstractDAOImpl<Timeoff> implements TimeoffDAO {

	@PersistenceContext
	private EntityManager entityManager;



	/**
	  * TimeoffDAOImpl. 
	  * <p>Title: </p>
	  * <p>Description: </p>
	  * @param pTClass
	  */
	public TimeoffDAOImpl() {
		super(Timeoff.class);
	}

	/**
	  * <p>Title: updateAuditor</p>
	  * <p>Description: </p>
	  * @param pT
	  * @param pUpdatedBy
	  * @see com.guitarcenter.scheduler.dao.impl.AbstractDAOImpl#updateAuditor(java.lang.Object, com.guitarcenter.scheduler.model.Person)
	  */
	@Override
	protected void updateAuditor(Timeoff pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}

	/**
	  * <p>Title: fetchOne</p>
	  * <p>Description: </p>
	  * @param pResult
	  * @param pFetchMode
	  * @see com.guitarcenter.scheduler.dao.impl.AbstractDAOImpl#fetchOne(java.lang.Object, int)
	  */
	@Override
	protected void fetchOne(Timeoff pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_INSTRUCTOR, pFetchMode, pResult.getInstructor());
	}

	/**
	  * <p>Title: fetchOne</p>
	  * <p>Description: </p>
	  * @param pCriteria
	  * @param pFetchMode
	  * @see com.guitarcenter.scheduler.dao.impl.AbstractDAOImpl#fetchOne(org.hibernate.Criteria, int)
	  */
	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) {
	 * addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy",
	 * FetchMode.JOIN, pCriteria); addFetchCriteria(FETCH_INSTRUCTOR, pFetchMode,
	 * "instructor", FetchMode.JOIN, pCriteria); }
	 */
	/**
	  * <p>Title: fetchMany</p>
	  * <p>Description: </p>
	  * @param pResult
	  * @param pFetchMode
	  * @see com.guitarcenter.scheduler.dao.impl.AbstractDAOImpl#fetchMany(java.lang.Object, int)
	  */
	@Override
	protected void fetchMany(Timeoff pResult, int pFetchMode) {
	}

	/**
	  * <p>Title: getCriterionInstance</p>
	  * <p>Description: </p>
	  * @return
	  * @see com.guitarcenter.scheduler.dao.impl.AbstractDAOImpl#getCriterionInstance()
	  */
	@Override
	protected Criterion<Timeoff, Timeoff> getCriterionInstance() {
		return TimeoffCriterion.getInstance();
	}

	/**
	  * <p>Title: getTimeoffByInstructorId</p>
	  * <p>Description: </p>
	  * @param instructorId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.TimeoffDAO#getTimeoffByInstructorId(long)
	  */
	@SuppressWarnings("unchecked")
	@Override
	public List<Timeoff> getTimeoffByInstructorId(long instructorId) {
/*
		Query query = entityManager.createQuery("select t.* from Timeoff t where to_char(t.end_time,'DD-MON-YY') >= to_char(sysdate,'DD-MON-YY')  and  t.instructor_Id = ? order by t.startTime asc");
		query.setParameter(1, instructorId);
		return query.getResultList();
		5055 less */

		 Query query = (org.hibernate.query.Query) entityManager.createNativeQuery(
				"select t.* from Timeoff t where to_char(t.end_time,'DD-MON-YY') >= to_char(sysdate,'DD-MON-YY')  and  t.instructor_Id = ? order by t.start_Time asc", Timeoff.class
		);
		query.unwrap(org.hibernate.query.NativeQuery.class);
		query.setParameter(1, instructorId);
		return query.getResultList();
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<Timeoff> getDisplayTimeoffByInstructorId(long instructorId) {
	//	Session session = super.getSessionFactory().getCurrentSession();
		//trunc value for endTime  5055
		Query query = entityManager.createQuery(" from Timeoff where  endTime  >= to_date(to_char(sysdate), 'DD-MON-YY') and instructor.instructorId = :instructorId order by updated desc");
		query.setParameter("instructorId", instructorId);
		List<Timeoff> lt= null;
			lt = query.getResultList();
		return lt;
	}
	
	@Override
	@SuppressWarnings("unchecked")
	public List<Timeoff> getTimeoffByDateInstructorId(long instructorId, String date) {
		//Session session = super.getSessionFactory().getCurrentSession();
		Query query = entityManager.createQuery("from Timeoff where instructor.instructorId = ? and trunc(TO_TIMESTAMP_TZ(?, ?)) between trunc(start_time) and trunc(end_time)");
		query.setParameter(0, instructorId);
		query.setParameter(1, date);
		query.setParameter(2, DateTimeUtil.DATE_PATTERN_SLASH);
		return query.getResultList();
	}

	/**
	  * <p>Title: checkTimeoffByTime</p>
	  * <p>Description: </p>
	  * @param startDate
	  * @param startTime
	  * @param endTime
	  * @param instructorId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.TimeoffDAO#checkTimeoffByTime(java.lang.String, java.lang.String, java.lang.String, long)
	 */
	@Override
	public boolean checkTimeoffByTime(String startDate, String startTime,
			String endTime, long instructorId) {
		//Session session = super.getSessionFactory().getCurrentSession();
		Query query = entityManager.createNamedQuery("timeoff.getTimeoffByTime");
		query.setParameter("startTime1", startDate + " " + startTime);
		query.setParameter("timezone1", DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter("endTime1", startDate + " " + endTime);
		query.setParameter("timezone2", DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter("startTime2", startDate + " " + startTime);
		query.setParameter("timezone3", DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter("endTime2", startDate + " " + endTime);
		query.setParameter("timezone4", DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter("startTime3", startDate + " " + startTime);
		query.setParameter("timezone5", DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter("endTime3", startDate + " " + endTime);
		query.setParameter("timezone6", DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter("instructorId", instructorId);

		@SuppressWarnings("unchecked")
        List<Timeoff> l = query.getResultList();
		return l.size()==0?true:false;
	}

	/**
	  * <p>Title: checkTimeoffByRecurringTime</p>
	  * <p>Description: </p>
	  * @param startDate
	  * @param startTime
	  * @param endTime
	  * @param instructorId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.TimeoffDAO#checkTimeoffByRecurringTime(java.util.List, java.lang.String, java.lang.String, long)
	 */
//	@Override
	public boolean checkTimeoffByRecurringTime1(List<String> startDate, String startTime, String endTime, long instructorId) {
		//Session session = super.getSessionFactory().getCurrentSession();
		//String sql = entityManager.createNamedQuery("timeoff.getTimeoffByRecurringTime").getSingleResult();
		String sql = entityManager.createNamedQuery("timeoff.getTimeoffByRecurringTime", String.class).getSingleResult();
		StringBuilder tempScrpts = new StringBuilder();
		for (int i = 0; i < startDate.size(); i++) {
			tempScrpts.append(SystemUtil.REPLACE_TEMP_SCRIPTS.replace("&PARAMETER1", "'"+ startDate.get(i)+ " " + startTime+ "' ").replace("&PARAMETER2", "'"+ startDate.get(i) + " " + endTime+ "'"));
			if(i<startDate.size()-1){
				tempScrpts.append(" UNION ");
			}
		}
		Query query = entityManager.createNativeQuery(sql.replace("&REPLACECONDITION", tempScrpts.toString()));
		query.setParameter("timeformat", DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter("instructorId", instructorId);

		Object o = query.getSingleResult();
		return Boolean.parseBoolean(o.toString());
	}

	@Override
	public boolean checkTimeoffByRecurringTime(List<String> startDate, String startTime, String endTime, long instructorId) {
		// Build the temporary scripts to replace in the query
		StringBuilder tempScrpts = new StringBuilder();
		for (int i = 0; i < startDate.size(); i++) {
			tempScrpts.append(SystemUtil.REPLACE_TEMP_SCRIPTS
					.replace("&PARAMETER1", "'" + startDate.get(i) + " " + startTime + "'")
					.replace("&PARAMETER2", "'" + startDate.get(i) + " " + endTime + "'"));
			if (i < startDate.size() - 1) {
				tempScrpts.append(" UNION ");
			}
		}

		// Use the base query and programmatically build the full query
		String baseQuery = "SELECT CASE WHEN EXISTS (" +
				"SELECT t.* FROM timeoff t, ( &REPLACECONDITION ) temp " +
				"WHERE (t.start_time BETWEEN TO_TIMESTAMP_TZ(temp.s, :timeformat) AND TO_TIMESTAMP_TZ(temp.e, :timeformat) " +
				"OR t.end_time BETWEEN TO_TIMESTAMP_TZ(temp.s, :timeformat) AND TO_TIMESTAMP_TZ(temp.e, :timeformat) " +
				"OR TO_TIMESTAMP_TZ(temp.s, :timeformat) BETWEEN t.start_time AND t.end_time " +
				"OR TO_TIMESTAMP_TZ(temp.e, :timeformat) BETWEEN t.start_time AND t.end_time) " +
				"AND t.instructor_id = :instructorId ) THEN 'false' ELSE 'true' END flag FROM DUAL";

		// Replace the placeholder in the base query with the dynamic temp scripts
		String finalQuery = baseQuery.replace("&REPLACECONDITION", tempScrpts.toString());

		// Create the native query and set the parameters
		Query query = entityManager.createNativeQuery(finalQuery);
		query.setParameter("timeformat", DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter("instructorId", instructorId);

		// Execute the query and return the result
		Object result = query.getSingleResult();
		return Boolean.parseBoolean(result.toString());
	}



	/**
	  * getTimeoffDTOByAvailabilityTime
	  * 
	  *
	  * @Title: getTimeoffDTOByAvailabilityTime
	  * @Description: 
	  * @param @param date
	  * @param @param locationId
	  * @param @return
	  * @return List<TimeoffDateDTO>
	  * @throws
	 */
	@Override
	@SuppressWarnings("unchecked")
	public List<TimeoffDateDTO> getTimeoffDateDTOByAvailabilityTime(String date, long locationId) {
		List<TimeoffDateDTO> l = new ArrayList<>();

		// Get the session from the EntityManager
		Session session = entityManager.unwrap(Session.class);

		// Get the named query as a string
		String sql = session.getNamedQuery("timeoff.getTimeoffDTOByAvailabilityTime").getQueryString();

		// Create the native query
		Query query = session.createNativeQuery(sql).unwrap(NativeQuery.class)
				.unwrap(org.hibernate.query.NativeQuery.class)  // Unwrap to Hibernate's NativeQuery
				.addScalar("start_time", StandardBasicTypes.DATE)
				.addScalar("end_time", StandardBasicTypes.DATE)
				.addScalar("instructor_id", StandardBasicTypes.LONG);

		// Set parameters
	//	query.setParameter("format", DateTimeUtil.DATE_PATTERN_SLASH);
		 query.setParameter(1, date);
	 	query.setParameter(2, locationId);

		// Execute the query
		List<Object[]> list = null;

			list = query.getResultList();

		for (Object[] objects : list) {
			TimeoffDateDTO dto = new TimeoffDateDTO();
			dto.setStartTime((Date) objects[0]);
			dto.setEndTime((Date) objects[1]);
			dto.setInstructorId((Long) objects[2]);
			l.add(dto);
		}

		return l;
	}

}
