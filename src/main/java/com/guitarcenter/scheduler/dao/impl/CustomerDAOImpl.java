package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_CUSTOMER_STATUS;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_MORE_INSTRUMENTS;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

//import org.hibernate.Criteria;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import org.hibernate.FetchMode;
import org.hibernate.Session;
import org.hibernate.query.NativeQuery;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.CustomerDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.CustomerCriterion;
import com.guitarcenter.scheduler.dao.util.PGPUtils;
import com.guitarcenter.scheduler.dto.CustomerDetailDTO;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.ParentDetails;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.enums.Canceled;

@Repository("customerDAO")
public class CustomerDAOImpl extends AbstractDAOImpl<Customer> implements CustomerDAO {

	@PersistenceContext
	private EntityManager entityManager;

	public CustomerDAOImpl() {
		super(Customer.class);
	}

	@Override
	protected void updateAuditor(Customer pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}

	@Override
	protected void fetchOne(Customer pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_CUSTOMER_STATUS, pFetchMode, pResult.getCustomerStatus());
		addFetchCriteria(FETCH_PERSON, pFetchMode, pResult.getPerson());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}

	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) {
	 * addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy",
	 * FetchMode.JOIN, pCriteria); addFetchCriteria(FETCH_CUSTOMER_STATUS,
	 * pFetchMode, "customerStatus", FetchMode.JOIN, pCriteria);
	 * addFetchCriteria(FETCH_PERSON, pFetchMode, "person", FetchMode.JOIN,
	 * pCriteria); addFetchCriteria(FETCH_SITE, pFetchMode, "site", FetchMode.JOIN,
	 * pCriteria); }
	 */

	@Override
	protected void fetchMany(Customer pResult, int pFetchMode) {
		addFetchCriteria(FETCH_MORE_INSTRUMENTS, pFetchMode, pResult.getInstruments());
	}

	@Override
	protected Criterion<Customer, Customer> getCriterionInstance() {
		return CustomerCriterion.getInstance();
	}

	@Override
	@Transactional
	public boolean checkCustomerByAppointmentTime(long customerId, String startDate, String startTime, String endTime, long profileId) {

		// Use EntityManager instead of Session

		//5055
		// Assuming named query customer.checkCustomerAppointmentTime is already defined in the entity mapping
		Query query = entityManager.createNamedQuery("customer.checkCustomerAppointmentTime");

		// Set query parameters
		query.setParameter(1, startDate + " " + startTime);
		query.setParameter(2, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(3, startDate + " " + endTime);
		query.setParameter(4, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(5, startDate + " " + startTime);
		query.setParameter(6, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(7, startDate + " " + endTime);
		query.setParameter(8, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(9, startDate + " " + startTime);
		query.setParameter(10, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(11, startDate + " " + endTime);
		query.setParameter(12, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(13, profileId);
		query.setParameter(14, Canceled.N.name());
		query.setParameter(15, Canceled.H.name());
		query.setParameter(16, customerId);

		// Execute the query and get a single result
		Object result = query.getSingleResult();

		// Handle the result, expecting a numeric value (1 for true, 0 for false)
		if (result instanceof Number) {
			return ((Number) result).intValue() > 0;
		}

		// Handle cases where result might be a String ("true"/"false")
		else if (result instanceof String) {
			return Boolean.parseBoolean((String) result);
		}

		// Default return value (no valid result)
		return false;
	}

	@Override
	public boolean checkCustomerByAppointmentRecurringTime(
			long customerId, String startDate, String endDate,
			String startTime, String endTime, long profileId) {
	/*	Session session = super.getSessionFactory().getCurrentSession();
		String sql = session.getNamedQuery("customer.checkCustomerAppointmentRecurringTime").getQueryString();*/
		Query query = entityManager.createNamedQuery("customer.checkCustomerAppointmentRecurringTime");
		//	NativeQuery<?> query = session.createNativeQuery(sql);
		query.setParameter(1, startDate);
		query.setParameter(2, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(3, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(4, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(5, startDate);
		query.setParameter(6, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(7, endDate);
		query.setParameter(8, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(9, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(10, startTime);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(12, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(13, endTime);
		query.setParameter(14, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(15, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(16, startTime);
		query.setParameter(17, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(18, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(19, endTime);
		query.setParameter(20, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(21, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(22, startTime);
		query.setParameter(23, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(24, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(25, endTime);
		query.setParameter(26, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(27, profileId);
		query.setParameter(28, Canceled.N.toString());
		query.setParameter(29, Canceled.H.toString());
		query.setParameter(30, customerId);
		Object o = query.getSingleResult();
		return Boolean.parseBoolean(o.toString());
	}

	@Override
	public boolean checkUpdateCustomerByAppointmentTime(long customerId,
														String startDate, String startTime, String endTime, long profileId,
														String excludeAppointmentIdParam) {
		// Get the Hibernate session from the EntityManager
		Session session = entityManager.unwrap(Session.class);

		// Get the SQL query string from the named query
		String sql = session.getNamedQuery("customer.checkUpdateCustomerAppointmentTime").getQueryString();

		// Replace the conditional part of the query if needed
		String replaceParameter = SystemUtil.REPLACE_CONDITION.replace("&PARAMETER", excludeAppointmentIdParam);
		String finalQuery = excludeAppointmentIdParam.isEmpty()
				? sql.replace("&REPLACECONDITION", "")
				: sql.replace("&REPLACECONDITION", replaceParameter);

		// Create a native SQL query using EntityManager
		Query query = entityManager.createNativeQuery(finalQuery);

		// Set the parameters for the query
		query.setParameter(1, startDate + " " + startTime);
		query.setParameter(2, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(3, startDate + " " + endTime);
		query.setParameter(4, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(5, startDate + " " + startTime);
		query.setParameter(6, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(7, startDate + " " + endTime);
		query.setParameter(8, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(9, startDate + " " + startTime);
		query.setParameter(10, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(11, startDate + " " + endTime);
		query.setParameter(12, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(13, profileId);
		query.setParameter(14, Canceled.N.toString());
		query.setParameter(15, Canceled.H.toString());
		query.setParameter(16, customerId);

		// Execute the query and get the result
		Object result = query.getSingleResult();

		// Parse the result to a boolean and return
		return Boolean.parseBoolean(result.toString());
	}

	@Override
	public boolean checkUpdateCustomerByAppointmentRecurringTime(
			long customerId, String startDate, String endDate,
			String startTime, String endTime, long profileId,
			String excludeAppointmentIdParam) {

		// Get the Hibernate session from the EntityManager
		Session session = entityManager.unwrap(Session.class);

		// Get the SQL query string from the named query (adjust according to your query storage)
		String sql = session.getNamedQuery("customer.checkUpdateCustomerAppointmentRecurringTime").getQueryString();

		// Replace the conditional part of the query if needed
		String replaceParameter = SystemUtil.REPLACE_CONDITION.replace("&PARAMETER", excludeAppointmentIdParam);
		String finalQuery = excludeAppointmentIdParam.isEmpty()
				? sql.replace("&REPLACECONDITION", "")
				: sql.replace("&REPLACECONDITION", replaceParameter);

		// Create a native SQL query using EntityManager (JPA)
		Query query = entityManager.createNativeQuery(finalQuery);

		// Set the parameters for the query (Note: Indexed parameters can be replaced with named parameters if preferred)
		query.setParameter(1, startDate); // Indexes in Hibernate 6 start from 1
		query.setParameter(2, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(3, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(4, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(5, startDate);
		query.setParameter(6, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(7, endDate);
		query.setParameter(8, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(9, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(10, startTime);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(12, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(13, endTime);
		query.setParameter(14, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(15, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(16, startTime);
		query.setParameter(17, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(18, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(19, endTime);
		query.setParameter(20, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(21, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(22, startTime);
		query.setParameter(23, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(24, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(25, endTime);
		query.setParameter(26, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(27, profileId);
		query.setParameter(28, Canceled.N.toString());
		query.setParameter(29, Canceled.H.toString());
		query.setParameter(30, customerId);

		// Execute the query and get the result
		Object result = query.getSingleResult();

		// Parse the result to a boolean and return
		return Boolean.parseBoolean(result.toString());

	}



	@Override
	@Transactional
	public ParentDetails getParentIdFromCustomerTable(Long customerId) {
		String sql = "SELECT c.PARENT_id, pd.version FROM PARENT_DETAILs pd JOIN customer c ON pd.PARENT_id = c.PARENT_id WHERE c.customer_id = :customerId";
		Query query = entityManager.createNativeQuery(sql);
		query.setParameter("customerId", customerId);
		List<Object[]> result = query.getResultList();

		ParentDetails parentDetails = new ParentDetails();
		if (!result.isEmpty()) {
			Object[] row = result.get(0);
			parentDetails.setParentId(Long.valueOf(row[0].toString()));
			parentDetails.setVersion(Long.valueOf(row[1].toString()));
		}

		return parentDetails;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public CustomerDetailDTO getCustomerDetailsById(Long customerId) {
		// Use EntityManager instead of Session


		// Define the native SQL query
		String sql = "SELECT c.parent_id as parent_id, p.FIRST_NAME as first_name, p.LAST_NAME as last_name, " +
				"p.EMAIL as email, pd.SECONDARY_EMAIL as secondary_email, pd.FULL_NAME as full_name, " +
				"p.PHONE as phone, c.EXTERNAL_ID as customer_ext_id, cs.EXTERNAL_ID as status, " +
				"c.LESSON_COUNT as lesson_count, c.customer_id as customer_id, pd.version as version " +
				"FROM person p JOIN customer c ON p.person_id = c.person_id " +
				"LEFT JOIN parent_details pd ON pd.parent_id = c.parent_id " +
				"LEFT JOIN CUSTOMER_STATUS cs ON cs.CUSTOMER_STATUS_ID = c.CUSTOMER_STATUS_ID " +
				"WHERE c.customer_id = :customerId";

		// Create the native query using EntityManager
		Query query = entityManager.createNativeQuery(sql);

		// Set the query parameter
		query.setParameter("customerId", customerId);

		// Execute the query and get the result list
		@SuppressWarnings("unchecked")
		List<Object[]> list = query.getResultList();

		// Initialize a new CustomerDetailDTO object
		CustomerDetailDTO customerDetailDTO = new CustomerDetailDTO();

		// Populate the CustomerDetailDTO object from the query result
		if (list != null && !list.isEmpty()) {
			Object[] row = list.get(0); // Get the first row
			customerDetailDTO.setCustomerFullName(row[1] + " " + row[2]);
			customerDetailDTO.setEmail(PGPUtils.setNullToEmpty(row[3]));
			customerDetailDTO.setSecondaryEmail(PGPUtils.setNullToEmpty(row[4]));
			customerDetailDTO.setPhoneNumber(PGPUtils.setNullToEmpty(row[6]));
			customerDetailDTO.setCustomerExternalId(PGPUtils.setNullToEmpty(row[7]));
			customerDetailDTO.setCustomerStatus(PGPUtils.setNullToEmpty(row[8]));
			customerDetailDTO.setLessonsCount(row[9] != null ? Integer.parseInt(row[9].toString()) : 0);
			customerDetailDTO.setGcId(Long.parseLong(row[10].toString()));
		}

		// Return the CustomerDetailDTO object
		return customerDetailDTO;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void saveOrUpdateInstructor(Customer ct) {
		// Use EntityManager instead of Session
		entityManager.merge(ct);  // merge will either save or update based on the entity's state
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public Customer getCustomerById(Long customerId) {
		// Use EntityManager instead of Session
		return entityManager.find(Customer.class, customerId);
	}
}
