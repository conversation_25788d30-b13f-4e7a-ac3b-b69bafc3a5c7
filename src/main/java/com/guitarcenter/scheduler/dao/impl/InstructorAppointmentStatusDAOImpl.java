package com.guitarcenter.scheduler.dao.impl;

import java.util.List;

//import org.hibernate.Criteria;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import org.hibernate.query.Query;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.InstructorAppointmentStatusDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.InstructorAppointmentStatus;
import com.guitarcenter.scheduler.model.Person;

@Repository("instructorAppointmentStatusDAO")
public class InstructorAppointmentStatusDAOImpl extends AbstractDAOImpl<InstructorAppointmentStatus> implements InstructorAppointmentStatusDAO {

	@PersistenceContext
	private EntityManager entityManager;

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void saveOrUpdateInstructorAppointmentStatus(InstructorAppointmentStatus pT) {

		if (pT.getAppointmentId() == null) {
			entityManager.persist(pT); // Save if new entity (id is null)
		} else {
			entityManager.merge(pT); // Update if entity already exists (id is not null)
		}
 
	}
 
	
	public InstructorAppointmentStatusDAOImpl() {
		super(InstructorAppointmentStatus.class);
	}
	
	


	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public List<InstructorAppointmentStatus> getInstructorAppointmentStatus(long appointmentId) {
		// Use EntityManager instead of Session
		String hql = "FROM InstructorAppointmentStatus WHERE appointmentId = :appointmentId";

		// Create a typed query
		TypedQuery<InstructorAppointmentStatus> query = entityManager.createQuery(hql, InstructorAppointmentStatus.class);
		query.setParameter("appointmentId", appointmentId);

		// Execute and return the results
		return query.getResultList();
	}


	@Override
	protected void updateAuditor(InstructorAppointmentStatus pT, Person pUpdatedBy) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchOne(InstructorAppointmentStatus pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) { //
	 * TODO Auto-generated method stub
	 * 
	 * }
	 */

	@Override
	protected void fetchMany(InstructorAppointmentStatus pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected Criterion<InstructorAppointmentStatus, InstructorAppointmentStatus> getCriterionInstance() {
		// TODO Auto-generated method stub
		return null;
	}

	 
	 
	
}
