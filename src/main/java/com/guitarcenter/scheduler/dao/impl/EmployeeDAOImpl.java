package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;
import java.util.Date;
//import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.springframework.stereotype.Repository;
import com.guitarcenter.scheduler.dao.EmployeeDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.EmployeeCriterion;
import com.guitarcenter.scheduler.model.Employee;
import com.guitarcenter.scheduler.model.Person;

@Repository("employeeDAO")
public class EmployeeDAOImpl extends AbstractDAOImpl<Employee> implements EmployeeDAO {

	public EmployeeDAOImpl() {
		super(Employee.class);
	}



	@Override
	protected void updateAuditor(Employee pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(Employee pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_LOCATION, pFetchMode, pResult.getLocation());
		addFetchCriteria(FETCH_PERSON, pFetchMode, pResult.getPerson());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}



	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) {
	 * addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy",
	 * FetchMode.JOIN, pCriteria); addFetchCriteria(FETCH_LOCATION, pFetchMode,
	 * "location", FetchMode.JOIN, pCriteria); addFetchCriteria(FETCH_PERSON,
	 * pFetchMode, "person", FetchMode.JOIN, pCriteria);
	 * addFetchCriteria(FETCH_SITE, pFetchMode, "site", FetchMode.JOIN, pCriteria);
	 * }
	 */



	@Override
	protected void fetchMany(Employee pResult, int pFetchMode) {
	}



	@Override
	protected Criterion<Employee, Employee> getCriterionInstance() {
		return EmployeeCriterion.getInstance();
	}
	
}
