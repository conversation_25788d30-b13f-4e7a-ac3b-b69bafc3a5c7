package com.guitarcenter.scheduler.dao.impl;

import java.util.HashMap;
import java.util.Map;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.hibernate.ScrollableResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import com.guitarcenter.scheduler.dao.DataBackUpMonthlyDAO;
import com.guitarcenter.scheduler.model.NativeString;
import com.guitarcenter.scheduler.service.impl.AppointmentServiceImpl;

@Repository("dataBackUpMonthlyDAO")
public class DataBackUpMonthlyDAOImpl implements DataBackUpMonthlyDAO {

    @PersistenceContext
    private EntityManager entityManager;

    private static final Logger LOG = LoggerFactory.getLogger(AppointmentServiceImpl.class);

    @Override
    @Transactional
    public Map<String, String> doBackUpForAppointmentTable() {

        Map<String, String> mdata = new HashMap<>();
        try {
            NativeString concurrentString1 = executeNamedQuery("callAppTabRecProcedure");
            NativeString concurrentString2 = executeNamedQuery("callAppTabDropProcedure");
            NativeString concurrentString3 = executeNamedQuery("callAppCustTabRecProcedure");
            NativeString concurrentString4 = executeNamedQuery("callAppCustTabDropProcedure");

            mdata.put("APPTMNT_BKP_TBL_CREATED", concurrentString1.getNoSlotsStr());
            mdata.put("APPTMNT_BKP_TBL_DROP", concurrentString2.getNoSlotsStr());
            mdata.put("APPTMNT_CUST_BKP_TBL_CREATED", concurrentString3.getNoSlotsStr());
            mdata.put("APPTMNT_CUST_BKP_TBL_DROP", concurrentString4.getNoSlotsStr());

        } catch (Exception e) {
            LOG.error("Caught an exception: {}", e.getMessage(), e);
        }

        return mdata;
    }

    private NativeString executeNamedQuery(String queryName) {
        Query query = entityManager.createNamedQuery(queryName);
        query.setParameter(1, 1003); // Note: Parameter indexes start from 1

        ScrollableResults scroll = (ScrollableResults) query.getResultList();
        NativeString nativeString = null;
		/*
		 * if (scroll.next()) { nativeString = (NativeString) scroll.get()[0]; }
		 */
        return nativeString;
    }
}
