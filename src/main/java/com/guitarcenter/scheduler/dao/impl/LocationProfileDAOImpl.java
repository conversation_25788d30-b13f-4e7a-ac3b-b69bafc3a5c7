package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_AVAILABILITY;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;
import java.util.List;

//import org.hibernate.Criteria;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import org.hibernate.FetchMode;
import org.hibernate.query.Query;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.LocationProfileDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.LocationProfileCriterion;
import com.guitarcenter.scheduler.model.LocationProfile;
import com.guitarcenter.scheduler.model.Person;

@Repository("locationProfileDAO")
public class LocationProfileDAOImpl extends AbstractDAOImpl<LocationProfile> implements LocationProfileDAO {

	@PersistenceContext
	private EntityManager entityManager;

	public LocationProfileDAOImpl() {
		super(LocationProfile.class);
	}



	@Override
	protected void updateAuditor(LocationProfile pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(LocationProfile pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_AVAILABILITY, pFetchMode, pResult.getAvailability());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}



	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) {
	 * addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy",
	 * FetchMode.JOIN, pCriteria); addFetchCriteria(FETCH_AVAILABILITY, pFetchMode,
	 * "availability", FetchMode.JOIN, pCriteria); addFetchCriteria(FETCH_SITE,
	 * pFetchMode, "site", FetchMode.JOIN, pCriteria); }
	 */



	@Override
	protected void fetchMany(LocationProfile pResult, int pFetchMode) {
	}



	@Override
	protected Criterion<LocationProfile, LocationProfile> getCriterionInstance() {
		return LocationProfileCriterion.getInstance();
	}



	/**
	  * <p>Title: getLocationProfileByTime</p>
	  * <p>Description: </p>
	  * @param profileId
	  * @param startDate
	  * @param startTime
	  * @param endTime
	  * @return
	  * @see com.guitarcenter.scheduler.dao.LocationProfileDAO#getLocationProfileByTime(long, java.lang.String, java.lang.String, java.lang.String)
	  */
	@Override
	public LocationProfile getLocationProfileByTime(long profileId, String startDate, String startTime, String endTime) {
		// Define the query using the EntityManager and JPQL (or HQL in Hibernate)
		TypedQuery<LocationProfile> query = entityManager.createNamedQuery("locationProfile.checkLocationProfileTime", LocationProfile.class);

		// Set the parameters for the query 5055
		query.setParameter(1, startDate);
		query.setParameter(2, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(3, startTime);
		query.setParameter(4, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(5, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(6, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(7, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(8, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(9, endTime);
		query.setParameter(10, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(12, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(13, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(14, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(15, profileId);

		// Get the list of results
		List<LocationProfile> resultList = query.getResultList();

		// Return the first result if available, otherwise return null
		return resultList.isEmpty() ? null : resultList.get(0);
	}

}
