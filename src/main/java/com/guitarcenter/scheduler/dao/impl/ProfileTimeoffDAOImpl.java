package com.guitarcenter.scheduler.dao.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

import com.guitarcenter.scheduler.model.Appointment;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.criteria.*;
//import org.hibernate.Criteria;
//import org.hibernate.SQLQuery;
import org.hibernate.Session;
//import org.hibernate.criterion.Order;
///import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.ProfileTimeoffDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dto.ProfileTimeOffDTO;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileTimeoff;

@Repository("profileTimeoffDAO")
public class ProfileTimeoffDAOImpl extends AbstractDAOImpl<ProfileTimeoff> implements ProfileTimeoffDAO {
	@PersistenceContext
	private EntityManager entityManager;
	
	private static final Logger	 LOGGER	= LoggerFactory.getLogger(ProfileTimeoffDAOImpl.class);
	
	public ProfileTimeoffDAOImpl() {
		super(ProfileTimeoff.class);
	}

	@SuppressWarnings("unchecked")
	public List<ProfileTimeoff> getTimeoffByProfileId(long profileId,Date startDate,Date endDate ) {

		// Use CriteriaBuilder and CriteriaQuery instead of the old Criteria API
		CriteriaBuilder builder = entityManager.getCriteriaBuilder();
		CriteriaQuery<ProfileTimeoff> query = builder.createQuery(ProfileTimeoff.class);
		Root<ProfileTimeoff> root = query.from(ProfileTimeoff.class);

		// Define the predicates (conditions)
		Predicate profilePredicate = builder.equal(root.get("profileId"), profileId);
		Predicate startDatePredicate = builder.greaterThanOrEqualTo(root.get("startTime"), startDate);
		Predicate endDatePredicate = builder.lessThanOrEqualTo(root.get("startTime"), endDate);

		// Combine predicates using AND
		query.where(builder.and(profilePredicate, startDatePredicate, endDatePredicate));

		// Execute the query and return the result list
		List<ProfileTimeoff> list = entityManager.createQuery(query).getResultList();
		return list;
	}

	@SuppressWarnings("unchecked")
	public List<ProfileTimeoff> getTimeoffByProfileIdInsAVL(long profileId, Date startDate, Date endDate) {
		// Inject the EntityManager


		// Use CriteriaBuilder to create CriteriaQuery
		CriteriaBuilder builder = entityManager.getCriteriaBuilder();
		CriteriaQuery<ProfileTimeoff> query = builder.createQuery(ProfileTimeoff.class);
		Root<ProfileTimeoff> root = query.from(ProfileTimeoff.class);

		// Create Predicates (conditions)
		Predicate profilePredicate = builder.equal(root.get("profileId"), profileId);
		Predicate startDatePredicate = builder.greaterThanOrEqualTo(root.get("startTime"), startDate);
		Predicate endDatePredicate = builder.lessThanOrEqualTo(root.get("startTime"), endDate);

		// Combine the Predicates with AND
		query.where(builder.and(profilePredicate, startDatePredicate, endDatePredicate));

		// Execute the query and return the result list
		List<ProfileTimeoff> list = entityManager.createQuery(query).getResultList();
		return list;
	}
	
	@Override
	protected void updateAuditor(ProfileTimeoff pT, Person pUpdatedBy) {
	
	}

	@Override
	protected void fetchOne(ProfileTimeoff pResult, int pFetchMode) {		
	}


	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) { }
	 */


	@Override
	protected void fetchMany(ProfileTimeoff pResult, int pFetchMode) {
		
	}


	@Override
	protected Criterion<ProfileTimeoff, ProfileTimeoff> getCriterionInstance() {
		return null;
	}
	

	@Override
	public List<ProfileTimeoff> getUpcomingTimeOffByProfileId(long profileId, Date startDate) throws Exception {
		//Session session = super.getSessionFactory().getCurrentSession();

		// Use CriteriaBuilder to create CriteriaQuery
		CriteriaBuilder builder = entityManager.getCriteriaBuilder();
		CriteriaQuery<ProfileTimeoff> query = builder.createQuery(ProfileTimeoff.class);
		Root<ProfileTimeoff> root = query.from(ProfileTimeoff.class);

		// Create Predicates (conditions)
		Predicate profilePredicate = builder.equal(root.get("profileId"), profileId);
		Predicate startDatePredicate = builder.greaterThanOrEqualTo(root.get("startTime"), startDate);

		// Combine the Predicates with AND
		query.where(builder.and(profilePredicate, startDatePredicate));

		// Add Order by startTime ascending
		query.orderBy(builder.asc(root.get("startTime")));

		// Create the query and set the maximum results
		List<ProfileTimeoff> list = entityManager.createQuery(query)
				.setMaxResults(2)
				.getResultList();

		return list;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<ProfileTimeoff> getDisplayProfileTimeoffById(long profileId) {
		Date startDate = new Date();
		//Session session = super.getSessionFactory().getCurrentSession();

		// Use CriteriaBuilder to create CriteriaQuery
		CriteriaBuilder builder = entityManager.getCriteriaBuilder();
		CriteriaQuery<ProfileTimeoff> query = builder.createQuery(ProfileTimeoff.class);
		Root<ProfileTimeoff> root = query.from(ProfileTimeoff.class);

		// Create Predicates (conditions)
		Predicate profilePredicate = builder.equal(root.get("profileId"), profileId);
		Predicate startDatePredicate = builder.greaterThanOrEqualTo(root.get("startTime"), startDate);

		// Combine the Predicates with AND
		query.where(builder.and(profilePredicate, startDatePredicate));

		// Add Order by startTime ascending
		query.orderBy(builder.asc(root.get("startTime")));

		// Execute the query
		List<ProfileTimeoff> list = entityManager.createQuery(query).getResultList();

		return list;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public ProfileTimeoff getProfileTimeoffByProfileTimeoffId(long profileTimeoffId) {
		//Session session = super.getSessionFactory().getCurrentSession();

		// Use CriteriaBuilder to create CriteriaQuery
		CriteriaBuilder builder = entityManager.getCriteriaBuilder();
		CriteriaQuery<ProfileTimeoff> query = builder.createQuery(ProfileTimeoff.class);
		Root<ProfileTimeoff> root = query.from(ProfileTimeoff.class);

		// Create Predicate for profiletimeoffId
		Predicate idPredicate = builder.equal(root.get("profiletimeoffId"), profileTimeoffId);

		// Set where clause
		query.where(idPredicate);

		// Execute the query
		List<ProfileTimeoff> list = entityManager.createQuery(query).getResultList();

		// Return the first result or null if the list is empty
		return list.isEmpty() ? null : list.get(0);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void saveOrUpdateProfileTimeOff(ProfileTimeoff pT) {

		if(pT.getProfileId() == null){
			entityManager.persist(pT);
		}else {
			entityManager.merge(pT);
		}




 
	}
 
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public  ProfileTimeOffDTO getAppointmentTimeForProfile(long profileId,String profileTimeOffDate) {
		//Session session = super.getSessionFactory().getCurrentSession();
		CriteriaBuilder builder = entityManager.getCriteriaBuilder();
		CriteriaQuery<Object[]> query = builder.createQuery(Object[].class);
		Root<Appointment> root = query.from(Appointment.class);

		// Convert date string to date format if needed
		Path<String> startTimePath = (Path<String>) builder.function("TO_CHAR", String.class, root.get("startTime"), builder.literal("MM/DD/YYYY"));

		// Create Predicates
		Predicate datePredicate = builder.equal(startTimePath, profileTimeOffDate);
		Predicate profilePredicate = builder.equal(root.get("profileId"), profileId);
		Predicate canceledPredicate = root.get("canceled").in("N", "H");

		// Select min and max times
		query.multiselect(
				builder.function("MIN", String.class, builder.function("TO_CHAR", String.class, root.get("startTime"), builder.literal("hh24:mi:ss"))),
				builder.function("MAX", String.class, builder.function("TO_CHAR", String.class, root.get("endTime"), builder.literal("hh24:mi:ss")))
		).where(builder.and(datePredicate, profilePredicate, canceledPredicate));

		// Execute query and process results
		List<Object[]> resultList = entityManager.createQuery(query).getResultList();
		ProfileTimeOffDTO dto = new ProfileTimeOffDTO();
		if (!resultList.isEmpty()) {
			Object[] row = resultList.get(0);
			dto.setFromTime((String) row[0]);
			dto.setToTime((String) row[1]);
		}
		return dto;
	}
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public  List<ProfileTimeOffDTO>  getAppointmentTimeListForProfileAndDate(long profileId,String profileTimeOffDate) {
		//Session session = super.getSessionFactory().getCurrentSession();
		CriteriaBuilder builder = entityManager.getCriteriaBuilder();
		CriteriaQuery<Object[]> query = builder.createQuery(Object[].class);
		Root<ProfileTimeoff> root = query.from(ProfileTimeoff.class);

		// Select start time, end time, and formatted date
		query.multiselect(
				builder.function("TO_CHAR", String.class, root.get("startTime"), builder.literal("hh24:mi")),
				builder.function("TO_CHAR", String.class, root.get("endTime"), builder.literal("hh24:mi")),
				builder.function("TO_CHAR", String.class, root.get("startTime"), builder.literal("MM/DD/YYYY"))
		).where(builder.equal(root.get("profileId"), profileId));

		// Execute query and process results
		List<Object[]> resultList = entityManager.createQuery(query).getResultList();
		List<ProfileTimeOffDTO> dtoList = new ArrayList<>();
		for (Object[] row : resultList) {
			ProfileTimeOffDTO dto = new ProfileTimeOffDTO();
			dto.setFromTime((String) row[0]);
			dto.setToTime((String) row[1]);
			dto.setFromDate((String) row[2]);
			dtoList.add(dto);
		}
		return dtoList;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public ProfileTimeOffDTO verifySingleAppointmentWithProfileTimeOff(long profileId, String profileTimeOffDate) {



		// Get the CriteriaBuilder from EntityManager
		CriteriaBuilder builder = entityManager.getCriteriaBuilder();

		// Define the query
		CriteriaQuery<Object[]> query = builder.createQuery(Object[].class);
		Root<ProfileTimeoff> root = query.from(ProfileTimeoff.class);

		// Create date conversion if necessary using function support in CriteriaBuilder
		Expression<String> startTimePath = builder.function("TO_CHAR", String.class, root.get("startTime"), builder.literal("MM/DD/YYYY"));

		// Create predicates for date and profile ID
		Predicate datePredicate = builder.equal(startTimePath, profileTimeOffDate);
		Predicate profilePredicate = builder.equal(root.get("profileId"), profileId);

		// Select fields using the function support
		query.multiselect(
				builder.function("TO_CHAR", String.class, root.get("startTime"), builder.literal("hh24:mi")),
				builder.function("TO_CHAR", String.class, root.get("endTime"), builder.literal("hh24:mi")),
				builder.function("TO_CHAR", String.class, root.get("startTime"), builder.literal("MM/DD/YYYY"))
		).where(builder.and(datePredicate, profilePredicate));

		// Execute the query using EntityManager
		List<Object[]> resultList = entityManager.createQuery(query).getResultList();

		// Create the DTO to hold the results
		ProfileTimeOffDTO dto = new ProfileTimeOffDTO();
		if (!resultList.isEmpty()) {
			Object[] row = resultList.get(0);
			dto.setFromTime((String) row[0]);
			dto.setToTime((String) row[1]);
			dto.setFromDate((String) row[2]);
		}

		// Return the populated DTO
		return dto;
	}


	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public ProfileTimeOffDTO  getProfileTimeOffIdbyDate(long profileId,String profileTimeOffDate) {
		//Session session = super.getSessionFactory().getCurrentSession();
		CriteriaBuilder builder = entityManager.getCriteriaBuilder();
		CriteriaQuery<Object[]> query = builder.createQuery(Object[].class);
		Root<ProfileTimeoff> root = query.from(ProfileTimeoff.class);

		// Convert date if necessary
		Path<String> startTimePath = (Path<String>) builder.function("TO_CHAR", String.class, root.get("startTime"), builder.literal("MM/DD/YYYY"));

		// Create predicates
		Predicate datePredicate = builder.equal(startTimePath, profileTimeOffDate);
		Predicate profilePredicate = builder.equal(root.get("profileId"), profileId);

		// Select fields
		query.multiselect(
				root.get("profiletimeoffId"),
				startTimePath
		).where(builder.and(datePredicate, profilePredicate));

		// Execute query and process results
		List<Object[]> resultList = entityManager.createQuery(query).getResultList();
		ProfileTimeOffDTO dto = new ProfileTimeOffDTO();
		if (!resultList.isEmpty()) {
			Object[] row = resultList.get(0);
			dto.setProfiletimeoffId(((Number) row[0]).longValue());
			dto.setFromDate((String) row[1]);
		}
		return dto;
	}
}
