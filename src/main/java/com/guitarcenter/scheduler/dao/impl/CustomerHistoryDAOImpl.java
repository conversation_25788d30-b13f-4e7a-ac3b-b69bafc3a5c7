package com.guitarcenter.scheduler.dao.impl;


//import org.hibernate.Criteria;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.CustomerHistoryDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.CustomerHistory;
import com.guitarcenter.scheduler.model.Person;

@Repository("customerHistoryDAO")
public class CustomerHistoryDAOImpl extends AbstractDAOImpl<CustomerHistory> implements CustomerHistoryDAO {

	@PersistenceContext
	private EntityManager entityManager;

	public CustomerHistoryDAOImpl() {
		super(CustomerHistory.class);
	}
 
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void saveCustomerHistory(CustomerHistory ct) {

		entityManager.persist(ct);
  
	}

	@Override
	protected void updateAuditor(CustomerHistory pT, Person pUpdatedBy) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchOne(CustomerHistory pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) { //
	 * TODO Auto-generated method stub
	 * 
	 * }
	 */
	@Override
	protected void fetchMany(CustomerHistory pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected Criterion<CustomerHistory, CustomerHistory> getCriterionInstance() {
		// TODO Auto-generated method stub
		return null;
	} 
 
}
