package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;
import java.util.List;

//import org.hibernate.Criteria;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import org.hibernate.FetchMode;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.RoleDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.RoleCriterion;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Role;

@Repository("roleDAO")
public class RoleDAOImpl extends AbstractDAOImpl<Role> implements RoleDAO {

	public RoleDAOImpl() {
		super(Role.class);
	}

	@PersistenceContext
	private EntityManager entityManager;

	@Override
	protected void updateAuditor(Role pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(Role pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}



	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) {
	 * addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy",
	 * FetchMode.JOIN, pCriteria); addFetchCriteria(FETCH_SITE, pFetchMode, "site",
	 * FetchMode.JOIN, pCriteria); }
	 */



	@Override
	protected void fetchMany(Role pResult, int pFetchMode) {
	}



	@Override
	protected Criterion<Role, Role> getCriterionInstance() {
		return RoleCriterion.getInstance();
	}


	@Override
	public Role geRoleOne(String externalId) {
		Role r= null;
		try {
			Query query = entityManager.createQuery("from Role where roleName = :roleName");
			query.setParameter("roleName", externalId);
			List<Role> lr = query.getResultList();
			r = lr.get(0);
		} catch (Exception e) {
			System.out.println("RAJ12");
			e.printStackTrace();
		}
		return r;
	}

}
