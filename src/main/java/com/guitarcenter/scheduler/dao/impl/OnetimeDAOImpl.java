/**
 * @Title: OnetimeDAOImpl.java
 * @Package com.guitarcenter.scheduler.dao.impl
 * @Description: 
 * Copyright: Copyright (c) 2014 
 * Company:
 * 
 * <AUTHOR>
 * @date May 29, 2014 3:24:12 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_INSTRUCTOR;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;
import java.util.List;

//import org.hibernate.Criteria;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import org.hibernate.FetchMode;
import org.hibernate.query.Query;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.OnetimeDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.OnetimeCriterion;
import com.guitarcenter.scheduler.model.Onetime;
import com.guitarcenter.scheduler.model.Person;


/**
 * @ClassName: OnetimeDAOImpl
 * @Description: 
 * <AUTHOR>
 * @date May 29, 2014 3:24:12 PM
 *
 */
@Repository("onetimeDAO")
public class OnetimeDAOImpl extends AbstractDAOImpl<Onetime> implements OnetimeDAO{

	@PersistenceContext
	private EntityManager entityManager;

	/**
	  * OnetimeDAOImpl. 
	  * <p>Title: </p>
	  * <p>Description: </p>
	  */
	public OnetimeDAOImpl() {
		super(Onetime.class);
	}

	/**
	  * <p>Title: getOnetimeByInstructorId</p>
	  * <p>Description: </p>
	  * @param instructorId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.OnetimeDAO#getOnetimeByInstructorId(long)
	  */
	@Override
	public List<Onetime> getOnetimeByInstructorId(long instructorId) {
		//5055
		Query query = (Query) entityManager.createNativeQuery(
				"select   t.* from Onetime t  where  t.end_time  >= sysdate and t.instructor_Id = :instructorId order by t.start_Time asc",Onetime.class
		);
		query.unwrap(org.hibernate.query.NativeQuery.class);
		query.setParameter("instructorId", instructorId);
		return query.getResultList();
	}

	/**
	  * <p>Title: getDisplayOnetimeByInstructorId</p>
	  * <p>Description: </p>
	  * @param instructorId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.OnetimeDAO#getDisplayOnetimeByInstructorId(long)
	  */
	@Override
	public List<Onetime> getDisplayOnetimeByInstructorId(long instructorId) {
		//trunc value for endTime  5055
		TypedQuery<Onetime> query = entityManager.createQuery(
				"from Onetime where  endTime  >= to_date(to_char(sysdate), 'DD-MON-YY')    and instructor.instructorId = :instructorId order by updated desc",
				Onetime.class
		);
		query.setParameter("instructorId", instructorId);
		return query.getResultList();
	}

	/**
	  * <p>Title: updateAuditor</p>
	  * <p>Description: </p>
	  * @param pT
	  * @param pUpdatedBy
	  * @see com.guitarcenter.scheduler.dao.impl.AbstractDAOImpl#updateAuditor(java.lang.Object, com.guitarcenter.scheduler.model.Person)
	  */
	@Override
	protected void updateAuditor(Onetime pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}

	/**
	  * <p>Title: fetchOne</p>
	  * <p>Description: </p>
	  * @param pResult
	  * @param pFetchMode
	  * @see com.guitarcenter.scheduler.dao.impl.AbstractDAOImpl#fetchOne(java.lang.Object, int)
	  */
	@Override
	protected void fetchOne(Onetime pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_INSTRUCTOR, pFetchMode, pResult.getInstructor());
	}

	/**
	  * <p>Title: fetchOne</p>
	  * <p>Description: </p>
	  * @param pCriteria
	  * @param pFetchMode
	  * @see com.guitarcenter.scheduler.dao.impl.AbstractDAOImpl#fetchOne(org.hibernate.Criteria, int)
	  */
	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) {
	 * addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy",
	 * FetchMode.JOIN, pCriteria); addFetchCriteria(FETCH_INSTRUCTOR, pFetchMode,
	 * "instructor", FetchMode.JOIN, pCriteria); }
	 */

	/**
	  * <p>Title: fetchMany</p>
	  * <p>Description: </p>
	  * @param pResult
	  * @param pFetchMode
	  * @see com.guitarcenter.scheduler.dao.impl.AbstractDAOImpl#fetchMany(java.lang.Object, int)
	  */
	@Override
	protected void fetchMany(Onetime pResult, int pFetchMode) {
	}

	/**
	  * <p>Title: getCriterionInstance</p>
	  * <p>Description: </p>
	  * @return
	  * @see com.guitarcenter.scheduler.dao.impl.AbstractDAOImpl#getCriterionInstance()
	  */
	@Override
	protected Criterion<Onetime, Onetime> getCriterionInstance() {
		return OnetimeCriterion.getInstance();
	}

	/**
	  * <p>Title: checkOnetimeAvailabilityByProfileId</p>
	  * <p>Description: </p>
	  * @param startDate
	  * @param startTime
	  * @param endTime
	  * @param profileId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.OnetimeDAO#checkOnetimeAvailabilityByProfileId(java.lang.String, java.lang.String, java.lang.String, long)
	  */
	@Override
	public boolean checkOnetimeAvailabilityByProfileId(String startDate, String startTime, String endTime, long profileId) {
		TypedQuery<Boolean> query = entityManager.createNamedQuery("onetime.checkOnetimeAvailabilityByProfileId", Boolean.class);
		query.setParameter(1, startDate);
		query.setParameter(2, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(3, startTime);
		query.setParameter(4, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(5, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(6, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(7, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(8, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(9, endTime);
		query.setParameter(10, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(12, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(13, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(14, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(15, profileId);
		return query.getSingleResult();
	}

	/**
	 * <p>Title: checkOnetimeByTime</p>
	 * <p>Description: </p>
	 * @param startDate
	 * @param startTime
	 * @param endTime
	 * @param instructorId
	 * @return
	 * @see com.guitarcenter.scheduler.dao.OnetimeDAO#checkOnetimeByTime(java.lang.String, java.lang.String, java.lang.String, long)
	 */
	@Override
	public boolean checkOnetimeByTime(String startDate, String startTime,String endDate,
			String endTime, long instructorId) {
		TypedQuery<Onetime> query = entityManager.createNamedQuery("onetime.checkOnetimeByTime", Onetime.class);

		query.setParameter(1, startDate+" "+startTime);
		query.setParameter(2, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(3, endDate+" "+endTime);
		query.setParameter(4, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(5, startDate+" "+startTime);
		query.setParameter(6, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(7, endDate+" "+endTime);
		query.setParameter(8, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(9, startDate+" "+startTime);
		query.setParameter(10, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(11, endDate+" "+endTime);
		query.setParameter(12, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(13, instructorId);
		// System.out.println("query.getResultList().isEmpty()  "+query.getResultList().isEmpty());


		return query.getResultList().isEmpty();
	}

	/**
	 * <p>Title: getOnetimeByTime</p>
	 * <p>Description: </p>
	 * @param startDate
	 * @param startTime
	 * @param endTime
	 * @param instructorId
	 * @return 
	 * @see com.guitarcenter.scheduler.dao.OnetimeDAO#getOnetimeByTime(java.lang.String, java.lang.String, java.lang.String, long)
	 */

	@Override
	public List<Onetime> getOnetimeByTime(String startDate, String startTime, String endTime, long instructorId) {
		TypedQuery<Onetime> query = entityManager.createNamedQuery("onetime.getOnetimeByTime", Onetime.class);
		query.setParameter("startTime1", startDate + " " + startTime);
		query.setParameter("timezone1", DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter("startTime2", startDate + " " + endTime);
		query.setParameter("timezone2", DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter("instructorId", instructorId);
		return query.getResultList();
	}

}
