package com.guitarcenter.scheduler.dao.impl;

import java.util.List;

//import org.hibernate.Criteria;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import org.hibernate.Session;
import org.hibernate.query.Query;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.OnlineAvailabilityDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.Onetime;
import com.guitarcenter.scheduler.model.OnlineAvailability;
import com.guitarcenter.scheduler.model.Person;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

@Repository("onlineAvailabilityDAO")
public class OnlineAvailabilityDAOImpl extends AbstractDAOImpl<OnlineAvailability> implements OnlineAvailabilityDAO {

	@PersistenceContext
	private EntityManager entityManager;

	public OnlineAvailabilityDAOImpl() {
		super(OnlineAvailability.class);
	}

	@Override
	public List<OnlineAvailability> getOnlineAvailabilityByInstructorId(long instructorId) {
		CriteriaBuilder builder = entityManager.getCriteriaBuilder();
		CriteriaQuery<OnlineAvailability> query = builder.createQuery(OnlineAvailability.class);
		Root<OnlineAvailability> root = query.from(OnlineAvailability.class);

		// Create a predicate to filter by instructorId
		Predicate predicate = builder.equal(root.get("instructor").get("instructorId"), instructorId);
		query.select(root).where(predicate);

		TypedQuery<OnlineAvailability> typedQuery = entityManager.createQuery(query);
		return typedQuery.getResultList();
	}

	@Override
	public List<OnlineAvailability> getDisplayOnlineAvailabilityeByInstructorId(long instructorId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public boolean checkOnlineAvailabilityByProfileId(String startDate, String startTime, String endTime, long profileId) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public boolean checkOnlineAvailabilityByTime(String startDate, String startTime, String endDate, String endTime, long instructorId) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public List<Onetime> getOnlineAvailabilityByTime(String startDate, String startTime, String endTime, long instructorId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void saveOrUpdate(OnlineAvailability pT, Person pUpdatedBy) {
		// Update the auditor before saving/updating
		updateAuditor(pT, pUpdatedBy);

		// Use EntityManager to merge the entity
		if (pT.getOnlineAvailabilityId() == null) {
			entityManager.persist(pT);  // Save the entity if it's new
		} else {
			entityManager.merge(pT);  // Update the entity if it already exists
		}
	}


	@Override
	protected void updateAuditor(OnlineAvailability pT, Person pUpdatedBy) {
		// TODO Auto-generated method stub
	}

	@Override
	protected void fetchOne(OnlineAvailability pResult, int pFetchMode) {
		// TODO Auto-generated method stub
	}

	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) { //
	 * TODO Auto-generated method stub }
	 */

	@Override
	protected void fetchMany(OnlineAvailability pResult, int pFetchMode) {
		// TODO Auto-generated method stub
	}

	@Override
	protected Criterion<OnlineAvailability, OnlineAvailability> getCriterionInstance() {
		// TODO Auto-generated method stub
		return null;
	}
}
