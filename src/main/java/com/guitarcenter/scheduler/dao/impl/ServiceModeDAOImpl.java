package com.guitarcenter.scheduler.dao.impl;

//---GSSP Instructor Mode update changes
import java.util.List;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
//import org.hibernate.Criteria;
import org.hibernate.Session;
//import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.ServiceModeDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ServiceMode;

@Repository("serviceModeDAO")
public class ServiceModeDAOImpl extends AbstractDAOImpl<ServiceMode> implements ServiceModeDAO {

	@PersistenceContext
	private EntityManager entityManager;

	private static final Logger	 LOGGER	= LoggerFactory.getLogger(ServiceModeDAOImpl.class);

	public ServiceModeDAOImpl() {
		super(ServiceMode.class);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public List<ServiceMode> getServiceModeList() throws Exception {
	//	Session session = super.getSessionFactory().getCurrentSession();
		CriteriaBuilder builder = entityManager.getCriteriaBuilder();

		// Create CriteriaQuery for ServiceMode
		CriteriaQuery<ServiceMode> query = builder.createQuery(ServiceMode.class);
		Root<ServiceMode> root = query.from(ServiceMode.class);
		query.select(root);

		// Execute query and get results
		List<ServiceMode> list = entityManager.createQuery(query).getResultList();
		return list;
	}


	@SuppressWarnings("unchecked")
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public ServiceMode getServiceMode(long serviceModeId) {
		//Session session = super.getSessionFactory().getCurrentSession();
		CriteriaBuilder builder = entityManager.getCriteriaBuilder();

		// Create CriteriaQuery for ServiceMode
		CriteriaQuery<ServiceMode> query = builder.createQuery(ServiceMode.class);
		Root<ServiceMode> root = query.from(ServiceMode.class);

		// Add restriction for serviceModeId
		query.select(root).where(builder.equal(root.get("serviceModeId"), serviceModeId));

		// Execute query and get a single result
		ServiceMode serviceMode = entityManager.createQuery(query).getSingleResult();
		return serviceMode;
	}


	@Override
	protected void updateAuditor(ServiceMode pT, Person pUpdatedBy) {

	}

	@Override
	protected void fetchOne(ServiceMode pResult, int pFetchMode) {
	}


	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) { }
	 */


	@Override
	protected void fetchMany(ServiceMode pResult, int pFetchMode) {

	}


	@Override
	protected Criterion<ServiceMode, ServiceMode> getCriterionInstance() {
		return null;
	}



}
