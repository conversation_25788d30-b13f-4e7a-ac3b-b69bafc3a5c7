package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ACTIVITY;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION_PROFILE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;

////import org.hibernate.Criteria;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import org.hibernate.FetchMode;
////import org.hibernate.SQLQuery;
import org.hibernate.Session;

import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.ProfileActivityDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.ProfileActivityCriterion;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileActivity;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Repository("profileActivityDAO")
public class ProfileActivityDAOImpl extends AbstractDAOImpl<ProfileActivity> implements ProfileActivityDAO {

	@PersistenceContext
	private EntityManager entityManager;

	public ProfileActivityDAOImpl() {
		super(ProfileActivity.class);
	}



	@Override
	protected void updateAuditor(ProfileActivity pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(ProfileActivity pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
		addFetchCriteria(FETCH_LOCATION_PROFILE, pFetchMode, pResult.getLocationProfile());
		addFetchCriteria(FETCH_ACTIVITY, pFetchMode, pResult.getActivity());

	}



	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) {
	 * addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy",
	 * FetchMode.JOIN, pCriteria); addFetchCriteria(FETCH_SITE, pFetchMode, "site",
	 * FetchMode.JOIN, pCriteria); addFetchCriteria(FETCH_LOCATION_PROFILE,
	 * pFetchMode, "locationProfile", FetchMode.JOIN, pCriteria);
	 * addFetchCriteria(FETCH_ACTIVITY, pFetchMode, "activity", FetchMode.JOIN,
	 * pCriteria); }
	 */



	@Override
	protected void fetchMany(ProfileActivity pResult, int pFetchMode) {
	}



	@Override
	protected Criterion<ProfileActivity, ProfileActivity> getCriterionInstance() {
		return ProfileActivityCriterion.getInstance();
	}
	
	/**
	 * <p>Title: deleteProfileActivityByProfileIdServiceId</p>
	 * <p>Description: </p>
	 * @param profileId
	 * @param serviceId
	 * @throws Exception
	 * @see com.guitarcenter.scheduler.dao.ProfileActivityDAO#deleteProfileActivityByProfileIdServiceId(java.lang.Long, java.lang.Long)
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void deleteProfileActivityByProfileIdServiceId(Long profileId, Long serviceId) throws Exception {
		String sql = "DELETE FROM profile_activity p " +
				"WHERE p.profile_id = :profileId " +
				"AND EXISTS (SELECT 1 FROM activity a WHERE a.activity_id = p.activity_id AND a.service_id = :serviceId)";

		try {
			// Use EntityManager to create a native query
			Query query = entityManager.createNativeQuery(sql);

			// Set parameters for the query
			query.setParameter("profileId", profileId);
			query.setParameter("serviceId", serviceId);

			// Execute the update
			query.executeUpdate();

		} catch (Exception e) {
			// Rethrow the exception with the original message
			throw new Exception(e.getMessage(), e);
		}
	}

}
