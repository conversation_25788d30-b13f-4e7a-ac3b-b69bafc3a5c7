package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;

//import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.SiteDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.SiteCriterion;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Site;

@Repository("siteDAO")
public class SiteDAOImpl extends AbstractDAOImpl<Site> implements SiteDAO {



	public SiteDAOImpl() {
		super(Site.class);
	}



	@Override
	protected void updateAuditor(Site pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(Site pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
	}



	/*
	 * @Override protected void fetchOne(Criteria pCriteria, int pFetchMode) {
	 * addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy",
	 * FetchMode.JOIN, pCriteria); }
	 */



	@Override
	protected void fetchMany(Site pResult, int pFetchMode) {
	}



	@Override
	protected Criterion<Site, Site> getCriterionInstance() {
		return SiteCriterion.getInstance();
	}

}
