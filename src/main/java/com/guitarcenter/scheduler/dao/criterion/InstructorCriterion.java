
package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.*;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.util.*;

import com.guitarcenter.scheduler.dao.criterion.util.InstructorSortedComparator;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import org.hibernate.ScrollableResults;
import org.hibernate.Session;
import org.hibernate.query.NativeQuery;
import org.hibernate.type.StandardBasicTypes;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.criterion.dto.*;
import com.guitarcenter.scheduler.dto.*;
import com.guitarcenter.scheduler.model.*;
import com.guitarcenter.scheduler.model.enums.Enabled;

public abstract class InstructorCriterion<E> extends AbstractCriterion<Instructor, E> implements Criterion<Instructor, E> {

    // Added for GSSP-240, for debugging purpose
    private static final Logger Log = LoggerFactory.getLogger(InstructorCriterion.class);

    private static final Criterion<Instructor, Instructor> DEFAULT_INSTANCE = new InstructorCriterion<Instructor>() {
    };

    private InstructorCriterion() {
        super(Instructor.class);
    }

    @Override
    protected String getFetchScript(int pFetchMode) {
        StringBuilder sb = new StringBuilder();
        sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
        sb.append(addFetchHQL(FETCH_AVAILABILITY, pFetchMode, "t.availability", true));
        sb.append(addFetchHQL(FETCH_LOCATION, pFetchMode, "t.location", true));
        sb.append(addFetchHQL(FETCH_PERSON, pFetchMode, "t.person", true));
        sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
        return sb.toString();
    }

    @Override
    public List<E> search(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }

    @Override
    public E get(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }

    public static Criterion<Instructor, Instructor> getInstance() {
        return DEFAULT_INSTANCE;
    }

    public static Criterion<Instructor, Instructor> findByLocation(final long pLocationId) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Instructor t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" where t.location.locationId = :locationId order by t.instructorId");

                TypedQuery<Instructor> query = entityManager.createQuery(sb.toString(), Instructor.class);
                query.setParameter("locationId", pLocationId);
                return query.getResultList();
            }
        };
    }

    public static Criterion<Instructor, Instructor> findByLocation(final long pLocationId, final Enabled pEnabled) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Instructor t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" where t.location.locationId = :locationId");
                sb.append(" and t.enabled = :enabled");
                sb.append(" order by t.instructorId");

                TypedQuery<Instructor> query = entityManager.createQuery(sb.toString(), Instructor.class);
                query.setParameter("locationId", pLocationId);
                query.setParameter("enabled", pEnabled.toString());
                return query.getResultList();
            }
        };
    }

    public static Criterion<Instructor, Instructor> findByActivityId(final long pActivityId) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Instructor t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" left join fetch t.activities a ");
                sb.append(" where a.activityId = :activityId order by t.instructorId");

                TypedQuery<Instructor> query = entityManager.createQuery(sb.toString(), Instructor.class);
                query.setParameter("activityId", pActivityId);
                return query.getResultList();
            }
        };
    }

    public static Criterion<Instructor, Instructor> findByLocationIdAndActivityId(final long pLocationId,
                                                                                  final long pActivityId, final Enabled pEnabled) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Instructor t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" left join fetch t.activities a ");
                sb.append(" where a.activityId = :activityId");
                sb.append(" and t.location.locationId = :locationId");
                sb.append(" and t.enabled = :enabled");
                sb.append(" order by t.instructorId");

                TypedQuery<Instructor> query = entityManager.createQuery(sb.toString(), Instructor.class);
                query.setParameter("activityId", pActivityId);
                query.setParameter("locationId", pLocationId);
                query.setParameter("enabled", pEnabled.toString());
                return query.getResultList();
            }
        };
    }

    public static Criterion<Instructor, Instructor> findByLocationIdAndActivityIds(final long pLocationId,
                                                                                   final Long... pActivityIds) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(EntityManager entityManager, int pFetchMode) {
                if (pActivityIds.length == 0) {
                    return Collections.emptyList();
                }
                StringBuilder sb = new StringBuilder("select distinct t from Instructor t ");
                sb.append(getFetchScript(pFetchMode));
                //   sb.append(" left join fetch t.activities a ");
                //  sb.append(" where a.activityId in (:activityIds)");
                sb.append(" Where t.location.locationId = :locationId");
                sb.append(" and t.enabled = :enabled");
                sb.append(" order by t.instructorId");

                TypedQuery<Instructor> query = entityManager.createQuery(sb.toString(), Instructor.class);
                //  query.setParameter("activityIds", pActivityIds);
                query.setParameter("locationId", pLocationId);
                query.setParameter("enabled", Enabled.Y);
                return query.getResultList();
            }
        };
    }

    public static Criterion<Instructor, Instructor> findByLocationIdAndActivityIdsInsAVL(final long instructorId) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select distinct t from Instructor t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" left join fetch t.activities a ");
                sb.append(" where t.instructorId = :instructorId");
                sb.append(" and t.enabled = :enabled");
                sb.append(" order by t.instructorId");

                TypedQuery<Instructor> query = entityManager.createQuery(sb.toString(), Instructor.class);
                query.setParameter("instructorId", instructorId);
                query.setParameter("enabled", Enabled.Y);
                return query.getResultList();
            }
        };
    }

    public static Criterion<Instructor, Instructor> findByInstructorIds(final Long... pInstructorIds) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(EntityManager entityManager, int pFetchMode) {
                List<Long> instructorIds;
                if (null == pInstructorIds || pInstructorIds.length == 0) {
                    return Collections.emptyList();
                } else {
                    instructorIds = Arrays.asList(pInstructorIds);
                }
                StringBuilder sb = new StringBuilder("from Instructor t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" where t.instructorId in (:instructorIds) order by t.instructorId");

                TypedQuery<Instructor> query = entityManager.createQuery(sb.toString(), Instructor.class);
                query.setParameter("instructorIds", instructorIds);
                return query.getResultList();
            }
        };
    }

    public static Criterion<Instructor, Instructor> findByInstructorId(final long pInstructorId) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Instructor t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" where t.instructorId = :instructorId order by t.instructorId");

                TypedQuery<Instructor> query = entityManager.createQuery(sb.toString(), Instructor.class);
                query.setParameter("instructorId", pInstructorId);
                return query.getResultList();
            }
        };
    }

    public static Criterion<Instructor, Instructor> findBySiteId(final long siteId) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(EntityManager entityManager, int pFetchMode) {
                TypedQuery<Instructor> query = entityManager.createQuery(new StringBuilder("from Instructor t ")
                        .append(getFetchScript(pFetchMode))
                        .append(" where t.site.siteId = :siteId")
                        .toString(), Instructor.class);
                query.setParameter("siteId", siteId);
                return query.getResultList();
            }
        };
    }

    public static Criterion<Instructor, Instructor> findByExternalId(final long pSiteId, final String pExternalId) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(EntityManager entityManager, int pFetchMode) {
                TypedQuery<Instructor> query = entityManager.createQuery(new StringBuilder("from Instructor t ")
                        .append(getFetchScript(pFetchMode))
                        .append(" where t.site.siteId = :siteId")
                        .append(" and t.externalId = :externalId")
                        .toString(), Instructor.class);
                query.setParameter("siteId", pSiteId);
                query.setParameter("externalId", pExternalId);
                return query.getResultList();
            }
        };
    }

    public static Criterion<Instructor, Instructor> findinstructorByExternal(final String pExternalId) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select t.person_id ,t.site_id");
                sb.append(" from instructor t");
                sb.append(" left join person a on t.person_id = a.person_id");
                sb.append(" where t.external_id = :externalId");

                NativeQuery<?> query = (NativeQuery<?>) entityManager.createNativeQuery(sb.toString());
                query.addScalar("person_id", StandardBasicTypes.LONG);
                query.addScalar("site_id", StandardBasicTypes.STRING);
                query.setParameter("externalId", pExternalId);

                ScrollableResults scroll = query.scroll();
                List<Instructor> result = new ArrayList<>();

                while (scroll.next()) {
                    Object[] objects = (Object[]) scroll.get();
                    Long person_id = (Long) objects[0];
                    String site_id = (String) objects[1];
                    Person pObj = new Person();
                    pObj.setPersonId(person_id);
                    Site sObj = new Site();
                    sObj.setSiteId(Long.parseLong(site_id));
                    Instructor dto = new Instructor(pObj, sObj);
                    result.add(dto);
                }

                scroll.close();
                return result;
            }
        };
    }

    public static Criterion<Instructor, Boolean> checkAppointmentDateTimeByInstructorAndDateTime(final Long pInstructorId, final Date pStartDate, final Date pEndDate, final Integer pDayOfWeek) {
        return new InstructorCriterion<Boolean>() {
            @Override
            @SuppressWarnings("unchecked")
            public Boolean get(EntityManager entityManager, int pFetchMode) {
                Boolean result = true;
                DateTime startTime = new DateTime(pStartDate);
                DateTime endTime = new DateTime(pEndDate);
                String dayOfWeek = String.valueOf(pDayOfWeek);
                String sql = "select t.instructor_id," +
                        " to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') as min_time," +
                        " to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') as max_time" +
                        " from appointment t" +
                        " where to_char(t.start_time - 1, 'd') = :dayOfWeek" +
                        " and t.instructor_id = :instructorId" +
                        " and t.start_time >= sysdate" +
                        " and (t.canceled is null or t.canceled = 'N' or t.canceled = 'H')";

                NativeQuery<?> query = (NativeQuery<?>) entityManager.createNativeQuery(sql);
                query.setParameter("instructorId", pInstructorId);
                query.setParameter("dayOfWeek", dayOfWeek);
                query.addScalar("instructor_id", StandardBasicTypes.LONG);
                query.addScalar("min_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("max_time", StandardBasicTypes.TIMESTAMP);
                List<Object[]> list = (List<Object[]>) query.list();
                if (list == null || list.isEmpty()) {
                    return result;
                }
                String checkOntime = " select to_char(ot.start_time, 'YYYY-MM-DD HH24:MI:SS') as sTime, to_char(ot.end_time, 'YYYY-MM-DD HH24:MI:SS')as eTime" +
                        " from onetime ot" +
                        " where ot.INSTRUCTOR_ID=:instructorId" +
                        " and ot.start_time >=sysdate" +
                        " and to_char(ot.start_time - 1, 'd') = :dayOfWeek" +
                        " order by sTime";

                NativeQuery<?> checkTimeQuery = (NativeQuery<?>) entityManager.createNativeQuery(checkOntime);
                checkTimeQuery.setParameter("instructorId", pInstructorId);
                checkTimeQuery.setParameter("dayOfWeek", dayOfWeek);
                checkTimeQuery.addScalar("sTime", StandardBasicTypes.TIMESTAMP);
                checkTimeQuery.addScalar("eTime", StandardBasicTypes.TIMESTAMP);
                List<Object[]> timeList = (List<Object[]>) checkTimeQuery.list();
                List<TimeIntervalDTO> sortList = new ArrayList<>();
                if (timeList != null && !timeList.isEmpty()) {
                    for (Object[] objectTime : timeList) {
                        TimeIntervalDTO timeInterval = new TimeIntervalDTO();
                        timeInterval.setStartTime(new DateTime(objectTime[0]));
                        timeInterval.setEndTime(new DateTime(objectTime[1]));
                        sortList.add(timeInterval);
                        TimeIntervalDTO availabilityTime = new TimeIntervalDTO();
                        availabilityTime.setStartTime(timeInterval.getStartTime()
                                .withMillisOfSecond(0)
                                .withTime(startTime.getHourOfDay(), startTime.getMinuteOfHour(), startTime.getSecondOfMinute(), startTime.getMillisOfSecond()));
                        availabilityTime.setEndTime(timeInterval.getEndTime()
                                .withMillisOfSecond(0)
                                .withTime(endTime.getHourOfDay(), endTime.getMinuteOfHour(), endTime.getSecondOfMinute(), endTime.getMillisOfSecond()));
                        sortList.add(availabilityTime);
                    }
                    sortList = DateTimeUtil.concatTime(sortList);
                }
                for (Object[] columns : list) {
                    result = false;
                    DateTime start_time = new DateTime(columns[1]);
                    DateTime end_time = new DateTime(columns[2]);
                    if (sortList != null && !sortList.isEmpty()) {
                        for (TimeIntervalDTO sort : sortList) {
                            DateTime sTime = sort.getStartTime();
                            DateTime eTime = sort.getEndTime();
                            if ((sTime.isBefore(start_time) || sTime.isEqual(start_time)) && (eTime.isAfter(end_time) || eTime.isEqual(end_time))) {
                                result = true;
                                break;
                            }
                        }
                    }
                    if (!result) {
                        DateTime availabilityStartTime = start_time.withMillisOfSecond(0)
                                .withTime(startTime.getHourOfDay(), startTime.getMinuteOfHour(), startTime.getSecondOfMinute(), startTime.getMillisOfSecond());
                        DateTime availabilityEndTime = end_time.withMillisOfSecond(0)
                                .withTime(endTime.getHourOfDay(), endTime.getMinuteOfHour(), endTime.getSecondOfMinute(), endTime.getMillisOfSecond());
                        if ((availabilityStartTime.isBefore(start_time) || availabilityStartTime.isEqual(start_time)) && (availabilityEndTime.isAfter(end_time) || availabilityEndTime.isEqual(end_time))) {
                            result = true;
                        }
                    }
                    if (!result) break;
                }
                return result;
            }
        };
    }

    // Add similar conversions for the rest of the methods in the class...


    // Example: Converting a native SQL query method to Hibernate 6.4.2
    public static Criterion<Instructor, InstructorActivitiesDTO> findInstructorActivities() {
        return new InstructorCriterion<InstructorActivitiesDTO>() {
            @Override
            public List<InstructorActivitiesDTO> search(EntityManager entityManager) {
                NativeQuery<?> query;
                Log.info("Entered into InstructorCriterion.findInstructorActivities()");

                StringBuilder sb = new StringBuilder("select l.EXTERNAL_ID, P.FIRST_NAME, P.LAST_NAME, l.LOCATION_NAME, A.ACTIVITY_NAME");
                sb.append(" from location l");
                sb.append(" join LOCATION_PROFILE lp on l.PROFILE_ID = lp.PROFILE_ID");
                sb.append(" join INSTRUCTOR I on l.LOCATION_ID = I.LOCATION_ID");
                sb.append(" join PERSON P on P.PERSON_ID = I.PERSON_ID");
                sb.append(" join INSTRUCTOR_ACTIVITIES IA on I.INSTRUCTOR_ID = IA.INSTRUCTOR_ID");
                sb.append(" join ACTIVITY A on IA.ACTIVITY_ID = A.ACTIVITY_ID");
                sb.append(" where lp.ENABLED = 'Y' and I.ENABLED = 'Y' and I.STATUS = 'A'  ");
                sb.append(" order by l.EXTERNAL_ID, P.FIRST_NAME, P.LAST_NAME");

                query = (NativeQuery<?>) entityManager.createNativeQuery(sb.toString());

                query.addScalar("EXTERNAL_ID", StandardBasicTypes.STRING);
                query.addScalar("FIRST_NAME", StandardBasicTypes.STRING);
                query.addScalar("LAST_NAME", StandardBasicTypes.STRING);
                query.addScalar("LOCATION_NAME", StandardBasicTypes.STRING);
                query.addScalar("ACTIVITY_NAME", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();
                List<InstructorActivitiesDTO> resultList = new ArrayList<>();

                while (scroll.next()) {
                    Object[] objects = (Object[]) scroll.get();
                    String externalId = (String) objects[0];
                    String firstName = (String) objects[1];
                    String lastName = (String) objects[2];
                    String locationName = (String) objects[3];
                    String activityName = (String) objects[4];

                    InstructorActivitiesDTO dto = new InstructorActivitiesDTO(externalId, locationName, firstName, lastName, activityName);
                    resultList.add(dto);
                }

                scroll.close();
                Log.info("InstructorCriterion.findInstructorActivities(), after retrieving DB values");

                return resultList;
            }
        };
    }

    // Continue with similar updates for the remaining methods...

    public static Criterion<Instructor, Instructor> findByProfileIdAndActivityIdAndDateTime(final long pProfileId, final long pActivityId, final Date pStartDate, final Date pEndDate, final Long pExcludeAppointmentId) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(EntityManager entityManager, int pFetchMode) {
                DateTime startTime = new DateTime(pStartDate);
                DateTime endTime = new DateTime(pEndDate);
                int dayOfWeek = startTime.getDayOfWeek();
                StringBuilder sb = new StringBuilder("select t.* ");
                sb.append("  from INSTRUCTOR t");
                sb.append("  left join person p");
                sb.append("    on t.person_id = p.person_id");
                sb.append("  left join location l");
                sb.append("    on t.location_id = l.location_id");
                sb.append("  left join availability ay");
                sb.append("    on t.availability_id = ay.availability_id");
                sb.append("  left join instructor_activities a_i ");
                sb.append("    on t.instructor_id = a_i.instructor_id");
                sb.append("  left join onetime ot ");
                sb.append("    on t.instructor_id = ot.instructor_id");
                sb.append("  where l.profile_id = :profileId");
                sb.append("    and a_i.activity_id = :activityId");
                sb.append("    and t.enabled = 'Y'");
                DayOfWeek dayOfWeek_enum = DayOfWeek.of(dayOfWeek);
                if (dayOfWeek_enum == null) return Collections.EMPTY_LIST;
                /*sb.append("    and ( ");
                dayOfWeek_enum.fillCondition(sb);
                sb.append("         or ");
                sb.append("            ((to_char(ot.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS'))");
                sb.append("             and (to_char(ot.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS')))");
                sb.append("         )");*/
                Query query = entityManager.createNativeQuery(sb.toString(), Instructor.class);
                // query.addEntity("t", Instructor.class);
                //query.addJoin("p", "t.person"); //TODO::
                query.setParameter("profileId", pProfileId);
                query.setParameter("activityId", pActivityId);
                //query.setTimestamp("startTime", pStartDate);
                //query.setTimestamp("endTime", pEndDate);
                List<Instructor> list = query.getResultList();
                Map<Long, Instructor> maps = new HashMap<Long, Instructor>();
                sb = new StringBuilder();
                sb.append("select count(*) as counts");
                sb.append("  from appointment t");
                sb.append(" where to_char(t.start_time, 'YYYY-MM-DD') = to_char(:targetDate, 'YYYY-MM-DD')");
                sb.append("   and (t.canceled = 'N' or t.canceled is null)");
                sb.append("   and t.profile_id = :profileId");
                sb.append("   and ((to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                sb.append("        or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                sb.append("        or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS'))");
                sb.append("        or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                sb.append("        )");
                sb.append("   and t.instructor_id = :instructorId");
                if (pExcludeAppointmentId != null) {
                    sb.append(" and t.appointment_id != :excludeAppId ");
                }
                String weekDay = dayOfWeek_enum.name();
                String checkTime = "select to_char(ay." + weekDay + "_start_time, 'HH24:MI:SS') as sTime, to_char(ay." + weekDay + "_end_time, 'HH24:MI:SS')as eTime" +
                        " from  availability ay,INSTRUCTOR t" +
                        " where ay.availability_id=t.availability_id" +
                        " and t.instructor_id=:instructorId" +
                        " and ay." + weekDay + "_start_time is not null" +
                        " union" +
                        " select to_char(ot.start_time, 'HH24:MI:SS') as sTime, to_char(ot.end_time, 'HH24:MI:SS')as eTime" +
                        " from onetime ot" +
                        " where ot.INSTRUCTOR_ID=:instructorId" +
                        " and ot.start_time >=sysdate" +
                        " and to_char(ot.start_time, 'YYYY-MM-DD')=to_char(:startTime, 'YYYY-MM-DD')" +
                        " order by sTime";
                String checkTimeOff = "select count(*) as counts" +
                        "  from timeoff t" +
                        "  where t.instructor_id = :instructorId" +
                        "    and ((to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))" +
                        "         or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))" +
                        "         or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS'))" +
                        "         or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))" +
                        "         )";
                for (Instructor objects : list) {
                    Instructor instructor = objects;
                    Long instructorId = instructor.getInstructorId();
                    if (!maps.containsKey(instructorId)) {
                        Query checkTimeQuery = entityManager.createNativeQuery(checkTime);
                        checkTimeQuery.setParameter("instructorId", instructorId);
                        checkTimeQuery.setParameter("startTime", pStartDate);
                        checkTimeQuery.unwrap(NativeQuery.class)
                                .addScalar("sTime", StandardBasicTypes.TIME)
                                .addScalar("eTime", StandardBasicTypes.TIME);
                        List<Object[]> timeList = checkTimeQuery.getResultList();
                        if (timeList == null || timeList.isEmpty()) continue;
                        Boolean result = false;
                        List<TimeIntervalDTO> sortList = new ArrayList<TimeIntervalDTO>();
                        for (Object[] objectTime : timeList) {
                            TimeIntervalDTO timeInterval = new TimeIntervalDTO();
                            timeInterval.setStartTime(new DateTime(objectTime[0]));
                            timeInterval.setEndTime(new DateTime(objectTime[1]));
                            sortList.add(timeInterval);
                        }
                        sortList = DateTimeUtil.concatTime(sortList);
                        for (TimeIntervalDTO sort : sortList) {
                            DateTime start_time = sort.getStartTime();
                            DateTime end_time = sort.getEndTime();
                            start_time = startTime.withMillisOfSecond(0).withTime(start_time.getHourOfDay(), start_time.getMinuteOfHour(), start_time.getSecondOfMinute(), start_time.getMillisOfSecond());
                            end_time = endTime.withMillisOfSecond(0).withTime(end_time.getHourOfDay(), end_time.getMinuteOfHour(), end_time.getSecondOfMinute(), end_time.getMillisOfSecond());
                            if ((startTime.withMillisOfSecond(0).isAfter(start_time) || startTime.withMillisOfSecond(0).isEqual(start_time)) && (endTime.withMillisOfSecond(0).isBefore(end_time) || endTime.withMillisOfSecond(0).isEqual(end_time))) {
                                result = true;
                                break;
                            }
                        }
                        if (!result) continue;
                        Long count;
                        Query checkTimeOffQuery = entityManager.createNativeQuery(checkTimeOff);
                        checkTimeOffQuery.unwrap(NativeQuery.class)
                                .addScalar("counts", StandardBasicTypes.LONG);
                        checkTimeOffQuery.setParameter("instructorId", instructorId);
                        checkTimeOffQuery.setParameter("startTime", pStartDate);
                        checkTimeOffQuery.setParameter("endTime", pEndDate);
                        // Object[] checkTimeOffQueryResult = (Object[]) checkTimeOffQuery.getSingleResult();
                        // Long count = (Long) checkTimeOffQueryResult[0];
                        //  Long count = 0;
                        Object obj = checkTimeOffQuery.getSingleResult();
                        if (obj instanceof Object[]) {
                            Object[] resultArray = (Object[]) obj;
                            count = (Long) resultArray[0];
                        } else {
                            count = (Long) obj;
                        }
                        //  Object checkTimeOffQueryResult = checkTimeOffQuery.getSingleResult();
                        // Long count = (Long) checkTimeOffQueryResult;
                        //  Long count = (Long) ((Object[]) checkTimeOffQueryResult)[0];
                        if (count != 0) continue; //current instructor has time off recode.
                        query = entityManager.createNativeQuery(sb.toString());
                        checkTimeOffQuery.unwrap(NativeQuery.class)
                                .addScalar("counts", StandardBasicTypes.LONG);
                        query.setParameter("targetDate", pStartDate);
                        query.setParameter("profileId", pProfileId);
                        query.setParameter("instructorId", instructorId);
                        query.setParameter("startTime", pStartDate);
                        query.setParameter("endTime", pEndDate);
                        if (pExcludeAppointmentId != null) {
                            query.setParameter("excludeAppId", pExcludeAppointmentId);
                        }
                        count = ((BigDecimal) query.getSingleResult()).longValueExact();
                        if (count == 0) {
                            maps.put(instructorId, instructor);
                        }
                    }
                }
                if (maps.size() == 0) {
                    return Collections.EMPTY_LIST;
                }
                String sql = "select instructor_id, duration" +
                        " from (select t.instructor_id," +
                        "        abs(((to_date(:endTimeStr, 'YYYY-MM-DD HH24:MI:SS') -" +
                        "                to_date(to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS')," +
                        "                        'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 * 60)) as duration" +
                        "        from appointment t" +
                        "        where t.profile_id = :profileId" +
                        "        and t.instructor_id in (:instructorIds)" +
                        "        and (t.canceled is null or t.canceled = 'N')" +
                        " and to_char(t.start_time, 'YYYY-MM-DD') >= :startDateStr" +
                        " and to_char(t.end_time, 'YYYY-MM-DD') <= :endDateStr" +
                        " union" +
                        " select t.instructor_id," +
                        "        abs(((to_date(:startTimeStr, 'YYYY-MM-DD HH24:MI:SS') -" +
                        "                to_date(to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS')," +
                        "                        'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 * 60)) as duration" +
                        " from appointment t" +
                        " where t.profile_id = :profileId" +
                        " and t.instructor_id in (:instructorIds)" +
                        " and (t.canceled is null or t.canceled = 'N')" +
                        " and to_char(t.start_time, 'YYYY-MM-DD') >= :startDateStr" +
                        " and to_char(t.end_time, 'YYYY-MM-DD') <= :endDateStr)" +
                        " order by duration";
                query = entityManager.createNativeQuery(sql);
                query.unwrap(NativeQuery.class)
                        .addScalar("instructor_id", StandardBasicTypes.LONG)
                        .addScalar("duration", StandardBasicTypes.LONG);
                query.setParameter("profileId", pProfileId);
                query.setParameter("instructorIds", maps.keySet());
                DateTime startDate = new DateTime(pStartDate);
                DateTime endDate = new DateTime(pEndDate);
                query.setParameter("startTimeStr", startDate.toString("yyyy-MM-dd HH:mm:ss"));
                query.setParameter("startDateStr", startDate.minusDays(7).toString("yyyy-MM-dd"));
                query.setParameter("endTimeStr", endDate.toString("yyyy-MM-dd HH:mm:ss"));
                query.setParameter("endDateStr", endDate.plusDays(7).toString("yyyy-MM-dd"));
                List<Object[]> list1 = query.getResultList();
                List<InstructorSortedDTO> instructorSortedDTOList = new LinkedList<InstructorSortedDTO>();
                Map<Long, InstructorSortedDTO> instructorSortedDTOCache = new HashMap<Long, InstructorSortedDTO>();
                for (Object[] objects : list1) {
                    Long instructor_id = (Long) objects[0];
                    Long duration = (Long) objects[1];
                    if (!instructorSortedDTOCache.containsKey(instructor_id)) {
                        InstructorSortedDTO instructorSortedDTO = new InstructorSortedDTO(instructor_id, duration);
                        instructorSortedDTOList.add(instructorSortedDTO);
                        instructorSortedDTOCache.put(instructor_id, instructorSortedDTO);
                    }
                }
                for (Map.Entry<Long, Instructor> entry : maps.entrySet()) {
                    Long instructor_id = entry.getKey();
                    if (!instructorSortedDTOCache.containsKey(instructor_id)) {
                        InstructorSortedDTO instructorSortedDTO = new InstructorSortedDTO(instructor_id, Long.MAX_VALUE);
                        instructorSortedDTOList.add(instructorSortedDTO);
                        instructorSortedDTOCache.put(instructor_id, instructorSortedDTO);
                    }
                }
                Collections.sort(instructorSortedDTOList, new InstructorSortedComparator());
                ArrayList<Instructor> instructors = new ArrayList<Instructor>();
                for (InstructorSortedDTO instructorSortedDTO : instructorSortedDTOList) {
                    instructors.add(maps.get(instructorSortedDTO.getInstructorId()));
                }
                return instructors;
            }

        };
    }



   /* public static Criterion<Instructor, Instructor> findByProfileIdAndActivityIdAndDateTime1(final long pProfileId, final long pActivityId, final Date pStartDate, final Date pEndDate, final Long pExcludeAppointmentId) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(EntityManager entityManager, int pFetchMode) {
                DateTime startTime = new DateTime(pStartDate);
                DateTime endTime = new DateTime(pEndDate);
                int dayOfWeek = startTime.getDayOfWeek();
                StringBuilder sb = new StringBuilder("select  t.* from Instructor t");
                sb.append("  left join Person p on t.person_id = p.person_id");
                sb.append("  left join location l on t.location_id = l.location_id ");
                sb.append("  left join availability a on  t.availability_id = a.availability_id ");
                sb.append("  left join  instructor_activities ia on t.instructor_id = ia.instructor_id ");
                sb.append("  left join activity a_i on ia.activity_id = a_i.activity_id");
                sb.append("  left join onetime o on t.instructor_id = o.instructor_id ");
                sb.append("  where l.profile_Id = :profileId");
                sb.append("    and a_i.activity_id = :activityId");
                sb.append("    and t.enabled = 'Y'");

                DayOfWeek dayOfWeekEnum = DayOfWeek.of(dayOfWeek);
                if (dayOfWeekEnum == null) return Collections.emptyList();

                String dayOfWeekField = dayOfWeekEnum.name().toLowerCase() + "_start_time"; // Assuming DayOfWeek enum maps to your DB column names

               *//* String checkTime = "select new com.guitarcenter.scheduler.dto.TimeIntervalDTO(" +
                        "function('to_char', ay.wednesdayStartTime, 'HH24:MI:SS'), " +
                        "function('to_char', ay.wednesdayEndTime, 'HH24:MI:SS')) " +
                        "from Availability ay " +
                        "join Instructor t on ay = t.availability " +
                        "where t.instructorId = :instructorId " +
                        "and ay.wednesdayStartTime is not null " +
                        "union all " +
                        "select new com.guitarcenter.scheduler.dto.TimeIntervalDTO(" +
                        "function('to_char', ot.startTime, 'HH24:MI:SS'), " +
                        "function('to_char', ot.endTime, 'HH24:MI:SS')) " +
                        "from Onetime ot " +
                        "where ot.instructorId = :instructorId " +
                        "and ot.startTime >= current_timestamp " +
                        "and function('to_char', ot.startTime, 'YYYY-MM-DD') = :startTimeStr " +
                        "order by sTime";*//*
                String availabilityQuery1 = "select new com.guitarcenter.scheduler.dto.TimeIntervalDTO("
                        + "to_char(ay.wednesdayStartTime, 'HH24:MI:SS'), "
                        + "to_char(ay.wednesdayEndTime, 'HH24:MI:SS')) "
                        + "from Availability ay "
                        + "join ay.Instructor t "
                        + "where t.instructorId = :instructorId "
                        + "and ay.wednesdayStartTime is not null";

                String availabilityQuery2 = "select new com.guitarcenter.scheduler.dto.TimeIntervalDTO(" +
                        "function('to_char', ay.wednesdayStartTime, 'HH24:MI:SS'), " +
                        "function('to_char', ay.wednesdayEndTime, 'HH24:MI:SS')) " +
                        "from Availability ay " +
                        "join Instructor t on ay = t.availability " +
                        "where t.instructorId = :instructorId " +
                        "and ay.wednesdayStartTime is not null " ;

                String availabilityQuery = "select new com.guitarcenter.scheduler.dto.TimeIntervalDTO1(to_char(ay." + "wednesdayStartTime" + ", 'HH24:MI:SS'), to_char(ay." + "wednesdayEndTime" + ", 'HH24:MI:SS'))"
                        + " from Availability ay"
                        + " join Instructor t on ay = t.availability "
                        + " where t.instructorId = :instructorId"
                        + " and ay." + "wednesdayStartTime" + " is not null";

                String onetimeQuery = "select new com.guitarcenter.scheduler.dto.TimeIntervalDTO1("
                        + "to_char(ot.startTime, 'HH24:MI:SS'), "
                        + "to_char(ot.endTime, 'HH24:MI:SS')) "
                        + "from Onetime ot "
                        + "where ot.instructor.instructorId = :instructorId "
                        + "and ot.startTime >= current_timestamp "
                        + "and to_char(ot.startTime, 'YYYY-MM-DD') = :startTimeStr";




                String checkTimeOff = "select count(t) from Timeoff t"
                        + " where t.instructor.instructorId = :instructorId"
                        + " and (t.startTime <= :startTime and t.endTime >= :endTime"
                        + " or t.startTime >= :startTime and t.endTime <= :endTime"
                        + " or t.startTime <= :startTime and t.endTime >= :startTime"
                        + " or t.startTime <= :endTime and t.endTime >= :endTime)";

                Query  query =  entityManager.createNativeQuery(sb.toString(),Instructor.class);
                query.setParameter("profileId", pProfileId);
                query.setParameter("activityId", pActivityId);
                List<Instructor> list = query.getResultList();

                Map<Long, Instructor> maps = new HashMap<>();

                for (Instructor instructor : list) {
                    Long instructorId = instructor.getInstructorId();
                    if (!maps.containsKey(instructorId)) {
                       *//* TypedQuery<TimeIntervalDTO> checkTimeQuery = entityManager.createQuery(checkTime, TimeIntervalDTO.class);
                        checkTimeQuery.setParameter("instructorId", instructorId);
                        checkTimeQuery.setParameter("startTimeStr", startTime.toString("yyyy-MM-dd"));
                        List<TimeIntervalDTO> timeList = checkTimeQuery.getResultList();*//*

                        // Execute the first query
                        Query availabilityQueryObj = entityManager.createQuery(availabilityQuery,TimeIntervalDTO1.class);
                        availabilityQueryObj.setParameter("instructorId", instructorId);
                        List<TimeIntervalDTO1> availabilityResults = availabilityQueryObj.getResultList();

// Execute the second query
                        Query onetimeQueryObj = entityManager.createQuery(onetimeQuery,TimeIntervalDTO1.class);
                        onetimeQueryObj.setParameter("instructorId", instructorId);
                        onetimeQueryObj.setParameter("startTimeStr", startTime.toString("yyyy-MM-dd"));
                        List<TimeIntervalDTO1> onetimeResults = onetimeQueryObj.getResultList();

// Combine the results
                        List<TimeIntervalDTO1> timeList = new ArrayList<>();
                        timeList.addAll(availabilityResults);
                        timeList.addAll(onetimeResults);




                        if (timeList == null || timeList.isEmpty()) continue;



                        boolean result = false;
                        List<TimeIntervalDTO> timeList1 = DateTimeUtil.castToTimeIntervalDTO(timeList);
                        List<TimeIntervalDTO> sortList = DateTimeUtil.concatTime(timeList1);
                        for (TimeIntervalDTO sort : sortList) {
                            DateTime start_time = sort.getStartTime();
                            DateTime end_time = sort.getEndTime();
                            start_time = startTime.withMillisOfSecond(0).withTime(start_time.getHourOfDay(), start_time.getMinuteOfHour(), start_time.getSecondOfMinute(), start_time.getMillisOfSecond());
                            end_time = endTime.withMillisOfSecond(0).withTime(end_time.getHourOfDay(), end_time.getMinuteOfHour(), end_time.getSecondOfMinute(), end_time.getMillisOfSecond());
                            if ((startTime.withMillisOfSecond(0).isAfter(start_time) || startTime.withMillisOfSecond(0).isEqual(start_time)) && (endTime.withMillisOfSecond(0).isBefore(end_time) || endTime.withMillisOfSecond(0).isEqual(end_time))) {
                                result = true;
                                break;
                            }
                        }
                        if (!result) continue;

                        TypedQuery<Long> checkTimeOffQuery = entityManager.createQuery(checkTimeOff, Long.class);
                        checkTimeOffQuery.setParameter("instructorId", instructorId);
                        checkTimeOffQuery.setParameter("startTime", pStartDate);
                        checkTimeOffQuery.setParameter("endTime", pEndDate);
                        Long count = checkTimeOffQuery.getSingleResult();
                        if (count != 0) continue; // current instructor has time off record

                        // Further query logic here...
                    }
                }

                if (maps.isEmpty()) {
                    return Collections.emptyList();
                }

                // Sorting and final processing logic...

                return new ArrayList<>(maps.values());
            }
        };
    }*/

    public static Criterion<Instructor, Boolean> checkTimeAvailability(
            final Long pInstructorId,
            final Date pStartDateTime,
            final Date pEndDateTime) {

        return new InstructorCriterion<Boolean>() {
            @Override
            public Boolean get(EntityManager entityManager, int pFetchMode) {
                DateTime startTime = new DateTime(pStartDateTime);
                int dayOfWeek = startTime.getDayOfWeek();

                StringBuilder sb = new StringBuilder("select count(t) from Instructor t");
                sb.append(" join t.availability ay");
                sb.append(" left join t.onetime ot");
                sb.append(" where t.instructorId = :instructorId");

                DayOfWeek dayOfWeekEnum = DayOfWeek.of(dayOfWeek);
                if (dayOfWeekEnum == null) return false;

                sb.append(" and ( ");
                sb.append("ay." + dayOfWeekEnum.name().toLowerCase() + "_startTime <= :startTime ");
                sb.append(" and ay." + dayOfWeekEnum.name().toLowerCase() + "_endTime >= :endTime");
                sb.append(" or ");
                sb.append(" (ot.startTime <= :startTime and ot.endTime >= :endTime)");
                sb.append(")");

                TypedQuery<Long> query = entityManager.createQuery(sb.toString(), Long.class);
                query.setParameter("instructorId", pInstructorId);
                query.setParameter("startTime", pStartDateTime);
                query.setParameter("endTime", pEndDateTime);

                Long count = query.getSingleResult();
                return count > 0;
            }
        };
    }

    public static Criterion<Instructor, Instructor> findFullinstructorByChanges() {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(EntityManager entityManager, int pFetchMode) {
                String hql = "select i from Instructor i where i.status = :status and i.enabled = :enabled";

                TypedQuery<Instructor> query = entityManager.createQuery(hql, Instructor.class);
                query.setParameter("status", "A");
                query.setParameter("enabled", Enabled.Y);

                List<Instructor> result = query.getResultList();
                return result;
            }
        };
    }

    public static Criterion<Instructor, Map<Long, ServiceMode>> findInstructorServiceModeMap(final List<Long> instructorIdList) {

        return new InstructorCriterion<Map<Long, ServiceMode>>() {

            @Override
            public Map<Long, ServiceMode> get(EntityManager entityManager, int pFetchMode) {
                String sql = "select im.INSTRUCTOR_ID, sm.SERVICE_MODE_NAME, sm.SERVICE_MODE_ID " +
                        "from Instructor_mode im " +
                        "join SERVICE_MODE sm on im.SERVICE_MODE_ID = sm.SERVICE_MODE_ID " +
                        "where im.Instructor_id in (:Instructor_id)";

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sql);
                query.setParameter("Instructor_id", instructorIdList);
                query.addScalar("INSTRUCTOR_ID", StandardBasicTypes.LONG);
                query.addScalar("SERVICE_MODE_NAME", StandardBasicTypes.STRING);
                query.addScalar("SERVICE_MODE_ID", StandardBasicTypes.LONG);

                List<Object[]> results = query.list();
                Map<Long, ServiceMode> serviceModeMap = new HashMap<>();

                for (Object[] objects : results) {
                    Long instructorId = (Long) objects[0];
                    ServiceMode sm = new ServiceMode();
                    sm.setServiceModeName((String) objects[1]);
                    sm.setServiceModeId((Long) objects[2]);
                    serviceModeMap.put(instructorId, sm);
                }

                return serviceModeMap;
            }
        };
    }
    //Changes made for GSSP-199

    /**
     * Returns a Criterion that can be used to find any existing instructor that
     * match the supplied person id.
     *
     * @param pPersonId person identifier
     * @return Criterion instance
     */
    public static Criterion<Instructor, Instructor> findByPersonId(final long pPersonId) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(EntityManager entityManager, int pFetchMode) {
                String hql = "from Instructor t " + getFetchScript(pFetchMode) + " where t.person.personId = :personId";
                TypedQuery<Instructor> query = entityManager.createQuery(hql, Instructor.class);
                query.setParameter("personId", pPersonId);
                return query.getResultList();
            }
        };
    }


    public static Criterion<Instructor, Boolean> checkAppointmentDateTimeByAvailabilityAndOntime(
            final Long pInstructorId, final Date pStartDate, final Date pEndDate) {
        return new InstructorCriterion<Boolean>() {
            @Override
            public Boolean get(EntityManager entityManager, int pFetchMode) {
                Boolean result = false;
                DateTime startTime = new DateTime(pStartDate);
                DateTime endTime = new DateTime(pEndDate);
                int dayOfWeek = startTime.getDayOfWeek();
                DayOfWeek dayOfWeekEnum = DayOfWeek.of(dayOfWeek);
                String weekDay = dayOfWeekEnum.name().toLowerCase();

                String checkTime = "select to_char(ay." + weekDay + "_start_time, 'HH24:MI:SS') as sTime, " +
                        "to_char(ay." + weekDay + "_end_time, 'HH24:MI:SS') as eTime " +
                        "from availability ay, INSTRUCTOR t " +
                        "where ay.availability_id = t.availability_id " +
                        "and t.instructor_id = :instructorId " +
                        "and ay." + weekDay + "_start_time is not null " +
                        "union " +
                        "select to_char(ot.start_time, 'HH24:MI:SS') as sTime, " +
                        "to_char(ot.end_time, 'HH24:MI:SS') as eTime " +
                        "from onetime ot " +
                        "where ot.INSTRUCTOR_ID = :instructorId " +
                        "and ot.start_time >= current_timestamp " +
                        "and to_char(ot.start_time, 'YYYY-MM-DD') = to_char(:startTime, 'YYYY-MM-DD') " +
                        "order by sTime";

                Query checkTimeQuery = entityManager.createNativeQuery(checkTime, Object[].class);
                checkTimeQuery.setParameter("instructorId", pInstructorId);
                checkTimeQuery.setParameter("startTime", pStartDate);

                List<Object[]> timeList = checkTimeQuery.getResultList();
                if (timeList == null || timeList.isEmpty()) {
                    return result;
                }

                List<TimeIntervalDTO> sortList = new ArrayList<>();
                for (Object[] objectTime : timeList) {
                    TimeIntervalDTO timeInterval = new TimeIntervalDTO();
                    timeInterval.setStartTime(new DateTime(objectTime[0]));
                    timeInterval.setEndTime(new DateTime(objectTime[1]));
                    sortList.add(timeInterval);
                }

                sortList = DateTimeUtil.concatTime(sortList);
                for (TimeIntervalDTO sort : sortList) {
                    DateTime start_time = sort.getStartTime();
                    DateTime end_time = sort.getEndTime();
                    start_time = startTime.withMillisOfSecond(0)
                            .withTime(start_time.getHourOfDay(), start_time.getMinuteOfHour(),
                                    start_time.getSecondOfMinute(), start_time.getMillisOfSecond());
                    end_time = endTime.withMillisOfSecond(0)
                            .withTime(end_time.getHourOfDay(), end_time.getMinuteOfHour(),
                                    end_time.getSecondOfMinute(), end_time.getMillisOfSecond());

                    if ((startTime.withMillisOfSecond(0).isAfter(start_time) || startTime.withMillisOfSecond(0).isEqual(start_time))
                            && (endTime.withMillisOfSecond(0).isBefore(end_time) || endTime.withMillisOfSecond(0).isEqual(end_time))) {
                        result = true;
                        break;
                    }
                }
                return result;
            }
        };
    }


    public static InstructorCriterion<Map<Long, ServiceMode>> findInstructorServiveModeMap(final List<Long> instructorIdList) {
        return new InstructorCriterion<Map<Long, ServiceMode>>() {

            @Override
            @SuppressWarnings("unchecked")
            public Map<Long, ServiceMode> get(EntityManager entityManager, int pFetchMode) {
                String sql = "select im.INSTRUCTOR_ID, sm.SERVICE_MODE_NAME, sm.SERVICE_MODE_ID " +
                        "from Instructor_mode im " +
                        "join SERVICE_MODE sm on im.SERVICE_MODE_ID = sm.SERVICE_MODE_ID " +
                        "where im.INSTRUCTOR_ID in (:Instructor_id)";

                // Create native query in Hibernate 6
                Query query = entityManager.createNativeQuery(sql);
                query.setParameter("Instructor_id", instructorIdList);

                // Add scalar mappings in Hibernate 6
                query.unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("INSTRUCTOR_ID", StandardBasicTypes.LONG)
                        .addScalar("SERVICE_MODE_NAME", StandardBasicTypes.STRING)
                        .addScalar("SERVICE_MODE_ID", StandardBasicTypes.LONG);

                // Use getResultList in Hibernate 6
                List<Object[]> results = query.getResultList();
                Map<Long, ServiceMode> serviceModeMap = new HashMap<>();

                for (Object[] result : results) {
                    Long instructorId = (Long) result[0];
                    ServiceMode serviceMode = new ServiceMode();
                    serviceMode.setServiceModeName((String) result[1]);
                    serviceMode.setServiceModeId((Long) result[2]);
                    serviceModeMap.put(instructorId, serviceMode);
                }

                return serviceModeMap;
            }
        };
    }


    public static InstructorCriterion<Instructor> findDisabledInstructorByChanges(final String updatedDate) {
        return new InstructorCriterion<Instructor>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select i.INSTRUCTOR_ID, i.STATUS, i.ENABLED ")
                        .append("from INSTRUCTOR i ")
                        .append("where i.instructor_id in (")
                        .append("    select distinct instructor_id ")
                        .append("    from instructor ")
                        .append("    where status = 'T' and TO_CHAR(updated, 'YYYY-MM-DD HH24:MI') >= :updatedDate ")
                        .append("    union ")
                        .append("    select distinct instructor_id ")
                        .append("    from instructor ")
                        .append("    where status = 'A' and enabled = 'N' and TO_CHAR(updated, 'YYYY-MM-DD HH24:MI') >= :updatedDate ")
                        .append(")");

                // Create the native query
                Query query = entityManager.createNativeQuery(sb.toString());

                // Set the updated date parameter
                query.setParameter("updatedDate", updatedDate);

                // Get the result list
                List<Object[]> results = query.getResultList();
                List<Instructor> result = new ArrayList<>();

                for (Object[] objects : results) {

                    BigDecimal bd = convertToBigDecimal(objects[0]);
                    //String insId = (String) objects[0];
                    String insId = bd.toString();
                    Instructor dto = new Instructor();
                    if (insId != null) {
                        dto.setInstructorId(Long.valueOf(insId));
                        dto.setStatus((String) objects[1]);
                        dto.setEnabled("Y".equals(objects[2]) ? Enabled.Y : Enabled.N);
                        result.add(dto);
                    }
                }

                return result;
            }
        };
    }

    private  static  BigDecimal convertToBigDecimal(Object obj) {
        if (obj == null) {
            throw new IllegalArgumentException("Object cannot be null");
        }

        if (obj instanceof BigDecimal) {
            return (BigDecimal) obj;
        } else if (obj instanceof String) {
            return new BigDecimal((String) obj);
        } else if (obj instanceof Integer || obj instanceof Long) {
            return BigDecimal.valueOf(((Number) obj).longValue());
        } else if (obj instanceof Double || obj instanceof Float) {
            return BigDecimal.valueOf(((Number) obj).doubleValue());
        } else {
            throw new IllegalArgumentException("Unsupported type for conversion to BigDecimal");
        }
    }
    public static InstructorCriterion<Instructor> findDisabledFullInstructorByChanges(final String updatedDate) {
        return new InstructorCriterion<Instructor>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select i.INSTRUCTOR_ID, i.STATUS, i.ENABLED ")
                        .append("from INSTRUCTOR i ")
                        .append("where i.instructor_id in (")
                        .append("    select distinct instructor_id ")
                        .append("    from instructor ")
                        .append("    where status = 'T' and TO_CHAR(updated, 'YYYY-MM-DD HH24:MI') >= :updatedDate ")
                        .append("    union ")
                        .append("    select distinct instructor_id ")
                        .append("    from instructor ")
                        .append("    where status = 'A' and enabled = 'N' and TO_CHAR(updated, 'YYYY-MM-DD HH24:MI') >= :updatedDate ")
                        .append(")");

                // Create the native query
                Query query = entityManager.createNativeQuery(sb.toString());

                // Set the updated date parameter
                query.setParameter("updatedDate", updatedDate);

                // Fetch the result list
                List<Object[]> results = query.getResultList();
                List<Instructor> result = new ArrayList<>();

                // Iterate through the results
                for (Object[] objects : results) {
                    String insId = (String) objects[0];
                    Instructor dto = new Instructor();
                    if (insId != null) {
                        dto.setInstructorId(Long.valueOf(insId));
                        dto.setStatus((String) objects[1]);
                        dto.setEnabled("Y".equals(objects[2]) ? Enabled.Y : Enabled.N);
                        result.add(dto);
                    }
                }

                return result;
            }
        };
    }

    public static InstructorCriterion<Instructor> findInstructorByChanges(final String updatedDate) {
        return new InstructorCriterion<Instructor>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(EntityManager entityManager, int pFetchMode) {

                List<Instructor> result = new ArrayList<>();


                StringBuilder sb = new StringBuilder()
                        .append("select i.INSTRUCTOR_ID, i.STATUS, i.ENABLED, i.EXTERNAL_ID ")
                        .append("from INSTRUCTOR i ")
                        .append("where i.instructor_id in (")
                        .append("    select a.INSTRUCTOR_ID ")
                        .append("    from appointment a ")
                        .append("    where TRUNC(a.START_TIME) <= TO_DATE(:after30day, 'YYYY-MM-DD') ")
                        .append("    and TRUNC(a.START_TIME) >= TO_DATE(:yesterday, 'YYYY-MM-DD') ")
                        .append("    and a.UPDATED >= TO_DATE(:updatedDate, 'YYYY-MM-DD HH24:MI') ")
                        .append("    union all ")
                        .append("    select ins.INSTRUCTOR_ID ")
                        .append("    from Availability av ")
                        .append("    join Instructor ins on av.AVAILABILITY_ID = ins.AVAILABILITY_ID ")
                        .append("    where av.UPDATED >= TO_DATE(:updatedDate, 'YYYY-MM-DD HH24:MI') ")
                        .append("    union all ")
                        .append("    select ia.INSTRUCTOR_ID ")
                        .append("    from Instructor_Activities ia ")
                        .append("    where ia.UPDATED >= TO_DATE(:updatedDate, 'YYYY-MM-DD HH24:MI') ")
                        .append("    union all ")
                        .append("    select ov.INSTRUCTOR_ID ")
                        .append("    from ONLINE_AVAILABILITY ov ")
                        .append("    where ov.UPDATED >= TO_DATE(:updatedDate, 'YYYY-MM-DD HH24:MI') ")
                        .append("    union all ")
                        .append("    select iv.INSTRUCTOR_ID ")
                        .append("    from INSTORE_AVAILABILITY iv ")
                        .append("    where iv.UPDATED >= TO_DATE(:updatedDate, 'YYYY-MM-DD HH24:MI') ")
                        .append(")");


                // Setting up dates
                Calendar c = Calendar.getInstance();
                c.add(Calendar.DATE, -1);
                Date yesterday01 = c.getTime();
                c.add(Calendar.DATE, 31);
                Date after30day = c.getTime();
                DateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
                String yesterday = dateFormatter.format(yesterday01);
                String after30dayStr = dateFormatter.format(after30day);

                // Creating query
                Query query = entityManager.createNativeQuery(sb.toString());
                query.setParameter("after30day", after30dayStr);
                query.setParameter("yesterday", yesterday);
                query.setParameter("updatedDate", updatedDate);

                // Fetching results

                List<Object[]> results = query.getResultList();

                // Processing the results
                for (Object[] objects : results) {
                    String insId = objects[0].toString();
                    Instructor dto = new Instructor();
                    if (insId != null) {
                        dto.setInstructorId(Long.valueOf(insId));
                        dto.setStatus(objects[1].toString());
                        dto.setEnabled("Y".equals(objects[2]) ? Enabled.Y : Enabled.N);
                        dto.setExternalId((String) objects[3]);
                        result.add(dto);
                    }
                }

                return result;
            }
        };
    }


    public static Criterion<Instructor, Instructor> findinstructorByChanges(final String updatedDate) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select i.instructor_id as instructorId, i.STATUS as status, i.ENABLED as enabled, i.EXTERNAL_ID as externalId ");
                sb.append("from INSTRUCTOR i where i.INSTRUCTOR_ID in (");
                sb.append(" select distinct a.INSTRUCTOR_ID from appointment a where to_char(a.START_TIME, 'YYYY-MM-DD') <= :after30day ");
                sb.append(" and to_char(a.START_TIME, 'YYYY-MM-DD') >= :yesterday ");
                sb.append(" and to_char(a.UPDATED, 'YYYY-MM-DD HH24:MI') >= :updatedDate ");
                sb.append(" union ");
                sb.append(" select distinct ins.INSTRUCTOR_ID from Availability av, INSTRUCTOR ins where av.AVAILABILITY_ID = ins.AVAILABILITY_ID ");
                sb.append(" and to_char(av.UPDATED, 'YYYY-MM-DD HH24:MI') >= :updatedDate ");
                sb.append(" union ");
                sb.append(" select distinct ia.INSTRUCTOR_ID from Instructor_Activities ia where to_char(ia.UPDATED, 'YYYY-MM-DD HH24:MI') >= :updatedDate ");
                sb.append(" union ");
                sb.append(" select distinct ins.INSTRUCTOR_ID from ONLINE_AVAILABILITY ov, INSTRUCTOR ins where ov.INSTRUCTOR_ID = ins.INSTRUCTOR_ID ");
                sb.append(" and to_char(ov.UPDATED, 'YYYY-MM-DD HH24:MI') >= :updatedDate ");
                sb.append(" union ");
                sb.append(" select distinct ins.INSTRUCTOR_ID from INSTORE_AVAILABILITY iv, INSTRUCTOR ins where iv.INSTRUCTOR_ID = ins.INSTRUCTOR_ID ");
                sb.append(" and to_char(iv.UPDATED, 'YYYY-MM-DD HH24:MI') >= :updatedDate)");
                sb.append(")");

                //###########################
                Calendar c = Calendar.getInstance();
                c.add(Calendar.DATE, -1);
                Date yesterday01 = c.getTime();
                c.add(Calendar.DATE, 31);
                Date after_30day = c.getTime();
                DateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
                String yesterday = dateFormatter.format(yesterday01);
                String after30day = dateFormatter.format(after_30day);

                //#########################################

                Query query = entityManager.createNativeQuery(sb.toString())
                        .setParameter("after30day", after30day)
                        .setParameter("yesterday", yesterday)
                        .setParameter("updatedDate", updatedDate);

                List<Object[]> resultList = query.getResultList();
                List<Instructor> result = new ArrayList<>();

                for (Object[] objects : resultList) {
                    Instructor dto = new Instructor();
                    dto.setInstructorId(Long.valueOf((String) objects[0]));
                    dto.setStatus((String) objects[1]);
                    dto.setEnabled("Y".equals(objects[2]) ? Enabled.Y : Enabled.N);
                    dto.setExternalId((String) objects[3]);
                    result.add(dto);
                }

                return result;
            }
        };
    }

    public static InstructorCriterion<InstructorAppointmentStatusDTO> getInstructorDetailsByExternalId(
            String instructorExternalId, String appointmentId) {

        return new InstructorCriterion<InstructorAppointmentStatusDTO>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<InstructorAppointmentStatusDTO> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("SELECT i.person_id, i.version, i.site_id ");
                sb.append("FROM instructor i ");
                sb.append("WHERE i.external_id = :external_id");

                // Using NativeQuery instead of SQLQuery
                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb.toString());

                // Setting the parameter
                query.setParameter("external_id", instructorExternalId);

                // Adding scalar mappings for each field
                query.addScalar("person_id", StandardBasicTypes.STRING);
                query.addScalar("version", StandardBasicTypes.STRING);
                query.addScalar("site_id", StandardBasicTypes.STRING);

                // Fetching results
                List<Object[]> resultList = query.getResultList();
                List<InstructorAppointmentStatusDTO> result = new ArrayList<>();

                // Iterating through results and populating the DTO
                for (Object[] objects : resultList) {
                    InstructorAppointmentStatusDTO dto = new InstructorAppointmentStatusDTO();
                    dto.setUpdatedBy((String) objects[0]);
                    dto.setVersion((String) objects[1]);
                    dto.setSite((String) objects[2]);
                    result.add(dto);
                }

                return result;
            }
        };
    }


    public static Criterion<Instructor, InstructorActivitiesDTO> getRecipientEmailIds() {
        return new InstructorCriterion<InstructorActivitiesDTO>() {

            @Override
            public List<InstructorActivitiesDTO> search(EntityManager entityManager) {
                StringBuilder sb = new StringBuilder("select recipientId");
                sb.append(" from EMAIL_LOOKUP");
                sb.append(" where description='Instructor Activities Report'");
                //  5055
                // NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb.toString());
                Query query = entityManager.createNativeQuery(sb.toString());
                //  query.addScalar("recipientId", StandardBasicTypes.STRING);

                List<InstructorActivitiesDTO> resultList = new ArrayList<>();
                // List<Object[]> results = query.list();
                List<String> results = query.getResultList();

                for (String objects : results) {
                    String recipientId =   objects;
                    InstructorActivitiesDTO dto = new InstructorActivitiesDTO(recipientId);
                    resultList.add(dto);
                }

                return resultList;
            }
        };
    }


    public static Criterion<Instructor, InstructorActivitiesAndAvailabilityDTO> findInstructorActivitiesAndAvailability() {
        return new InstructorCriterion<InstructorActivitiesAndAvailabilityDTO>() {

            @Override
            public List<InstructorActivitiesAndAvailabilityDTO> search(EntityManager entityManager) {
                Log.info("entered into InstructorCriterion.findInstructorActivitiesAndAvailability() {}");

                StringBuilder sb = new StringBuilder("select distinct P.FIRST_NAME || ' ' || P.LAST_NAME AS INSTRUCTOR_NAME, "
                        + "l.EXTERNAL_ID || ' ' || l.LOCATION_NAME AS STORE, AC.ACTIVITY_NAME   AS ACTIVITY, ");
                sb.append(" TO_CHAR(av.monday_start_time, 'HH12:MI:SS AM') MONDAY_START_TIME, ");
                sb.append(" TO_CHAR(av.monday_end_time, 'HH12:MI:SS AM') MONDAY_END_TIME, ");
                sb.append(" TO_CHAR(av.tuesday_start_time, 'HH12:MI:SS AM') TUESDAY_START_TIME, ");
                sb.append(" TO_CHAR(av.tuesday_end_time, 'HH12:MI:SS AM') TUESDAY_END_TIME, ");
                sb.append(" TO_CHAR(av.wednesday_start_time, 'HH12:MI:SS AM') WED_START_TIME, ");
                sb.append(" TO_CHAR(av.wednesday_end_time, 'HH12:MI:SS AM') WED_END_TIME, ");
                sb.append(" TO_CHAR(av.thursday_start_time, 'HH12:MI:SS AM') THURS_START_TIME, ");
                sb.append(" TO_CHAR(av.thursday_end_time, 'HH12:MI:SS AM') THURS_END_TIME, ");
                sb.append(" TO_CHAR(av.friday_start_time, 'HH12:MI:SS AM') FRI_START_TIME, ");
                sb.append(" TO_CHAR(av.friday_end_time, 'HH12:MI:SS AM') FRI_END_TIME, ");
                sb.append(" TO_CHAR(av.saturday_start_time, 'HH12:MI:SS AM') SAT_START_TIME, ");
                sb.append(" TO_CHAR(av.saturday_end_time, 'HH12:MI:SS AM') SAT_END_TIME, ");
                sb.append(" TO_CHAR(av.sunday_start_time, 'HH12:MI:SS AM') SUN_START_TIME, ");
                sb.append(" TO_CHAR(av.sunday_end_time, 'HH12:MI:SS AM') SUN_END_TIME ");
                sb.append(" from location l join LOCATION_PROFILE lp on l.PROFILE_ID = lp.PROFILE_ID ");
                sb.append(" join INSTRUCTOR I on l.LOCATION_ID = I.LOCATION_ID ");
                sb.append(" join PERSON P on P.PERSON_ID = I.PERSON_ID ");
                sb.append(" join INSTRUCTOR_ACTIVITIES IA on I.INSTRUCTOR_ID = IA.INSTRUCTOR_ID ");
                sb.append(" join ACTIVITY A on IA.ACTIVITY_ID = A.ACTIVITY_ID ");
                sb.append(" join AVAILABILITY av on i.availability_id = av.availability_id ");
                sb.append(" left join ");
                sb.append(" ( select distinct  P.FIRST_NAME || ' ' || P.LAST_NAME AS INSTRUCTOR_NAME, ");
                sb.append(" LISTAGG(  A.ACTIVITY_NAME, ',') WITHIN GROUP (ORDER BY A.ACTIVITY_NAME) AS Activity_Name ");
                sb.append(" from PERSON P ");
                sb.append(" join INSTRUCTOR I on P.PERSON_ID = I.PERSON_ID ");
                sb.append(" join INSTRUCTOR_ACTIVITIES IA on I.INSTRUCTOR_ID = IA.INSTRUCTOR_ID ");
                sb.append(" join ACTIVITY A on IA.ACTIVITY_ID = A.ACTIVITY_ID ");
                sb.append(" where a.ACTIVITY_ID NOT IN (120,100,140,320,20,200,400)   ");
                sb.append(" group by P.FIRST_NAME || ' ' || P.LAST_NAME) AC ");
                sb.append(" on AC.INSTRUCTOR_NAME = P.FIRST_NAME || ' ' || P.LAST_NAME ");
                sb.append(" where lp.ENABLED = 'Y' and I.ENABLED = 'Y' and I.STATUS = 'A'   ");
                sb.append(" group by AC.ACTIVITY_NAME,I.LOCATION_ID,l.EXTERNAL_ID,l.LOCATION_NAME,I.INSTRUCTOR_ID,IA.ACTIVITY_ID,P.FIRST_NAME,P.LAST_NAME, ");
                sb.append(" av.monday_start_time,av.monday_end_time, ");
                sb.append(" av.tuesday_start_time,av.tuesday_end_time, ");
                sb.append(" av.wednesday_start_time,av.wednesday_end_time, ");
                sb.append(" av.thursday_start_time,av.thursday_end_time, ");
                sb.append(" av.friday_start_time,av.friday_end_time, ");
                sb.append(" av.saturday_start_time,av.saturday_end_time, ");
                sb.append(" av.sunday_start_time,av.sunday_end_time ");
                sb.append(" ORDER BY STORE,INSTRUCTOR_NAME ");

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb.toString());
                query.addScalar("STORE", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_NAME", StandardBasicTypes.STRING);
                query.addScalar("ACTIVITY", StandardBasicTypes.STRING);
                query.addScalar("MONDAY_START_TIME", StandardBasicTypes.STRING);
                query.addScalar("MONDAY_END_TIME", StandardBasicTypes.STRING);
                query.addScalar("TUESDAY_START_TIME", StandardBasicTypes.STRING);
                query.addScalar("TUESDAY_END_TIME", StandardBasicTypes.STRING);
                query.addScalar("WED_START_TIME", StandardBasicTypes.STRING);
                query.addScalar("WED_END_TIME", StandardBasicTypes.STRING);
                query.addScalar("THURS_START_TIME", StandardBasicTypes.STRING);
                query.addScalar("THURS_END_TIME", StandardBasicTypes.STRING);
                query.addScalar("FRI_START_TIME", StandardBasicTypes.STRING);
                query.addScalar("FRI_END_TIME", StandardBasicTypes.STRING);
                query.addScalar("SAT_START_TIME", StandardBasicTypes.STRING);
                query.addScalar("SAT_END_TIME", StandardBasicTypes.STRING);
                query.addScalar("SUN_START_TIME", StandardBasicTypes.STRING);
                query.addScalar("SUN_END_TIME", StandardBasicTypes.STRING);

                List<Object[]> list = query.list();
                List<InstructorActivitiesAndAvailabilityDTO> resultList = new ArrayList<>();
                for (Object[] obj : list) {
                    InstructorActivitiesAndAvailabilityDTO dto = new InstructorActivitiesAndAvailabilityDTO();
                    dto.setStore((String) obj[0]);
                    dto.setInstructorName((String) obj[1]);
                    dto.setActivityName((String) obj[2]);
                    dto.setMondayStartTime((String) obj[3]);
                    dto.setMondayEndTime((String) obj[4]);
                    dto.setTuesdayStartTime((String) obj[5]);
                    dto.setTuesdayendTime((String) obj[6]);
                    dto.setWednesdayStartTime((String) obj[7]);
                    dto.setWednesdayEndTime((String) obj[8]);
                    dto.setThursdayStartTime((String) obj[9]);
                    dto.setThursdayEndTime((String) obj[10]);
                    dto.setFridayStartTime((String) obj[11]);
                    dto.setFridayEndTime((String) obj[12]);
                    dto.setSaturdayStarTime((String) obj[13]);
                    dto.setSaturdayEndTime((String) obj[14]);
                    dto.setSundayStartTime((String) obj[15]);
                    dto.setSundayEndTime((String) obj[16]);
                    resultList.add(dto);
                }
                return resultList;
            }
        };
    }


    public static Criterion<Instructor, InstructorActivitiesAndAvailabilityDTO> getInstructorActivitiesAndAvailabilityRecipientEmailIds() {
        return new InstructorCriterion<InstructorActivitiesAndAvailabilityDTO>() {

            @Override
            public List<InstructorActivitiesAndAvailabilityDTO> search(EntityManager entityManager) {
                StringBuilder sb = new StringBuilder("select recipientId from EMAIL_LOOKUP where description = 'Instructor Activities And Availability Report'");

                //  5055
                // NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb.toString());
                Query query = entityManager.createNativeQuery(sb.toString());
                //  query.addScalar("recipientId", StandardBasicTypes.STRING);

                List<InstructorActivitiesAndAvailabilityDTO> resultList = new ArrayList<>();
                // List<Object[]> results = query.list();
                List<String> results = query.getResultList();

                for (String objects : results) {
                    String recipientId =   objects;
                    InstructorActivitiesAndAvailabilityDTO dto = new InstructorActivitiesAndAvailabilityDTO(recipientId);
                    resultList.add(dto);
                }

                return resultList;
            }
        };
    }



    public static Criterion<Instructor, Boolean> checkOneTimeHasAppointment(final Long pInstructorId, final Date pStartDate, final Date pEndDate) {
        return new InstructorCriterion<Boolean>() {
            @Override
            public Boolean get(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select count(t.appointment_id) as counts");
                sb.append(" from appointment t");
                sb.append(" where t.instructor_id = :instructorId");
                sb.append(" and ((t.start_time <= :startTime and t.end_time >= :endTime)");
                sb.append(" or (t.start_time >= :startTime and t.end_time < :endTime and t.start_time < :endTime)");
                sb.append(" or (t.start_time < :startTime and t.end_time <= :endTime and t.end_time > :startTime)");
                sb.append(" or (t.start_time >= :startTime and t.start_time < :endTime and t.end_time > :endTime))");
                sb.append(" and (t.canceled is null or t.canceled = 'N' or t.canceled = 'H')");

                NativeQuery<Long> query = (NativeQuery<Long>) entityManager.createNativeQuery(sb.toString());
                query.setParameter("instructorId", pInstructorId);
                query.setParameter("startTime", pStartDate);
                query.setParameter("endTime", pEndDate);
                query.addScalar("counts", StandardBasicTypes.LONG);

                Long count = query.uniqueResult();
                return (count > 0);
            }
        };
    }

    public static Criterion<Instructor, InstructorActivitiesDTO> getRecipientDailySubcEmailIds() {
        return new InstructorCriterion<InstructorActivitiesDTO>() {

            @Override
            public List<InstructorActivitiesDTO> search(EntityManager entityManager) {
                StringBuilder sb = new StringBuilder("select recipientId");
                sb.append(" from EMAIL_LOOKUP");
                sb.append(" where description='Daily Subscription Report'");

                // Create the query using the new API
                Query query = entityManager.createNativeQuery(sb.toString(), String.class);

                // Execute the query and retrieve results
                List<String> recipientIds = query.getResultList();
                List<InstructorActivitiesDTO> resultList = new ArrayList<>();

                for (String recipientId : recipientIds) {
                    InstructorActivitiesDTO dto = new InstructorActivitiesDTO(recipientId);
                    resultList.add(dto);
                }

                return resultList;
            }
        };
    }


    public static Criterion<Instructor, Boolean> hasAppointmentByInstructorAndDayOfWeek(final Long pInstructorId, final Integer pDayOfWeek) {
        return new InstructorCriterion<Boolean>() {
            @Override
            public Boolean get(EntityManager entityManager, int pFetchMode) {
                String dayOfWeek = String.valueOf(pDayOfWeek);
                Boolean result = true;

                StringBuilder sb = new StringBuilder("select t.start_time as sTime, t.end_time as eTime");
                sb.append(" from appointment t");
                sb.append(" where to_char(t.start_time - 1, 'd') = :dayOfWeek");
                sb.append(" and t.instructor_id = :instructorId");
                sb.append(" and t.start_time >= sysdate");
                sb.append(" and (t.canceled is null or t.canceled = 'N' or t.canceled = 'H')");

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb.toString());
                query.setParameter("instructorId", pInstructorId);
                query.setParameter("dayOfWeek", dayOfWeek);
                query.addScalar("sTime", StandardBasicTypes.TIMESTAMP);
                query.addScalar("eTime", StandardBasicTypes.TIMESTAMP);

                List<Object[]> list = query.list();
                if (list == null || list.isEmpty()) {
                    return !result;
                }

                String checkOntime = " select ot.start_time as sTime, ot.end_time as eTime"
                        + " from onetime ot"
                        + " where ot.INSTRUCTOR_ID = :instructorId"
                        + " and ot.start_time >= sysdate"
                        + " order by sTime";

                NativeQuery<Object[]> checkTimeQuery = (NativeQuery<Object[]>) entityManager.createNativeQuery(checkOntime);
                checkTimeQuery.setParameter("instructorId", pInstructorId);
                checkTimeQuery.addScalar("sTime", StandardBasicTypes.TIMESTAMP);
                checkTimeQuery.addScalar("eTime", StandardBasicTypes.TIMESTAMP);

                List<Object[]> timeList = checkTimeQuery.list();
                if (timeList == null || timeList.isEmpty()) {
                    return result;
                }

                List<TimeIntervalDTO> sortList = new ArrayList<>();
                for (Object[] objectTime : timeList) {
                    TimeIntervalDTO timeInterval = new TimeIntervalDTO();
                    timeInterval.setStartTime(new DateTime(objectTime[0]));
                    timeInterval.setEndTime(new DateTime(Objects.requireNonNull(objectTime[1])));
                    sortList.add(timeInterval);
                }

                sortList = DateTimeUtil.concatTime(sortList);

                for (Object[] columns : list) {
                    result = true;
                    DateTime start_time = new DateTime(columns[0]);
                    DateTime end_time = new DateTime(columns[1]);
                    for (TimeIntervalDTO sort : sortList) {
                        DateTime startTime = sort.getStartTime();
                        DateTime endTime = sort.getEndTime();
                        if ((startTime.isBefore(start_time) || startTime.isEqual(start_time))
                                && (endTime.isAfter(end_time) || endTime.isEqual(end_time))) {
                            result = false;
                            break;
                        }
                    }
                    if (result) break;
                }
                return result;
            }
        };
    }

    private static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-dd-MM HH:mm:ss");
    private static String getCustStartTime() {

        Calendar day = Calendar.getInstance();

        day.set(Calendar.MILLISECOND, 0);
        day.set(Calendar.SECOND, 0);
        day.set(Calendar.MINUTE, 0);
        day.set(Calendar.HOUR_OF_DAY, 0);
        day.add(Calendar.DATE, -1);

        return simpleDateFormat.format(day.getTime());
    }

    private static String getCustEndTime() {

        Calendar day = Calendar.getInstance();

        day.set(Calendar.MILLISECOND, 0);
        day.set(Calendar.SECOND, 0);
        day.set(Calendar.MINUTE, 59);
        day.set(Calendar.HOUR_OF_DAY, 23);
        day.add(Calendar.DATE, -1);

        return simpleDateFormat.format(day.getTime());
    }


    public static Criterion<Instructor, DailySubscriptionReportDTO> findCustomerReportRecords() {
        return new InstructorCriterion<DailySubscriptionReportDTO>() {

            @Override
            public List<DailySubscriptionReportDTO> search(EntityManager entityManager) {
                Log.error("Entered into InstructorCriterion.findCustomerReportRecords()");

                String startTime = getCustStartTime();
                String endTime = getCustEndTime();

                StringBuilder sb = new StringBuilder("select c.external_id customer#,p.first_name,p.last_name,p.email,l.external_id || ' ' || l.location_name location,cs.status_name,");
                sb.append("p.phone phone, to_char(c.customer_created, 'MM-DD-YYYY') SIGN_UP_DATE");
                sb.append(" from customer c,person p,location l,customer_status cs ");
                sb.append(" where c.person_id = p.person_id ");
                sb.append(" and c.location_external_id = l.external_id ");
                sb.append(" and c.customer_status_id =  cs.customer_status_id ");
                sb.append(" and c.customer_status_id != '2' ");
                sb.append(" and l.external_id not in ('998','999') ");
                sb.append(" and c.customer_created BETWEEN to_date(?, 'YYYY-DD-MM HH24:MI:SS') and to_date(?, 'YYYY-DD-MM HH24:MI:SS') ");

                // Create the query using EntityManager
                Query query = entityManager.createNativeQuery(sb.toString());

                // Set parameters using named parameters
                query.setParameter(1, startTime);
                query.setParameter(2, endTime);

                // Execute the query and retrieve results
                List<Object[]> results = query.getResultList();
                List<DailySubscriptionReportDTO> resultList = new ArrayList<>();

                for (Object[] objects : results) {

                    String customerNo = (String) objects[0];
                    String firstName = (String) objects[1];
                    String lastName = (String) objects[2];
                    String email=(String) objects[3];
                    String location=(String) objects[4];
                    String statusName=(String) objects[5];
                    String phone=(String) objects[6];
                    String signUpDate=(String) objects[7];

                    DailySubscriptionReportDTO dto = new DailySubscriptionReportDTO(customerNo, firstName, lastName,
                            email, location,statusName,phone,signUpDate);
                    resultList.add(dto);

                }

                Log.info("InstructorCriterion.findCustomerReportRecords(), retrieved DB values: {}", resultList);
                return resultList;
            }
        };
    }


    public static Criterion<Instructor, DailySubscriptionReportDTO> findDailySubcribtionLocation() {
        return new InstructorCriterion<DailySubscriptionReportDTO>() {

            @Override
            public List<DailySubscriptionReportDTO> search(EntityManager entityManager) {
                // SQLQuery query = null;
                Log.info("entered into InstructorCriterion.findInstructorActivities() {}");


                String startTime = getCustStartTime();
                String endTime = getCustEndTime();

  						/*StringBuilder sb = new StringBuilder("select l.EXTERNAL_ID,P.FIRST_NAME,P.LAST_NAME,l.LOCATION_NAME,A.ACTIVITY_NAME");
  						sb.append(" from location l");
  						sb.append(" join LOCATION_PROFILE lp on l.PROFILE_ID = lp.PROFILE_ID");
  						sb.append(" join INSTRUCTOR I on l.LOCATION_ID = I.LOCATION_ID");
  						sb.append(" join PERSON P on P.PERSON_ID = I.PERSON_ID");
  						sb.append(" join INSTRUCTOR_ACTIVITIES IA on I.INSTRUCTOR_ID = IA.INSTRUCTOR_ID");
  						sb.append(" join ACTIVITY A on IA.ACTIVITY_ID = A.ACTIVITY_ID");
  						sb.append(" where lp.ENABLED = 'Y' and I.ENABLED = 'Y' and I.STATUS = 'A'");
  						sb.append(" order by l.EXTERNAL_ID,P.FIRST_NAME,P.LAST_NAME");*/

                StringBuilder sb = new StringBuilder("select lo.external_id || ' ' || lo.location_name LOCATION ");
                sb.append("from location lo where  lo.external_id not in ('998','999') and lo.location_id not in ( ");
                sb.append(" select distinct l.location_id   ");
                sb.append(" from customer c,person p,location l,customer_status cs  ");
                sb.append(" where c.person_id = p.person_id  ");
                sb.append(" and c.location_external_id = l.external_id  ");
                sb.append(" and c.customer_status_id =  cs.customer_status_id  ");
                sb.append(" and c.customer_status_id != '2'   ");
                sb.append(" and l.external_id not in ('998','999')  ");
                sb.append(" and c.customer_created BETWEEN to_date(?, 'YYYY-DD-MM HH24:MI:SS') and to_date(?, 'YYYY-DD-MM HH24:MI:SS')) ");

                //  query = pSession.createSQLQuery(sb.toString());
                // Create the query using EntityManager
                Query query = entityManager.createNativeQuery(sb.toString());

                query.setParameter(1, startTime);
                query.setParameter(2, endTime);

                //query.addScalar("LOCATION", StandardBasicTypes.STRING);


                // Execute the query and retrieve results
                List<String> results = query.getResultList();
                List<DailySubscriptionReportDTO> resultList = new ArrayList<>();

                for (String objects : results) {

                    String location = objects;


                    DailySubscriptionReportDTO dto = new DailySubscriptionReportDTO();
                    dto.setLocation(location);
                    resultList.add(dto);
                }

                //scroll.close();
                Log.error("InstructorCriterion.get sign up (), after retrieving DB values");

                return resultList;
            }
        };
    }


}








