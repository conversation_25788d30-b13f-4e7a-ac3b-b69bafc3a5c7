package com.guitarcenter.scheduler.dao.criterion.dto;

import java.util.HashSet;
import java.util.Set;

import com.guitarcenter.scheduler.model.Room;

/**
 * Created by josedeng on 12/19/13
 */
public class RoomDTO {

    private Room room;
    private Set<Room> subRooms = new HashSet<Room>();

    public RoomDTO(Room room, Room subRoom) {
        this.room = room;
        if (subRoom != null) {
            subRooms.add(room);
        }
    }

    public void addSubRoom(Room room) {
        if (room != null) {
            subRooms.add(room);
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        RoomDTO roomDTO = (RoomDTO) o;

        if (!room.getRoomId().equals(roomDTO.room.getRoomId())) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return room.getRoomId().hashCode();
    }

    public Room getRoom() {
        return room;
    }

    public Set<Room> getSubRooms() {
        return subRooms;
    }
}
