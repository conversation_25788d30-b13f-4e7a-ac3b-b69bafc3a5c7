package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROOM_TYPE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.List;

import jakarta.persistence.EntityManager;
import org.hibernate.Session;

import com.guitarcenter.scheduler.model.RoomSize;

public abstract class RoomSizeCriterion<E> extends AbstractCriterion<RoomSize, E> implements Criterion<RoomSize, E> {

	private static final Criterion<RoomSize, RoomSize>	DEFAULT_INSTANCE	= new RoomSizeCriterion<RoomSize>() {
																			};



	private RoomSizeCriterion() {
		super(RoomSize.class);
	}



	@Override
	protected String getFetchScript(int pFetchMode) {
		StringBuilder sb = new StringBuilder();
		sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
		sb.append(addFetchHQL(FETCH_ROOM_TYPE, pFetchMode, "t.roomType", true));
		sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
		return sb.toString();
	}



	@Override
	public List<E> search(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	@Override
	public E get(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	public static Criterion<RoomSize, RoomSize> getInstance() {
		return DEFAULT_INSTANCE;
	}

}
