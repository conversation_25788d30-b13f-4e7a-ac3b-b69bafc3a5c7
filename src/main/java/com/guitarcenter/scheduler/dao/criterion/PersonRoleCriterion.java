package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION_PROFILE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROLE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.List;

import jakarta.persistence.EntityManager;
import org.hibernate.query.Query;
import org.hibernate.Session;

import com.guitarcenter.scheduler.model.PersonRole;

public abstract class PersonRoleCriterion<E> extends AbstractCriterion<PersonRole, E> implements
		Criterion<PersonRole, E> {

	private static final Criterion<PersonRole, PersonRole>	DEFAULT_INSTANCE	= new PersonRoleCriterion<PersonRole>() {
																				};



	private PersonRoleCriterion() {
		super(PersonRole.class);
	}



	@Override
	protected String getFetchScript(int pFetchMode) {
		StringBuilder sb = new StringBuilder();
		sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
		sb.append(addFetchHQL(FETCH_LOCATION, pFetchMode, "t.location", true));
		sb.append(addFetchHQL(FETCH_PERSON, pFetchMode, "t.person", true));
		sb.append(addFetchHQL(FETCH_ROLE, pFetchMode, "t.role", true));
		sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
		sb.append(addFetchHQL(FETCH_LOCATION_PROFILE, pFetchMode, "t.location.locationProfile", true));
		return sb.toString();
	}



	@Override
	public List<E> search(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	@Override
	public E get(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	public static Criterion<PersonRole, PersonRole> getInstance() {
		return DEFAULT_INSTANCE;
	}



	public static Criterion<PersonRole, PersonRole> findByPerson(long pSiteId, long pPersonId) {
		final long siteId = pSiteId;
		final long personId = pPersonId;

		Criterion<PersonRole, PersonRole> instance = new PersonRoleCriterion<PersonRole>() {

			@Override
			@SuppressWarnings("unchecked")
			public List<PersonRole> search(EntityManager entityManager, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from PersonRole t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append("where t.site.siteId = :siteId and t.person.personId = :personId");
				Query query = (Query) entityManager.createQuery(sb.toString());
				query.setParameter("siteId", siteId);
				query.setParameter("personId", personId);
				return query.list();
			}

		};
		return instance;
	}
	//Created for GSSP-146
	public static Criterion<PersonRole, PersonRole> findRolesByPersondIds(List<Long> pPersonId) {
		final List<Long> personId = pPersonId;			
		Criterion<PersonRole, PersonRole> instance = new PersonRoleCriterion<PersonRole>() {		

			@Override
			@SuppressWarnings("unchecked")
			public List<PersonRole> search(EntityManager entityManager, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from PersonRole t ");
				sb.append(getFetchScript(pFetchMode));					
				sb.append(" where t.person.personId in :personId ");
				Query query = (Query) entityManager.createQuery(sb.toString());
				query.setParameterList("personId", personId);
				return query.list();
			}	
		};
		return instance; 
	}

	
	
	/**
	 * Returns a List of PersonRole instances for the combination of site and
	 * authentication id provided.
	 * 
	 * @param pSiteId
	 * @param pAuthId
	 * @return List of PersonRole instances
	 */
	public static Criterion<PersonRole, PersonRole> findByAuthId(final long pSiteId, final String authId) {
	    return new PersonRoleCriterion<PersonRole>() {
	        @Override
	        @SuppressWarnings("unchecked")
            public List<PersonRole> search(EntityManager entityManager, int pFetchMode) {
	            Query query = (Query) entityManager.createQuery(new StringBuilder(" from PersonRole t ")
	                                                   .append(getFetchScript(pFetchMode))
	                                                   .append(" where t.site.siteId = :siteId ")
	                                                   .append("   and lower(t.person.authId) = lower(:authId) ")
	                                                   .toString());
	            query.setParameter("siteId", pSiteId);
	            query.setParameter("authId", authId);
	            return query.list();
	        }
	    };
	}

	public static Criterion<PersonRole, PersonRole> findByPersonLocation(long pSiteId, long pPersonId, long pLocationId) {
		final long siteId = pSiteId;
		final long personId = pPersonId;
		final long locationId = pLocationId;

		Criterion<PersonRole, PersonRole> instance = new PersonRoleCriterion<PersonRole>() {

			@Override
			@SuppressWarnings("unchecked")
			public List<PersonRole> search(EntityManager entityManager, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from PersonRole t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append("where t.site.siteId = :siteId and t.person.personId = :personId and t.location.locationId = :locationId");
				Query query = (Query) entityManager.createQuery(sb.toString());
				query.setParameter("siteId", siteId);
				query.setParameter("personId", personId);
				query.setParameter("locationId", locationId);
				return query.list();
			}

		};
		return instance;
	}

}
