package com.guitarcenter.scheduler.dao.criterion;

import java.util.List;
import jakarta.persistence.EntityManager;

public interface Criterion<T, E> {

	List<E> search(EntityManager entityManager);

	List<E> search(EntityManager entityManager, int fetchMode);

	List<T> searchAll(EntityManager entityManager);

	List<T> searchAll(EntityManager entityManager, int fetchMode);

	E get(EntityManager entityManager);

	E get(EntityManager entityManager, int fetchMode);

}
