package com.guitarcenter.scheduler.dao.criterion.dto;

/**
 * Created by <PERSON><PERSON><PERSON> on 27/06/19
 */
public class CRMAppointmentDataFileDTO {

	 	private String gc;
		private String pos;
	 	private String customerId;
	 	private String AppointmentId;
	 	private String AppointmentCustomerId;
	 	private String customerStatusName;
	 	private String customerStatusId;
	 	private String PersonId;
	 	private String studentFirstName;
	 	private String studentLastName;
	 	private String studentEmail;
	 	private String studentPhoneNumber;
	 	private String appointmentActivityId;
	 	private String activityName;
	 	private String activityMinimunDuration;
	 	private String activityMaxmumDuration;
	 	private String activityId;
	 	private String activityServiceName;
	 	private String appointmentProfileId;
	 	private String appointmentNumber;
	 	private String appointmentLocationName;
	 	private String appointmentInstructorId;
	 	private String instructorEmployeeId;
	 	private String instructorFirstName;
	 	private String instructorLastName;
	 	private String instructorEmail;
	 	private String instructorStatus;
	 	private String instructorId;
	 	private String instructionLocationName;
	 	private String instructorEnabled;
	 	private String appointmentStartTime;
	 	private String appointmentEndTime;
	 	private String appointmentCancelled;
	 	private String cancelReasonId;
	 	private String cancelReasinDescription;
	 	private String cancelReason;
	 	private String appointmentBandName;
	 	private String appointmentCreateTime;
	 	private String authId;
	 	private String appointmentUpdateTime;
	 	private String appointmenSeriesId;
	 	private String appointmenSeriesStartTime;
	 	private String appointmenSeriesEndTime;
	 	private String appointmenSeriesFlag;
	 	private String appointmenSeriesActivityId;
	 	private String appointmenSeriesName;
	 	private String seriesId;
	 	private String seriesName;
		public String getGc() {
			return gc;
		}
		public void setGc(String gc) {
			this.gc = gc;
		}
		public String getPos() {
			return pos;
		}
		public void setPos(String pos) {
			this.pos = pos;
		}
		public String getCustomerId() {
			return customerId;
		}
		public void setCustomerId(String customerId) {
			this.customerId = customerId;
		}
		public String getAppointmentId() {
			return AppointmentId;
		}
		public void setAppointmentId(String appointmentId) {
			AppointmentId = appointmentId;
		}
		public String getAppointmentCustomerId() {
			return AppointmentCustomerId;
		}
		public void setAppointmentCustomerId(String appointmentCustomerId) {
			AppointmentCustomerId = appointmentCustomerId;
		}
		public String getCustomerStatusName() {
			return customerStatusName;
		}
		public void setCustomerStatusName(String customerStatusName) {
			this.customerStatusName = customerStatusName;
		}
		public String getCustomerStatusId() {
			return customerStatusId;
		}
		public void setCustomerStatusId(String customerStatusId) {
			this.customerStatusId = customerStatusId;
		}
		public String getPersonId() {
			return PersonId;
		}
		public void setPersonId(String personId) {
			PersonId = personId;
		}
		public String getStudentFirstName() {
			return studentFirstName;
		}
		public void setStudentFirstName(String studentFirstName) {
			this.studentFirstName = studentFirstName;
		}
		public String getStudentLastName() {
			return studentLastName;
		}
		public void setStudentLastName(String studentLastName) {
			this.studentLastName = studentLastName;
		}
		public String getStudentEmail() {
			return studentEmail;
		}
		public void setStudentEmail(String studentEmail) {
			this.studentEmail = studentEmail;
		}
		public String getStudentPhoneNumber() {
			return studentPhoneNumber;
		}
		public void setStudentPhoneNumber(String studentPhoneNumber) {
			this.studentPhoneNumber = studentPhoneNumber;
		}
		public String getAppointmentActivityId() {
			return appointmentActivityId;
		}
		public void setAppointmentActivityId(String appointmentActivityId) {
			this.appointmentActivityId = appointmentActivityId;
		}
		public String getActivityName() {
			return activityName;
		}
		public void setActivityName(String activityName) {
			this.activityName = activityName;
		}
		public String getActivityMinimunDuration() {
			return activityMinimunDuration;
		}
		public void setActivityMinimunDuration(String activityMinimunDuration) {
			this.activityMinimunDuration = activityMinimunDuration;
		}
		public String getActivityMaxmumDuration() {
			return activityMaxmumDuration;
		}
		public void setActivityMaxmumDuration(String activityMaxmumDuration) {
			this.activityMaxmumDuration = activityMaxmumDuration;
		}
		public String getActivityId() {
			return activityId;
		}
		public void setActivityId(String activityId) {
			this.activityId = activityId;
		}
		public String getActivityServiceName() {
			return activityServiceName;
		}
		public void setActivityServiceName(String activityServiceName) {
			this.activityServiceName = activityServiceName;
		}
		public String getAppointmentProfileId() {
			return appointmentProfileId;
		}
		public void setAppointmentProfileId(String appointmentProfileId) {
			this.appointmentProfileId = appointmentProfileId;
		}
		public String getAppointmentNumber() {
			return appointmentNumber;
		}
		public void setAppointmentNumber(String appointmentNumber) {
			this.appointmentNumber = appointmentNumber;
		}
		public String getAppointmentLocationName() {
			return appointmentLocationName;
		}
		public void setAppointmentLocationName(String appointmentLocationName) {
			this.appointmentLocationName = appointmentLocationName;
		}
		public String getAppointmentInstructorId() {
			return appointmentInstructorId;
		}
		public void setAppointmentInstructorId(String appointmentInstructorId) {
			this.appointmentInstructorId = appointmentInstructorId;
		}
		public String getInstructorEmployeeId() {
			return instructorEmployeeId;
		}
		public void setInstructorEmployeeId(String instructorEmployeeId) {
			this.instructorEmployeeId = instructorEmployeeId;
		}
		public String getInstructorFirstName() {
			return instructorFirstName;
		}
		public void setInstructorFirstName(String instructorFirstName) {
			this.instructorFirstName = instructorFirstName;
		}
		public String getInstructorLastName() {
			return instructorLastName;
		}
		public void setInstructorLastName(String instructorLastName) {
			this.instructorLastName = instructorLastName;
		}
		public String getInstructorEmail() {
			return instructorEmail;
		}
		public void setInstructorEmail(String instructorEmail) {
			this.instructorEmail = instructorEmail;
		}
		public String getInstructorStatus() {
			return instructorStatus;
		}
		public void setInstructorStatus(String instructorStatus) {
			this.instructorStatus = instructorStatus;
		}
		public String getInstructorId() {
			return instructorId;
		}
		public void setInstructorId(String instructorId) {
			this.instructorId = instructorId;
		}
		public String getInstructionLocationName() {
			return instructionLocationName;
		}
		public void setInstructionLocationName(String instructionLocationName) {
			this.instructionLocationName = instructionLocationName;
		}
		public String getInstructorEnabled() {
			return instructorEnabled;
		}
		public void setInstructorEnabled(String instructorEnabled) {
			this.instructorEnabled = instructorEnabled;
		}
		public String getAppointmentStartTime() {
			return appointmentStartTime;
		}
		public void setAppointmentStartTime(String appointmentStartTime) {
			this.appointmentStartTime = appointmentStartTime;
		}
		public String getAppointmentEndTime() {
			return appointmentEndTime;
		}
		public void setAppointmentEndTime(String appointmentEndTime) {
			this.appointmentEndTime = appointmentEndTime;
		}
		public String getAppointmentCancelled() {
			return appointmentCancelled;
		}
		public void setAppointmentCancelled(String appointmentCancelled) {
			this.appointmentCancelled = appointmentCancelled;
		}
		public String getCancelReasonId() {
			return cancelReasonId;
		}
		public void setCancelReasonId(String cancelReasonId) {
			this.cancelReasonId = cancelReasonId;
		}
		public String getCancelReasinDescription() {
			return cancelReasinDescription;
		}
		public void setCancelReasinDescription(String cancelReasinDescription) {
			this.cancelReasinDescription = cancelReasinDescription;
		}
		public String getCancelReason() {
			return cancelReason;
		}
		public void setCancelReason(String cancelReason) {
			this.cancelReason = cancelReason;
		}
		public String getAppointmentBandName() {
			return appointmentBandName;
		}
		public void setAppointmentBandName(String appointmentBandName) {
			this.appointmentBandName = appointmentBandName;
		}
		public String getAppointmentCreateTime() {
			return appointmentCreateTime;
		}
		public void setAppointmentCreateTime(String appointmentCreateTime) {
			this.appointmentCreateTime = appointmentCreateTime;
		}
		public String getAuthId() {
			return authId;
		}
		public void setAuthId(String authId) {
			this.authId = authId;
		}
		public String getAppointmentUpdateTime() {
			return appointmentUpdateTime;
		}
		public void setAppointmentUpdateTime(String appointmentUpdateTime) {
			this.appointmentUpdateTime = appointmentUpdateTime;
		}
		public String getAppointmenSeriesId() {
			return appointmenSeriesId;
		}
		public void setAppointmenSeriesId(String appointmenSeriesId) {
			this.appointmenSeriesId = appointmenSeriesId;
		}
		public String getAppointmenSeriesStartTime() {
			return appointmenSeriesStartTime;
		}
		public void setAppointmenSeriesStartTime(String appointmenSeriesStartTime) {
			this.appointmenSeriesStartTime = appointmenSeriesStartTime;
		}
		public String getAppointmenSeriesEndTime() {
			return appointmenSeriesEndTime;
		}
		public void setAppointmenSeriesEndTime(String appointmenSeriesEndTime) {
			this.appointmenSeriesEndTime = appointmenSeriesEndTime;
		}
		public String getAppointmenSeriesFlag() {
			return appointmenSeriesFlag;
		}
		public void setAppointmenSeriesFlag(String appointmenSeriesFlag) {
			this.appointmenSeriesFlag = appointmenSeriesFlag;
		}
		public String getAppointmenSeriesActivityId() {
			return appointmenSeriesActivityId;
		}
		public void setAppointmenSeriesActivityId(String appointmenSeriesActivityId) {
			this.appointmenSeriesActivityId = appointmenSeriesActivityId;
		}
		public String getAppointmenSeriesName() {
			return appointmenSeriesName;
		}
		public void setAppointmenSeriesName(String appointmenSeriesName) {
			this.appointmenSeriesName = appointmenSeriesName;
		}
		public String getSeriesId() {
			return seriesId;
		}
		public void setSeriesId(String seriesId) {
			this.seriesId = seriesId;
		}
		public String getSeriesName() {
			return seriesName;
		}
		public void setSeriesName(String seriesName) {
			this.seriesName = seriesName;
		}
		@Override
		public String toString() {
			return  gc + "~" + pos + "~" + customerId + "~"
					+ AppointmentId + "~" + AppointmentCustomerId + "~"
					+ customerStatusName + "~" + customerStatusId + "~" + PersonId
					+ "~" + studentFirstName + "~" + studentLastName
					+ "~" + studentEmail + "~" + studentPhoneNumber
					+ "~" + appointmentActivityId + "~" + activityName
					+ "~" + activityMinimunDuration + "~"
					+ activityMaxmumDuration + "~" + activityId + "~"
					+ activityServiceName + "~" + appointmentProfileId + "~"
					+ appointmentNumber + "~" + appointmentLocationName
					+ "~" + appointmentInstructorId + "~"
					+ instructorEmployeeId + "~" + instructorFirstName + "~"
					+ instructorLastName + "~" + instructorEmail + "~"
					+ instructorStatus + "~" + instructorId + "~"
					+ instructionLocationName + "~" + instructorEnabled + "~"
					+ appointmentStartTime + "~" + appointmentEndTime + "~"
					+ appointmentCancelled + "~" + cancelReasonId + "~"
					+ cancelReasinDescription + "~" + cancelReason + "~"
					+ appointmentBandName + "~" + appointmentCreateTime + "~" + authId
					+ "~" + appointmentUpdateTime + "~" + appointmenSeriesId
					+ "~" + appointmenSeriesStartTime + "~"
					+ appointmenSeriesEndTime + "~" + appointmenSeriesFlag
					+ "~" + appointmenSeriesActivityId + "~"
					+ appointmenSeriesName + "~" + seriesId + "~" + seriesName + "\n";
		}

	 	
}
