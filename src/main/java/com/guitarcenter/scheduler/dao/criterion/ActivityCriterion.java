package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SERVICE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.List;

import jakarta.persistence.EntityManager;


//import org.hibernate.SQLQuery;
import jakarta.persistence.Query;
import org.hibernate.Session;

import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.enums.Enabled;

public abstract class ActivityCriterion<E> extends AbstractCriterion<Activity, E> implements Criterion<Activity, E> {

    private static final Criterion<Activity, Activity> DEFAULT_INSTANCE = new ActivityCriterion<Activity>() {
    };


    private ActivityCriterion() {
        super(Activity.class);
    }


    @Override
    protected String getFetchScript(int pFetchMode) {
        StringBuilder sb = new StringBuilder();
        sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
        sb.append(addFetchHQL(FETCH_SERVICE, pFetchMode, "t.service", true));
        sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
        return sb.toString();
    }


    @Override
    public List<E> search(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }


    @Override
    public E get(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }


    public static Criterion<Activity, Activity> getInstance() {
        return DEFAULT_INSTANCE;
    }


    public static Criterion<Activity, Activity> findByService(final long pServiceId) {

        ActivityCriterion<Activity> instance = new ActivityCriterion<Activity>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Activity> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Activity t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append("where t.service.serviceId = :serviceId order by t.activityId");
                Query query = entityManager.createQuery(sb.toString());
                query.setParameter("serviceId", pServiceId);
                return query.getResultList();
            }

        };
        return instance;
    }


    public static Criterion<Activity, Boolean> hasByServiceIdAndEnabled(final long pServiceId, final Enabled pEnabled) {

        ActivityCriterion<Boolean> instance = new ActivityCriterion<Boolean>() {

            @Override
            public Boolean get(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select count(t) from Activity t ");
                sb.append(" where t.service.serviceId = :serviceId ");
                sb.append(" and t.enabled = :enabled ");
                Query query = entityManager.createQuery(sb.toString());
                query.setMaxResults(1);
                query.setParameter("serviceId", pServiceId);
                query.setParameter("enabled", pEnabled.toString());
                Long count = (Long) query.getSingleResult();
                return count > 0 ? Boolean.TRUE : Boolean.FALSE;
            }

        };
        return instance;
    }


    public static Criterion<Activity, Activity> findBySiteId(final long pSiteId) {

        ActivityCriterion<Activity> instance = new ActivityCriterion<Activity>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Activity> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Activity t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append("where t.site.siteId = :siteId order by t.activityId");
                Query query = entityManager.createQuery(sb.toString());
                query.setParameter("siteId", pSiteId);
                return query.getResultList();
            }

        };
        return instance;
    }


    public static Criterion<Activity, Activity> findByActivityName(final String pActivityName, final Enabled pEnabled) {

        ActivityCriterion<Activity> instance = new ActivityCriterion<Activity>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Activity> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Activity t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append("where lower(t.activityName) like :activityName");
                sb.append(" and t.enabled = :enabled");
                sb.append(" order by t.activityId");
                Query query = entityManager.createQuery(sb.toString());
                query.setParameter("activityName", "%" + pActivityName.toLowerCase() + "%");
                query.setParameter("enabled", pEnabled);
                return query.getResultList();
            }

        };
        return instance;
    }

    public static Criterion<Activity, Activity> findByProfileIdAndServiceIddAndRoomId(final long pProfileId, final long pServiceId, final long pRoomId) {
        return new ActivityCriterion<Activity>() {
            @Override
            public List<Activity> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select {t.*}");
                sb.append("  from activity t");
                sb.append("  left join room_activities r_a");
                sb.append("    on t.activity_id = r_a.activity_id");
                sb.append("  left join profile_activity p_a");
                sb.append("    on t.activity_id = p_a.activity_id");
                sb.append(" where t.service_id = :serviceId");
                sb.append("   and p_a.enabled = 'Y'");
                sb.append("   and p_a.profile_id = :profileId");
                sb.append("   and r_a.room_id = :roomId");
                sb.append(" order by t.activity_id");
                Query query = entityManager.createNativeQuery(sb.toString(), Activity.class);
                //  query.addEntity("t", Activity.class);
                query.setParameter("serviceId", pServiceId);
                query.setParameter("profileId", pProfileId);
                query.setParameter("roomId", pRoomId);
                return query.getResultList();
            }
        };
    }

    public static Criterion<Activity, Activity> findByProfileIdAndServiceIddAndInstructorId(final long pProfileId, final long pServiceId, final long pInstructorId) {
        return new ActivityCriterion<Activity>() {
            @Override
            public List<Activity> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select t.* ");
                sb.append("  from activity t");
                sb.append("  left join instructor_activities i_a");
                sb.append("    on t.activity_id = i_a.activity_id");
                sb.append("  left join profile_activity p_a");
                sb.append("    on t.activity_id = p_a.activity_id");
                sb.append(" where i_a.instructor_id = :instructorId");
                sb.append("   and p_a.enabled = 'Y'");
                sb.append("   and p_a.profile_id = :profileId");
                sb.append("   and t.service_id = :serviceId");
                sb.append(" order by t.activity_id");
                Query query = entityManager.createNativeQuery(sb.toString(), Activity.class);
                //  query.addEntity("t", Activity.class);
                query.setParameter("serviceId", pServiceId);
                query.setParameter("profileId", pProfileId);
                query.setParameter("instructorId", pInstructorId);
                return query.getResultList();
            }
        };
    }
}
