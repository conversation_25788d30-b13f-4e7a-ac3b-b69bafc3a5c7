package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION_PROFILE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SERVICE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.List;

import jakarta.persistence.EntityManager;
import org.hibernate.query.Query;
import org.hibernate.Session;

import com.guitarcenter.scheduler.model.ProfileService;
import com.guitarcenter.scheduler.model.enums.Enabled;

public abstract class ProfileServiceCriterion<E> extends AbstractCriterion<ProfileService, E> implements
		Criterion<ProfileService, E> {

	private static final Criterion<ProfileService, ProfileService>	DEFAULT_INSTANCE	= new ProfileServiceCriterion<ProfileService>() {
																						};



	private ProfileServiceCriterion() {
		super(ProfileService.class);
	}



	@Override
	protected String getFetchScript(int pFetchMode) {
		StringBuilder sb = new StringBuilder();
		sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
		sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
		sb.append(addFetchHQL(FETCH_LOCATION_PROFILE, pFetchMode, "t.locationProfile", true));
		sb.append(addFetchHQL(FETCH_SERVICE, pFetchMode, "t.service", true));
		return sb.toString();
	}



	@Override
	public List<E> search(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	@Override
	public E get(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	public static Criterion<ProfileService, ProfileService> getInstance() {
		return DEFAULT_INSTANCE;
	}



	public static Criterion<ProfileService, ProfileService> findByProfileId(final long pProfileId) {
		ProfileServiceCriterion<ProfileService> instance = new ProfileServiceCriterion<ProfileService>() {

			@Override
			@SuppressWarnings("unchecked")
			public List<ProfileService> search(EntityManager entityManager, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from ProfileService t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append("where t.locationProfile.profileId = :profileId");
				Query query = (Query) entityManager.createQuery(sb.toString());
				query.setParameter("profileId", pProfileId);
				return query.list();
			}

		};
		return instance;
	}



	public static Criterion<ProfileService, ProfileService> findByProfileIdAndEnabled(final long pProfileId,
			final Enabled pEnabled) {
		ProfileServiceCriterion<ProfileService> instance = new ProfileServiceCriterion<ProfileService>() {

			@Override
			@SuppressWarnings("unchecked")
			public List<ProfileService> search(EntityManager entityManager, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from ProfileService t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append("where t.locationProfile.profileId = :profileId");
				sb.append(" and t.enabled = :enabled");
				sb.append(" order by t.service.serviceId ");
				Query query = (Query) entityManager.createQuery(sb.toString());
				query.setParameter("profileId", pProfileId);
				query.setParameter("enabled", pEnabled.Y);
				return query.list();
			}

		};
		return instance;
	}



	public static Criterion<ProfileService, ProfileService> getByProfileIdAndServiceId(final long pProfileId,
			final long pServiceId) {
		ProfileServiceCriterion<ProfileService> instance = new ProfileServiceCriterion<ProfileService>() {

			@Override
			public ProfileService get(EntityManager entityManager, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from ProfileService t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append("where t.locationProfile.profileId = :profileId");
				sb.append(" and t.service.serviceId = :serviceId");
				sb.append(" order by t.service.serviceId ");
				Query query = (Query) entityManager.createQuery(sb.toString());
				query.setParameter("profileId", pProfileId);
				query.setParameter("serviceId", pServiceId);
				query.setMaxResults(1);
				return (ProfileService) query.uniqueResult();
			}

		};
		return instance;
	}
	
	public static Criterion<ProfileService, ProfileService> getByProfileServiceId(final long profileServiceId) {
		ProfileServiceCriterion<ProfileService> instance = new ProfileServiceCriterion<ProfileService>() {

			@Override
			public ProfileService get(EntityManager entityManager, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from ProfileService t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append("where t.profileServiceId = :profileServiceId");
				sb.append(" order by t.service.serviceId ");
				Query query = (Query) entityManager.createQuery(sb.toString());
				query.setParameter("profileServiceId", profileServiceId);
				query.setMaxResults(1);
				return (ProfileService) query.uniqueResult();
			}

		};
		return instance;
	}



	public static Criterion<ProfileService, Boolean> hasServiceByServiceIds(final Long... pServiceIds) {
		ProfileServiceCriterion<Boolean> instance = new ProfileServiceCriterion<Boolean>() {

			@Override
			public Boolean get(EntityManager entityManager, int pFetchMode) {
				if (pServiceIds.length == 0) {
					return Boolean.FALSE;
				}
				StringBuilder sb = new StringBuilder("select count(t.profileServiceId) from ProfileService t ");
				sb.append(" where t.service.serviceId in(:serviceIds)");
				Query query = (Query) entityManager.createQuery(sb.toString());
				query.setParameterList("serviceIds", pServiceIds);
				query.setMaxResults(1);
				Long result = (Long) query.uniqueResult();
				if (result > 0) {
					return Boolean.TRUE;
				}
				return Boolean.FALSE;
			}

		};
		return instance;
	}



	public static Criterion<ProfileService, Boolean> hasByProfileIdAndServiceIdAndEnabled(final long pProfileId,
			final long pServiceId, final Enabled pEnabled) {
		ProfileServiceCriterion<Boolean> instance = new ProfileServiceCriterion<Boolean>() {

			@Override
			public Boolean get(EntityManager entityManager, int pFetchMode) {
				StringBuilder sb = new StringBuilder("select count(t) from ProfileService t ");
				sb.append(" where t.service.serviceId = :serviceId");
				sb.append(" and t.locationProfile.profileId = :profileId ");
				sb.append(" and t.enabled = :enabled ");
				Query query = (Query) entityManager.createQuery(sb.toString());
			//	query.setMaxResults(1);
				query.setParameter("profileId", pProfileId);
				query.setParameter("serviceId", pServiceId);
				query.setParameter("enabled", pEnabled);
				Long count = (Long) query.uniqueResult();
				return count > 0 ? Boolean.TRUE : Boolean.FALSE;
			}

		};
		return instance;
	}



	public static Criterion<ProfileService, ProfileService> findByServiceIdAndEnabled(final long pServiceId,
			final Enabled pEnabled) {
		ProfileServiceCriterion<ProfileService> instance = new ProfileServiceCriterion<ProfileService>() {

			@Override
			@SuppressWarnings("unchecked")
			public List<ProfileService> search(EntityManager entityManager, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from ProfileService t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append("where t.service.serviceId = :serviceId");
				sb.append(" and t.enabled = :enabled");
				sb.append(" order by t.profileServiceId ");
				Query query = (Query) entityManager.createQuery(sb.toString());
				query.setParameter("serviceId", pServiceId);
				query.setParameter("enabled", pEnabled.toString());
				return query.list();
			}

		};
		return instance;
	}

}
