package com.guitarcenter.scheduler.dao.criterion;


import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SERVICE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.List;

import jakarta.persistence.EntityManager;
import org.hibernate.query.Query;
import org.hibernate.Session;

import com.guitarcenter.scheduler.model.ProfileLookup;


public abstract class ProfileLookupCriterion<E> extends AbstractCriterion<ProfileLookup, E> implements Criterion<ProfileLookup, E> {

    private static final Criterion<ProfileLookup, ProfileLookup> DEFAULT_INSTANCE = new ProfileLookupCriterion<ProfileLookup>() {
    };


    private ProfileLookupCriterion() {
        super(ProfileLookup.class);
    }


    @Override
    protected String getFetchScript(int pFetchMode) {
        StringBuilder sb = new StringBuilder();
        sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
        sb.append(addFetchHQL(FETCH_SERVICE, pFetchMode, "t.service", true));
        sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
        return sb.toString();
    }


    @Override
    public List<E> search(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }


    @Override
    public E get(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }


    public static Criterion<ProfileLookup, ProfileLookup> getInstance() {
        return DEFAULT_INSTANCE;
    }


    public static Criterion<ProfileLookup, ProfileLookup> fetchAll() {

    	ProfileLookupCriterion<ProfileLookup> instance = new ProfileLookupCriterion<ProfileLookup>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<ProfileLookup> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from ProfileLookup t ");
                sb.append(getFetchScript(pFetchMode));                
                Query query = (Query) entityManager.createQuery(sb.toString());
                return query.list();
            }

        };
        return instance;
    }


    }
