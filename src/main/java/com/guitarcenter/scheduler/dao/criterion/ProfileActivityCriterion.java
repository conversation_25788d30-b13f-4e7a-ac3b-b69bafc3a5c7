package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ACTIVITY;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION_PROFILE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import jakarta.persistence.EntityManager;
//import org.hibernate.SQLQuery;
import jakarta.persistence.Query;
import jakarta.persistence.Tuple;
import jakarta.persistence.TypedQuery;
import org.hibernate.Session;
import org.hibernate.type.StandardBasicTypes;

import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.ProfileActivity;
import com.guitarcenter.scheduler.model.Service;
import com.guitarcenter.scheduler.model.enums.Enabled;

public abstract class ProfileActivityCriterion<E> extends AbstractCriterion<ProfileActivity, E> implements
		Criterion<ProfileActivity, E> {

	private static final Criterion<ProfileActivity, ProfileActivity>	DEFAULT_INSTANCE	= new ProfileActivityCriterion<ProfileActivity>() {
																							};



	private ProfileActivityCriterion() {
		super(ProfileActivity.class);
	}



	@Override
	protected String getFetchScript(int pFetchMode) {
		StringBuilder sb = new StringBuilder();
		sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
		sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
		sb.append(addFetchHQL(FETCH_LOCATION_PROFILE, pFetchMode, "t.locationProfile", true));
		sb.append(addFetchHQL(FETCH_ACTIVITY, pFetchMode, "t.activity", true));
		return sb.toString();
	}



	@Override
	public List<E> search(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	@Override
	public E get(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	public static Criterion<ProfileActivity, ProfileActivity> getInstance() {
		return DEFAULT_INSTANCE;
	}



	public static Criterion<ProfileActivity, ProfileActivity> findByProfileId(final long pProfileId) {
		ProfileActivityCriterion<ProfileActivity> instance = new ProfileActivityCriterion<ProfileActivity>() {

			@Override
			@SuppressWarnings("unchecked")
			public List<ProfileActivity> search(EntityManager entityManager, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from ProfileActivity t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append(" left join fetch t.activity.service ");
				sb.append("where t.locationProfile.profileId = :profileId");
				sb.append(" order by t.activity.activityId ");
				Query query = (Query) entityManager.createQuery(sb.toString());
				query.setParameter("profileId", pProfileId);
				return query.getResultList();
			}

		};
		return instance;
	}



	public static Criterion<ProfileActivity, ProfileActivity> findByProfileIdAndEnabled(final long pProfileId,
			final Enabled pEnabled) {
		ProfileActivityCriterion<ProfileActivity> instance = new ProfileActivityCriterion<ProfileActivity>() {

			@Override
			@SuppressWarnings("unchecked")
			public List<ProfileActivity> search(EntityManager entityManager, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from ProfileActivity t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append(" left join fetch t.activity.service ");
				sb.append("where t.locationProfile.profileId = :profileId");
				sb.append(" and t.enabled = :enabled");
				sb.append(" order by t.activity.activityId ");
				Query query = (Query) entityManager.createQuery(sb.toString(),ProfileActivity.class);
				query.setParameter("profileId", pProfileId);
				query.setParameter("enabled", pEnabled.Y);
				return query.getResultList();
			}

		};
		return instance;
	}



	public static Criterion<ProfileActivity, ProfileActivity> getByProfileIdAndActivityId(final long pProfileId,
			final long pActivityId) {
		ProfileActivityCriterion<ProfileActivity> instance = new ProfileActivityCriterion<ProfileActivity>() {

			@Override
			public ProfileActivity get(EntityManager entityManager, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from ProfileActivity t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append(" left join fetch t.activity.service ");
				sb.append("where t.locationProfile.profileId = :profileId");
				sb.append(" and t.activity.activityId = :activityId");
				sb.append(" order by t.activity.activityId ");
				Query query = (Query) entityManager.createQuery(sb.toString());
				query.setParameter("profileId", pProfileId);
				query.setParameter("activityId", pActivityId);
				query.setMaxResults(1);
				return (ProfileActivity) query.getSingleResult();
			}

		};
		return instance;
	}



	public static Criterion<ProfileActivity, ProfileActivity> findByProfileIdAndServiceIdAndEnabled(
			final long pProfileId, final long pServiceId, final Enabled pEnabled) {
		ProfileActivityCriterion<ProfileActivity> instance = new ProfileActivityCriterion<ProfileActivity>() {

			@Override
			@SuppressWarnings("unchecked")
			public List<ProfileActivity> search(EntityManager entityManager, int pFetchMode) {
				try {
					StringBuilder sb = new StringBuilder("from ProfileActivity t ");
					sb.append(getFetchScript(pFetchMode));
					sb.append(" left join fetch t.activity.service ");
					sb.append(" where t.locationProfile.profileId = :profileId");
					sb.append(" and t.activity.service.serviceId = :serviceId");
					sb.append(" and t.enabled = :enabled");
					sb.append(" order by t.activity.activityId ");
					Query query =  entityManager.createQuery(sb.toString());
					query.setParameter("profileId", pProfileId);
					query.setParameter("serviceId", pServiceId);
					query.setParameter("enabled", pEnabled);
					return query.getResultList();
				} catch (Exception e) {
					throw new RuntimeException(e);
				}
			}

		};
		return instance;
	}



	public static ProfileActivityCriterion<ProfileActivity> findByProfileIdAndServiceIds(final long pProfileId, final Long... pServiceIds) {
		ProfileActivityCriterion<ProfileActivity> instance = new ProfileActivityCriterion<ProfileActivity>() {

			@Override
			@SuppressWarnings("unchecked")
			public List<ProfileActivity> search(EntityManager entityManager, int pFetchMode) {
				if (pServiceIds.length == 0) {
					return Collections.emptyList();
				}
				StringBuilder sb = new StringBuilder(" from ProfileActivity t ");
				sb.append(getFetchScript(pFetchMode));
				//sb.append(" left join fetch t.activity.service ");
				sb.append(" where t.locationProfile.profileId = :profileId");
				//sb.append(" and t.activity.service.serviceId in (:serviceIds)");
				sb.append(" and t.enabled = :enabled");
				sb.append(" order by t.activity.activityId ");

				// Use createQuery with the expected result class
				Query query = entityManager.createQuery(sb.toString(), ProfileActivity.class);
				query.setParameter("profileId", pProfileId);
				// Use setParameter with a collection in Hibernate 6
				//query.setParameter("serviceIds", Arrays.asList(pServiceIds));
				query.setParameter("enabled", Enabled.Y);

				// Use getResultList() in Hibernate 6
				return query.getResultList();
			}

		};
		return instance;
	}




	public static Criterion<ProfileActivity, ProfileActivity> findByActivityIdAndSiteIdAndEnabled(
			final long pActivityId, final long pSiteId, final Enabled pEnabled) {
		ProfileActivityCriterion<ProfileActivity> instance = new ProfileActivityCriterion<ProfileActivity>() {

			@Override
			@SuppressWarnings("unchecked")
			public List<ProfileActivity> search(EntityManager entityManager, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from ProfileActivity t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append("where t.site.siteId = :siteId");
				sb.append(" and t.activity.activityId = :activityId");
				sb.append(" and t.enabled = :enabled");
				Query query =  entityManager.createQuery(sb.toString());
				query.setParameter("siteId", pSiteId);
				query.setParameter("activityId", pActivityId);
				query.setParameter("enabled", pEnabled.toString());
				return query.getResultList();
			}

		};
		return instance;
	}



	public static Criterion<ProfileActivity, Boolean> hasActivityByActivityIds(final Long... pActivityIds) {
		ProfileActivityCriterion<Boolean> instance = new ProfileActivityCriterion<Boolean>() {

			@Override
			public Boolean get(EntityManager entityManager, int pFetchMode) {
				if (pActivityIds.length == 0) {
					return Boolean.FALSE;
				}
				StringBuilder sb = new StringBuilder("select count(t.profileActivityId) from ProfileActivity t ");
				sb.append(" where t.activity.activityId in(:activityIds)");
				Query query = (Query) entityManager.createQuery(sb.toString());
				query.setParameter("activityIds", pActivityIds);
				query.setMaxResults(1);
				Long result = (Long) query.getSingleResult();
				if (result > 0) {
					return Boolean.TRUE;
				}
				return Boolean.FALSE;
			}

		};
		return instance;
	}



	public static Criterion<ProfileActivity, Boolean> hasByProfileIdAndServiceIdAndEnabled(final long pProfileId,
			final long pServiceId, final Enabled pEnabled) {
		ProfileActivityCriterion<Boolean> instance = new ProfileActivityCriterion<Boolean>() {

			@Override
			public Boolean get(EntityManager entityManager, int pFetchMode) {
				StringBuilder sb = new StringBuilder("select count(t) from ProfileActivity t ");
				sb.append(" where t.locationProfile.profileId = :profileId");
				sb.append(" and t.activity.service.serviceId = :serviceId ");
				sb.append(" and t.enabled = :enabled");
				Query query = (Query) entityManager.createQuery(sb.toString());
				query.setMaxResults(1);
				query.setParameter("profileId", pProfileId);
				query.setParameter("serviceId", pServiceId);
				query.setParameter("enabled", pEnabled.toString());
				Long count = (Long) query.getSingleResult();
				return count > 0 ? Boolean.TRUE : Boolean.FALSE;
			}

		};
		return instance;
	}



	public static Criterion<ProfileActivity, Boolean> hasBySiteIdAndActivityId(final long pSiteId,
			final long pActivityId) {
		ProfileActivityCriterion<Boolean> instance = new ProfileActivityCriterion<Boolean>() {

			@Override
			public Boolean get(EntityManager entityManager, int pFetchMode) {
				StringBuilder sb = new StringBuilder("select count(*) as numbers from profile_activity t");
				sb.append("   left join activity a");
				sb.append("     on t.activity_id = a.activity_id");
				sb.append("   left join profile_service ps");
				sb.append("     on a.service_id = ps.service_id");
				sb.append(" where t.site_id = :siteId");
				sb.append(" and t.activity_id = :activityId ");
				sb.append(" and ps.enabled = :enabled");
				Query query = entityManager.createNativeQuery(sb.toString())
						.unwrap(org.hibernate.query.NativeQuery.class)

						.addScalar("numbers", StandardBasicTypes.LONG);
				query.setMaxResults(1);

				query.setParameter("siteId", pSiteId);
				query.setParameter("activityId", pActivityId);
				query.setParameter("enabled", Enabled.N);
				Long count = (Long) query.getSingleResult();
				return count > 0 ? Boolean.TRUE : Boolean.FALSE;
			}

		};
		return instance;
	}
	
	/**
	 * For gcss-578, Find the activity list by profile and service that is enabled
	 * 
	 * @param pProfileId
	 * @return
	 */
    @SuppressWarnings("unchecked")
	public static Criterion<ProfileActivity, ProfileActivity> findAcitivitiesEnabledServiceAndByProfile(final long pProfileId) {
		ProfileActivityCriterion<ProfileActivity> instance = new ProfileActivityCriterion<ProfileActivity>() {
			@Override
			public List<ProfileActivity> search(EntityManager entityManager) {
				StringBuilder sb = new StringBuilder("select pa.*, a.*, s.* from profile_activity pa ")
						.append("join activity a on pa.activity_id = a.activity_id ")
						.append("join service s on a.service_id = s.service_id ")
						.append("join profile_service ps on s.service_id = ps.service_id ")
						.append("where ps.enabled = 'Y' ")
						.append("and ps.profile_id = pa.profile_id ")
						.append("and pa.profile_id = :profileId ")
						.append("order by a.activity_id asc");

				TypedQuery<Tuple> query = (TypedQuery) entityManager.createNativeQuery(sb.toString(), Tuple.class);
				query.setParameter("profileId", pProfileId);

				List<Tuple> list = query.getResultList();
				List<ProfileActivity> result = new ArrayList<>();

				for (Tuple tuple : list) {
					BigDecimal profileActivityId = tuple.get("profile_activity_id", BigDecimal.class);
					BigDecimal activityId = tuple.get("activity_id", BigDecimal.class);
					BigDecimal serviceId = tuple.get("service_id", BigDecimal.class);

					ProfileActivity profileActivity = entityManager.find(ProfileActivity.class, profileActivityId);
					Activity activity = entityManager.find(Activity.class, activityId);
					Service service = entityManager.find(Service.class,serviceId);

					activity.setService(service);
					profileActivity.setActivity(activity);
					result.add(profileActivity);

					/*ProfileActivity pa = (ProfileActivity) o[0];
					Activity a = (Activity) o[1];
					Service s = (Service) o[2];
					a.setService(s);
					pa.setActivity(a);
					result.add(pa);*/
				}
				return result;
			}
		};
		return instance;
	}

}
