package com.guitarcenter.scheduler.dao.criterion.dto;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.enums.Canceled;


public class ActiveStudentsDTO {
	private String date1;
	private Long appointmentId;
	
    private Date startTime;
    private Date endTime;
    private Canceled canceled;
    private Long duration;
    private String studentEmail;
    private String roomName;
    private String activityName;
    private Instructor instructor;
    private List<Customer> customers = new ArrayList<Customer>();
    private Map<Long, Customer> customerMap = new HashMap<Long, Customer>();
	public ActiveStudentsDTO(String date1,Long pAppointmentId, Date pStartTime, Date pEndTime, String pCanceled, Long pDuration, String pRoomName, String pActivityName, Instructor pInstructor,String pStudentEmail) {
		    this.date1 = date1;
			this.appointmentId = pAppointmentId;
	        this.startTime = pStartTime;
	        this.endTime = pEndTime;
	        this.canceled = Canceled.valueOf(pCanceled == null ? "N" : pCanceled);
	        this.duration = Math.round(pDuration / 60.0D / 60.0D);
	        this.roomName = pRoomName;
	        this.activityName = pActivityName;
	        this.instructor = pInstructor;
	        this.studentEmail=pStudentEmail;
	}

	public String getDate1() {
		return date1;
	}

	public void setDate1(String date1) {
		this.date1 = date1;
	}

	public Long getAppointmentId() {
		return appointmentId;
	}

	public void setAppointmentId(Long appointmentId) {
		this.appointmentId = appointmentId;
	}

	

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	


	public String getStudentEmail() {
		return studentEmail;
	}

	public void setStudentEmail(String studentEmail) {
		this.studentEmail = studentEmail;
	}

	public Canceled getCanceled() {
		return canceled;
	}

	
	public Long getDuration() {
		return duration;
	}

	public void setDuration(Long duration) {
		this.duration = duration;
	}

	public String getRoomName() {
		return roomName;
	}

	public void setRoomName(String roomName) {
		this.roomName = roomName;
	}

	public String getActivityName() {
		return activityName;
	}

	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}

	public Instructor getInstructor() {
		return instructor;
	}

	public void setInstructor(Instructor instructor) {
		this.instructor = instructor;
	}

	public List<Customer> getCustomers() {
		return customers;
	}

	public void setCustomers(List<Customer> customers) {
		this.customers = customers;
	}

	public void addCustomer(Customer customer) {
        Long customerId = customer.getCustomerId();
        Customer customer1 = customerMap.get(customerId);
        if (customer1 == null) {
            customers.add(customer);
            customerMap.put(customerId, customer);
        }
    }
	
	/* (non-Javadoc)
	 * @see java.lang.Object#hashCode()
	 */
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((date1 == null) ? 0 : date1.hashCode());
		result = prime * result
				+ ((instructor == null) ? 0 : instructor.hashCode());
		return result;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#equals(java.lang.Object)
	 */
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ActiveStudentsDTO other = (ActiveStudentsDTO) obj;
		if (date1 == null) {
			if (other.date1 != null)
				return false;
		} else if (!date1.equals(other.date1))
			return false;
		if (instructor == null) {
			if (other.instructor != null)
				return false;
		} else if (!instructor.equals(other.instructor))
			return false;
		return true;
	}


}
