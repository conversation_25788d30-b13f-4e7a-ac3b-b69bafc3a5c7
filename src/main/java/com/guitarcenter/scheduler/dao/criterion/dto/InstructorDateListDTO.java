package com.guitarcenter.scheduler.dao.criterion.dto;

import java.util.Date;
import java.util.List;

import com.guitarcenter.scheduler.model.Instructor;

/**
 * Created by josedeng on 12/13/13.
 */
public class InstructorDateListDTO {

    private String date1;
    private List<InstructorScheduleDTO> list;
    private Instructor instructor;
    private Date startHours;
    private Date endHours;
    
    // GSSP - 252
    private String external_id;

    public InstructorDateListDTO(String date1, Instructor instructor, List<InstructorScheduleDTO> list, Date startHours, Date endHours, String externalId) {
        this.date1 = date1;
        this.instructor = instructor;
        this.list = list;
        this.startHours = startHours;
        this.endHours = endHours;
        this.external_id = external_id;
    }
 // GSSP - 252
    public String getExternal_id() {
		return external_id;
	}

	public void setExternal_id(String external_id) {
		this.external_id = external_id;
	}

    public String getDate1() {
        return date1;
    }

    public List<InstructorScheduleDTO> getList() {
        return list;
    }

    public Instructor getInstructor() {
        return instructor;
    }

    public Date getStartHours() {
        return startHours;
    }

    public Date getEndHours() {
        return endHours;
    }

	public void setStartTimeAndEndTime(Date startTime, Date endTime) {
		this.startHours = startTime;
        this.endHours = endTime;
	}
	
	/* (non-Javadoc)
	 * @see java.lang.Object#hashCode()
	 */
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((date1 == null) ? 0 : date1.hashCode());
		result = prime * result
				+ ((instructor == null) ? 0 : instructor.hashCode());
		return result;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#equals(java.lang.Object)
	 */
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		InstructorDateListDTO other = (InstructorDateListDTO) obj;
		if (date1 == null) {
			if (other.date1 != null)
				return false;
		} else if (!date1.equals(other.date1))
			return false;
		if (instructor == null) {
			if (other.instructor != null)
				return false;
		} else if (!instructor.equals(other.instructor))
			return false;
		return true;
	}
	
	
}
