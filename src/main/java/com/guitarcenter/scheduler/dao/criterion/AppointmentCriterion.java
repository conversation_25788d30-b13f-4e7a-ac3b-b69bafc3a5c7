package com.guitarcenter.scheduler.dao.criterion;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.criterion.dto.*;
import com.guitarcenter.scheduler.dao.util.PGPUtils;
import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.dto.CancelledAppointmentDateListDTO;
import com.guitarcenter.scheduler.dto.CancelledApptDailyDTO;
import com.guitarcenter.scheduler.dto.CustomerAppointmentDetailsDTO;
import com.guitarcenter.scheduler.dto.CustomerSearchPageDTO;
import com.guitarcenter.scheduler.dto.EmployeeScheduleDTO;
import com.guitarcenter.scheduler.model.*;
import com.guitarcenter.scheduler.model.dto.InstructorAppointmentBusinessHours;
import com.guitarcenter.scheduler.model.enums.Canceled;
import com.guitarcenter.scheduler.model.enums.RequiresInstructor;
import com.guitarcenter.scheduler.webservice.dto.AppointmentStatusDTO;
import com.guitarcenter.scheduler.webservice.dto.CustomerAppointmentDetailsResultDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceResponseDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAvailableHoursDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorServiceResponseDTO;
import com.guitarcenter.scheduler.webservice.dto.Lesson;
import com.guitarcenter.scheduler.webservice.dto.LessonSeries;
import com.guitarcenter.scheduler.webservice.dto.LocationProfileInfoDTO;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.persistence.TemporalType;
import jakarta.persistence.TypedQuery;
import org.apache.commons.lang3.StringUtils;
//import org.hibernate.type.LongType;
import org.hibernate.Hibernate;
import org.hibernate.ScrollMode;
import org.hibernate.ScrollableResults;
import org.hibernate.query.NativeQuery;
import org.hibernate.type.StandardBasicTypes;
///import org.hibernate.type.StringType;
//import org.hibernate.type.TimestampType;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.math.BigDecimal;

import java.sql.Time;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;
import java.text.ParseException;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.*;

public abstract class AppointmentCriterion<E> extends AbstractCriterion<Appointment, E> implements
        Criterion<Appointment, E> {

    private static final Logger		LOG					= LoggerFactory.getLogger(AppointmentCriterion.class);

    private static final Criterion<Appointment, Appointment> DEFAULT_INSTANCE = new AppointmentCriterion<Appointment>() {
    };

    //Added for Client Facing project
    //279 GSSP-  edit disabled for jumpstart  activity Id in the webservices.
    //LES-631 Activity changed from Jump Start to Trial Lesson and Online Trial Lesson


    private static final String[] editDisabled = {"In-Store Group Class", "In-Store Summer Camp","In-Store Rockshow", "In-Store Open Office", "In-Store Home Recording","In-Store Sparring Partner","In-Store Trial Lesson","Online Trial Lesson"};
    private static final String EMAIL_REGEX ="^(.+)@(.+)$";

    private AppointmentCriterion() {
        super(Appointment.class);
    }

    public static Criterion<Appointment, Appointment> getInstance() {
        return DEFAULT_INSTANCE;
    }

    public static Criterion<Appointment, Appointment> find(long pSiteId, long pProfileId, Date pStartTime,
                                                           Date pEndTime, Long[] pServiceTypeIds, Long[] pInstructorIds, Long[] pRoomTypeIds, Long[] pActivityIds) {
        final long siteId = pSiteId;
        final long profileId = pProfileId;
        //Added for GSSP-282 Changes:: Dataformate chagnes for dayview load
        final Date dStartTime = new Date(pStartTime.getTime());
        final Date dEndTime = new Date(pEndTime.getTime());

        DateFormat outputFormatter = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH);

        DateFormat outputFormattermonth = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_HIPHEN_MONTH);

        String startTime = outputFormattermonth.format(dStartTime);
        String endTime = outputFormattermonth.format(dEndTime);

        String comparStartDate = outputFormatter.format(pStartTime);
        String comparEndDate = outputFormatter.format(pEndTime);


        final Long[] serviceTypeIds = (pServiceTypeIds == null || pServiceTypeIds.length == 0) ? null : Arrays.copyOf(
                pServiceTypeIds, pServiceTypeIds.length);
        final Long[] instructorIds = (pInstructorIds == null || pInstructorIds.length == 0) ? null : Arrays.copyOf(
                pInstructorIds, pInstructorIds.length);
        final Long[] roomTypeIds = (pRoomTypeIds == null || pRoomTypeIds.length == 0) ? null : Arrays.copyOf(
                pRoomTypeIds, pRoomTypeIds.length);
        final Long[] activityIds = (pActivityIds == null || pActivityIds.length == 0) ? null : Arrays.copyOf(
                pActivityIds, pActivityIds.length);

        return new AppointmentCriterion<Appointment>() {

            private boolean allowNullInstructor = false;
            private boolean rejectNullInstructor = false;




            @Override
            @SuppressWarnings("unchecked")
            public List<Appointment> search(EntityManager entityManager, int pFetchMode) {

                initializeRequireInstructorOption(entityManager);

                if (checkCondition()) return Collections.EMPTY_LIST;

                /**
                 * For gcss-574,add 'AppointmentSeries' object fetch in query,use the attribute 'isRecurring' of it to identify recurring appointment
                 */
                StringBuilder sb = new StringBuilder("from Appointment t ");
                sb.append(" left join fetch t.appointmentSeries apse ");
                sb.append(" left join fetch t.activity a");
                sb.append(" left join fetch a.service s");
                sb.append(" left join fetch t.customers c ");
                sb.append(" left join fetch c.person ");
                sb.append(" left join fetch t.instructor i");
                sb.append(" left join fetch t.room r ");

                if(comparStartDate.equals(comparEndDate))
                {
                    sb.append("where t.site.siteId = :siteId and t.locationProfile.profileId = :profileId and to_Char(cast(t.startTime as date), 'DD-Mon-YY')  = :startTime and to_Char(cast(t.endTime as date), 'DD-Mon-YY')  = :endTime");
                }
                else
                {
                    //sb.append("where t.site.siteId = :siteId and t.locationProfile.profileId = :profileId and to_Char(cast(t.startTime as date), 'DD-Mon-YY')  >= :startTime and to_Char(cast(t.endTime as date), 'DD-Mon-YY') <= :endTime");
                    sb.append("where t.site.siteId = :siteId and t.locationProfile.profileId = :profileId and  t.startTime >=  function('TO_DATE', :startTime, 'DD-MON-YY') and  t.endTime   <=  function('TO_DATE', :endTime, 'DD-MON-YY')");
                }

                if (null != serviceTypeIds) {
                    sb.append(" and s.serviceId in (:serviceTypeIds) ");
                }
                if (null != roomTypeIds) {
                    sb.append(" and r.roomId in (:roomTypeIds)");
                }
                if (null != activityIds) {
                    sb.append(" and a.activityId in (:activityIds)");
                }
                if (rejectNullInstructor && allowNullInstructor && null != instructorIds) {
                    sb.append(" and (i.instructorId in (:instructorIds) or i.instructorId is null)");
                } else {
                    if (rejectNullInstructor && null != instructorIds) {
                        sb.append(" and i.instructorId in (:instructorIds)");
                    } else if (allowNullInstructor) {
                        sb.append(" and i.instructorId is null");
                    }
                }
                sb.append(" and (t.canceled = :canceled or t.canceled is null or t.canceled = 'H' )");
                sb.append(" order by t.appointmentId ");
                Query query = entityManager.createQuery(sb.toString());
                query.setParameter("siteId", siteId);
                query.setParameter("profileId", profileId);
                query.setParameter("startTime", startTime);
                query.setParameter("endTime", endTime);
                query.setParameter("canceled", Canceled.N);
                if (null != serviceTypeIds) {
                    query.setParameter("serviceTypeIds", Arrays.asList(serviceTypeIds));
                }
                if (null != roomTypeIds) {
                    query.setParameter("roomTypeIds", Arrays.asList(roomTypeIds));
                }
                if (null != activityIds) {
                    query.setParameter("activityIds", Arrays.asList(activityIds));
                }
                if (null != instructorIds && rejectNullInstructor) {
                    query.setParameter("instructorIds", Arrays.asList(instructorIds));
                }
                List<Appointment> list = query.getResultList();
                Set<Long> ids = new HashSet<Long>();
                List<Appointment> result = new ArrayList<Appointment>();
                for (Appointment appt : list) {
                    Long id = appt.getAppointmentId();
                    if (!ids.contains(id)) {
                        ids.add(id);
                        result.add(appt);
                    }
                }
                return result;
            }




















            private void initializeRequireInstructorOption(EntityManager entityManager) {
                if (activityIds != null && activityIds.length > 0) {
                    // Convert Long[] to List<Long> and then to a comma-separated string
                    String activityIdsString = Arrays.stream(activityIds)
                            .map(String::valueOf)
                            .collect(Collectors.joining(","));

                    // Construct dynamic query with the string of activityIds
                    String sql = "select t.requires_instructor from activity t where t.activity_id in (" + activityIdsString + ") group by t.requires_instructor";

                    // Inject EntityManager

                    // Create native query
                    Query query = entityManager.createNativeQuery(sql);

                    // Execute the query and retrieve results
                    @SuppressWarnings("unchecked")
                    List<Character> resultList = query.getResultList();

                    // Process the result list
                    for (Character requiresInstructor : resultList) {
                        if (RequiresInstructor.N.name().equals(requiresInstructor.toString())) {
                            allowNullInstructor = true;
                        } else if (RequiresInstructor.R.name().equals(requiresInstructor.toString())) {
                            rejectNullInstructor = true;
                        } else if (RequiresInstructor.O.name().equals(requiresInstructor.toString())) {
                            allowNullInstructor = true;
                            rejectNullInstructor = true;
                        }
                    }
                }
            }


            private boolean checkCondition() {
                boolean roomAndActivityIsNull = (null == roomTypeIds || null == activityIds);
                return (rejectNullInstructor && roomAndActivityIsNull)
                        || (!allowNullInstructor && rejectNullInstructor && (null == instructorIds || roomAndActivityIsNull));
            }
        };
    }

    public static Criterion<Appointment, Appointment> findByCustomerAndDateTime(final long pCustomerId,
                                                                                final Date pStartTime, final Date pEndTime, final long pProfileId) {

        return new AppointmentCriterion<Appointment>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Appointment> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select t.*, a.*, i.*, r.*, aps.* from appointment t");
                sb.append("  left join appointment_customers a_c on t.appointment_id = a_c.appointment_id");
                sb.append("  left join customer c on a_c.customer_id = c.customer_id");
                sb.append("  left join person p on c.person_id = p.person_id");
                sb.append("  left join activity a on t.activity_id = a.activity_id");
                sb.append("  left join service s on a.service_id = s.service_id");
                sb.append("  left join instructor i on t.instructor_id = i.instructor_id");
                sb.append("  left join room r on t.room_id = r.room_id");

                /**
                 * For GCSS-544, add join to fetch AppointmentSeries
                 */
                sb.append("  left join appointment_series aps on t.appointment_series_id = aps.appointment_series_id");

                sb.append("  left join profile_service p_s on s.service_id = p_s.service_id");
                sb.append("  left join profile_activity p_a on a.activity_id = p_a.activity_id");
                sb.append(" where c.customer_id = :customerId");
                sb.append("   and to_date(to_char(t.start_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') >= :startTime");
                sb.append("   and to_date(to_char(t.end_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') <= :endTime");
                sb.append("   and t.profile_id = :profileId");
                sb.append("   and (t.canceled = :canceled1 or t.canceled = :canceled or t.canceled is null)");
                sb.append("   and p_s.enabled = 'Y'");
                sb.append("   and p_a.enabled = 'Y'");
                sb.append("   and (i.enabled = 'Y' or i.enabled is null)");
                sb.append("   and r.enabled = 'Y'");

                // Use createNativeQuery instead of createSQLQuery
                Query query = entityManager.createNativeQuery(sb.toString(), "AppointmentMapping");

                // Set parameters using setParameter
                query.setParameter("customerId", pCustomerId);
                query.setParameter("startTime", pStartTime);
                query.setParameter("endTime", pEndTime);
                query.setParameter("profileId", pProfileId);
                query.setParameter("canceled", Canceled.N.name());
                query.setParameter("canceled1", Canceled.H.name());

                List<Object[]> list = query.getResultList();
                Map<Long, Appointment> maps = new HashMap<>();
                if (list != null) {
                    for (Object[] object : list) {
                        Appointment app = ((Appointment) object[0]);
                        Long id = app.getAppointmentId();
                        if (maps.get(id) == null) {
                            maps.put(id, app);
                            Hibernate.initialize(app.getCustomers());
                            for (Customer customer : app.getCustomers()) {
                                Hibernate.initialize(customer.getPerson());
                            }
                        }
                    }
                }
                return new ArrayList<>(maps.values());
            }
        };
    }



    public static Criterion<Appointment, Appointment> getByAppointmentId(final long pAppointmentId) {

        return new AppointmentCriterion<Appointment>() {

            @Override
            public Appointment get(EntityManager entityManager, int pFetchMode) {

                StringBuilder sb = new StringBuilder("select t from Appointment t ");
                sb.append(" left join fetch t.activity a");
                sb.append(" left join fetch t.appointmentSeries");
                sb.append(" left join fetch t.instructor i");
                sb.append(" left join fetch t.room");
                sb.append(" left outer join fetch t.customers c");
                sb.append(" left join fetch a.service");
                sb.append(" left join fetch c.person");
                sb.append(" left join fetch i.person");
                sb.append(" where t.appointmentId = :appointmentId");
                Query query = entityManager.createQuery(sb.toString());


                query.setParameter("appointmentId", pAppointmentId);

                return (Appointment) query.getSingleResult();
            }

        };
    }

    public static Criterion<Appointment, Appointment> findApptsForMailReminder() {

        return new AppointmentCriterion<Appointment>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Appointment> search(EntityManager entityManager, int pFetchMode) {
                // Get the current date
                LocalDate currentDate = LocalDate.now();

                // Convert LocalDate to java.sql.Date
                java.sql.Date sqlDate = java.sql.Date.valueOf(currentDate); // Convert LocalDate to java.sql.Date

                StringBuilder sqlBuilder = new StringBuilder();

                sqlBuilder.append("FROM Appointment appt ")
                        .append("LEFT JOIN FETCH appt.activity ac ")
                        .append("LEFT JOIN FETCH appt.locationProfile lp ")
                        .append("LEFT JOIN FETCH ac.service s ")
                        .append("LEFT JOIN FETCH appt.customers c ")
                        .append("LEFT JOIN FETCH c.person ")
                        .append("WHERE s.serviceId IN (1,20) ")
                        .append("AND to_date(to_char(appt.startTime,'YYYY-MM-DD'), 'YYYY-MM-DD') >= :startDate ") // Compare startTime directly
                        .append("AND to_date(to_char(appt.startTime,'YYYY-MM-DD'), 'YYYY-MM-DD') < :endDate ") // Ensure it falls within the same day
                        .append("AND (appt.canceled = 'N' OR appt.canceled IS NULL)");

                Query query = entityManager.createQuery(sqlBuilder.toString(), Appointment.class);
                // Set parameters for start and end of the day
                query.setParameter("startDate", sqlDate);
                query.setParameter("endDate", java.sql.Date.valueOf(currentDate.plusDays(1))); // Next day

                List<Appointment> result = new ArrayList<>();
                result.add((Appointment) query.getResultList().get(0));

                return result;
            }

        };
    }

    public static Criterion<Appointment, Appointment> getClosestScheduledAppByActivity(final long pActivityId) {

        return new AppointmentCriterion<Appointment>() {

            @Override
            public Appointment get(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder(" from Appointment a ");
                sb.append(" left join fetch a.instructor i ");
                sb.append(" left join fetch i.person ");
                sb.append(" where a.activity.activityId=:activityId ");
                sb.append(" order by a.appointmentId desc ");
                Query query = entityManager.createQuery(sb.toString());
                query.setMaxResults(1);
                query.setParameter("activityId", pActivityId);
                return (Appointment) query.getSingleResult();
            }

        };
    }

    public static Criterion<Appointment, Appointment> findByAppointmentSeriesAndStartTime(
            final long pAppointmentSeriesId, final Date pStartTime) {

        return new AppointmentCriterion<Appointment>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Appointment> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Appointment t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" where t.appointmentSeries.appointmentSeriesId = :appointmentSeriesId and t.startTime >= :startTime and (t.canceled='H' or t.canceled='N' or t.canceled is null) ");
                sb.append(" order by t.appointmentId ");
                Query query = entityManager.createQuery(sb.toString());
                query.setParameter("appointmentSeriesId", pAppointmentSeriesId);
                query.setParameter("startTime", pStartTime);
                return (List<Appointment>) query.getResultList();
            }

        };
    }

    public static Criterion<Appointment, Appointment> findByAppointmentSeriesAndDateTime(final long pAppointmentSeriesId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<Appointment>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Appointment> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Appointment t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" where t.appointmentSeries.appointmentSeriesId = :appointmentSeriesId and t.startTime >= :startTime ");
                if (pEndTime != null) {
                    sb.append(" and t.endTime <= :endTime ");
                }
                sb.append(" order by t.appointmentId ");
                Query query = entityManager.createQuery(sb.toString());
                query.setParameter("appointmentSeriesId", pAppointmentSeriesId);
                query.setParameter("startTime", pStartTime);
                if (pEndTime != null) {
                    query.setParameter("endTime", pEndTime);
                }
                return query.getResultList();
            }

        };
    }

    public static Criterion<Appointment, Appointment> findByActiveAppointmentSeriesAndDateTime(final long pAppointmentSeriesId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<Appointment>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Appointment> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Appointment t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" where t.appointmentSeries.appointmentSeriesId = :appointmentSeriesId and t.canceled != 'Y' and t.startTime >= :startTime ");
                if (pEndTime != null) {
                    sb.append(" and t.endTime <= :endTime ");
                }
                sb.append(" order by t.appointmentId ");
                Query query = entityManager.createQuery(sb.toString());
                query.setParameter("appointmentSeriesId", pAppointmentSeriesId);
                query.setParameter("startTime", pStartTime);
                if (pEndTime != null) {
                    query.setParameter("endTime", pEndTime);
                }
                return query.getResultList();
            }

        };
    }


    public static Criterion<Appointment, Boolean> hasByProfileIdAndActivityId(final Long pProfileId,
                                                                              final Long pActivityId) {

        return new AppointmentCriterion<Boolean>() {

            @Override
            public Boolean get(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select count(t) from Appointment t ");
                sb.append(" where t.activity.activityId = :activityId ");
                sb.append(" and t.locationProfile.profileId = :profileId ");
                sb.append(" and (t.canceled = :canceled or t.canceled = :canceled1 )");
                sb.append(" and (t.startTime >= systimestamp or t.endTime >= systimestamp) ");
                Query query = entityManager.createQuery(sb.toString());
                //    query.setMaxResults(1);
                query.setParameter("activityId", pActivityId);
                query.setParameter("profileId", pProfileId);
                query.setParameter("canceled", Canceled.N);
                query.setParameter("canceled1", Canceled.H);
                Long count = (Long) query.getSingleResult();
                return count > 0 ? Boolean.TRUE : Boolean.FALSE;
            }

        };

    }

    public static Criterion<Appointment, Boolean> hasBySiteIdAndServiceId(final Long pSiteId, final Long pServiceId) {

        return new AppointmentCriterion<Boolean>() {

            @Override
            public Boolean get(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select count(t) from Appointment t ");
                sb.append(" where t.activity.service.serviceId = :serviceId ");
                sb.append(" and t.site.siteId = :siteId ");
                sb.append(" and (t.canceled = :canceled or t.canceled = :canceled1) ");
                sb.append(" and (t.startTime >= current_timestamp or t.endTime >= current_timestamp) ");

                // Create query using EntityManager
                TypedQuery<Long> query = entityManager.createQuery(sb.toString(), Long.class);

                // Set parameters
                query.setParameter("serviceId", pServiceId);
                query.setParameter("siteId", pSiteId);
                query.setParameter("canceled", Canceled.N);
                query.setParameter("canceled1", Canceled.H);

                // Get the result
                Long count = query.getSingleResult();
                return count > 0;
            }
        };
    }


    public static Criterion<Appointment, Boolean> hasBySiteAndActivityId(final long pSiteId, final Long pActivityId) {

        return new AppointmentCriterion<Boolean>() {

            @Override
            public Boolean get(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select count(t) from Appointment t ");
                sb.append(" where t.activity.activityId = :activityId ");
                sb.append(" and t.site.siteId = :siteId");
                sb.append(" and (t.canceled = :canceled or t.canceled = :canceled1)");
                sb.append(" and (t.startTime >= systimestamp or t.endTime >= systimestamp) ");
                Query query = entityManager.createQuery(sb.toString());
                query.setMaxResults(1);
                query.setParameter("siteId", pSiteId);
                query.setParameter("activityId", pActivityId);
                query.setParameter("canceled", Canceled.N);
                query.setParameter("canceled1", Canceled.H);
                Long count = (Long) query.getSingleResult();
                return count > 0 ? Boolean.TRUE : Boolean.FALSE;
            }

        };

    }

    public static Criterion<Appointment, Boolean> hasByInstructorId(final Long pInstructorId) {

        return new AppointmentCriterion<Boolean>() {

            @Override
            public Boolean get(EntityManager entityManager, int pFetchMode) {
                //changed to Native query 5055  testing after appointment
                StringBuilder sb = new StringBuilder("select count(*) t1 from Appointment t ");
                sb.append(" where t.instructor_id = :instructorId ");
                sb.append(" and (t.canceled = :canceled or t.canceled is null or t.canceled = :canceled1)");
                sb.append(" and t.start_Time > sysdate ");
              /*  Query query = entityManager.createQuery(sb.toString());
                query.setMaxResults(1);*/

                Query query =  entityManager.createNativeQuery(sb.toString(), Long.class);
                query.setMaxResults(1);

                query.setParameter("instructorId", pInstructorId);
                query.setParameter("canceled", Canceled.N.name());
                query.setParameter("canceled1", Canceled.H.name());
                Long count = (Long) query.getSingleResult();
                return count > 0 ? Boolean.TRUE : Boolean.FALSE;
            }

        };

    }


    public static Criterion<Appointment, InstructorDateListDTO> findInstructorScheduleByLocationIdAndDateTime(
            final long pLocationId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<InstructorDateListDTO>() {

            @Override
            public List<InstructorDateListDTO> search(EntityManager entityManager, int pFetchMode) {
                String sb = "select to_char(t.start_time, 'dd-MON-yyyy') as date1 ,t.appointment_id as appointment_id, " +
                        " t.start_time as start_time, t.end_time as end_time, t.canceled as canceled," +
                        " (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration," +
                        " r.profile_room_name as profile_room_name, a.activity_name as activity_name," +
                        " i.instructor_id as instructor_id, p_i.first_name as i_first_name, p_i.last_name as i_last_name," +
                        " c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name, " +
                        " ay.monday_start_time as mon_s,ay.monday_end_time as mon_e," +
                        " ay.tuesday_start_time as tue_s,ay.tuesday_end_time as tue_e,ay.wednesday_start_time as wed_s,ay.wednesday_end_time as wed_e," +
                        " ay.thursday_start_time as thu_s,ay.thursday_end_time as thu_e, ay.friday_start_time as fri_s, ay.friday_end_time as fri_e," +
                        " ay.saturday_start_time as sat_s,ay.saturday_end_time as sat_e,ay.sunday_start_time as sun_s,ay.sunday_end_time as sun_e," +
                        " i.external_id as external_id" +
                        " from appointment t" +
                        " left join location l on t.profile_id = l.profile_id" +
                        " left join appointment_customers a_c on t.appointment_id = a_c.appointment_id" +
                        " left join customer c on a_c.customer_id = c.customer_id" +
                        " left join customer_status c_s on c.customer_status_id = c_s.customer_status_id" +
                        " left join person p_c on c.person_id = p_c.person_id" +
                        " left join instructor i on t.instructor_id = i.instructor_id" +
                        " left join person p_i on i.person_id = p_i.person_id" +
                        " left join room r on t.room_id = r.room_id" +
                        " left join activity a on t.activity_id = a.activity_id" +
                        " left join availability ay on i.availability_id = ay.availability_id" +
                        " where l.location_id = :locationId " +
                        "   and t.instructor_id is not null" +
                        "   and t.start_time >= :startTime " +
                        "   and t.end_time <= :endTime " +
                        "   and decode(c_s.external_id, null, '_', c_s.external_id) != 'C' " +
                        "   and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        " order by to_char(t.start_time, 'dd-MON-yyyy'), p_i.first_name, p_i.last_name, t.start_time, p_c.first_name, p_c.last_name";

                // Use createNativeQuery instead of createSQLQuery
                Query query = entityManager.createNativeQuery(sb);

                // Set parameters using setParameter
                query.setParameter("locationId", pLocationId);
                query.setParameter("startTime", pStartTime);
                query.setParameter("endTime", pEndTime);

                // Define result mappings
                query.unwrap(NativeQuery.class)
                        .addScalar("date1", StandardBasicTypes.STRING)
                        .addScalar("appointment_id", StandardBasicTypes.LONG)
                        .addScalar("start_time", StandardBasicTypes.TIMESTAMP)
                        .addScalar("end_time", StandardBasicTypes.TIMESTAMP)
                        .addScalar("canceled", StandardBasicTypes.STRING)
                        .addScalar("duration", StandardBasicTypes.LONG)
                        .addScalar("profile_room_name", StandardBasicTypes.STRING)
                        .addScalar("activity_name", StandardBasicTypes.STRING)
                        .addScalar("instructor_id", StandardBasicTypes.LONG)
                        .addScalar("i_first_name", StandardBasicTypes.STRING)
                        .addScalar("i_last_name", StandardBasicTypes.STRING)
                        .addScalar("customer_id", StandardBasicTypes.LONG)
                        .addScalar("c_first_name", StandardBasicTypes.STRING)
                        .addScalar("c_last_name", StandardBasicTypes.STRING)
                        .addScalar("mon_s", StandardBasicTypes.TIMESTAMP)
                        .addScalar("mon_e", StandardBasicTypes.TIMESTAMP)
                        .addScalar("tue_s", StandardBasicTypes.TIMESTAMP)
                        .addScalar("tue_e", StandardBasicTypes.TIMESTAMP)
                        .addScalar("wed_s", StandardBasicTypes.TIMESTAMP)
                        .addScalar("wed_e", StandardBasicTypes.TIMESTAMP)
                        .addScalar("thu_s", StandardBasicTypes.TIMESTAMP)
                        .addScalar("thu_e", StandardBasicTypes.TIMESTAMP)
                        .addScalar("fri_s", StandardBasicTypes.TIMESTAMP)
                        .addScalar("fri_e", StandardBasicTypes.TIMESTAMP)
                        .addScalar("sat_s", StandardBasicTypes.TIMESTAMP)
                        .addScalar("sat_e", StandardBasicTypes.TIMESTAMP)
                        .addScalar("sun_s", StandardBasicTypes.TIMESTAMP)
                        .addScalar("sun_e", StandardBasicTypes.TIMESTAMP)
                        .addScalar("external_id", StandardBasicTypes.STRING);

                List<Object[]> list = query.getResultList();
                List<InstructorDateListDTO> result = new ArrayList<>();
                Map<InstructorDateDTO, List<InstructorScheduleDTO>> cache = new HashMap<>();
                Map<Long, InstructorScheduleDTO> instructorScheduleDTOs = new HashMap<>();
                Map<Long, Instructor> instructors = new HashMap<>();
                Map<Long, Customer> customers = new HashMap<>();

                for (Object[] objects : list) {
                    String date1 = (String) objects[0];
                    Long appointment_id = (Long) objects[1];
                    Long instructorId = (Long) objects[8];
                    String externalId = (String) objects[28];
                    InstructorDateDTO search = new InstructorDateDTO(date1, instructorId);
                    Instructor instructor = instructors.get(instructorId);
                    if (instructor == null) {
                        instructor = new Instructor();
                        instructor.setExternalId(externalId);
                        instructor.setInstructorId(instructorId);
                        Person person = new Person();
                        person.setFirstName((String) objects[9]);
                        person.setLastName((String) objects[10]);
                        instructor.setPerson(person);
                        instructors.put(instructorId, instructor);
                    }
                    if (!cache.containsKey(search)) {
                        List<InstructorScheduleDTO> listDTO = new ArrayList<>();
                        cache.put(search, listDTO);
                        int dayOfWeek = new DateTime(objects[2]).getDayOfWeek();
                        Date startHours;
                        Date endHours;

                        switch (dayOfWeek) {
                            case 1:
                                startHours = (Date) objects[14];
                                endHours = (Date) objects[15];
                                break;
                            case 2:
                                startHours = (Date) objects[16];
                                endHours = (Date) objects[17];
                                break;
                            case 3:
                                startHours = (Date) objects[18];
                                endHours = (Date) objects[19];
                                break;
                            case 4:
                                startHours = (Date) objects[20];
                                endHours = (Date) objects[21];
                                break;
                            case 5:
                                startHours = (Date) objects[22];
                                endHours = (Date) objects[23];
                                break;
                            case 6:
                                startHours = (Date) objects[24];
                                endHours = (Date) objects[25];
                                break;
                            case 7:
                                startHours = (Date) objects[26];
                                endHours = (Date) objects[27];
                                break;
                            default:
                                startHours = null;
                                endHours = null;
                        }

                        result.add(new InstructorDateListDTO(date1, instructor, listDTO, startHours, endHours, externalId));
                    }
                    List<InstructorScheduleDTO> instructorScheduleDTOList = cache.get(search);
                    InstructorScheduleDTO instructorScheduleDTO = instructorScheduleDTOs.get(appointment_id);
                    if (instructorScheduleDTO == null) {
                        instructorScheduleDTO = new InstructorScheduleDTO(appointment_id, (Date) objects[2], (Date) objects[3], (String) objects[4], (Long) objects[5], (String) objects[6], (String) objects[7], externalId);
                        instructorScheduleDTOList.add(instructorScheduleDTO);
                        instructorScheduleDTOs.put(appointment_id, instructorScheduleDTO);
                    }
                    if (instructorScheduleDTO.getInstructor() == null) {
                        instructorScheduleDTO.setInstructor(instructor);
                    }
                    Long customerId = (Long) objects[11];
                    if (customerId == null) continue;
                    Customer customer = customers.get(customerId);
                    if (customer == null) {
                        customer = new Customer();
                        customer.setCustomerId(customerId);
                        Person person = new Person();
                        person.setFirstName((String) objects[12]);
                        person.setLastName((String) objects[13]);
                        customer.setPerson(person);
                        customers.put(customerId, customer);
                    }
                    instructorScheduleDTO.addCustomer(customer);
                }

                /**
                 * For GCSS-592, Show instructor schedule in instructor schedule report
                 */
                for (InstructorDateListDTO instructorDateListDTO : result) {
                    List<InstructorScheduleDTO> listDTO = instructorDateListDTO.getList();
                    Date startTime = listDTO.get(0).getStartTime();
                    Date endTime = listDTO.get(listDTO.size() - 1).getEndTime();
                    instructorDateListDTO.setStartTimeAndEndTime(startTime, endTime);
                }
                return result;
            }
        };
    }



    public static Criterion<Appointment, ExportAppointmentDTO> searchAppointmentByLocationIdAndDateTime(
            final long pLocationId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<ExportAppointmentDTO>() {

            @Override
            public List<ExportAppointmentDTO> search(EntityManager entityManager, int pFetchMode) {
                String sql = " select to_char(t.start_time, 'MM/DD/YYYY') as start_date, " +
                        " TO_CHAR(t.start_time, 'd') start_day, " +
                        " t.canceled as canceled, " +
                        " a.activity_name as activity_name, " +
                        " p_i.first_name || ' ' || p_i.last_name as instructor_name, " +
                        " c.external_id customer_gc_id, c_s.external_id stat_code, " +
                        " p_c.first_name as customer_first_name, p_c.last_name as customer_last_name, " +
                        " nvl(a_s.is_recurring, 'N') as recurring " +
                        " from appointment t " +
                        " left join location l on t.profile_id = l.profile_id " +
                        " left join appointment_customers a_c on t.appointment_id = a_c.appointment_id " +
                        " left join customer c on a_c.customer_id = c.customer_id " +
                        " left join customer_status c_s on c.customer_status_id = c_s.customer_status_id " +
                        " left join person p_c on c.person_id = p_c.person_id " +
                        " left join instructor i on t.instructor_id = i.instructor_id " +
                        " left join person p_i on i.person_id = p_i.person_id " +
                        " left join activity a on t.activity_id = a.activity_id " +
                        " left join service s on a.service_id = s.service_id " +
                        " inner join appointment_series a_s on t.appointment_series_id = a_s.appointment_series_id " +
                        " where l.location_id = :locationId " +
                        " and upper(s.service_name) = upper('Lesson') " +
                        " and t.instructor_id is not null " +
                        " and t.start_time >= :startTime " +
                        " and t.end_time <= :endTime " +
                        " and decode(c_s.external_id, null, '_', c_s.external_id) != 'C' " +
                        " order by to_char(t.start_time, 'YYYY-MM-DD')";

                // Create native query using EntityManager
                Query query = entityManager.createNativeQuery(sql)
                        .setParameter("locationId", pLocationId)
                        .setParameter("startTime", pStartTime)
                        .setParameter("endTime", pEndTime);

                // Setting result mapping
                query.unwrap(NativeQuery.class)
                        .addScalar("start_date", StandardBasicTypes.STRING)
                        .addScalar("start_day", StandardBasicTypes.STRING)
                        .addScalar("canceled", StandardBasicTypes.STRING)
                        .addScalar("activity_name", StandardBasicTypes.STRING)
                        .addScalar("instructor_name", StandardBasicTypes.STRING)
                        .addScalar("customer_gc_id", StandardBasicTypes.STRING)
                        .addScalar("stat_code", StandardBasicTypes.STRING)
                        .addScalar("customer_first_name", StandardBasicTypes.STRING)
                        .addScalar("customer_last_name", StandardBasicTypes.STRING)
                        .addScalar("recurring", StandardBasicTypes.STRING);

                // Using pagination instead of ScrollableResults
                int batchSize = 50; // Adjust batch size as needed
                int startPosition = 0;
                List<ExportAppointmentDTO> result = new ArrayList<>();
                List<Object[]> batchResult;

                do {
                    query.setFirstResult(startPosition);
                    query.setMaxResults(batchSize);
                    batchResult = query.getResultList();

                    for (Object[] row : batchResult) {
                        String startDate = (String) row[0];
                        String startDay = convertDayOfWeek((String) row[1]);
                        String appointmentCancelled = (String) row[2];
                        String activityName = (String) row[3];
                        String instructorName = (String) row[4];
                        String customerGCID = (String) row[5];
                        String statCode = (String) row[6];
                        String customerFirstName = (String) row[7];
                        String customerLastName = (String) row[8];
                        String recurring = (String) row[9];

                        ExportAppointmentDTO dto = new ExportAppointmentDTO(
                                customerFirstName, customerLastName, customerGCID, statCode,
                                startDate, startDay, activityName, instructorName,
                                appointmentCancelled, recurring
                        );
                        result.add(dto);
                    }

                    startPosition += batchSize; // Move to the next batch

                } while (!batchResult.isEmpty());

                return result;
            }

            // Helper method to convert numeric day to string
            private String convertDayOfWeek(String day) {
                switch (day) {
                    case "1":
                        return "Sun";
                    case "2":
                        return "Mon";
                    case "3":
                        return "Tue";
                    case "4":
                        return "Wed";
                    case "5":
                        return "Thu";
                    case "6":
                        return "Fri";
                    case "7":
                        return "Sat";
                    default:
                        return "";
                }
            }
        };
    }




    public static Criterion<Appointment, RehearsalScheduleDateListDTO> findRehearsalScheduleByProfileIdAndDateTime(
            final long pProfileId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<RehearsalScheduleDateListDTO>() {

            @Override
            public List<RehearsalScheduleDateListDTO> search(EntityManager entityManager, int pFetchMode) {
                String sb = "select to_char(t.start_time, 'MM/DD/YYYY') as date1 ,t.appointment_id as appointment_id, " +
                        " t.start_time as start_time, t.end_time as end_time, t.canceled as canceled," +
                        " (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration," +
                        " r.profile_room_name as profile_room_name, a.activity_name as activity_name," +
                        " c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name," +
                        " t.band_name as band_name " +
                        " from appointment t" +
                        " left join appointment_customers a_c on t.appointment_id = a_c.appointment_id" +
                        " left join customer c on a_c.customer_id = c.customer_id" +
                        " left join customer_status c_s on c.customer_status_id = c_s.customer_status_id" +
                        " left join person p_c on c.person_id = p_c.person_id" +
                        " left join room r on t.room_id = r.room_id" +
                        " left join activity a on t.activity_id = a.activity_id" +
                        " left join service s on a.service_id = s.service_id" +
                        " where t.profile_id = :profileId " +
                        "   and lower(s.service_name) = lower('rehearsal')" +
                        "   and t.start_time >= :startTime " +
                        "   and t.end_time <= :endTime " +
                        "   and decode(c_s.external_id, null, '_', c_s.external_id) != 'C' " +
                        "   and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        " order by to_char(t.start_time, 'YYYY-MM-DD'), t.start_time, p_c.first_name, p_c.last_name";

                // Using EntityManager to create the query
                Query query = entityManager.createNativeQuery(sb)
                        .setParameter("profileId", pProfileId)
                        .setParameter("startTime", pStartTime)
                        .setParameter("endTime", pEndTime);

                // Adding scalar mappings using unwrap to NativeQuery
                query.unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("date1", StandardBasicTypes.STRING)
                        .addScalar("appointment_id", StandardBasicTypes.LONG)
                        .addScalar("start_time", StandardBasicTypes.TIMESTAMP)
                        .addScalar("end_time", StandardBasicTypes.TIMESTAMP)
                        .addScalar("canceled", StandardBasicTypes.STRING)
                        .addScalar("duration", StandardBasicTypes.LONG)
                        .addScalar("profile_room_name", StandardBasicTypes.STRING)
                        .addScalar("activity_name", StandardBasicTypes.STRING)
                        .addScalar("customer_id", StandardBasicTypes.LONG)
                        .addScalar("c_first_name", StandardBasicTypes.STRING)
                        .addScalar("c_last_name", StandardBasicTypes.STRING)
                        .addScalar("band_name", StandardBasicTypes.STRING);

                List<Object[]> results = query.getResultList();
                List<RehearsalScheduleDateListDTO> result = new ArrayList<>();
                Map<String, List<RehearsalScheduleDTO>> cache = new HashMap<>();
                Map<Long, RehearsalScheduleDTO> rehearsalScheduleDTOs = new HashMap<>();
                Map<Long, Customer> customers = new HashMap<>();

                for (Object[] row : results) {
                    String date1 = (String) row[0];
                    Long appointment_id = (Long) row[1];

                    if (!cache.containsKey(date1)) {
                        List<RehearsalScheduleDTO> list = new ArrayList<>();
                        cache.put(date1, list);
                        result.add(new RehearsalScheduleDateListDTO(date1, list));
                    }

                    List<RehearsalScheduleDTO> rehearsalScheduleDTOList = cache.get(date1);
                    RehearsalScheduleDTO rehearsalScheduleDTO = rehearsalScheduleDTOs.get(appointment_id);

                    if (rehearsalScheduleDTO == null) {
                        rehearsalScheduleDTO = new RehearsalScheduleDTO(
                                appointment_id,
                                (Date) row[2],
                                (Date) row[3],
                                (String) row[4],
                                (Long) row[5],
                                (String) row[6],
                                (String) row[7]
                        );
                        rehearsalScheduleDTO.setBandName((String) row[11]);
                        rehearsalScheduleDTOList.add(rehearsalScheduleDTO);
                        rehearsalScheduleDTOs.put(appointment_id, rehearsalScheduleDTO);
                    }

                    Long customerId = (Long) row[8];
                    if (customerId != null) {
                        Customer customer = customers.computeIfAbsent(customerId, id -> {
                            Customer c = new Customer();
                            c.setCustomerId(id);
                            Person person = new Person();
                            person.setFirstName((String) row[9]);
                            person.setLastName((String) row[10]);
                            c.setPerson(person);
                            return c;
                        });
                        rehearsalScheduleDTO.addCustomer(customer);
                    }
                }

                return result;
            }
        };
    }


    public static Criterion<Appointment, RehearsalBookingDateListDTO> findRehearsalBookingByProfileIdAndCreateTime(
            final long pProfileId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<RehearsalBookingDateListDTO>() {

            @Override
            public List<RehearsalBookingDateListDTO> search(EntityManager entityManager, int pFetchMode) {
                String sb = "select to_char(t.start_time, 'MM/DD/YYYY') as date1 ,t.appointment_id as appointment_id, " +
                        " t.start_time as start_time, t.end_time as end_time, t.canceled as canceled," +
                        " (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration," +
                        " r.profile_room_name as profile_room_name, a.activity_name as activity_name," +
                        " c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name," +
                        " t.band_name as band_name, t.appointment_series_id as series_id " +
                        " from appointment t" +
                        " left join appointment_customers a_c on t.appointment_id = a_c.appointment_id" +
                        " left join customer c on a_c.customer_id = c.customer_id" +
                        " left join customer_status c_s on c.customer_status_id = c_s.customer_status_id" +
                        " left join person p_c on c.person_id = p_c.person_id" +
                        " left join room r on t.room_id = r.room_id" +
                        " left join activity a on t.activity_id = a.activity_id" +
                        " left join service s on a.service_id = s.service_id" +
                        " where t.profile_id = :profileId " +
                        "   and lower(s.service_name) = lower('rehearsal')" +
                        "   and t.create_time >= :startTime " +
                        "   and t.create_time <= :endTime " +
                        "   and decode(c_s.external_id, null, '_', c_s.external_id) != 'C' " +
                        "   and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        " order by to_char(t.start_time, 'YYYY-MM-DD'), t.start_time, p_c.first_name, p_c.last_name";

                // Using EntityManager to create the query and unwrap NativeQuery
                Query query = entityManager.createNativeQuery(sb)
                        .setParameter("profileId", pProfileId)
                        .setParameter("startTime", pStartTime)
                        .setParameter("endTime", pEndTime);

                query.unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("date1", StandardBasicTypes.STRING)
                        .addScalar("appointment_id", StandardBasicTypes.LONG)
                        .addScalar("start_time", StandardBasicTypes.TIMESTAMP)
                        .addScalar("end_time", StandardBasicTypes.TIMESTAMP)
                        .addScalar("canceled", StandardBasicTypes.STRING)
                        .addScalar("duration", StandardBasicTypes.LONG)
                        .addScalar("profile_room_name", StandardBasicTypes.STRING)
                        .addScalar("activity_name", StandardBasicTypes.STRING)
                        .addScalar("customer_id", StandardBasicTypes.LONG)
                        .addScalar("c_first_name", StandardBasicTypes.STRING)
                        .addScalar("c_last_name", StandardBasicTypes.STRING)
                        .addScalar("band_name", StandardBasicTypes.STRING)
                        .addScalar("series_id", StandardBasicTypes.LONG);

                List<Object[]> results = query.getResultList();
                List<RehearsalBookingDateListDTO> result = new ArrayList<>();
                Map<String, List<RehearsalBookingDTO>> cache = new HashMap<>();
                Set<Long> seriesCache = new HashSet<>();
                Map<Long, RehearsalBookingDTO> rehearsalBookingDTOs = new HashMap<>();
                Map<Long, Customer> customers = new HashMap<>();

                for (Object[] row : results) {
                    Long series_id = (Long) row[12];
                    if (seriesCache.contains(series_id)) {
                        continue;
                    }
                    seriesCache.add(series_id);

                    String date1 = (String) row[0];
                    Long appointment_id = (Long) row[1];

                    if (!cache.containsKey(date1)) {
                        List<RehearsalBookingDTO> list = new ArrayList<>();
                        cache.put(date1, list);
                        result.add(new RehearsalBookingDateListDTO(date1, list));
                    }

                    List<RehearsalBookingDTO> rehearsalBookingDTOList = cache.get(date1);
                    RehearsalBookingDTO rehearsalBookingDTO = rehearsalBookingDTOs.get(appointment_id);

                    if (rehearsalBookingDTO == null) {
                        rehearsalBookingDTO = new RehearsalBookingDTO(
                                appointment_id,
                                (Date) row[2],
                                (Date) row[3],
                                (String) row[4],
                                (Long) row[5],
                                (String) row[6],
                                (String) row[7]
                        );
                        rehearsalBookingDTO.setBandName((String) row[11]);
                        rehearsalBookingDTOList.add(rehearsalBookingDTO);
                        rehearsalBookingDTOs.put(appointment_id, rehearsalBookingDTO);
                    }

                    Long customerId = (Long) row[8];
                    if (customerId != null) {
                        Customer customer = customers.computeIfAbsent(customerId, id -> {
                            Customer c = new Customer();
                            c.setCustomerId(id);
                            Person person = new Person();
                            person.setFirstName((String) row[9]);
                            person.setLastName((String) row[10]);
                            c.setPerson(person);
                            return c;
                        });
                        rehearsalBookingDTO.addCustomer(customer);
                    }
                }

                return result;
            }
        };
    }



    public static Criterion<Appointment, MasterDailyDateListDTO> findDailyMasterByProfileIdAndDateTime(
            final long pProfileId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<MasterDailyDateListDTO>() {

            @Override
            public List<MasterDailyDateListDTO> search(EntityManager entityManager, int pFetchMode) {
                String sb = "select to_char(t.start_time, 'MM/DD/YYYY') as date1 ,t.appointment_id as appointment_id, " +
                        " t.start_time as start_time, t.end_time as end_time, t.canceled as canceled," +
                        " (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration," +
                        " r.profile_room_name as profile_room_name, a.activity_name as activity_name," +
                        " i.instructor_id as instructor_id, p_i.first_name as i_first_name, p_i.last_name as i_last_name," +
                        " c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name, t.note as notes" +
                        " from appointment t" +
                        " left join appointment_customers a_c on t.appointment_id = a_c.appointment_id" +
                        " left join customer c on a_c.customer_id = c.customer_id" +
                        " left join customer_status c_s on c.customer_status_id = c_s.customer_status_id" +
                        " left join person p_c on c.person_id = p_c.person_id" +
                        " left join instructor i on t.instructor_id = i.instructor_id" +
                        " left join person p_i on i.person_id = p_i.person_id" +
                        " left join room r on t.room_id = r.room_id" +
                        " left join activity a on t.activity_id = a.activity_id" +
                        " where t.profile_id = :profileId " +
                        "   and t.start_time >= :startTime " +
                        "   and t.end_time <= :endTime " +
                        "   and decode(c_s.external_id, null, '_', c_s.external_id) != 'C' " +
                        "   and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        " order by to_char(t.start_time, 'YYYY-MM-DD'), t.start_time, p_i.first_name, p_i.last_name, p_c.first_name, p_c.last_name";

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb, Object[].class);
                query.setParameter("profileId", pProfileId);
                query.setParameter("startTime", pStartTime);
                query.setParameter("endTime", pEndTime);

                query.addScalar("date1", StandardBasicTypes.STRING)
                        .addScalar("appointment_id", StandardBasicTypes.LONG)
                        .addScalar("start_time", StandardBasicTypes.TIMESTAMP)
                        .addScalar("end_time", StandardBasicTypes.TIMESTAMP)
                        .addScalar("canceled", StandardBasicTypes.STRING)
                        .addScalar("duration", StandardBasicTypes.LONG)
                        .addScalar("profile_room_name", StandardBasicTypes.STRING)
                        .addScalar("activity_name", StandardBasicTypes.STRING)
                        .addScalar("instructor_id", StandardBasicTypes.LONG)
                        .addScalar("i_first_name", StandardBasicTypes.STRING)
                        .addScalar("i_last_name", StandardBasicTypes.STRING)
                        .addScalar("customer_id", StandardBasicTypes.LONG)
                        .addScalar("c_first_name", StandardBasicTypes.STRING)
                        .addScalar("c_last_name", StandardBasicTypes.STRING)
                        .addScalar("notes", StandardBasicTypes.STRING);

                List<Object[]> resultSet = query.getResultList();

                List<MasterDailyDateListDTO> result = new ArrayList<>();
                Map<String, List<MasterDailyDTO>> cache = new HashMap<>();
                Map<Long, MasterDailyDTO> masterDailyDTOs = new HashMap<>();
                Map<Long, Instructor> instructors = new HashMap<>();
                Map<Long, Customer> customers = new HashMap<>();

                for (Object[] objects : resultSet) {
                    String date1 = (String) objects[0];
                    Long appointmentId = (Long) objects[1];
                    Long instructorId = (Long) objects[8];

                    Instructor instructor = instructors.computeIfAbsent(instructorId, id -> {
                        Instructor inst = new Instructor();
                        inst.setInstructorId(id);
                        Person person = new Person();
                        person.setFirstName((String) objects[9]);
                        person.setLastName((String) objects[10]);
                        inst.setPerson(person);
                        return inst;
                    });

                    cache.computeIfAbsent(date1, k -> {
                        List<MasterDailyDTO> list = new ArrayList<>();
                        result.add(new MasterDailyDateListDTO(date1, list));
                        return list;
                    });

                    List<MasterDailyDTO> masterDailyDTOList = cache.get(date1);
                    MasterDailyDTO masterDailyDTO = masterDailyDTOs.computeIfAbsent(appointmentId, id -> {
                        MasterDailyDTO dto = new MasterDailyDTO(
                                id, (Date) objects[2], (Date) objects[3], (String) objects[4], (Long) objects[5],
                                (String) objects[6], (String) objects[7]);
                        masterDailyDTOList.add(dto);
                        return dto;
                    });

                    if (masterDailyDTO.getInstructor() == null && instructor != null) {
                        masterDailyDTO.setInstructor(instructor);
                    }

                    masterDailyDTO.setNotes((String) objects[14]);

                    Long customerId = (Long) objects[11];
                    if (customerId != null) {
                        Customer customer = customers.computeIfAbsent(customerId, id -> {
                            Customer cust = new Customer();
                            cust.setCustomerId(id);
                            Person person = new Person();
                            person.setFirstName((String) objects[12]);
                            person.setLastName((String) objects[13]);
                            cust.setPerson(person);
                            return cust;
                        });
                        masterDailyDTO.addCustomer(customer);
                    }
                }

                return result;
            }
        };
    }


    public static Criterion<Appointment, Map<String, List<InstructorAppointmentBusinessHours>>> findInstructorAppointmentBusinessHoursByProfileIdAndDateTime(
            final long pProfileId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<Map<String, List<InstructorAppointmentBusinessHours>>>() {

            @Override
            public Map<String, List<InstructorAppointmentBusinessHours>> get(EntityManager entityManager, int pFetchMode) {
                String sb = "select to_char(t2.date1, 'MM/DD/YYYY') as date2, t2.date1, t2.instructor_id, t2.minimum_time, t2.maximum_time, p.first_name, p.last_name" +
                        "  from (select t1.date1 ," +
                        "               t1.INSTRUCTOR_ID," +
                        "               min(t1.START_TIME) as minimum_time," +
                        "               max(t1.END_TIME) as maximum_time" +
                        "          from (select to_date(to_char(t.start_time, 'YYYY-MM-DD'),'YYYY-MM-DD') date1," +
                        "		                  t.INSTRUCTOR_ID," +
                        "				          t.START_TIME," +
                        "                       t.END_TIME" +
                        "                 from APPOINTMENT t" +
                        "                 left join appointment_customers a_c on t.appointment_id = a_c.appointment_id" +
                        "                 left join customer c on a_c.customer_id = c.customer_id" +
                        "                 left join customer_status c_s on c.customer_status_id = c_s.customer_status_id" +
                        "                 where t.instructor_id is not null" +
                        "                   and t.profile_id = :profileId" +
                        "                   and t.start_time >= :startTime" +
                        "                   and t.end_time <= :endTime" +
                        "                   and decode(c_s.external_id, null, '_', c_s.external_id) != 'C'" +
                        "                   and decode(t.canceled, null, 'N', t.canceled) != 'Y') t1" +
                        "         group by t1.date1, t1.INSTRUCTOR_ID" +
                        "         order by t1.date1, t1.INSTRUCTOR_ID) t2" +
                        "  left join INSTRUCTOR i" +
                        "    on t2.instructor_id = i.instructor_id" +
                        "  left join person p" +
                        "    on i.person_id = p.person_id" +
                        "  order by t2.date1, p.first_name, p.last_name";

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb);
                query.setParameter("profileId", pProfileId);
                query.setParameter("startTime", pStartTime);
                query.setParameter("endTime", pEndTime);

                query.addScalar("date2", StandardBasicTypes.STRING);
                query.addScalar("date1", StandardBasicTypes.DATE);
                query.addScalar("instructor_id", StandardBasicTypes.LONG);
                query.addScalar("minimum_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("maximum_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("first_name", StandardBasicTypes.STRING);
                query.addScalar("last_name", StandardBasicTypes.STRING);

                ScrollableResults<Object[]> scroll = query.scroll(ScrollMode.FORWARD_ONLY);
                Map<String, List<InstructorAppointmentBusinessHours>> result = new HashMap<>();

                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    String date1 = (String) objects[0];
                    if (!result.containsKey(date1)) {
                        result.put(date1, new ArrayList<>());
                    }
                    List<InstructorAppointmentBusinessHours> list = result.get(date1);
                    InstructorAppointmentBusinessHours obj = new InstructorAppointmentBusinessHours();
                    obj.setCurrentDate((Date) objects[1]);
                    obj.setInstructorId((Long) objects[2]);
                    obj.setMinimumTime((Date) objects[3]);
                    obj.setMaximumTime((Date) objects[4]);
                    obj.setFirstName((String) objects[5]);
                    obj.setLastName((String) objects[6]);
                    list.add(obj);
                }
                scroll.close();
                return result;
            }
        };
    }


    /**
     * Return all appointments that are associated with the supplied series id.
     *
     * @param appointmentSeriesId long integer identifying the series to load
     * @return a Criterion that can search for matching appointments
     */
    public static Criterion<Appointment, Appointment> findBySeries(final long appointmentSeriesId) {
        return new AppointmentCriterion<Appointment>() {
            @SuppressWarnings("unchecked")
            @Override
            public List<Appointment> search(EntityManager entityManager, int pFetchMode) {
                Query query = entityManager.createQuery(new StringBuilder(" from Appointment t ")
                        .append(getFetchScript(pFetchMode))
                        .append(" where t.appointmentSeries.id = :appointmentSeriesId ")
                        .append("  order by t.endTime ")
                        .toString());
                query.setParameter("appointmentSeriesId", appointmentSeriesId);
                return query.getResultList();
            }
        };
    }

    @Override
    protected String getFetchScript(int pFetchMode) {
        StringBuilder sb = new StringBuilder();
        sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
        sb.append(addFetchHQL(FETCH_ACTIVITY, pFetchMode, "t.activity", true));
        sb.append(addFetchHQL(FETCH_APPOINTMENT_SERIES, pFetchMode, "t.appointmentSeries", true));
        sb.append(addFetchHQL(FETCH_INSTRUCTOR, pFetchMode, "t.instructor", true));
        sb.append(addFetchHQL(FETCH_LOCATION_PROFILE, pFetchMode, "t.locationProfile", true));
        sb.append(addFetchHQL(FETCH_ROOM, pFetchMode, "t.room", true));
        sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
        sb.append(addFetchHQL(FETCH_MORE_CUSTOMERS, pFetchMode, "t.customers", true));
        return sb.toString();
    }

    @Override
    public List<E> search(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }

    @Override
    public E get(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }

    /**
     * For gcss-
     *
     * @param pProfileId
     * @param pStartTime
     * @param pEndTime
     * @return
     */


    /**
     * Added for NewInsAptReport _ June 2015 Enhancement
     *

     * @return
     */
    // GSSP-190 added a new feild-InstructorName
    public static Criterion<Appointment, InstructorOpenAppointmentsDTO> findInstructorOpenAppointmentsByLocationIdAndDateTime(
            final long pLocationId, final Date pStartTime, final Date pEndTime, final String pInputExternalId, final String pInstructorName) {

        return new AppointmentCriterion<InstructorOpenAppointmentsDTO>() {

            @Override
            public List<InstructorOpenAppointmentsDTO> search(EntityManager entityManager, int pFetchMode) {
                String sb, firstName = null, lastName = "", check;
                check = pInputExternalId;
                sb = "select to_char(t.start_time, 'DD-Mon-YYYY') as date1 ,t.appointment_id as appointment_id, " +
                        " t.start_time as start_time, t.end_time as end_time, t.canceled as canceled," +
                        " (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration," +
                        " r.profile_room_name as profile_room_name, a.activity_name as activity_name," +
                        " i.instructor_id as instructor_id, p_i.first_name as i_first_name, p_i.last_name as i_last_name," +
                        " c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name " +
                        " from appointment t" +
                        " join location l on t.profile_id = l.profile_id" +
                        " left join appointment_customers a_c on t.appointment_id = a_c.appointment_id" +
                        " left join customer c on a_c.customer_id = c.customer_id" +
                        " left join person p_c on c.person_id = p_c.person_id" +
                        " join instructor i on t.instructor_id = i.instructor_id" +
                        " left join person p_i on i.person_id = p_i.person_id" +
                        " left join room r on t.room_id = r.room_id" +
                        " left join activity a on t.activity_id = a.activity_id" +
                        " left join availability ay on i.availability_id = ay.availability_id" +
                        " where l.location_id = :locationId " +
                        "   and i.external_id = :inputExternalId " +
                        "   and t.start_time >= :startTime " +
                        "   and t.end_time <= :endTime " +
                        "   and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        " order by to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS')";

                String fullName = null;
                if (!(check.trim().length() > 0) && pInstructorName != null && !pInstructorName.isEmpty()) {
                    fullName = pInstructorName.replaceAll("[^a-zA-Z0-9]", "").toUpperCase();
                    sb = sb.replace("i.external_id = :inputExternalId",
                            "UPPER(REGEXP_REPLACE((p_i.first_name || p_i.last_name), '[^0-9A-Za-z]', '')) = :fullName");
                }

                // Create the native query using EntityManager
                Query query = entityManager.createNativeQuery(sb)
                        .setParameter("locationId", pLocationId)
                        .setParameter("startTime", pStartTime)
                        .setParameter("endTime", pEndTime);

                if (!(check.trim().length() > 0)) {
                    query.setParameter("fullName", fullName.trim());
                } else {
                    query.setParameter("inputExternalId", pInputExternalId);
                }

                query.unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("date1", StandardBasicTypes.STRING)
                        .addScalar("appointment_id", StandardBasicTypes.LONG)
                        .addScalar("start_time", StandardBasicTypes.TIMESTAMP)
                        .addScalar("end_time", StandardBasicTypes.TIMESTAMP)
                        .addScalar("canceled", StandardBasicTypes.STRING)
                        .addScalar("duration", StandardBasicTypes.LONG)
                        .addScalar("profile_room_name", StandardBasicTypes.STRING)
                        .addScalar("activity_name", StandardBasicTypes.STRING)
                        .addScalar("instructor_id", StandardBasicTypes.LONG)
                        .addScalar("i_first_name", StandardBasicTypes.STRING)
                        .addScalar("i_last_name", StandardBasicTypes.STRING)
                        .addScalar("customer_id", StandardBasicTypes.LONG)
                        .addScalar("c_first_name", StandardBasicTypes.STRING)
                        .addScalar("c_last_name", StandardBasicTypes.STRING);

                List<Object[]> resultList = query.getResultList();
                List<InstructorOpenAppointmentsDTO> result = new ArrayList<>();
                Map<Long, InstructorOpenAppointmentsDTO> instructorOpenAppointmentsDTOs = new HashMap<>();
                Map<Long, Instructor> instructors = new HashMap<>();
                Map<Long, Customer> customers = new HashMap<>();

                for (Object[] row : resultList) {
                    String date1 = (String) row[0];
                    Long appointment_id = (Long) row[1];
                    Long instructorId = (Long) row[8];

                    Instructor instructor = instructors.computeIfAbsent(instructorId, id -> {
                        Instructor instr = new Instructor();
                        instr.setInstructorId(id);
                        Person person = new Person();
                        person.setFirstName((String) row[9]);
                        person.setLastName((String) row[10]);
                        instr.setPerson(person);
                        return instr;
                    });

                    InstructorOpenAppointmentsDTO instructorOpenAppointmentsDTO = instructorOpenAppointmentsDTOs.computeIfAbsent(appointment_id, id -> {
                        return new InstructorOpenAppointmentsDTO(
                                date1,
                                appointment_id,
                                (Date) row[2],
                                (Date) row[3],
                                (String) row[4],
                                (Long) row[5],
                                (String) row[6],
                                (String) row[7],
                                instructor
                        );
                    });

                    Long customerId = (Long) row[11];
                    if (customerId != null) {
                        Customer customer = customers.computeIfAbsent(customerId, id -> {
                            Customer cust = new Customer();
                            cust.setCustomerId(id);
                            Person person = new Person();
                            person.setFirstName((String) row[12]);
                            person.setLastName((String) row[13]);
                            cust.setPerson(person);
                            return cust;
                        });
                        instructorOpenAppointmentsDTO.addCustomer(customer);
                    }

                    result.add(instructorOpenAppointmentsDTO);
                }

                return result;
            }
        };
    }



    /**
     * For GSSP-170, Conflicting Appointments by Instructor
     *   //Changes made for GSSP-238

     * @return
     */
    public static Criterion<Appointment, InstructorOpenAppointmentsDTO> findConflictAppointmentsByInstructorProfileIdAndDateTime(
            final Long pProfileId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<InstructorOpenAppointmentsDTO>() {

            @Override
            public List<InstructorOpenAppointmentsDTO> search(EntityManager entityManager, int pFetchMode) {

                StringBuilder sb = new StringBuilder("SELECT TO_CHAR(t.start_time, 'DD-Mon-YYYY') AS date1, t.appointment_id AS appointment_id,");
                sb.append(" t.start_time AS start_time, t.end_time AS end_time,");
                sb.append(" EXTRACT(DAY FROM (t.end_time - t.start_time)) AS duration,");
                sb.append(" a.activity_name AS activity_name,");
                sb.append(" i.instructor_id AS instructor_id, p_i.first_name AS i_first_name, p_i.last_name AS i_last_name,");
                sb.append(" c.customer_id AS customer_id, p_c.first_name AS c_first_name, p_c.last_name AS c_last_name,");
                sb.append(" a_s.is_recurring AS is_recurring,");
                sb.append(" l.external_id || ' ' || l.location_name AS location_name");
                sb.append(" FROM appointment t");

                if (pProfileId != null) {
                    sb.append(" JOIN appointment t2 ON t.profile_id = :profileId");
                    sb.append(" AND t.profile_id = t2.profile_id");
                } else {
                    sb.append(" JOIN appointment t2 ON t.profile_id = t2.profile_id");
                }

                sb.append(" AND t.instructor_id = t2.instructor_id");
                sb.append(" AND t.appointment_id != t2.appointment_id");
                sb.append(" AND t.start_time >= :startTime");
                sb.append(" AND t2.start_time >= :startTime");
                sb.append(" AND t.end_time <= :endTime");
                sb.append(" AND t2.end_time <= :endTime");
                sb.append(" AND (t2.start_time >= t.start_time AND t2.start_time < t.end_time");
                sb.append(" OR t2.end_time > t.start_time AND t2.end_time <= t.end_time");
                sb.append(" OR t.start_time >= t2.start_time AND t.end_time <= t2.end_time)");
                sb.append(" JOIN instructor i ON t.instructor_id = i.instructor_id");
                sb.append(" JOIN person p_i ON p_i.person_id = i.person_id");
                sb.append(" LEFT JOIN appointment_customers a_c ON t.appointment_id = a_c.appointment_id");
                sb.append(" LEFT JOIN customer c ON a_c.customer_id = c.customer_id");
                sb.append(" LEFT JOIN person p_c ON c.person_id = p_c.person_id");
                sb.append(" JOIN activity a ON a.activity_id = t.activity_id");
                sb.append(" JOIN location l ON l.profile_id = t.profile_id");
                sb.append(" JOIN appointment_series a_s ON a_s.appointment_series_id = t.appointment_series_id");
                sb.append(" WHERE NVL(t.canceled, 'N') != 'Y'");
                sb.append(" AND NVL(t2.canceled, 'N') != 'Y'");
                sb.append(" ORDER BY t.start_time, t.instructor_id, t.start_time, t.end_time");

                // Execute query using EntityManager
                TypedQuery<Object[]> query = (TypedQuery<Object[]>) entityManager.createNativeQuery(sb.toString(), Object[].class);

                if (pProfileId != null) {
                    query.setParameter("profileId", pProfileId);
                }

                query.setParameter("startTime", pStartTime, TemporalType.TIMESTAMP);
                query.setParameter("endTime", pEndTime, TemporalType.TIMESTAMP);

                List<Object[]> results = query.getResultList();

                List<InstructorOpenAppointmentsDTO> result = new ArrayList<>();
                Map<Long, InstructorOpenAppointmentsDTO> instructorOpenAppointmentsDTOs = new HashMap<>();
                Map<Long, Instructor> instructors = new HashMap<>();
                Map<Long, Customer> customers = new HashMap<>();

                for (Object[] objects : results) {
                    String date1 = (String) objects[0];
                    Long appointment_id = (Long) objects[1];
                    String is_recurring = (String) objects[12];
                    Long instructorId = (Long) objects[6];
                    String location_name = (String) objects[13];

                    Instructor instructor = instructors.computeIfAbsent(instructorId, id -> {
                        Instructor instr = new Instructor();
                        instr.setInstructorId(id);
                        Person person = new Person();
                        person.setFirstName((String) objects[7]);
                        person.setLastName((String) objects[8]);
                        instr.setPerson(person);
                        return instr;
                    });

                    InstructorOpenAppointmentsDTO instructorOpenAppointmentsDTO = instructorOpenAppointmentsDTOs.computeIfAbsent(appointment_id, id -> {
                        InstructorOpenAppointmentsDTO dto = new InstructorOpenAppointmentsDTO(
                                date1, id, (Date) objects[2], (Date) objects[3], (Long) objects[4], (String) objects[5], instructor, is_recurring, location_name);
                        result.add(dto);
                        return dto;
                    });

                    Long customerId = (Long) objects[9];
                    if (customerId != null) {
                        Customer customer = customers.computeIfAbsent(customerId, id -> {
                            Customer cust = new Customer();
                            cust.setCustomerId(id);
                            Person person = new Person();
                            person.setFirstName((String) objects[10]);
                            person.setLastName((String) objects[11]);
                            cust.setPerson(person);
                            return cust;
                        });
                        instructorOpenAppointmentsDTO.addCustomer(customer);
                    }
                }

                return result;
            }
        };
    }



    /**
     * For GSSP-170, Conflicting Appointments by Room
     *

     * @return
     */
    public static Criterion<Appointment, InstructorOpenAppointmentsDTO> findConflictAppointmentsByRoomProfileIdAndDateTime(
            final Long pProfileId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<InstructorOpenAppointmentsDTO>() {

            @Override
            public List<InstructorOpenAppointmentsDTO> search(EntityManager entityManager, int pFetchMode) {

                StringBuilder sb = new StringBuilder("select to_char(t.start_time, 'DD-Mon-YYYY') as date1 ,t.appointment_id as appointment_id, ");
                sb.append(" t.start_time as start_time, t.end_time as end_time,");
                sb.append(" (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration,");
                sb.append(" r.profile_room_name as profile_room_name, a.activity_name as activity_name,");
                sb.append(" c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name,");
                sb.append(" a_s.is_recurring as is_recurring, ");
                sb.append(" l.external_id ||  ' ' || l.location_name as location_name");

                sb.append(" from appointment t");

                if (null != pProfileId) {
                    sb.append(" join appointment t2 on t.profile_id = :profileId ");
                    sb.append("  and t.profile_id = t2.profile_id");
                } else {
                    sb.append(" join appointment t2 on t.profile_id = t2.profile_id ");
                }

                sb.append("  and t.room_id = t2.room_id");
                sb.append("  and t.appointment_id != t2.appointment_id");
                sb.append("  and to_date(to_char(t.start_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') >= :startTime ");
                sb.append("  and to_date(to_char(t2.start_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') >= :startTime ");
                sb.append("  and to_date(to_char(t.end_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') <= :endTime ");
                sb.append("  and to_date(to_char(t2.end_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') <= :endTime ");
                sb.append("  and (t2.start_time >= t.start_time and t2.start_time < t.end_time");
                sb.append("   or t2.end_time > t.start_time and t2.end_time <= t.end_time");
                sb.append("   or t.start_time >= t2.start_time and t.end_time <= t2.end_time)");
                sb.append(" left join appointment_customers a_c on t.appointment_id = a_c.appointment_id");
                sb.append(" left join customer c on a_c.customer_id = c.customer_id");
                sb.append(" left join person p_c on c.person_id = p_c.person_id");
                sb.append(" join room r on r.room_id = t.room_id");
                sb.append(" join activity a on a.activity_id = t.activity_id");
                sb.append(" join appointment_series a_s on a_s.appointment_series_id = t.appointment_series_id");
                sb.append(" join location l on l.profile_id = t.profile_id");

                sb.append(" where decode(t.canceled, null, 'N', t.canceled) != 'Y'");
                sb.append("  and decode(t2.canceled, null, 'N', t2.canceled) != 'Y' ");
                sb.append(" order by to_date(to_char(t.start_time, 'YYYY-MM-DD'), 'YYYY-MM-DD'), r.profile_room_name,");
                sb.append("to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS'),");
                sb.append("  to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS')");

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb.toString());

                if (null != pProfileId) {
                    query.setParameter("profileId", pProfileId);
                }
                query.setParameter("startTime", pStartTime);
                query.setParameter("endTime", pEndTime);

                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("appointment_id", StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("end_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("duration", StandardBasicTypes.LONG);
                query.addScalar("profile_room_name", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("customer_id", StandardBasicTypes.LONG);
                query.addScalar("c_first_name", StandardBasicTypes.STRING);
                query.addScalar("c_last_name", StandardBasicTypes.STRING);
                query.addScalar("is_recurring", StandardBasicTypes.STRING);
                query.addScalar("location_name", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();
                List<InstructorOpenAppointmentsDTO> result = new ArrayList<>();
                Map<Long, InstructorOpenAppointmentsDTO> instructorOpenAppointmentsDTOs = new HashMap<>();
                Map<Long, Customer> customers = new HashMap<>();

                while (scroll.next()) {
                    Object[] objects = (Object[]) scroll.get();
                    String date1 = (String) objects[0];
                    Long appointment_id = (Long) objects[1];
                    String is_recurring = (String) objects[10];
                    String location_name = (String) objects[11];

                    InstructorOpenAppointmentsDTO instructorOpenAppointmentsDTO = instructorOpenAppointmentsDTOs.get(appointment_id);
                    if (instructorOpenAppointmentsDTO == null) {
                        instructorOpenAppointmentsDTO = new InstructorOpenAppointmentsDTO(date1, appointment_id, (Date) objects[2], (Date) objects[3], (Long) objects[4], (String) objects[5], (String) objects[6], is_recurring, location_name);
                        instructorOpenAppointmentsDTOs.put(appointment_id, instructorOpenAppointmentsDTO);
                        result.add(instructorOpenAppointmentsDTO);
                    }

                    Long customerId = (Long) objects[7];
                    if (customerId == null) continue;
                    Customer customer = customers.get(customerId);
                    if (customer == null) {
                        customer = new Customer();
                        customer.setCustomerId(customerId);
                        Person person = new Person();
                        person.setFirstName((String) objects[8]);
                        person.setLastName((String) objects[9]);
                        customer.setPerson(person);
                        customers.put(customerId, customer);
                    }
                    instructorOpenAppointmentsDTO.addCustomer(customer);
                }

                scroll.close();
                return result;
            }
        };
    }


    /**
     * For GSSP-161, Added for Instructor outside availability appointments report
     *

     * @return
     */
    public static Criterion<Appointment, InstructorOpenAppointmentsDTO> findInstructorOutsideAppointmentsByLocationIdAndDateTime(
            final Long pProfileId, final String pStartTime, final String pEndTime, final String pInputExternalId, final String pDayType) {

        return new AppointmentCriterion<InstructorOpenAppointmentsDTO>() {

            @Override
            public List<InstructorOpenAppointmentsDTO> search(EntityManager entityManager, int pFetchMode) {

                // Query to get one-time appointment IDs
                String sb2 = "select t.appointment_id as appointment_id " +
                        " from appointment t" +
                        " join onetime o on t.instructor_id = o.instructor_id" +
                        " join instructor i on t.instructor_id = i.instructor_id" +
                        " where t.profile_id = :profileId " +
                        "   and i.external_id = :inputExternalId " +
                        "   and t.start_time >= sysdate " +
                        "   and to_char(t.start_time - 1, 'd') = :dayType " +
                        "   and o.start_time <= t.start_time and o.end_time >= t.end_time";

                NativeQuery<Long> query2 = (NativeQuery<Long>) entityManager.createNativeQuery(sb2);
                query2.setParameter("profileId", pProfileId);
                query2.setParameter("inputExternalId", pInputExternalId);
                query2.setParameter("dayType", pDayType);
                query2.addScalar("appointment_id", StandardBasicTypes.LONG);
                List<Long> oneTimeAppointmentIDs = query2.getResultList();

                // Query to get detailed appointment information
                String sb = "select to_char(t.start_time, 'DD-Mon-YYYY') as date1 ,t.appointment_id as appointment_id, " +
                        " t.start_time as start_time, t.end_time as end_time, t.canceled as canceled," +
                        " (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration," +
                        " r.profile_room_name as profile_room_name, a.activity_name as activity_name," +
                        " i.instructor_id as instructor_id, p_i.first_name as i_first_name, p_i.last_name as i_last_name," +
                        " c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name " +
                        " from appointment t" +
                        " left join appointment_customers a_c on t.appointment_id = a_c.appointment_id" +
                        " left join customer c on a_c.customer_id = c.customer_id" +
                        " left join person p_c on c.person_id = p_c.person_id" +
                        " join instructor i on t.instructor_id = i.instructor_id" +
                        " join person p_i on i.person_id = p_i.person_id" +
                        " join room r on t.room_id = r.room_id" +
                        " join activity a on t.activity_id = a.activity_id" +
                        " where t.profile_id = :profileId " +
                        "   and i.external_id = :inputExternalId " +
                        "   and to_char(t.start_time - 1, 'd') = :dayType " +
                        "   and (to_char(t.start_time, 'HH24:MI') < :startTime " +
                        "    or to_char(t.end_time, 'HH24:MI') > :endTime )" +
                        "   and t.start_time >= sysdate " +
                        "   and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        " order by to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS')";

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb);
                query.setParameter("profileId", pProfileId);
                query.setParameter("startTime", pStartTime);
                query.setParameter("endTime", pEndTime);
                query.setParameter("inputExternalId", pInputExternalId);
                query.setParameter("dayType", pDayType);
                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("appointment_id", StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("end_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("canceled", StandardBasicTypes.STRING);
                query.addScalar("duration", StandardBasicTypes.LONG);
                query.addScalar("profile_room_name", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("instructor_id", StandardBasicTypes.LONG);
                query.addScalar("i_first_name", StandardBasicTypes.STRING);
                query.addScalar("i_last_name", StandardBasicTypes.STRING);
                query.addScalar("customer_id", StandardBasicTypes.LONG);
                query.addScalar("c_first_name", StandardBasicTypes.STRING);
                query.addScalar("c_last_name", StandardBasicTypes.STRING);

                List<Object[]> results = query.getResultList();
                List<InstructorOpenAppointmentsDTO> result = new ArrayList<>();
                Map<Long, InstructorOpenAppointmentsDTO> instructorOpenAppointmentsDTOs = new HashMap<>();
                Map<Long, Instructor> instructors = new HashMap<>();
                Map<Long, Customer> customers = new HashMap<>();

                for (Object[] objects : results) {
                    String date1 = (String) objects[0];
                    Long appointment_id = (Long) objects[1];
                    Long instructorId = (Long) objects[8];
                    Instructor instructor = instructors.get(instructorId);
                    if (instructor == null) {
                        instructor = new Instructor();
                        instructor.setInstructorId(instructorId);
                        Person person = new Person();
                        person.setFirstName((String) objects[9]);
                        person.setLastName((String) objects[10]);
                        instructor.setPerson(person);
                        instructors.put(instructorId, instructor);
                    }

                    if (!oneTimeAppointmentIDs.contains(appointment_id)) {
                        InstructorOpenAppointmentsDTO instructorOpenAppointmentsDTO = instructorOpenAppointmentsDTOs.get(appointment_id);
                        if (instructorOpenAppointmentsDTO == null) {
                            instructorOpenAppointmentsDTO = new InstructorOpenAppointmentsDTO(date1, appointment_id, (Date) objects[2], (Date) objects[3], (String) objects[4], (Long) objects[5], (String) objects[6], (String) objects[7], instructor);
                            instructorOpenAppointmentsDTOs.put(appointment_id, instructorOpenAppointmentsDTO);
                            result.add(instructorOpenAppointmentsDTO);
                        }

                        Long customerId = (Long) objects[11];
                        if (customerId == null) continue;
                        Customer customer = customers.get(customerId);
                        if (customer == null) {
                            customer = new Customer();
                            customer.setCustomerId(customerId);
                            Person person = new Person();
                            person.setFirstName((String) objects[12]);
                            person.setLastName((String) objects[13]);
                            customer.setPerson(person);
                            customers.put(customerId, customer);
                        }
                        instructorOpenAppointmentsDTO.addCustomer(customer);
                    }
                }

                return result;
            }
        };
    }




    //Added for GSSP-158
    /**
     * Returns a Criterion that can be used to find any existing employees that belong to the zone id
     *
     * @param
     * @return Criterion instance
     */
    public static Criterion<Appointment, Map<Long, List<String>>> findByZoneID(final String locationTimezone) {

        return new AppointmentCriterion<Map<Long, List<String>>>() {

            @Override
            public List<Map<Long, List<String>>> search(EntityManager entityManager, int pFetchMode) {

                // Build SQL query
                StringBuilder sb = new StringBuilder("select l.location_id, p.email from employee e ");
                sb.append("join person p on p.person_id = e.person_id ");
                sb.append("join person_role pr on pr.person_id = e.person_id ");
                sb.append("join location l on l.location_id = pr.location_id ");
                sb.append("join location_profile lp on l.profile_id = lp.profile_id ");
                sb.append("where e.enterprise_status='A' and (e.status = 'Enable') ");

                if (!StringUtils.isEmpty(locationTimezone)) {
                    sb.append("and lp.TZ = :timeZone ");
                }

                sb.append("order by l.location_id");

                // Create and configure NativeQuery
                Query query = entityManager.createNativeQuery(sb.toString());

                if (!StringUtils.isEmpty(locationTimezone)) {
                    query.setParameter("timeZone", locationTimezone);
                }

                query.unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("location_id", StandardBasicTypes.LONG)
                        .addScalar("email", StandardBasicTypes.STRING);

                // Execute query
                List<Object[]> results = query.getResultList();

                // Process results
                Map<Long, List<String>> cache = new HashMap<>();

                for (Object[] objects : results) {
                    Long locationId = (Long) objects[0];
                    String email = (String) objects[1];

                    cache.computeIfAbsent(locationId, k -> new ArrayList<>()).add(email);
                }

                // Convert cache to list of maps
                List<Map<Long, List<String>>> result = new ArrayList<>();
                result.add(cache);

                return result;
            }
        };
    }




    //For Gssp-255
    public static Criterion<Appointment, Map<Long, List<String>>> findByInstructorPersonalEmail(final String locationTimezone) {

        return new AppointmentCriterion<Map<Long, List<String>>>() {

            @Override
            public List<Map<Long, List<String>>> search(EntityManager entityManager, int pFetchMode) {

                // Build SQL query
                StringBuilder sb = new StringBuilder("select l.location_id, ppd.personal_email from instructor i");
                sb.append(" join person_personal_details ppd on ppd.person_id = i.person_id");
                sb.append(" join location l on l.location_id = i.location_id");
                sb.append(" join location_profile lp on l.profile_id = lp.profile_id");
                sb.append(" where i.status = 'A' and i.enabled = 'Y'");

                if (!StringUtils.isEmpty(locationTimezone)) {
                    sb.append(" and lp.TZ = :timeZone");
                }

                sb.append(" order by l.location_id");

                // Create and configure NativeQuery
                Query query = entityManager.createNativeQuery(sb.toString());

                if (!StringUtils.isEmpty(locationTimezone)) {
                    query.setParameter("timeZone", locationTimezone);
                }

                query.unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("location_id", StandardBasicTypes.LONG)
                        .addScalar("personal_email", StandardBasicTypes.STRING);

                // Execute query
                List<Object[]> results = query.getResultList();

                // Process results
                Map<Long, List<String>> cache = new HashMap<>();

                for (Object[] objects : results) {
                    Long locationId = (Long) objects[0];
                    String personalEmail = (String) objects[1];

                    if (personalEmail != null && personalEmail.matches(EMAIL_REGEX)) {
                        personalEmail = personalEmail.trim().toLowerCase();

                        cache.computeIfAbsent(locationId, k -> new ArrayList<>()).add(personalEmail);
                    }
                }

                // Convert cache to list of maps
                List<Map<Long, List<String>>> result = new ArrayList<>();
                result.add(cache);

                return result;
            }
        };
    }


    //For GSSP-243 send email with cc Associates/Managers/Leads

    public static AppointmentCriterion<String> findEmployeeEmailIdsbyProfileID(final long profileID) {

        return new AppointmentCriterion<String>() {
            @Override
            public List<String> search(EntityManager entityManager, int pFetchMode) {

                // Build SQL query
                StringBuilder sb = new StringBuilder()
                        .append("select p.email from EMPLOYEE e ")
                        .append("join person p on p.person_id = e.person_id ")
                        .append("join person_role pr on pr.person_id = e.person_id ")
                        .append("join location l on l.location_id = pr.location_id ")
                        .append("where l.profile_id = :ProfileID ")
                        .append("and e.enterprise_status = 'A' ")
                        .append("and e.status = 'Enable' ")
                        .append("and pr.role_id in (1, 2, 3)");

                // Create and configure NativeQuery
                Query query = entityManager.createNativeQuery(sb.toString());
                query.setParameter("ProfileID", profileID);
                query.unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("email", StandardBasicTypes.STRING);

                // Execute query and process results
                List<String> emailList = query.getResultList();
                return emailList;
            }
        };
    }



    //Added for Scheduling Phase II - Month View Service
    /**
     * Returns a Criterion that can be used to find  details of appointments belonging to an appointment series for the specified month
     *
     * @param appointmentSeriesId Long
     * @param pStartTime Date
     * @param pEndTime Date
     *
     * @return Criterion instance
     */
    public static Criterion<Appointment, Lesson> findAppointmentBySeriesAndMonth(
            final Long appointmentSeriesId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<Lesson>() {

            @Override
            public List<Lesson> search(EntityManager entityManager, int pFetchMode) {
                // SQL query
                String sb = "select t.appointment_id as appointment_id,to_char(t.start_time, 'HH:MI AM') as time1, " +
                        " to_char(t.end_time, 'HH:MI AM') as time2,"+
                        " to_char(t.start_time, 'YYYY-MM-DD') as date1, a.activity_name as activity_name,"+
                        " p_i.first_name as i_first_name, p_i.last_name as i_last_name"+
                        " from appointment t" +
                        " join activity a on a.activity_id = t.activity_id" +
                        " left join instructor i on t.instructor_id = i.instructor_id" +
                        " left join person p_i on i.person_id = p_i.person_id	" +
                        " where t.appointment_series_id= :appointmentSeriesId " +
                        " and t.start_time > :pStartTime" +
                        " and t.end_time <=  :pEndTime" +
                        " and decode(t.canceled, null, 'N', t.canceled) != 'Y'" +
                        //Changes made for LES-152
                        " order by t.start_time" ;

                // Create and configure NativeQuery
                Query query = entityManager.createNativeQuery(sb);
                query.setParameter("appointmentSeriesId", appointmentSeriesId);
                query.setParameter("pStartTime", pStartTime);
                query.setParameter("pEndTime", pEndTime);

                // Specify the result type
                query.unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("appointment_id", StandardBasicTypes.LONG)
                        .addScalar("time1", StandardBasicTypes.STRING)
                        .addScalar("time2", StandardBasicTypes.STRING)
                        .addScalar("date1", StandardBasicTypes.STRING)
                        .addScalar("activity_name", StandardBasicTypes.STRING)
                        .addScalar("i_first_name", StandardBasicTypes.STRING)
                        .addScalar("i_last_name", StandardBasicTypes.STRING);

                // Execute query and process results
                List<Object[]> resultList = query.getResultList();
                List<Lesson> result = new ArrayList<>();

                List<String> editDisabledList = Arrays.asList(editDisabled);

                for (Object[] objects : resultList) {
                    String activityName = (String) objects[4];
                    String editEnable = editDisabledList.contains(activityName) ? "N" : "Y";

                    Lesson lesson = new Lesson(
                            (Long) objects[0],
                            activityName,
                            (String) objects[1],
                            (String) objects[5],
                            (String) objects[6],
                            (String) objects[2],
                            (String) objects[3],
                            editEnable
                    );
                    result.add(lesson);
                }

                return result;
            }
        };
    }

    public static Criterion<Appointment, CancelledAppointmentDateListDTO> findCancelledApptReportByProfileIdAndDateTime(
            final Long pProfileId,
            final Date pStartTime,
            final Date pEndTime,
            boolean justLessonActivities) {

        return new AppointmentCriterion<CancelledAppointmentDateListDTO>() {
            DateFormat outputFormatter = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_HIPHEN);

            @Override
            public List<CancelledAppointmentDateListDTO> search(EntityManager entityManager, int pFetchMode) {
                final Date startTime = new Date(pStartTime.getTime());
                final Date endTime = new Date(pEndTime.getTime());

                String comparStartDate = outputFormatter.format(startTime);
                String comparEndDate = outputFormatter.format(endTime);

                StringBuilder sb = new StringBuilder("SELECT trunc(t.start_time) as date1, t.appointment_id as appointment_id, ");
                sb.append("t.start_time as start_time, t.end_time as end_time, t.canceled as canceled, ");
                sb.append("a_cr.cancel_reason as cancel_reason, ");
                sb.append("r.profile_room_name as profile_room_name, a.activity_name as activity_name, ");
                sb.append("i.instructor_id as instructor_id, p_i.first_name as i_first_name, p_i.last_name as i_last_name, ");
                sb.append("c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name, t.note as notes, ");
                sb.append("p_cu.FIRST_NAME as cu_first_name, p_cu.LAST_NAME as cu_last_name, ");
                sb.append("to_char(t.UPDATED, 'DD-MON-YYYY HH:MI AM') as time1, t.updated_by as updated_id ");
                sb.append("FROM appointment t ");
                sb.append("LEFT JOIN appointment_cancel a_ca ON t.appointment_id = a_ca.appointment_id ");
                sb.append("LEFT JOIN appointment_cancel_reason a_cr ON a_ca.appointment_cancel_reason_id = a_cr.appointment_cancel_reason_id ");
                sb.append("LEFT JOIN appointment_customers a_c ON t.appointment_id = a_c.appointment_id ");
                sb.append("LEFT JOIN customer c ON a_c.customer_id = c.customer_id ");
                sb.append("LEFT JOIN customer_status c_s ON c.customer_status_id = c_s.customer_status_id ");
                sb.append("LEFT JOIN person p_c ON c.person_id = p_c.person_id ");
                sb.append("LEFT JOIN instructor i ON t.instructor_id = i.instructor_id ");
                sb.append("LEFT JOIN person p_i ON i.person_id = p_i.person_id ");
                sb.append("LEFT JOIN person p_cu ON t.UPDATED_BY = p_cu.PERSON_ID ");
                sb.append("LEFT JOIN room r ON t.room_id = r.room_id ");
                sb.append("LEFT JOIN activity a ON t.activity_id = a.activity_id ");
                sb.append("LEFT JOIN service s ON s.service_id = a.service_id ");
                sb.append("WHERE t.profile_id = :profileId ");
                if (comparStartDate.equals(comparEndDate)) {
                    sb.append("AND trunc(t.start_time) = :startTime ");
                    sb.append("AND trunc(t.end_time) = :endTime ");
                } else {
                    sb.append("AND trunc(t.start_time) >= :startTime ");
                    sb.append("AND trunc(t.end_time) <= :endTime ");
                }
                sb.append("AND decode(c_s.external_id, null, '_', c_s.external_id) != 'C' ");
                sb.append("AND decode(t.canceled, null, 'N', t.canceled) = 'Y' ");
                sb.append("AND upper(s.service_name) <> 'REHEARSAL' ");
                sb.append("AND EXISTS (SELECT 1 FROM appointment_customers WHERE appointment_id = t.appointment_id) ");
                sb.append("ORDER BY to_char(t.start_time, 'DD-MON-YYYY'), t.start_time, p_i.first_name, p_i.last_name, p_c.first_name, p_c.last_name");

                // Create and configure NativeQuery
                NativeQuery<?> query = (NativeQuery<?>) entityManager.createNativeQuery(sb.toString());
                query.setParameter("profileId", pProfileId);
                query.setParameter("startTime", startTime);
                query.setParameter("endTime", endTime);

                // Specify the result type
                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("appointment_id", StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("end_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("canceled", StandardBasicTypes.STRING);
                query.addScalar("cancel_reason", StandardBasicTypes.STRING);
                query.addScalar("profile_room_name", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("instructor_id", StandardBasicTypes.LONG);
                query.addScalar("i_first_name", StandardBasicTypes.STRING);
                query.addScalar("i_last_name", StandardBasicTypes.STRING);
                query.addScalar("customer_id", StandardBasicTypes.LONG);
                query.addScalar("c_first_name", StandardBasicTypes.STRING);
                query.addScalar("c_last_name", StandardBasicTypes.STRING);
                query.addScalar("notes", StandardBasicTypes.STRING);
                query.addScalar("cu_first_name", StandardBasicTypes.STRING);
                query.addScalar("cu_last_name", StandardBasicTypes.STRING);
                query.addScalar("time1", StandardBasicTypes.STRING);
                query.addScalar("updated_id", StandardBasicTypes.LONG);

                ScrollableResults scroll = query.scroll();
                List<CancelledAppointmentDateListDTO> result = new ArrayList<>();
                Map<String, List<CancelledApptDailyDTO>> cache = new HashMap<>();
                Map<Long, CancelledApptDailyDTO> cancelledApptDailyDTOs = new HashMap<>();
                Map<Long, Instructor> instructors = new HashMap<>();
                Map<Long, Instructor> cancelledUser = new HashMap<>();
                Map<Long, Customer> customers = new HashMap<>();

                try {
                    while (scroll.next()) {
                        Object[] objects = (Object[]) scroll.get();
                        String date1 = (String) objects[0];
                        Long appointmentId = (Long) objects[1];
                        Long instructorId = (Long) objects[8];
                        Long updatedId = (Long) objects[18];

                        Instructor instructor = null;
                        if (instructorId != null) {
                            instructor = instructors.get(instructorId);
                            if (instructor == null) {
                                instructor = new Instructor();
                                instructor.setInstructorId(instructorId);
                                Person person = new Person();
                                person.setFirstName((String) objects[9]);
                                person.setLastName((String) objects[10]);
                                instructor.setPerson(person);
                                instructors.put(instructorId, instructor);
                            }
                        }

                        if (!cache.containsKey(date1)) {
                            List<CancelledApptDailyDTO> list = new ArrayList<>();
                            cache.put(date1, list);
                            result.add(new CancelledAppointmentDateListDTO(date1, list));
                        }

                        List<CancelledApptDailyDTO> cancelledDailyDTOList = cache.get(date1);
                        CancelledApptDailyDTO cancelledDailyDTO = cancelledApptDailyDTOs.get(appointmentId);
                        if (cancelledDailyDTO == null) {
                            cancelledDailyDTO = new CancelledApptDailyDTO(
                                    appointmentId,
                                    (Date) objects[2],
                                    (Date) objects[3],
                                    (String) objects[4],
                                    (String) objects[6],
                                    (String) objects[7]
                            );
                            cancelledDailyDTOList.add(cancelledDailyDTO);
                            cancelledApptDailyDTOs.put(appointmentId, cancelledDailyDTO);
                        }

                        if (cancelledDailyDTO.getInstructor() == null && instructor != null) {
                            cancelledDailyDTO.setInstructor(instructor);
                        }

                        // Set notes and cancelled reason
                        cancelledDailyDTO.setNotes(objects[14] != null ? objects[14].toString() : "");
                        cancelledDailyDTO.setCancelledReason(objects[5] != null ? objects[5].toString() : "");

                        // Set cancelled time and cancelled user
                        cancelledDailyDTO.setCancelledTime(objects[17] != null ? objects[17].toString() : "");
                        if (instructorId != null && updatedId != null) {
                            Instructor cancelledInstructor = cancelledUser.get(updatedId);
                            if (cancelledInstructor == null) {
                                cancelledInstructor = new Instructor();
                                cancelledInstructor.setInstructorId(updatedId);
                                Person person = new Person();
                                person.setFirstName((String) objects[15]);
                                person.setLastName((String) objects[16]);
                                cancelledInstructor.setPerson(person);
                                cancelledUser.put(updatedId, cancelledInstructor);
                            }
                            cancelledDailyDTO.setCancelledUser(cancelledInstructor);
                        }

                        // Handle customer information
                        Long customerId = (Long) objects[11];
                        if (customerId != null) {
                            Customer customer = customers.get(customerId);
                            if (customer == null) {
                                customer = new Customer();
                                customer.setCustomerId(customerId);
                                Person person = new Person();
                                person.setFirstName((String) objects[12]);
                                person.setLastName((String) objects[13]);
                                customer.setPerson(person);
                                customers.put(customerId, customer);
                            }
                            cancelledDailyDTO.addCustomer(customer);
                        }
                    }
                } finally {
                    scroll.close();
                }

                return result;
            }
        };
    }



    //Added for Scheduling Phase II - Month View Service
    /**
     * Returns a Criterion that can be used to find  details of profile and location  of  an  appointment series
     *
     * @param appointmentSeriesId Long
     *
     * @return Criterion instance
     */
    public static Criterion<Appointment, LocationProfileInfoDTO> findProfileDetailsByAppointmentSeriesID(
            final Long appointmentSeriesId) {

        return new AppointmentCriterion<LocationProfileInfoDTO>() {

            @Override
            public List<LocationProfileInfoDTO> search(EntityManager entityManager, int pFetchMode) {
                String sb = " select l.location_name as studio_name, l.phone as phone , lp.profile_id as profile_id, "+
                        " to_char(ap.series_start_time,'YYYY-MM-DD')as series_start_date  from appointment_series ap " +
                        " join  location_profile lp on  ap.profile_id = lp.profile_id "+
                        " join  location l  on ap.profile_id = l.profile_id "+
                        " where ap.appointment_series_id=:appointmentSeriesId";

                // Create and configure NativeQuery
                Query query = entityManager.createNativeQuery(sb);
                query.setParameter("appointmentSeriesId", appointmentSeriesId);

                // Specify the result type
                query.unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("studio_name", StandardBasicTypes.STRING)
                        .addScalar("phone", StandardBasicTypes.STRING)
                        .addScalar("profile_id", StandardBasicTypes.LONG)
                        .addScalar("series_start_date", StandardBasicTypes.STRING);

                // Execute query and process results
                List<Object[]> resultList = query.getResultList();
                List<LocationProfileInfoDTO> result = new ArrayList<>();

                for (Object[] objects : resultList) {
                    LocationProfileInfoDTO locationProfileInfoDTO = new LocationProfileInfoDTO(
                            (Long) objects[2],
                            (String) objects[0],
                            (String) objects[1],
                            (String) objects[3]
                    );
                    result.add(locationProfileInfoDTO);
                }

                return result;
            }
        };
    }


    //Added for Scheduling Phase II - Month View Service
    /**
     * Returns a Criterion that can be used to find  customer details based on bade Number
     *
     * @param
     *
     * @return Criterion instance
     */

    public static AppointmentCriterion<String> findCustomerNameByExternalId(
            final String customerMemberId) {

        return new AppointmentCriterion<String>() {

            @Override
            public String get(EntityManager entityManager, int pFetchMode) {

                String sb = " select p.first_name, p.last_name  from person p " +
                        " join customer c on c.person_id = p.person_id "+
                        " where c.EXTERNAL_ID = :customerMemberId";

                // Create and configure NativeQuery
                Query query = entityManager.createNativeQuery(sb);
                query.setParameter("customerMemberId", customerMemberId);

                // Specify the result type
                query.unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("first_name", StandardBasicTypes.STRING)
                        .addScalar("last_name", StandardBasicTypes.STRING);

                // Execute query and process results
                List<Object[]> resultList = query.getResultList();
                String firstName = "", lastName = "", fullName = "";

                if (!resultList.isEmpty()) {
                    Object[] objects = resultList.get(0);
                    firstName = (String) objects[0];
                    lastName = (String) objects[1];
                    fullName = StringUtils.isBlank(firstName) ? "" : firstName + " " + (StringUtils.isBlank(lastName) ? "" : lastName);
                }

                return fullName;
            }
        };
    }


    //Added for Scheduling Phase II - Instructor Fetch  View Service
    /**
     * Returns a Criterion that can be used to find  details of appointments passed from the UI
     *
     * @param appointmentId Long
     *
     * @return Criterion instance
     */
    public static Criterion<Appointment, LocationProfileInfoDTO> findProfileDetailByAppointmentID(
            final Long appointmentId) {

        return new AppointmentCriterion<LocationProfileInfoDTO>() {

            @Override
            public List<LocationProfileInfoDTO> search(EntityManager entityManager, int pFetchMode) {

                String sb = " select t.profile_id as profileId, t.appointment_series_id ,l.location_id, t.activity_id, " +
                        " l.location_name as locationName, l.phone  ,to_char(ap.series_start_time,'YYYY-MM-DD')as seriesStartDate, " +
                        " to_char(t.start_time, 'YYYY-MM-DD') as appointmentDate,  " +
                        " (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration, " +
                        " to_char(t.start_time,'HH:MI AM') as start_time, to_char(t.end_time,'HH:MI AM') as end_time, "+
                        " to_char(ap.series_end_time,'YYYY-MM-DD')as seriesEndDate ," +
                        " ap.is_recurring as isRecurring" +
                        " from appointment t  " +
                        " join appointment_series ap on t.appointment_series_id = ap.appointment_series_id "+
                        " join location_profile lp on t.profile_id = lp.profile_id "+
                        " join location l on lp.profile_id = l.profile_id " +
                        " where t.appointment_id=:appointmentId";

                // Create and configure NativeQuery
                Query query = entityManager.createNativeQuery(sb);
                query.setParameter("appointmentId", appointmentId);

                // Specify the result type
                query.unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("profileId", StandardBasicTypes.LONG)
                        .addScalar("appointment_series_id", StandardBasicTypes.LONG)
                        .addScalar("location_id", StandardBasicTypes.LONG)
                        .addScalar("activity_id", StandardBasicTypes.LONG)
                        .addScalar("locationName", StandardBasicTypes.STRING)
                        .addScalar("phone", StandardBasicTypes.STRING)
                        .addScalar("seriesStartDate", StandardBasicTypes.STRING)
                        .addScalar("appointmentDate", StandardBasicTypes.STRING)
                        .addScalar("duration", StandardBasicTypes.LONG)
                        .addScalar("start_time", StandardBasicTypes.STRING)
                        .addScalar("end_time", StandardBasicTypes.STRING)
                        .addScalar("seriesEndDate", StandardBasicTypes.STRING)
                        .addScalar("isRecurring", StandardBasicTypes.STRING);

                // Execute query and process results
                List<Object[]> resultList = query.getResultList();
                List<LocationProfileInfoDTO> result = new ArrayList<>();

                for (Object[] objects : resultList) {
                    LocationProfileInfoDTO locationProfileInfoDTO = new LocationProfileInfoDTO(
                            (Long) objects[0],
                            (String) objects[4],
                            (String) objects[5],
                            (String) objects[6],
                            (Long) objects[1],
                            (Long) objects[2],
                            (Long) objects[3],
                            (String) objects[7],
                            (Long) objects[8],
                            (String) objects[9],
                            (String) objects[10],
                            (String) objects[11],
                            (String) objects[12]
                    );

                    result.add(locationProfileInfoDTO);
                }

                return result;
            }
        };
    }






    //Added for Phase2_LES-27 Changes
    /**
     * Returns a Criterion that can be used to find  details of appointments passed from the UI
     * @param
     *
     * @param
     *
     * @return Criterion instance
     *
     *      */

    //GSSP-287 Code changes.
    public static Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> findInstructorAvailableHours(
            final Long instructorId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<Map<String, List<InstructorAvailableHoursDTO>>>() {

            @Override
            public Map<String, List<InstructorAvailableHoursDTO>> get(EntityManager entityManager, int pFetchMode) {

                String sb = "select to_char(t.start_time, 'yyyy-MM-dd') as date1 , " +
                        "to_char(t.start_time,'HH24:Mi') as start_time, to_char(t.end_time,'HH24:Mi') as end_time from appointment t " +
                        "where t.start_time >= :pStartTime and t.end_time <= :pEndTime and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        "and t.instructor_id in (:instructorId) " +
                        "minus " +
                        "select to_char(t.start_time, 'yyyy-MM-dd') as date1 , " +
                        "to_char(t.start_time,'HH24:Mi') as start_time, " +
                        "to_char(t.end_time,'HH24:Mi') as end_time " +
                        "from appointment t ,onetime ot " +
                        "where " +
                        "t.instructor_id = ot.instructor_id " +
                        "and t.start_time > :pStartTime " +
                        "and t.end_time <= :pEndTime " +
                        "and t.start_time >= ot.start_time " +
                        "and t.end_time <= ot.end_time " +
                        "and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        "and t.instructor_id in (:instructorId) order by date1 desc " ;

                // Create and configure NativeQuery
                Query query = entityManager.createNativeQuery(sb);
                query.setParameter("instructorId", instructorId);
                query.setParameter("pStartTime", pStartTime);
                query.setParameter("pEndTime", pEndTime);

                // Specify the result type
                query.unwrap(NativeQuery.class)
                        .addScalar("date1", StandardBasicTypes.STRING)
                        .addScalar("start_time", StandardBasicTypes.STRING)
                        .addScalar("end_time", StandardBasicTypes.STRING);

                // Execute query and process results
                List<Object[]> resultList = query.getResultList();
                Map<String, List<InstructorAvailableHoursDTO>> cache = new HashMap<>();
                // DateTimeFormatter df1 =  DateTimeFormatter.ofPattern(DateTimeUtil.HOURS_PATTERN);

                org.joda.time.format.DateTimeFormatter df1 = DateTimeFormat.forPattern("HH:mm");

                for (Object[] objects : resultList) {
                    String instructorDate = (String) objects[0];
                    List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList = cache.computeIfAbsent(instructorDate, k -> new ArrayList<>());

                    String startTime = (String) objects[1];
                    String endTime = (String) objects[2];

                    // Parse using java.time.LocalTime
                    LocalTime javaStartTime = LocalTime.parse(startTime);
                    LocalTime javaEndTime = LocalTime.parse(endTime);

                    // Convert to org.joda.time.LocalTime
                    org.joda.time.LocalTime jodaStartTime = new org.joda.time.LocalTime(javaStartTime.getHour(), javaStartTime.getMinute());
                    org.joda.time.LocalTime jodaEndTime = new org.joda.time.LocalTime(javaEndTime.getHour(), javaEndTime.getMinute());

                    InstructorAvailableHoursDTO instructorAvailableHoursDTO = new InstructorAvailableHoursDTO(
                            jodaStartTime,
                            jodaEndTime
                    );

                    instructorAvailableHoursDTOList.add(instructorAvailableHoursDTO);
                }

                return cache;
            }
        };
    }


    //GSSP-287 Code changes.
    public static Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> findInstructorAvailableHoursInsAVL(
            final Long instructorId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<Map<String, List<InstructorAvailableHoursDTO>>>() {

            @Override
            public Map<String, List<InstructorAvailableHoursDTO>> get(EntityManager entityManager, int pFetchMode) {


                String sb = "select to_char(t.start_time, 'yyyy-MM-dd') as date1 , " +
                        "to_char(t.start_time,'HH24:Mi') as start_time, to_char(t.end_time,'HH24:Mi') as end_time from appointment t " +
                        "where t.start_time >= :pStartTime and t.end_time <= :pEndTime and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        "and t.instructor_id in (:instructorId) " +
                        "minus " +
                        "select to_char(t.start_time, 'yyyy-MM-dd') as date1 , " +
                        "to_char(t.start_time,'HH24:Mi') as start_time, " +
                        "to_char(t.end_time,'HH24:Mi') as end_time " +
                        "from appointment t ,onetime ot " +
                        "where " +
                        "t.instructor_id = ot.instructor_id " +
                        "and t.start_time > :pStartTime " +
                        "and t.end_time <= :pEndTime " +
                        "and t.start_time >= ot.start_time " +
                        "and t.end_time <= ot.end_time " +
                        "and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        "and t.instructor_id in (:instructorId) order by date1 desc " ;


                // Create and configure NativeQuery
                Query query = entityManager.createNativeQuery(sb);
                query.setParameter("instructorId", instructorId);
                query.setParameter("pStartTime", pStartTime);
                query.setParameter("pEndTime", pEndTime);

                // Specify the result type
                query.unwrap(NativeQuery.class)
                        .addScalar("date1", StandardBasicTypes.STRING)
                        .addScalar("start_time", StandardBasicTypes.STRING)
                        .addScalar("end_time", StandardBasicTypes.STRING);

                // Execute query and process results
                List<Object[]> resultList = query.getResultList();
                Map<String, List<InstructorAvailableHoursDTO>> cache = new HashMap<>();
                DateTimeFormatter df1 = DateTimeFormat.forPattern("HH:mm");

                for (Object[] objects : resultList) {
                    String instructorDate = (String) objects[0];
                    List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList = cache.computeIfAbsent(instructorDate, k -> new ArrayList<>());

                    String startTime = (String) objects[1];
                    String endTime = (String) objects[2];
                    InstructorAvailableHoursDTO instructorAvailableHoursDTO = new InstructorAvailableHoursDTO(
                            org.joda.time.LocalTime.parse(startTime, df1),
                            org.joda.time.LocalTime.parse(endTime, df1)
                    );

                    instructorAvailableHoursDTOList.add(instructorAvailableHoursDTO);
                }

                return cache;
            }
        };
    }

    //Added for Phase2_LES-27 Changes
    /**
     * Returns a Criterion that can be used to find time off details for instructors
     *
     * @param
     *
     * @return Criterion instance
     */


    public static Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> getTimeOffForInstructors(
            final Long[] pInstructorIds, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<Map<String, List<InstructorAvailableHoursDTO>>>() {

            @Override
            public Map<String, List<InstructorAvailableHoursDTO>> get(EntityManager entityManager, int pFetchMode) {

                String sb =  "select  instructor_id, to_char(start_time,'yyyy-MM-dd') as start_date, to_char(end_time,'yyyy-MM-dd') as end_date,"
                        + "to_char(start_time,'HH24:Mi') as start_time , "+
                        " to_char(end_time,'HH24:Mi') as end_time from timeoff  " +
                        "  where ( "
                        + "(start_time  >= :pStartTime and start_time <= :pEndTime) or"
                        + "(end_time    >= :pStartTime and end_time   <= :pEndTime)   or "
                        + "(:pStartTime >= start_time  and :pEndTime  <=  end_time)"
                        + ") "
                        + "and  instructor_id in (:instructorIds) ";
                // Create and configure NativeQuery
                Query query = entityManager.createNativeQuery(sb);
                query.setParameter("pStartTime", new java.sql.Timestamp(pStartTime.getTime()));
                query.setParameter("pEndTime", new java.sql.Timestamp(pEndTime.getTime()));
                query.setParameter("instructorIds", pInstructorIds);

                // Specify the result type
                query.unwrap(NativeQuery.class)
                        .addScalar("instructor_id", StandardBasicTypes.STRING)
                        .addScalar("start_date", StandardBasicTypes.STRING)
                        .addScalar("end_date", StandardBasicTypes.STRING)
                        .addScalar("start_time", StandardBasicTypes.STRING)
                        .addScalar("end_time", StandardBasicTypes.STRING);

                // Create SimpleDateFormat for parsing
                SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
                SimpleDateFormat timeFormatter = new SimpleDateFormat("HH:mm", Locale.ENGLISH);

                // Execute query and process results
                List<Object[]> resultList = query.getResultList();
                Map<String, List<InstructorAvailableHoursDTO>> cache = new HashMap<>();

                for (Object[] objects : resultList) {
                    String instructor = (String) objects[0];
                    List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList = cache.computeIfAbsent(instructor, k -> new ArrayList<>());

                    String startDate = (String) objects[1];
                    String endDate = (String) objects[2];
                    String startTime = (String) objects[3];
                    String endTime = (String) objects[4];

                    DateTime startDateTime = DateTime.parse(startDate);
                    DateTime endDateTime = DateTime.parse(endDate);
                    org.joda.time.LocalTime startLocalTime = org.joda.time.LocalTime.parse(startTime, DateTimeFormat.forPattern("HH:mm"));
                    org.joda.time.LocalTime endLocalTime = org.joda.time.LocalTime.parse(endTime, DateTimeFormat.forPattern("HH:mm"));

                    InstructorAvailableHoursDTO instructorAvailableHoursDTO = new InstructorAvailableHoursDTO(
                            startLocalTime,
                            endLocalTime,
                            startDateTime,
                            endDateTime
                    );

                    instructorAvailableHoursDTOList.add(instructorAvailableHoursDTO);
                }

                return cache;
            }
        };
    }




    /**
     * Returns a Criterion that can be used to find time off details for instructors
     *
     * @param
     *
     * @return Criterion instance
     */
    public static Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> getTimeOffForInstructorsInsAVL(
            Long[] pInstructorIds, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<Map<String, List<InstructorAvailableHoursDTO>>>() {

            @Override
            public Map<String, List<InstructorAvailableHoursDTO>> get(EntityManager entityManager, int pFetchMode) {

                String sb =  "select  instructor_id, to_char(start_time,'yyyy-MM-dd') as start_date, to_char(end_time,'yyyy-MM-dd') as end_date,"
                        + "to_char(start_time,'HH24:Mi') as start_time , "+
                        " to_char(end_time,'HH24:Mi') as end_time from timeoff  " +
                        "  where ( "
                        + "(start_time  >= :pStartTime and start_time <= :pEndTime) or"
                        + "(end_time    >= :pStartTime and end_time   <= :pEndTime)   or "
                        + "(:pStartTime >= start_time  and :pEndTime  <=  end_time)"
                        + ") "
                        + "and  instructor_id in (:instructorIds) ";

                // Create and configure NativeQuery
                Query query = entityManager.createNativeQuery(sb);
                query.setParameter("pStartTime", new java.sql.Timestamp(pStartTime.getTime()));
                query.setParameter("pEndTime", new java.sql.Timestamp(pEndTime.getTime()));
                query.setParameter("instructorIds", Arrays.asList(pInstructorIds));

                // Specify the result type
                query.unwrap(NativeQuery.class)
                        .addScalar("instructor_id", StandardBasicTypes.STRING)
                        .addScalar("start_date", StandardBasicTypes.STRING)
                        .addScalar("end_date", StandardBasicTypes.STRING)
                        .addScalar("start_time", StandardBasicTypes.STRING)
                        .addScalar("end_time", StandardBasicTypes.STRING);

                // Create DateTimeFormatter for parsing
                DateTimeFormatter dateFormatter = DateTimeFormat.forPattern("yyyy-MM-dd");
                DateTimeFormatter timeFormatter = DateTimeFormat.forPattern("HH:mm");

                // Execute query and process results
                List<Object[]> resultList = query.getResultList();
                Map<String, List<InstructorAvailableHoursDTO>> cache = new HashMap<>();

                for (Object[] objects : resultList) {
                    String instructor = (String) objects[0];
                    List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList = cache.computeIfAbsent(instructor, k -> new ArrayList<>());

                    String startDate = (String) objects[1];
                    String endDate = (String) objects[2];
                    String startTime = (String) objects[3];
                    String endTime = (String) objects[4];

                    org.joda.time.DateTime startDateTime = org.joda.time.DateTime.parse(startDate, dateFormatter);
                    org.joda.time.DateTime endDateTime = org.joda.time.DateTime.parse(endDate, dateFormatter);
                    org.joda.time.LocalTime startLocalTime = org.joda.time.LocalTime.parse(startTime, timeFormatter);
                    org.joda.time.LocalTime endLocalTime = org.joda.time.LocalTime.parse(endTime, timeFormatter);

                    InstructorAvailableHoursDTO instructorAvailableHoursDTO = new InstructorAvailableHoursDTO(
                            startLocalTime,
                            endLocalTime,
                            startDateTime,
                            endDateTime
                    );

                    instructorAvailableHoursDTOList.add(instructorAvailableHoursDTO);
                }

                return cache;
            }
        };
    }

    //Added for Scheduling Phase II - Validation Service

    public static Criterion<Appointment, LessonSeries> findAppointmentSeriesByCustomerId(final Long customerId) {

        return new AppointmentCriterion<LessonSeries>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<LessonSeries> search(EntityManager entityManager, int pFetchMode) {
                String sb = "select distinct(t.appointment_series_id) as seriesId,t.activity_id, " +
                        " (select min(to_char(t2.start_time,'YYYY-MM-DD')) from appointment t2 " +
                        " where t2.appointment_series_id = t.appointment_series_id and decode(t2.canceled, null, 'N', t2.canceled) != 'Y') as series_start_date,"+
                        " lp.profile_id as profile_id, a.activity_name from customer c"+
                        " join customer_appointment_series cas on c.customer_id = cas.customer_id "+
                        " join appointment_series ap on ap.appointment_series_id = cas.appointment_series_id"+
                        " join appointment t  on t.appointment_series_id = ap.appointment_series_id" +
                        " join location_profile lp on lp.profile_id = ap.profile_id" +
                        " join activity a on a.activity_id = t.activity_id" +
                        " where c.customer_id = :customerId 	" +
                        " and (select count(appointment_id) from appointment ti where t.appointment_series_id = ti.appointment_series_id  " +
                        " and to_timestamp(to_char(t.START_TIME, 'YYYY-MM-DD hh24:MI:SS'), 'YYYY-MM-DD hh24:MI:SS') " +
                        " > to_timestamp(to_char(CURRENT_TIMESTAMP AT TIME ZONE lp.TZ , 'YYYY-MM-DD hh24:MI:SS'), 'YYYY-MM-DD hh24:MI:SS') and t.canceled in ('N','H') ) >0 "
                        + " and a.service_id !=0 " ;

                NativeQuery query = (NativeQuery) entityManager.createNativeQuery(sb);

                query.setParameter("customerId", customerId);

                query.addScalar("seriesId", StandardBasicTypes.LONG);
                query.addScalar("activity_id", StandardBasicTypes.STRING);
                query.addScalar("series_start_date", StandardBasicTypes.STRING);
                query.addScalar("profile_id", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();
                List<LessonSeries> lessonSeriesList = new ArrayList<>();

                while (scroll.next()) {
                    Object[] objects = (Object[]) scroll.get();

                    LessonSeries lessonSeries = new LessonSeries(
                            (Long) objects[0],  // seriesId
                            (String) objects[2], // series_start_date
                            (String) objects[4], // activity_name
                            (String) objects[1], // activity_id
                            (String) objects[3]  // profile_id
                    );

                    lessonSeriesList.add(lessonSeries);
                }

                scroll.close();

                return lessonSeriesList;
            }
        };
    }



    //Added for Scheduling Phase II - update lesson service-LES 7
    /**
     * Returns a Criterion that can be used to find  person details based on bade Number
     *
     * @param external_id String
     *
     * @return Criterion instance
     */

    public static AppointmentCriterion<Long> findPersonIDByExternalId(
            final String external_id) {

        return new AppointmentCriterion<Long>() {

            @Override
            public Long get(EntityManager entityManager, int pFetchMode) {
                String sb = " select person_id from customer " +
                        " where external_id= :external_id";

                // Create and configure NativeQuery
                NativeQuery<?> query = (NativeQuery<?>) entityManager.createNativeQuery(sb);
                query.setParameter("external_id", external_id);
                query.addScalar("person_id", StandardBasicTypes.BIG_INTEGER);

                Long personID = null;
                try (ScrollableResults scroll = query.scroll()) {
                    if (scroll.next()) {
                        // Cast the result to a Number and then to Long
                        personID = ((Number) scroll.get()).longValue();
                    }
                } // The ScrollableResults is automatically closed here

                return personID;
            }
        };
    }



    //Added for Scheduling Phase II - Lesson update service LES-7
    /**
     * Returns a Criterion that can be used to find  person details based on bade Number
     *
     * @param  String
     *
     * @return Criterion instance
     */
    //GSSP- 253 removed the location and phone from the query and fetching these details from the sendEmailForModifySingleAppt methods
    public static Criterion<Appointment, List<Map<String, Object>>> getEmailIdsByCustomersExternalId(
            final String[] customerMemberId, final String customerName, final Long profileId) {

        return new AppointmentCriterion<List<Map<String, Object>>>() {

            @Override
            public List<Map<String, Object>> get(EntityManager entityManager, int pFetchMode) {
                List<Map<String, Object>> emailBodies = new ArrayList<>();

                String sb="select Upper(p.email) as email,p.first_name as firstname,p.last_name as lastname "+
                        "from person p "+
                        "join customer c on p.person_id = c.person_id "+
                        "where c.EXTERNAL_ID in (:customerMemberId) and p.email is not null Union "+
                        "select Upper(ce.CUSTOMER_EMAIL) as email,p.first_name as firstname,p.last_name as lastname "+
                        "from person p "+
                        "join customer c on c.person_id=p.person_id "+
                        "join CUSTOMER_EMAIL ce on ce.external_customer_id=c.external_id "+
                        "where ce.EXTERNAL_CUSTOMER_ID in (:customerMemberId) and ce.CUSTOMER_EMAIL is not null";

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb);
                query.setParameterList("customerMemberId", customerMemberId);
                query.addScalar("email", StandardBasicTypes.STRING);
                query.addScalar("firstname", StandardBasicTypes.STRING);
                query.addScalar("lastname", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();
                Set<String> emailSet = new HashSet<>();

                while (scroll.next()) {
                    Object[] row = (Object[]) scroll.get();
                    String email = (String) row[0];
                    String firstName = (String) row[1];
                    String lastName = (String) row[2];
                    String customerName = firstName + " " + lastName;

                    if (email != null && !emailSet.contains(email)) {
                        emailSet.add(email);

                        Map<String, Object> dataMap = new HashMap<>();
                        dataMap.put(AppConstants.EMAIL_TYPE_TO, new String[]{email});
                        dataMap.put("customerName", customerName.trim());

                        emailBodies.add(dataMap);
                    }
                }
                scroll.close();

                return emailBodies;
            }
        };
    }




    //Added for Scheduling Phase II - Update Service-LES 7
    /**
     *
     * @param  Long
     *
     * @return Criterion instance
     */

    public static AppointmentCriterion<Long> checkInstructorDBRecord(final Long instructorId) {

        return new AppointmentCriterion<Long>() {

            @Override
            public Long get(EntityManager entityManager, int pFetchMode) {
                String sb = " select count(*) as instructorCount from instructor " +
                        " where instructor_id = :instructorId";

                NativeQuery<Long> query = (NativeQuery<Long>) entityManager.createNativeQuery(sb);
                query.setParameter("instructorId", instructorId);
                query.addScalar("instructorCount",  StandardBasicTypes.LONG);

                Long count = query.uniqueResult();

                return count;
            }
        };
    }

    //Added for Scheduling Phase II - Update Service-LES 7
    /**
     *
     * @param
     *
     * @return Criterion instance
     */

    public static AppointmentCriterion<Long> checkCustomerExternalIdDBRecord(final String EXTERNAL_ID) {

        return new AppointmentCriterion<Long>() {

            @Override
            public Long get(EntityManager entityManager, int pFetchMode) {
                String sb = " select count(*) as customerCount from customer " +
                        " where EXTERNAL_ID = :EXTERNAL_ID";

                NativeQuery<Long> query = (NativeQuery<Long>) entityManager.createNativeQuery(sb);
                query.setParameter("EXTERNAL_ID", EXTERNAL_ID);
                query.addScalar("customerCount",  StandardBasicTypes.LONG);

                Long count = query.uniqueResult();

                return count;
            }
        };
    }


    //Added for Scheduling Phase II - update lesson service-LES 7
    /**
     * Returns a Criterion that can be used to find  person details based on bade Number
     *
     * @param external_id String
     *
     * @return Criterion instance
     */

    public static AppointmentCriterion<String> findTimeZoneByAptID(final String appointment_id) {

        return new AppointmentCriterion<String>() {

            @Override
            public String get(EntityManager entityManager, int pFetchMode) {
                String sb = " select lp.tz as timeZone from  location_profile lp " +
                        " join appointment a on a.profile_id= lp.profile_id " +
                        " where appointment_id= :appointment_id ";

                NativeQuery<String> query = (NativeQuery<String>) entityManager.createNativeQuery(sb);
                query.setParameter("appointment_id", appointment_id);
                query.addScalar("timeZone", StandardBasicTypes.STRING);

                return query.uniqueResult();
            }
        };
    }


    // Code changes done for GSSP-197
    public static AppointmentCriterion<String> findEnvironment() {

        return new AppointmentCriterion<String>() {

            @Override
            public List<String> search(EntityManager entityManager, int pFetchMode) {
                String sb = "select  e.environment_name from environment e" +
                        " where e.environment_id=1";

                NativeQuery<String> query = (NativeQuery<String>) entityManager.createNativeQuery(sb);
                query.addScalar("environment_name", StandardBasicTypes.STRING);

                List<String> result = query.getResultList();

                return result;
            }
        };
    }


    // Added New report GSSP-185
    public static AppointmentCriterion<ActiveStudentsDTO> findActiveStudentsByActivityAndDateTime(
            final long pLocationId, final Date pStartTime, final Date pEndTime, final String pactivityType, final String pInstructorName) {

        return new AppointmentCriterion<ActiveStudentsDTO>() {

            @Override
            public List<ActiveStudentsDTO> search(EntityManager entityManager, int pFetchMode) {
                String fullName = null;

                StringBuilder sb1 = new StringBuilder("select to_char(t.start_time, 'DD-Mon-YYYY') as date1, t.appointment_id as appointment_id,");
                sb1.append(" t.start_time as start_time, t.end_time as end_time, t.canceled as canceled,");
                sb1.append(" (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration,");
                sb1.append(" r.profile_room_name as profile_room_name, a.activity_name as activity_name,");
                sb1.append(" i.instructor_id as instructor_id, p_i.first_name as i_first_name, p_i.last_name as i_last_name,");
                sb1.append(" c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name, p_c.email as student_email");
                sb1.append(" from appointment t");
                sb1.append(" join location l on t.profile_id = l.profile_id");
                sb1.append(" left join appointment_customers a_c on t.appointment_id = a_c.appointment_id");
                sb1.append(" left join customer c on a_c.customer_id = c.customer_id");
                sb1.append(" left join person p_c on c.person_id = p_c.person_id");
                sb1.append(" join instructor i on t.instructor_id = i.instructor_id");
                sb1.append(" left join person p_i on i.person_id = p_i.person_id");
                sb1.append(" left join room r on t.room_id = r.room_id");
                sb1.append(" left join activity a on t.activity_id = a.activity_id");
                sb1.append(" left join availability ay on i.availability_id = ay.availability_id");
                sb1.append(" where l.location_id = :locationId");
                sb1.append(" and t.start_time >= :startTime");
                sb1.append(" and t.end_time <= :endTime");
                sb1.append(" and coalesce(t.canceled, 'N') != 'Y'");

                if (pactivityType != null && !pactivityType.equalsIgnoreCase("0") && !pactivityType.trim().isEmpty()) {
                    sb1.append(" and t.activity_id = :activityType");
                }

                if (pInstructorName != null && !pInstructorName.trim().isEmpty()) {
                    fullName = pInstructorName.replaceAll("[^a-zA-Z0-9]", "").toUpperCase();
                    sb1.append(" and UPPER(REGEXP_REPLACE(p_i.FIRST_name || p_i.Last_name, '[^0-9A-Za-z]', '')) = :fullName");
                }

                sb1.append(" order by to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS')");

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb1.toString());
                query.setParameter("locationId", pLocationId);
                query.setParameter("startTime", pStartTime);
                query.setParameter("endTime", pEndTime);

                if (pactivityType != null && !pactivityType.equalsIgnoreCase("0") && !pactivityType.trim().isEmpty()) {
                    query.setParameter("activityType", pactivityType);
                }

                if (pInstructorName != null && !pInstructorName.trim().isEmpty()) {
                    query.setParameter("fullName", fullName.trim());
                }

                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("appointment_id",  StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("end_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("canceled", StandardBasicTypes.STRING);
                query.addScalar("duration",  StandardBasicTypes.LONG);
                query.addScalar("profile_room_name", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("instructor_id",  StandardBasicTypes.LONG);
                query.addScalar("i_first_name", StandardBasicTypes.STRING);
                query.addScalar("i_last_name", StandardBasicTypes.STRING);
                query.addScalar("customer_id",  StandardBasicTypes.LONG);
                query.addScalar("c_first_name", StandardBasicTypes.STRING);
                query.addScalar("c_last_name", StandardBasicTypes.STRING);
                query.addScalar("student_email", StandardBasicTypes.STRING);

                List<Object[]> resultList = query.getResultList();
                List<ActiveStudentsDTO> result = new ArrayList<>();
                Map<Long, ActiveStudentsDTO> activeStudentsDTOs = new HashMap<>();
                Map<Long, Instructor> instructors = new HashMap<>();
                Map<Long, Customer> customers = new HashMap<>();

                for (Object[] objects : resultList) {
                    String date1 = (String) objects[0];
                    Long appointment_id = (Long) objects[1];
                    Long instructorId = (Long) objects[8];
                    Instructor instructor = instructors.get(instructorId);
                    if (instructor == null) {
                        instructor = new Instructor();
                        instructor.setInstructorId(instructorId);
                        Person person = new Person();
                        person.setFirstName((String) objects[9]);
                        person.setLastName((String) objects[10]);
                        instructor.setPerson(person);
                        instructors.put(instructorId, instructor);
                    }

                    ActiveStudentsDTO activeStudentsDTO = activeStudentsDTOs.get(appointment_id);
                    if (activeStudentsDTO == null) {
                        activeStudentsDTO = new ActiveStudentsDTO(date1, appointment_id, (Date) objects[2], (Date) objects[3], (String) objects[4], (Long) objects[5], (String) objects[6], (String) objects[7], instructor, (String) objects[14]);
                        activeStudentsDTOs.put(appointment_id, activeStudentsDTO);
                        result.add(activeStudentsDTO);
                    }

                    Long customerId = (Long) objects[11];
                    if (customerId == null) continue;
                    Customer customer = customers.get(customerId);
                    if (customer == null) {
                        customer = new Customer();
                        customer.setCustomerId(customerId);
                        Person person = new Person();
                        person.setFirstName((String) objects[12]);
                        person.setLastName((String) objects[13]);
                        customer.setPerson(person);
                        customers.put(customerId, customer);
                    }
                    activeStudentsDTO.addCustomer(customer);
                }

                return result;
            }
        };
    }




    // Added New report GSSP-185
    public static Criterion<Appointment, ActivityDTO> findLessonTypes(final long pLocationId) {

        return new AppointmentCriterion<ActivityDTO>() {

            @Override
            public List<ActivityDTO> search(EntityManager entityManager, int pFetchMode) {

                String sb = "select a.activity_id,a.activity_name from activity a" +
                        " join profile_activity pa on pa.activity_id = a.activity_id" +
                        " join location l on l.profile_id = pa.profile_id"		+
                        " where l.location_id=:locationId and a.service_id=1 and a.activity_id!=20 order by a.activity_name ";

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb);

                query.setParameter("locationId", pLocationId);

                query.addScalar("activity_id",  StandardBasicTypes.LONG);
                query.addScalar("activity_name", StandardBasicTypes.STRING);

                List<Object[]> resultList = query.getResultList();
                List<ActivityDTO> result = new ArrayList<>();

                for (Object[] objects : resultList) {
                    ActivityDTO dto = new ActivityDTO((Long) objects[0], (String) objects[1]);
                    result.add(dto);
                }

                return result;
            }
        };
    }





    // Added New report GSSP-203
    public static Criterion<Appointment, ActiveStudentsDTO> findStudentsCheckInByDateandTime(
            final long pLocationId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<ActiveStudentsDTO>() {

            @Override
            public List<ActiveStudentsDTO> search(EntityManager entityManager, int pFetchMode) {

                StringBuilder sb1 = new StringBuilder("select to_char(t.start_time, 'DD-Mon-YYYY') as date1 ,t.appointment_id as appointment_id,");
                sb1.append(" t.start_time as start_time, t.end_time as end_time, t.canceled as canceled,");
                sb1.append(" (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration,");
                sb1.append(" r.profile_room_name as profile_room_name, a.activity_name as activity_name,");
                sb1.append(" i.instructor_id as instructor_id, p_i.first_name as i_first_name, p_i.last_name as i_last_name,");
                sb1.append(" c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name,p_c.email as student_email  " );
                sb1.append(" from appointment t");
                sb1.append(" join location l on t.profile_id = l.profile_id");
                // GSSP-333 lesson types without customers should not be visible in Student check-in sheet.
                sb1.append( " join appointment_customers a_c on t.appointment_id = a_c.appointment_id");
                sb1.append(" left join customer c on a_c.customer_id = c.customer_id");
                sb1.append( " left join person p_c on c.person_id = p_c.person_id");
                sb1.append(" join instructor i on t.instructor_id = i.instructor_id");
                sb1.append(" left join person p_i on i.person_id = p_i.person_id");
                sb1.append( " left join room r on t.room_id = r.room_id");
                sb1.append(" left join activity a on t.activity_id = a.activity_id");
                sb1.append(" left join availability ay on i.availability_id = ay.availability_id");
                sb1.append(" where l.location_id = :locationId ");
                sb1.append(" and t.start_time >= :startTime ");
                // GSSP-333 lesson types without customers should not be visible in Student check-in sheet.
                sb1.append(" and t.start_time <= :endTime ");
                sb1.append( " and decode(t.canceled, null, 'N', t.canceled) != 'Y' ");
                sb1.append(" order by to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS')");

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb1.toString());

                query.setParameter("locationId", pLocationId);
                query.setParameter("startTime", pStartTime);
                query.setParameter("endTime", pEndTime);

                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("appointment_id",  StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("end_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("canceled", StandardBasicTypes.STRING);
                query.addScalar("duration",  StandardBasicTypes.LONG);
                query.addScalar("profile_room_name", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("instructor_id",  StandardBasicTypes.LONG);
                query.addScalar("i_first_name", StandardBasicTypes.STRING);
                query.addScalar("i_last_name", StandardBasicTypes.STRING);
                query.addScalar("customer_id",  StandardBasicTypes.LONG);
                query.addScalar("c_first_name", StandardBasicTypes.STRING);
                query.addScalar("c_last_name", StandardBasicTypes.STRING);
                query.addScalar("student_email", StandardBasicTypes.STRING);

                List<Object[]> resultList = query.getResultList();
                List<ActiveStudentsDTO> result = new ArrayList<>();
                Map<Long, ActiveStudentsDTO> activeStudentsDTOs = new HashMap<>();
                Map<Long, Instructor> instructors = new HashMap<>();
                Map<Long, Customer> customers = new HashMap<>();

                for (Object[] objects : resultList) {
                    String date1 = (String) objects[0];
                    Long appointmentId = (Long) objects[1];
                    Long instructorId = (Long) objects[8];

                    Instructor instructor = instructors.get(instructorId);
                    if (instructor == null) {
                        instructor = new Instructor();
                        instructor.setInstructorId(instructorId);
                        Person person = new Person();
                        person.setFirstName((String) objects[9]);
                        person.setLastName((String) objects[10]);
                        instructor.setPerson(person);
                        instructors.put(instructorId, instructor);
                    }

                    ActiveStudentsDTO activeStudentsDTO = activeStudentsDTOs.get(appointmentId);
                    if (activeStudentsDTO == null) {
                        activeStudentsDTO = new ActiveStudentsDTO(
                                date1,
                                appointmentId,
                                (Date) objects[2],
                                (Date) objects[3],
                                (String) objects[4],
                                (Long) objects[5],
                                (String) objects[6],
                                (String) objects[7],
                                instructor,
                                (String) objects[14]
                        );
                        activeStudentsDTOs.put(appointmentId, activeStudentsDTO);
                        result.add(activeStudentsDTO);
                    }

                    Long customerId = (Long) objects[11];
                    if (customerId != null) {
                        Customer customer = customers.get(customerId);
                        if (customer == null) {
                            customer = new Customer();
                            customer.setCustomerId(customerId);
                            Person person = new Person();
                            person.setFirstName((String) objects[12]);
                            person.setLastName((String) objects[13]);
                            customer.setPerson(person);
                            customers.put(customerId, customer);
                        }
                        activeStudentsDTO.addCustomer(customer);
                    }
                }

                return result;
            }
        };
    }
    //GSSP-205 added for new report
    //GSSP-271 changes made for student Inactive
    public static Criterion<Appointment, InActiveStudentsDTO> findInActiveStudentsByDate(
            final String locationId, String externalId) {

        return new AppointmentCriterion<InActiveStudentsDTO>() {

            @Override
            public List<InActiveStudentsDTO> search(EntityManager entityManager, int pFetchMode) {
                String sb = "select c2.customer_id as customer_id,p.FIRST_NAME as c_first_name,p.LAST_NAME as c_last_name,p.EMAIL as Student_Email,p.PHONE as Student_Phone from customer c2 " +
                        " join person p on p.person_id=c2.person_id " +
                        " where c2.customer_id not in " +
                        " (select distinct(c.customer_id) from customer c " +
                        " join appointment_customers ac on c.customer_id = ac.customer_id " +
                        " join appointment a on a.APPOINTMENT_ID=ac.APPOINTMENT_ID " +
                        " where a.CANCELED='N' and a.START_TIME>sysdate-180 and c.external_id like :locationId and c.customer_status_id in (0,20)) " +
                        " and c2.external_id like :locationId and c2.customer_status_id in (0,20)  order by (p.FIRST_NAME || p.last_name) ";

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb);

                query.setParameter("locationId", locationId + "%");

                query.addScalar("customer_id",  StandardBasicTypes.LONG);
                query.addScalar("c_first_name", StandardBasicTypes.STRING);
                query.addScalar("c_last_name", StandardBasicTypes.STRING);
                query.addScalar("Student_Email", StandardBasicTypes.STRING);
                query.addScalar("Student_Phone", StandardBasicTypes.STRING);

                List<Object[]> resultList = query.getResultList();
                List<InActiveStudentsDTO> result = new ArrayList<>();

                for (Object[] objects : resultList) {
                    Long customerId = (Long) objects[0];
                    String firstName = (String) objects[1];
                    String lastName = (String) objects[2];
                    String email = (String) objects[3];
                    String phone = (String) objects[4];

                    InActiveStudentsDTO dto = new InActiveStudentsDTO(firstName, lastName, email, phone, externalId);
                    result.add(dto);
                }

                return result;
            }
        };
    }

    public static AppointmentCriterion<String> logService(
            final ServiceLogger serviceLogger) {
        return new AppointmentCriterion<String>() {
            @Override
            public String get(EntityManager entityManager, int pFetchMode) {
                entityManager.persist(serviceLogger);
                return "";             }
        };

    }

    /**
     * Added for New Excel Report
     *
     *
     * @param startDate
     * @param endDate
     * @return
     */
    // GSSP-213 new report
    public static Criterion<Appointment, ExportDetailsDTO> findProfileByDate(
            final Map<String, Boolean> daysofWeek, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<ExportDetailsDTO>() {

            @Override
            public List<ExportDetailsDTO> search(EntityManager entityManager, int pFetchMode) {

                DateFormat outputFormatter = new SimpleDateFormat("MM/dd/yyyy");

                final String comparStartDate = outputFormatter.format(pStartTime);
                final String comparEndDate = outputFormatter.format(pEndTime);

                StringBuilder sb = new StringBuilder()
                        .append("select ttv.profile3 as profileId, ")
                        .append("nvl(actvc.appointment_count,0) as active_appointments, ")
                        .append("nvl(actvc.active_student_count,0) as activestudents, ")
                        .append("nvl(ctvc.canceled_appointment_count,0) as canceled_appointments_count, ")
                        .append("ttv.location_name as location, ttv.store_number as store_number, ")
                        .append("ttv.service_count as service_Count, ttv.room_count as room_Count, ")
                        .append("ttv.activity_count as activity_Count, avl.sundayTime as sundayTime, ")
                        .append("avl.mondayTime as mondayTime, avl.tuesdayTime as tuesdayTime, ")
                        .append("avl.wednesdayTime as wednesdayTime, avl.thursdayTime as thursdayTime, ")
                        .append("avl.fridayTime as fridayTime, avl.saturdayTime as saturdayTime, ")
                        .append("nvl(tc.total_conflicts,0) as totalConflicts, ")
                        .append("nvl(tiv.total_instructor,0) as total_instructors ")
                        .append("from (select l.profile_id as profile3, ")
                        .append("l.location_name as Location_name, l.EXTERNAL_ID as Store_Number, ")
                        .append("count(distinct(ps.service_id)) as service_count, ")
                        .append("count(distinct(r.room_id)) as room_count, ")
                        .append("count(distinct(pa.activity_id)) as activity_count ")
                        .append("from location l ")
                        .append("left join location_profile lp on l.profile_id = lp.profile_id ")
                        .append("left join profile_service ps on l.profile_id = ps.profile_id ")
                        .append("left join room r on l.profile_id = r.profile_id ")
                        .append("left join profile_activity pa on l.profile_id = pa.profile_id ")
                        .append("left join availability a on lp.availability_id = a.availability_id ")
                        .append("where lp.enabled='Y' ")
                        .append("and r.Enabled ='Y' ")
                        .append("group by l.profile_id, l.location_name, l.external_id ")
                        .append("order by l.location_name) ttv ")
                        .append("left outer join (select l.profile_id as profile4, ")
                        .append("to_char(a.sunday_start_time, 'hh.mi am') || ' - ' || to_char(a.sunday_end_time, 'hh.mi.ss am') as sundayTime, ")
                        .append("to_char(a.monday_start_time, 'hh.mi am') || ' - ' || to_char(a.monday_end_time, 'hh.mi.ss am') as mondayTime, ")
                        .append("to_char(a.tuesday_start_time, 'hh.mi am') || ' - ' || to_char(a.tuesday_end_time, 'hh.mi.ss am') as tuesdayTime, ")
                        .append("to_char(a.wednesday_start_time, 'hh.mi am') || ' - ' || to_char(a.wednesday_end_time, 'hh.mi.ss am') as wednesdayTime, ")
                        .append("to_char(a.thursday_start_time, 'hh.mi am') || ' - ' || to_char(a.thursday_end_time, 'hh.mi.ss am') as thursdayTime, ")
                        .append("to_char(a.friday_start_time, 'hh.mi am') || ' - ' || to_char(a.friday_end_time, 'hh.mi.ss am') as fridayTime, ")
                        .append("to_char(a.saturday_start_time, 'hh.mi am') || ' - ' || to_char(a.saturday_end_time, 'hh.mi.ss am') as saturdayTime ")
                        .append("from location l ")
                        .append("join location_profile lp on l.profile_id = lp.profile_id ")
                        .append("join AVAILABILITY a on a.availability_id = lp.availability_id) avl ")
                        .append("left outer join (select l.profile_id as profile6, ")
                        .append("count(distinct(instructor_id)) as total_instructor ")
                        .append("from location l ")
                        .append("join instructor i on l.location_id = i.location_id ")
                        .append("join availability a on a.availability_id = i.availability_id ")
                        .append("where i.enabled='Y' and i.status='A' and (");

                if (daysofWeek.containsKey(AppConstants.WEEKEND_CHOSE[0])) {
                    sb.append("a.sunday_start_time is not null or ");
                }
                if (daysofWeek.containsKey(AppConstants.WEEKEND_CHOSE[1])) {
                    sb.append("a.monday_start_time is not null or ");
                }
                if (daysofWeek.containsKey(AppConstants.WEEKEND_CHOSE[2])) {
                    sb.append("a.tuesday_start_time is not null or ");
                }
                if (daysofWeek.containsKey(AppConstants.WEEKEND_CHOSE[3])) {
                    sb.append("a.wednesday_start_time is not null or ");
                }
                if (daysofWeek.containsKey(AppConstants.WEEKEND_CHOSE[4])) {
                    sb.append("a.thursday_start_time is not null or ");
                }
                if (daysofWeek.containsKey(AppConstants.WEEKEND_CHOSE[5])) {
                    sb.append("a.friday_start_time is not null or ");
                }
                if (daysofWeek.containsKey(AppConstants.WEEKEND_CHOSE[6])) {
                    sb.append("a.saturday_start_time is not null or ");
                }

                // Remove the last "or"
                int lastIndexOfOr = sb.lastIndexOf("or");
                if (lastIndexOfOr != -1) {
                    sb.delete(lastIndexOfOr, lastIndexOfOr + 2);
                }

                sb.append(") group by profile_id) tiv ")
                        .append("left outer join (select l.profile_id as profile1, ")
                        .append("count(distinct(ac.customer_id)) as active_student_count, ")
                        .append("count(ap.appointment_id) as appointment_count ")
                        .append("from appointment ap ")
                        .append("join appointment_customers ac on ap.appointment_id = ac.appointment_id ")
                        .append("join location l on l.profile_id = ap.profile_id ");

                if (comparStartDate.equals(comparEndDate)) {
                    sb.append("where trunc(ap.start_time) = :startTime ")
                            .append("and trunc(ap.end_time) = :endTime ")
                            .append("and ap.canceled = 'N' ")
                            .append("group by l.profile_id) actvc ");
                } else {
                    sb.append("where trunc(ap.start_time) >= :startTime ")
                            .append("and trunc(ap.end_time) <= :endTime ")
                            .append("and ap.canceled = 'N' ")
                            .append("group by l.profile_id) actvc ");
                }

                sb.append("left outer join (select l.profile_id as profile2, ")
                        .append("count(ap.appointment_id) as canceled_appointment_count ")
                        .append("from appointment ap ")
                        .append("join location l on l.profile_id = ap.profile_id ");

                if (comparStartDate.equals(comparEndDate)) {
                    sb.append("where trunc(ap.start_time) = :startTime ")
                            .append("and trunc(ap.end_time) = :endTime ")
                            .append("and ap.canceled = 'Y' ")
                            .append("group by l.profile_id) ctvc ");
                } else {
                    sb.append("where trunc(ap.start_time) >= :startTime ")
                            .append("and trunc(ap.end_time) <= :endTime ")
                            .append("and ap.canceled = 'Y' ")
                            .append("group by l.profile_id) ctvc ");
                }

                sb.append("left outer join (select t.profile_id as profile5, ")
                        .append("count(t.appointment_id) as total_conflicts ")
                        .append("from appointment t ")
                        .append("join appointment t2 on t.profile_id = t2.profile_id ")
                        .append("and (t.room_id = t2.room_id or t.instructor_id = t2.instructor_id) ")
                        .append("and t.appointment_id != t2.appointment_id ");

                if (comparStartDate.equals(comparEndDate)) {
                    sb.append("and trunc(t.start_time) = :startTime ")
                            .append("and trunc(t2.start_time) = :startTime ")
                            .append("and trunc(t.end_time) = :endTime ")
                            .append("and trunc(t2.end_time) = :endTime ");
                } else {
                    sb.append("and trunc(t.start_time) >= :startTime ")
                            .append("and trunc(t2.start_time) >= :startTime ")
                            .append("and trunc(t.end_time) <= :endTime ")
                            .append("and trunc(t2.end_time) <= :endTime ");
                }

                sb.append("and (t2.start_time >= t.start_time and t2.start_time < t.end_time ")
                        .append("or t2.end_time > t.start_time and t2.end_time <= t.end_time ")
                        .append("or t.start_time >= t2.start_time and t.end_time <= t2.end_time) ")
                        .append("where t.canceled = 'N' ")
                        .append("and t2.canceled = 'N' ")
                        .append("group by t.profile_id) tc ")
                        .append("on tc.profile5 = ctvc.profile2 ")
                        .append("on ctvc.profile2 = actvc.profile1 ")
                        .append("on actvc.profile1 = tiv.profile6 ")
                        .append("on tiv.profile6 = avl.profile4 ")
                        .append("on avl.profile4 = ttv.profile3");

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb.toString());
                query.setParameter("startTime", pStartTime);
                query.setParameter("endTime", pEndTime);

                query.addScalar("profileId",  StandardBasicTypes.LONG);
                query.addScalar("active_appointments",  StandardBasicTypes.LONG);
                query.addScalar("activestudents",  StandardBasicTypes.LONG);
                query.addScalar("canceled_appointments_count",  StandardBasicTypes.LONG);
                query.addScalar("location", StandardBasicTypes.STRING);
                query.addScalar("store_number",  StandardBasicTypes.LONG);
                query.addScalar("service_Count",  StandardBasicTypes.LONG);
                query.addScalar("room_Count",  StandardBasicTypes.LONG);
                query.addScalar("activity_Count",  StandardBasicTypes.LONG);
                query.addScalar("sundayTime", StandardBasicTypes.STRING);
                query.addScalar("mondayTime", StandardBasicTypes.STRING);
                query.addScalar("tuesdayTime", StandardBasicTypes.STRING);
                query.addScalar("wednesdayTime", StandardBasicTypes.STRING);
                query.addScalar("thursdayTime", StandardBasicTypes.STRING);
                query.addScalar("fridayTime", StandardBasicTypes.STRING);
                query.addScalar("saturdayTime", StandardBasicTypes.STRING);
                query.addScalar("totalConflicts",  StandardBasicTypes.LONG);
                query.addScalar("total_instructors",  StandardBasicTypes.LONG);

                List<Object[]> results = query.getResultList();
                List<ExportDetailsDTO> result = new ArrayList<>();

                for (Object[] objects : results) {
                    Long profileId = (Long) objects[0];
                    Long activeAppointments = (Long) objects[1];
                    Long activeStudents = (Long) objects[2];
                    Long canceledAppointmentsCount = (Long) objects[3];
                    String location = (String) objects[4];
                    Long storeNumber = (Long) objects[5];
                    Long serviceCount = (Long) objects[6];
                    Long roomCount = (Long) objects[7];
                    Long activityCount = (Long) objects[8];
                    String[] weekList = new String[7];
                    for (int i = 9; i < 16; i++) {
                        weekList[i - 9] = (String) objects[i];
                    }
                    String studioTiming = formStudioHours(daysofWeek, weekList);

                    Long totalConflicts = (Long) objects[16];
                    Long totalInstructors = (Long) objects[17];

                    ExportDetailsDTO dto = new ExportDetailsDTO(profileId, activeAppointments, activeStudents, canceledAppointmentsCount,
                            location, storeNumber, serviceCount, roomCount, activityCount, studioTiming, totalConflicts, totalInstructors);
                    result.add(dto);
                }

                return result;
            }
        };
    }


    private static String formStudioHours(Map<String,Boolean> daysofWeek,String[] weekList)
    {
        StringBuffer studiHours = new StringBuffer();



        for(int i= 0 ; i < 7; i++)
        {

            if(!weekList[i].equalsIgnoreCase(" - "))
            {

                if(daysofWeek.containsKey(AppConstants.WEEKEND_CHOSE[i]))
                {
                    studiHours.append(AppConstants.WEEKEND_CHOSE[i] + ":");
                    studiHours.append(weekList[i]);
                    studiHours.append(System.lineSeparator());
                }
            }

        }

        return studiHours.toString();
    }
    public static AppointmentCriterion<String> logService1(
            final AppointmentLog appointmentLog) {
        return new AppointmentCriterion<String>() {
            @Override
            public String get(EntityManager entityManager, int pFetchMode) {
                entityManager.persist(appointmentLog);
                return "";             }
        };

    }
    //GSSP-250
    public static AppointmentCriterion<String> cancelService(
            final AppointmentCancel appointmentCancel) {
        return new AppointmentCriterion<String>() {
            @Override
            public String get(EntityManager entityManager, int pFetchMode) {

                entityManager.persist(appointmentCancel);
                return "";             }
        };

    }
    /**
     * Added for New Report GSSP-210
     *

     * @return
     */
    public static Criterion<Appointment, AppointmentHistoryDTO> findAppointmentHistory(
            final Date pStartDate, final Date pEndDate, final String pInputExternalId) {

        return new AppointmentCriterion<AppointmentHistoryDTO>() {

            @Override
            public List<AppointmentHistoryDTO> search(EntityManager entityManager, int pFetchMode) {

                DateFormat outputFormatter = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_HIPHEN);

                String comparStartDate = outputFormatter.format(pStartDate);
                String comparEndDate = outputFormatter.format(pEndDate);

                String sb = " select al.APPOINTMENT_ID as APPOINTMENT_ID,(pu.FIRST_NAME||' '||pu.LAST_NAME) as Updated_By,to_char(al.UPDATED,'MM/DD/YYYY hh:mi.ss am') as Updated_timestamp, " +
                        " (p_c.FIRST_NAME||' '||p_c.LAST_NAME) as customer_name,  s.SERVICE_NAME as Service, a.ACTIVITY_NAME as Lesson_Type," +
                        " to_char(al.START_TIME,'MM/DD/YYYY')as Start_Date  ,to_char(al.end_time ,'MM/DD//YYYY')as End_Date, " +
                        " ass.IS_RECURRING as IS_Recurring_Appointment,  to_char(al.START_TIME,'hh:mi am') as Time,   " +
                        " (extract(day from ((al.end_time - al.start_time) * 24 * 60 ))) as duration, " +
                        " (pi.FIRST_NAME || ' '  || pi.LAST_NAME) as instructor_name, " +
                        " rm.PROFILE_ROOM_NAME as Room,nvl(al.NOTE,'-') as Note,al.CANCELED as IS_Cancelled " +
                        "  from appointment_log al " +
                        "  left join instructor ins on al.INSTRUCTOR_ID=ins.INSTRUCTOR_ID " +
                        " left join person pi on pi.person_id=ins.person_id " +
                        " join room rm on rm.ROOM_ID=al.ROOM_ID " +
                        " join person pu on pu.PERSON_ID=al.UPDATED_BY  " +
                        " join APPOINTMENT_CUSTOMERS ac on ac.APPOINTMENT_ID=al.APPOINTMENT_ID " +
                        " join APPOINTMENT ap on ap.APPOINTMENT_ID=al.APPOINTMENT_ID " +
                        " join appointment_series ass on ap.APPOINTMENT_SERIES_ID=ass.APPOINTMENT_SERIES_ID  " +
                        " join customer c on ac.CUSTOMER_ID=c.CUSTOMER_ID " +
                        " join activity a on a.ACTIVITY_ID=al.ACTIVITY_ID " +
                        " join person p_c on c.PERSON_ID=p_c.PERSON_ID " +
                        " join SERVICE s on s.SERVICE_ID=a.SERVICE_ID " +
                        " where trunc(ap.START_TIME)>=:startDate and " +
                        " trunc(ap.END_TIME)<=:endDate and " +
                        " c.EXTERNAL_ID= :inputExternalId " +
                        " order by al.appointment_id,to_char(al.START_TIME,'DD/MM/YYYY'),to_char(al.START_TIME,'hh:mi am'),al.UPDATED ";

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb);
                query.setParameter("startDate", comparStartDate);
                query.setParameter("endDate", comparEndDate);
                query.setParameter("inputExternalId", pInputExternalId);

                query.addScalar("APPOINTMENT_ID", StandardBasicTypes.STRING);
                query.addScalar("Updated_By", StandardBasicTypes.STRING);
                query.addScalar("Updated_timestamp", StandardBasicTypes.STRING);
                query.addScalar("customer_name", StandardBasicTypes.STRING);
                query.addScalar("Service", StandardBasicTypes.STRING);
                query.addScalar("Lesson_Type", StandardBasicTypes.STRING);
                query.addScalar("Start_Date", StandardBasicTypes.STRING);
                query.addScalar("End_Date", StandardBasicTypes.STRING);
                query.addScalar("IS_Recurring_Appointment", StandardBasicTypes.STRING);
                query.addScalar("Time", StandardBasicTypes.STRING);
                query.addScalar("duration", StandardBasicTypes.LONG);
                query.addScalar("instructor_name", StandardBasicTypes.STRING);
                query.addScalar("Room", StandardBasicTypes.STRING);
                query.addScalar("Note", StandardBasicTypes.STRING);
                query.addScalar("IS_Cancelled", StandardBasicTypes.STRING);

                List<Object[]> results = query.getResultList();
                List<AppointmentHistoryDTO> result = new ArrayList<>();

                for (Object[] objects : results) {
                    String appointmentId = (String) objects[0];
                    String updatedBy = (String) objects[1];
                    String updatedTimestamp = (String) objects[2];
                    String customerName = (String) objects[3];
                    String serviceType = (String) objects[4];
                    String lessonType = (String) objects[5];
                    String startDate = (String) objects[6];
                    String endDate = (String) objects[7];
                    String isRecurring = (String) objects[8];
                    String time = (String) objects[9];
                    Long duration = (Long) objects[10];
                    String instructorName = (String) objects[11];
                    String room = (String) objects[12];
                    String note = (String) objects[13];
                    String isCancelled = (String) objects[14];

                    AppointmentHistoryDTO appointmentHistoryDTO = new AppointmentHistoryDTO(
                            appointmentId, updatedBy, updatedTimestamp, customerName,
                            serviceType, lessonType, startDate, endDate, isRecurring, time,
                            duration, instructorName, room, note, isCancelled);

                    result.add(appointmentHistoryDTO);
                }

                return result;
            }
        };
    }


    //Added for GSSP-230
    /**
     * Returns a Criterion that can be used to find Instructors who are terminated but are disabled
     *
     *
     * @return Criterion instance
     */
    public static Criterion<Appointment, Map<Long, List<String>>> findTerminatedEnabledInstructor() {

        return new AppointmentCriterion<Map<Long, List<String>>>() {

            @Override
            public List<Map<Long, List<String>>> search(EntityManager entityManager, int pFetchMode) {

                String sb = "select i.location_id  as location_id,i.external_id as external_id, p.first_name as first_name," +
                        " p.last_name as last_name" +
                        " from instructor i " +
                        " join person p on p.person_id = i.person_id " +
                        " where status='T' and enabled='Y' order by location_id ";

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb);
                query.addScalar("location_id", StandardBasicTypes.LONG);
                query.addScalar("external_id", StandardBasicTypes.STRING);
                query.addScalar("first_name", StandardBasicTypes.STRING);
                query.addScalar("last_name", StandardBasicTypes.STRING);

                List<Object[]> results = query.getResultList();
                Map<Long, List<String>> cache = new HashMap<>();

                for (Object[] objects : results) {
                    Long locationId = (Long) objects[0];
                    String externalId = (String) objects[1];
                    String firstName = (String) objects[2];
                    String lastName = (String) objects[3];

                    String fullName = StringUtils.isBlank(firstName) ? "" : firstName + " " + (StringUtils.isBlank(lastName) ? "" : lastName);

                    if (externalId != null && !StringUtils.isEmpty(fullName)) {
                        cache.computeIfAbsent(locationId, k -> new ArrayList<>())
                                .add(externalId + " " + fullName);
                    }
                }

                List<Map<Long, List<String>>> result = new ArrayList<>();
                result.add(cache);

                return result;
            }
        };
    }


    //-- Phase  Changes GSSP-295 Over loaded findInstructorAvailableHours method ---------------------------
    public static Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> findInstructorAvailableHours(
            final List<String> instructorId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<Map<String, List<InstructorAvailableHoursDTO>>>() {

            @Override
            public Map<String, List<InstructorAvailableHoursDTO>> get(EntityManager entityManager, int pFetchMode) {

                String sb = "select to_char(t.start_time, 'yyyy-MM-dd') as date1 , " +
                        "to_char(t.start_time,'HH24:Mi') as start_time, to_char(t.end_time,'HH24:Mi') as end_time,t.instructor_id  AS instructor_id from appointment t " +
                        "where t.start_time >= :pStartTime and t.end_time <= :pEndTime and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        "and t.instructor_id in (:instructorId)  minus  " ;
                sb = sb+" SELECT  TO_CHAR(t.start_time, 'yyyy-MM-dd') AS date1 , TO_CHAR(t.start_time,'HH24:Mi')     AS start_time, TO_CHAR(t.end_time,'HH24:Mi') AS end_time,t.instructor_id  AS instructor_id FROM (SELECT * FROM  appointment t  WHERE  t.start_time >= :pStartTime AND  t.end_time <= :pEndTime  AND   DECODE(t.canceled, NULL, 'N', t.canceled) != 'Y' AND t.instructor_id    IN (:instructorId) ) t, (select * from   onetime st where  st.instructor_id   IN (:instructorId)   ) ot WHERE t.instructor_id = ot.instructor_id AND t.start_time between ot.start_time and TO_DATE('01-03-9999','DD-MM-YYYY') AND t.end_time between  sysdate  and  ot.end_time  ORDER BY date1 DESC";


                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb);
                query.setParameterList("instructorId", instructorId);
                query.setParameter("pStartTime", pStartTime);
                query.setParameter("pEndTime", pEndTime);

                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("start_time", StandardBasicTypes.STRING);
                query.addScalar("end_time", StandardBasicTypes.STRING);
                query.addScalar("instructor_id", StandardBasicTypes.STRING);

                List<Object[]> results = query.getResultList();

                Map<String, List<InstructorAvailableHoursDTO>> cache = new HashMap<>();

                DateTimeFormatter df1 = DateTimeFormat.forPattern(DateTimeUtil.HOURS_PATTERN);

                for (Object[] objects : results) {
                    String date = (String) objects[0];
                    String instructorIdValue = (String) objects[3];
                    String instructorDate = date + "~and~" + instructorIdValue;

                    cache.computeIfAbsent(instructorDate, k -> new ArrayList<>());

                    String startTime = (String) objects[1];
                    String endTime = (String) objects[2];
                    InstructorAvailableHoursDTO dto = new InstructorAvailableHoursDTO(
                            df1.parseLocalTime(startTime),
                            df1.parseLocalTime(endTime)
                    );

                    cache.get(instructorDate).add(dto);
                }

                return cache;
            }
        };
    }


    //--   Changes CRMI-338 --Method for get Map of Appointment Data -------
    Map<String,  CRMAppointmentDataFileDTO> cRMAppointmentDataMap = null;


    public static Criterion<Appointment, Map<String, CRMAppointmentDataFileDTO>> getCRMAppointmentDataFile(final String dateInString) {
        return new AppointmentCriterion<Map<String, CRMAppointmentDataFileDTO>>() {

            @Override
            public Map<String, CRMAppointmentDataFileDTO> get(EntityManager entityManager, int pFetchMode) {

                Map<String, CRMAppointmentDataFileDTO> crmAppointmentDataMap = new HashMap<>();

                String sb = "SELECT   'GC' AS BRAND_CD, "+
                        "'POS' AS ACCT_SRC_CD, "+
                        "C.EXTERNAL_ID AS ACCT_SRC_NBR, "+
                        "A.APPOINTMENT_ID AS APPT_CD, "+
                        "B.CUSTOMER_ID AS APPT_CUST_CD, "+
                        "C.CUSTOMER_STATUS_ID AS CUST_STS_CD, "+
                        "D.STATUS_NAME AS CUST_STS_DESC, "+
                        "C.PERSON_ID AS APPT_STUDT_CD, "+
                        "E.FIRST_NAME AS STUDT_FST_NAME, "+
                        "E.LAST_NAME AS STUDT_LST_NAME, "+
                        "E.EMAIL AS STUDT_EMAIL, "+
                        "E.PHONE AS STUDT_PHN, "+
                        "A.ACTIVITY_ID AS APPT_ACTVTY_CD, "+
                        "F.ACTIVITY_NAME AS ACTVTY_DESC, "+
                        "F.MINIMUM_DURATION AS ACTVTY_MIN_DUR, "+
                        "F.MAXIMUM_DURATION AS ACTVTY_MAX_DUR, "+
                        "F.SERVICE_ID AS ACTVTY_SRVC_CD, "+
                        "G.SERVICE_NAME AS ACTVTY_SRVC_DESC, "+
                        "A.PROFILE_ID AS APPT_PROF_CD, "+
                        "H.EXTERNAL_ID AS APPT_STR_NO, "+
                        "H.LOCATION_NAME AS APPT_STR_DESC, "+
                        "A.INSTRUCTOR_ID AS APPT_INSTRUCT_CD, "+
                        "I.EXTERNAL_ID AS INSTRUCT_EMP_NO, "+
                        "J.FIRST_NAME AS INSTRUCT_FST_NAME, "+
                        "J.LAST_NAME AS INSTRUCT_LST_NAME, "+
                        "J.EMAIL AS INSTRUCT_EMAIL, "+
                        "I.STATUS AS INSTRUCT_STS, "+
                        "K.EXTERNAL_ID AS INSTRUCT_STR_NO, "+
                        "K.LOCATION_NAME AS INSTRUCT_STR_DESC, "+
                        "I.ENABLED AS INSTRUCT_ENABLED, "+
                        "TO_CHAR(A.START_TIME,'YYYY-MM-DD HH24:MI:SS') AS APPT_START_DTTM, "+
                        "TO_CHAR(A.END_TIME,'YYYY-MM-DD HH24:MI:SS') AS APPT_END_DTTM, "+
                        "A.CANCELED AS APPT_CANCL_FLG, "+
                        "L.APPOINTMENT_CANCEL_REASON_ID AS CNCL_REASON_CD, "+
                        "M.CANCEL_REASON AS CNCL_REASON_DESC, "+
                        " REPLACE(REPLACE(a.note, CHR(13), ' '), CHR(10), ' ') AS APPT_NOTE, "+
                        "A.BAND_NAME AS APPT_BAND_NAME, "+
                        "TO_CHAR(A.CREATE_TIME,'YYYY-MM-DD HH24:MI:SS') AS APPT_CREATE_DTTM, "+
                        "N.AUTH_ID AS UPDT_EMP_NO, "+
                        "TO_CHAR(A.UPDATED,'YYYY-MM-DD HH24:MI:SS') AS ACTIVITY_TS, "+
                        "A.APPOINTMENT_SERIES_ID AS APPT_SERIES_CD, "+
                        "TO_CHAR(O.SERIES_START_TIME,'YYYY-MM-DD HH24:MI:SS') AS SERIES_START_DTTM, "+
                        "TO_CHAR(O.SERIES_END_TIME,'YYYY-MM-DD HH24:MI:SS') AS SERIES_END_DTTM, "+
                        "O.IS_RECURRING AS SERIES_RECUR_FLG, "+
                        "O.ACTIVITY_ID AS SERIES_ACTVTY_CD, "+
                        "P.ACTIVITY_NAME AS SERIES_ACTVTY_DESC, "+
                        "P.SERVICE_ID AS SERIES_ACTVTY_SRVC_CD, "+
                        "Q.SERVICE_NAME AS SERIES_ACTVTY_SRVC_DESC "+
                        "FROM     APPOINTMENT A  "+
                        "JOIN APPOINTMENT_CUSTOMERS B ON A.APPOINTMENT_ID = B.APPOINTMENT_ID  "+
                        "JOIN CUSTOMER C ON B.CUSTOMER_ID = C.CUSTOMER_ID  "+
                        "LEFT JOIN CUSTOMER_STATUS D ON C.CUSTOMER_STATUS_ID = D.CUSTOMER_STATUS_ID  "+
                        "LEFT JOIN PERSON E ON C.PERSON_ID = E.PERSON_ID  "+
                        "LEFT JOIN ACTIVITY F ON A.ACTIVITY_ID = F.ACTIVITY_ID  "+
                        "LEFT JOIN SERVICE G ON F.SERVICE_ID = G.SERVICE_ID  "+
                        "LEFT JOIN LOCATION H ON A.PROFILE_ID = H.PROFILE_ID  "+
                        "LEFT JOIN INSTRUCTOR I ON A.INSTRUCTOR_ID = I.INSTRUCTOR_ID  "+
                        "LEFT JOIN PERSON J ON I.PERSON_ID = J.PERSON_ID  "+
                        "LEFT JOIN LOCATION K ON I.LOCATION_ID = K.LOCATION_ID  "+
                        "LEFT JOIN APPOINTMENT_CANCEL L ON A.APPOINTMENT_ID = L.APPOINTMENT_ID  "+
                        "LEFT JOIN APPOINTMENT_CANCEL_REASON M ON L.APPOINTMENT_CANCEL_REASON_ID = M.APPOINTMENT_CANCEL_REASON_ID  "+
                        "LEFT JOIN PERSON N ON A.UPDATED_BY = N.PERSON_ID  "+
                        "LEFT JOIN APPOINTMENT_SERIES O ON A.APPOINTMENT_SERIES_ID = O.APPOINTMENT_SERIES_ID  "+
                        "LEFT JOIN ACTIVITY P ON O.ACTIVITY_ID = P.ACTIVITY_ID "+
                        "LEFT JOIN SERVICE Q ON P.SERVICE_ID = Q.SERVICE_ID "+
                        "WHERE    TRUNC(A.UPDATED) >= '"+dateInString+"'";


                List<Object[]> results = entityManager.createNativeQuery(sb).getResultList();

               /* NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb);

                query.setParameter("dateInString", dateInString);

                query.addScalar("BRAND_CD", StandardBasicTypes.STRING);
                query.addScalar("ACCT_SRC_CD", StandardBasicTypes.STRING);
                query.addScalar("ACCT_SRC_NBR", StandardBasicTypes.STRING);
                query.addScalar("APPT_CD", StandardBasicTypes.INTEGER);
                query.addScalar("APPT_CUST_CD", StandardBasicTypes.INTEGER);
                query.addScalar("CUST_STS_CD", StandardBasicTypes.INTEGER);
                query.addScalar("CUST_STS_DESC", StandardBasicTypes.STRING);
                query.addScalar("APPT_STUDT_CD", StandardBasicTypes.INTEGER);
                query.addScalar("STUDT_FST_NAME", StandardBasicTypes.STRING);
                query.addScalar("STUDT_LST_NAME", StandardBasicTypes.STRING);
                query.addScalar("STUDT_EMAIL", StandardBasicTypes.STRING);
                query.addScalar("STUDT_PHN", StandardBasicTypes.STRING);
                query.addScalar("APPT_ACTVTY_CD", StandardBasicTypes.INTEGER);
                query.addScalar("ACTVTY_DESC", StandardBasicTypes.STRING);
                query.addScalar("ACTVTY_MIN_DUR", StandardBasicTypes.INTEGER);
                query.addScalar("ACTVTY_MAX_DUR", StandardBasicTypes.INTEGER);
                query.addScalar("ACTVTY_SRVC_CD", StandardBasicTypes.INTEGER);
                query.addScalar("ACTVTY_SRVC_DESC", StandardBasicTypes.STRING);
                query.addScalar("APPT_PROF_CD", StandardBasicTypes.INTEGER);
                query.addScalar("APPT_STR_NO", StandardBasicTypes.STRING);
                query.addScalar("APPT_STR_DESC", StandardBasicTypes.STRING);
                query.addScalar("APPT_INSTRUCT_CD", StandardBasicTypes.INTEGER);
                query.addScalar("INSTRUCT_EMP_NO", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCT_FST_NAME", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCT_LST_NAME", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCT_EMAIL", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCT_STS", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCT_STR_NO", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCT_STR_DESC", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCT_ENABLED", StandardBasicTypes.CHARACTER);
                query.addScalar("APPT_START_DTTM", StandardBasicTypes.STRING);
                query.addScalar("APPT_END_DTTM", StandardBasicTypes.STRING);
                query.addScalar("APPT_CANCL_FLG", StandardBasicTypes.CHARACTER);
                query.addScalar("CNCL_REASON_CD", StandardBasicTypes.INTEGER);
                query.addScalar("CNCL_REASON_DESC", StandardBasicTypes.STRING);
                query.addScalar("APPT_NOTE", StandardBasicTypes.STRING);
                query.addScalar("APPT_BAND_NAME", StandardBasicTypes.STRING);
                query.addScalar("APPT_CREATE_DTTM", StandardBasicTypes.STRING);
                query.addScalar("UPDT_EMP_NO", StandardBasicTypes.STRING);
                query.addScalar("ACTIVITY_TS", StandardBasicTypes.STRING);
                query.addScalar("APPT_SERIES_CD", StandardBasicTypes.INTEGER);
                query.addScalar("SERIES_START_DTTM", StandardBasicTypes.STRING);
                query.addScalar("SERIES_END_DTTM", StandardBasicTypes.STRING);
                query.addScalar("SERIES_RECUR_FLG", StandardBasicTypes.CHARACTER);
                query.addScalar("SERIES_ACTVTY_CD", StandardBasicTypes.INTEGER);
                query.addScalar("SERIES_ACTVTY_DESC", StandardBasicTypes.STRING);
                query.addScalar("SERIES_ACTVTY_SRVC_CD", StandardBasicTypes.INTEGER);
                query.addScalar("SERIES_ACTVTY_SRVC_DESC", StandardBasicTypes.STRING);

                List<Object[]> results = query.getResultList();*/

                for (Object[] obj : results) {
                    CRMAppointmentDataFileDTO dto = new CRMAppointmentDataFileDTO();
                    dto.setGc(PGPUtils.getRemoveNewLineAndTilde((String) obj[0]));
                    dto.setPos(PGPUtils.getRemoveNewLineAndTilde((String) obj[1]));
                    dto.setCustomerId(PGPUtils.getRemoveNewLineAndTilde((String) obj[2]));
                    dto.setAppointmentId(PGPUtils.getRemoveNewLineAndTilde(convertToBigDecimal(obj[3])));
                    dto.setAppointmentCustomerId(PGPUtils.getRemoveNewLineAndTilde(convertToBigDecimal( obj[4])));
                    dto.setCustomerStatusName(PGPUtils.getRemoveNewLineAndTilde(convertToBigDecimal( obj[5])));
                    dto.setCustomerStatusId(PGPUtils.getRemoveNewLineAndTilde(obj[6]));
                    dto.setPersonId(PGPUtils.getRemoveNewLineAndTilde(convertToBigDecimal( obj[7])));
                    dto.setStudentFirstName(PGPUtils.getRemoveNewLineAndTilde((String) obj[8]));
                    dto.setStudentLastName(PGPUtils.getRemoveNewLineAndTilde((String) obj[9]));
                    dto.setStudentEmail(PGPUtils.getRemoveNewLineAndTilde((String) obj[10]));
                    dto.setStudentPhoneNumber(PGPUtils.getRemoveNewLineAndTilde((String) obj[11]));
                    dto.setAppointmentActivityId(PGPUtils.getRemoveNewLineAndTilde(convertToBigDecimal( obj[12])));
                    dto.setActivityName(PGPUtils.getRemoveNewLineAndTilde((String) obj[13]));
                    dto.setActivityMinimunDuration(PGPUtils.getRemoveNewLineAndTilde(convertToBigDecimal( obj[14])));
                    dto.setActivityMaxmumDuration(PGPUtils.getRemoveNewLineAndTilde(convertToBigDecimal(  obj[15])));
                    dto.setActivityId(PGPUtils.getRemoveNewLineAndTilde(convertToBigDecimal( obj[16])));
                    dto.setActivityServiceName(PGPUtils.getRemoveNewLineAndTilde((String) obj[17]));
                    dto.setAppointmentProfileId(PGPUtils.getRemoveNewLineAndTilde(convertToBigDecimal(obj[18])));
                    dto.setAppointmentNumber(PGPUtils.getRemoveNewLineAndTilde((String) obj[19]));
                    dto.setAppointmentLocationName(PGPUtils.getRemoveNewLineAndTilde((String) obj[20]));
                    dto.setAppointmentInstructorId(PGPUtils.getRemoveNewLineAndTilde(convertToBigDecimal( obj[21])));
                    dto.setInstructorEmployeeId(PGPUtils.getRemoveNewLineAndTilde((String) obj[22]));
                    dto.setInstructorFirstName(PGPUtils.getRemoveNewLineAndTilde((String) obj[23]));
                    dto.setInstructorLastName(PGPUtils.getRemoveNewLineAndTilde((String) obj[24]));
                    dto.setInstructorEmail(PGPUtils.getRemoveNewLineAndTilde((String) obj[25]));
                    dto.setInstructorStatus(PGPUtils.getRemoveNewLineAndTilde((String) obj[26]));
                    dto.setInstructorId(PGPUtils.getRemoveNewLineAndTilde((String) obj[27]));
                    dto.setInstructionLocationName(PGPUtils.getRemoveNewLineAndTilde((String) obj[28]));
                    dto.setInstructorEnabled(PGPUtils.getRemoveNewLineAndTilde((Character) obj[29]));
                    dto.setAppointmentStartTime(PGPUtils.getRemoveNewLineAndTilde((String) obj[30]));
                    dto.setAppointmentEndTime(PGPUtils.getRemoveNewLineAndTilde((String) obj[31]));
                    dto.setAppointmentCancelled(PGPUtils.getRemoveNewLineAndTilde((Character) obj[32]));
                    dto.setCancelReasonId(PGPUtils.getRemoveNewLineAndTilde(convertToBigDecimal(obj[33])));
                    dto.setCancelReasinDescription(PGPUtils.getRemoveNewLineAndTilde((String) obj[34]));
                    dto.setCancelReason(PGPUtils.getRemoveNewLineAndTilde((String) obj[35]));
                    dto.setAppointmentBandName(PGPUtils.getRemoveNewLineAndTilde((String) obj[36]));
                    dto.setAppointmentCreateTime(PGPUtils.getRemoveNewLineAndTilde((String) obj[37]));
                    dto.setAuthId(PGPUtils.getRemoveNewLineAndTilde((String) obj[38]));
                    dto.setAppointmentUpdateTime(PGPUtils.getRemoveNewLineAndTilde((String) obj[39]));
                    dto.setAppointmenSeriesId(PGPUtils.getRemoveNewLineAndTilde(convertToBigDecimal(  obj[40])));
                    dto.setAppointmenSeriesStartTime(PGPUtils.getRemoveNewLineAndTilde((String) obj[41]));
                    dto.setAppointmenSeriesEndTime(PGPUtils.getRemoveNewLineAndTilde((String) obj[42]));
                    dto.setAppointmenSeriesFlag(PGPUtils.getRemoveNewLineAndTilde((Character) obj[43]));
                    dto.setAppointmenSeriesActivityId(PGPUtils.getRemoveNewLineAndTilde(convertToBigDecimal( obj[44])));
                    dto.setAppointmenSeriesName(PGPUtils.getRemoveNewLineAndTilde((String) obj[45]));
                    dto.setSeriesId(PGPUtils.getRemoveNewLineAndTilde(convertToBigDecimal( obj[46])));
                    dto.setSeriesName(PGPUtils.getRemoveNewLineAndTilde((String) obj[47]));

                    crmAppointmentDataMap.put(String.valueOf(obj[3]), dto);
                }

                return crmAppointmentDataMap;
            }
        };
    }


    //################################################351_----##############################################
    // Added New report GSSP-351
    public static Criterion<Appointment, CustomerSearchPageDTO> findAllActiveStudentsByLocation(
            final String locationId) {

        return new AppointmentCriterion<CustomerSearchPageDTO>() {

            @Override
            public List<CustomerSearchPageDTO> search(EntityManager entityManager, int pFetchMode) {

                StringBuilder sb1 = new StringBuilder(" select "
                        + " c.external_id external_id,"
                        + " p.first_name || ' ' || p.last_name as Customer_name,"
                        + " p.email as student_email,"
                        + " p.phone as phone,"
                        + " cs.external_id as customer_status,"
                        + " c.lesson_count as lesson_count,"
                        + " c.last_booked as last_booked,"
                        + " c.customer_id as customer_id "
                        + " from location l"
                        + " join customer c on  l.external_id =c.location_external_id "
                        + " join person p on c.person_id = p.person_id"
                        + " join customer_status cs on c.customer_status_id = cs.customer_status_id "
                        + " where "
                        + " c.customer_status_id  != '2'");

                if (locationId != null && !locationId.isEmpty()) {
                    sb1.append(" AND l.profile_id = :location_id ");
                }
                sb1.append(" ORDER BY c.last_booked DESC");

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb1.toString());

                if (locationId != null && !locationId.isEmpty()) {
                    query.setParameter("location_id", locationId);
                }

                query.addScalar("external_id", StandardBasicTypes.STRING);
                query.addScalar("Customer_name", StandardBasicTypes.STRING);
                query.addScalar("student_email", StandardBasicTypes.STRING);
                query.addScalar("phone", StandardBasicTypes.STRING);
                query.addScalar("customer_status", StandardBasicTypes.STRING);
                query.addScalar("lesson_count", StandardBasicTypes.STRING);
                query.addScalar("last_booked", StandardBasicTypes.DATE);
                query.addScalar("customer_id", StandardBasicTypes.STRING);

                List<Object[]> results = query.getResultList();
                List<CustomerSearchPageDTO> result = new ArrayList<>();

                for (Object[] objects : results) {
                    CustomerSearchPageDTO searchDTO = new CustomerSearchPageDTO();

                    searchDTO.setCustomerExtId((String) objects[0]);
                    searchDTO.setCustomerName((String) objects[1]);
                    searchDTO.setEmail((String) objects[2]);
                    searchDTO.setPhone((String) objects[3]);
                    searchDTO.setCustomerStatus(PGPUtils.getRemoveNewLineAndTilde((String) objects[4]));
                    searchDTO.setLessonCount((String) objects[5]);
                    searchDTO.setLastBooked((Date) objects[6]);
                    searchDTO.setCustomerId((String) objects[7]);

                    result.add(searchDTO);
                }

                return result;
            }
        };
    }

    //########################################################################################################
    //################################################351_----##############################################
    // Added New report GSSP-351


    public static Criterion<Appointment, CustomerSearchPageDTO> findInstructorInstrumentStudentsByLocation(
            final List<String> customerIds) {

        return new AppointmentCriterion<CustomerSearchPageDTO>() {

            @Override
            public List<CustomerSearchPageDTO> search(EntityManager entityManager, int pFetchMode) {

                StringBuilder sb1 = new StringBuilder("select cc.EXTERNAL_ID external_id, act.ACTIVITY_NAME ACTIVITY_NAME,   "
                        + "  ip.first_name || ' ' || ip.last_name as Instructor_name,   "
                        + "  cc.LAST_BOOKED   LAST_BOOKED "
                        + "  from appointment t,appointment_customers aptcuss,customer cc,   "
                        + "  activity act,instructor i,person ip   "
                        + "  where t.appointment_id = aptcuss.appointment_id   "
                        + "  and i.instructor_id = t.instructor_id   "
                        + "  and ip.person_id = i.person_id   "
                        + "  and aptcuss.customer_id = cc.customer_id   "
                        + "  and t.ACTIVITY_ID = act.activity_id   "
                        + "  and t.appointment_id in (   "
                        + "  (select  max(apt.appointment_id)   "
                        + "  from appointment apt,appointment_customers aptcus,customer c   "
                        + "  where apt.appointment_id = aptcus.APPOINTMENT_ID   "
                        + "  and aptcus.CUSTOMER_ID = c.customer_id   "
                        + "  and apt.CANCELED != 'Y'   "
                        + "  and c.EXTERNAL_ID in (:customer_Ids)   "
                        + "  GROUP BY c.EXTERNAL_ID)) ");

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb1.toString());

                if (customerIds != null && !customerIds.isEmpty()) {
                    query.setParameterList("customerIds", customerIds);
                }

                query.addScalar("external_id", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("Instructor_name", StandardBasicTypes.STRING);
                query.addScalar("last_booked", StandardBasicTypes.DATE);

                List<Object[]> results = query.getResultList();
                List<CustomerSearchPageDTO> result = new ArrayList<>();

                for (Object[] objects : results) {
                    CustomerSearchPageDTO searchDTO = new CustomerSearchPageDTO();

                    searchDTO.setCustomerExtId((String) objects[0]);
                    searchDTO.setActivityName((String) objects[1]);
                    searchDTO.setInstructor((String) objects[2]);
                    searchDTO.setLastBooked((Date) objects[3]);

                    result.add(searchDTO);
                }

                return result;
            }
        };
    }


    public static Criterion<Appointment, InstructorAppointmentStatusDTO> findInstructorAppointmentStatusByLocationIdAndDateTime(
            final long pLocationId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<InstructorAppointmentStatusDTO>() {

            @Override
            public List<InstructorAppointmentStatusDTO> search(EntityManager entityManager, int pFetchMode) {

                String sb = " SELECT " +
                        " T.APPOINTMENT_ID, " +
                        " L.EXTERNAL_ID || ' ' || L.LOCATION_NAME LOCATION, " +
                        " I.EXTERNAL_ID AS INSTRUCTOR_EXTERNALID, " +
                        " P_I.FIRST_NAME  || ' ' || P_I.LAST_NAME AS INSTRUCTOR_NAME, " +
                        " TO_CHAR(T.START_TIME, 'DD-MON-YYYY') AS DATE_OF_APPOINTMENT , " +
                        " TO_CHAR( T.START_TIME, 'HH:MI AM') || '-' ||TO_CHAR( T.END_TIME, 'HH:MI AM') AS TIME_FRAME, " +
                        " (EXTRACT(DAY FROM ((T.END_TIME - T.START_TIME)*24*60))) AS DURATION, " +
                        "  A.ACTIVITY_NAME AS ACTIVITY_NAME, " +
                        "  C.EXTERNAL_ID AS CUSTOMER, " +
                        "  P_C.FIRST_NAME || ' ' || P_C.LAST_NAME AS CUSTOMER_NAME, " +
                        "  P_C.EMAIL CUSTOMER_EMAIL, " +
                        "  P_C.PHONE CUSTOMER_PHONE, " +
                        "  IAS.SHOW_STATUS, " +
                        "  IAS.COMMENTS, " +
                        "  IAS.STUDENT_NOTE " +
                        " FROM APPOINTMENT T " +
                        " JOIN APPOINTMENT_CUSTOMERS A_C ON T.APPOINTMENT_ID = A_C.APPOINTMENT_ID " +
                        " LEFT JOIN ACTIVITY A ON T.ACTIVITY_ID = A.ACTIVITY_ID " +
                        " LEFT JOIN CUSTOMER C ON A_C.CUSTOMER_ID = C.CUSTOMER_ID   " +
                        " LEFT JOIN LOCATION L  ON T.PROFILE_ID = L.PROFILE_ID        " +
                        " LEFT JOIN PERSON P_C ON C.PERSON_ID = P_C.PERSON_ID " +
                        " LEFT JOIN INSTRUCTOR I ON T.INSTRUCTOR_ID = I.INSTRUCTOR_ID " +
                        " LEFT JOIN PERSON P_I ON I.PERSON_ID = P_I.PERSON_ID " +
                        " LEFT JOIN INSTRUCTOR_APPOINTMENT_STATUS IAS  ON T.APPOINTMENT_ID = IAS.APPOINTMENT_ID " +
                        " WHERE T.START_TIME >= :STARTTIME " +
                        " AND T.START_TIME <= :ENDTIME " +
                        " AND L.LOCATION_ID = :LOCATIONID " +
                        " AND DECODE(C.EXTERNAL_ID, NULL, '_', C.EXTERNAL_ID) != 'C'  " +
                        " AND DECODE(T.CANCELED, NULL, 'N', T.CANCELED) != 'Y'  " +
                        //	" AND IAS.SHOW_STATUS IS NOT NULL " +
                        " ORDER BY TO_CHAR(T.START_TIME, 'DD-MON-YYYY'), P_I.FIRST_NAME, P_I.LAST_NAME, T.START_TIME, P_C.FIRST_NAME, P_C.LAST_NAME " ;

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb);
                query.setParameter("STARTTIME", pStartTime);
                query.setParameter("ENDTIME", pEndTime);
                query.setParameter("LOCATIONID", pLocationId);

                query.addScalar("appointment_id", StandardBasicTypes.LONG);
                query.addScalar("location", StandardBasicTypes.STRING);
                query.addScalar("instructor_externalid", StandardBasicTypes.STRING);
                query.addScalar("instructor_name", StandardBasicTypes.STRING);
                query.addScalar("date_of_appointment", StandardBasicTypes.STRING);
                query.addScalar("time_frame", StandardBasicTypes.STRING);
                query.addScalar("duration", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("customer", StandardBasicTypes.STRING);
                query.addScalar("customer_name", StandardBasicTypes.STRING);
                query.addScalar("customer_email", StandardBasicTypes.STRING);
                query.addScalar("customer_phone", StandardBasicTypes.STRING);
                query.addScalar("show_status", StandardBasicTypes.STRING);
                query.addScalar("comments", StandardBasicTypes.STRING);
                query.addScalar("student_note", StandardBasicTypes.STRING);

                List<Object[]> results = query.getResultList();
                List<InstructorAppointmentStatusDTO> instructorAppointmentStatusDTOList = new ArrayList<>();

                for (Object[] objects : results) {
                    InstructorAppointmentStatusDTO dto = new InstructorAppointmentStatusDTO();
                    dto.setAppointmentId((Long) objects[0]);
                    dto.setLocation(PGPUtils.setNullToEmpty((String) objects[1]));
                    dto.setInstructorExternalId(PGPUtils.setNullToEmpty((String) objects[2]));
                    dto.setInstructorName(PGPUtils.setNullToEmpty((String) objects[3]));
                    dto.setDateOfAppointment(PGPUtils.setNullToEmpty((String) objects[4]));
                    dto.setTimeFrame(PGPUtils.setNullToEmpty((String) objects[5]));
                    dto.setDuration(PGPUtils.setNullToEmpty((String) objects[6]));
                    dto.setActivityName(PGPUtils.setNullToEmpty((String) objects[7]));
                    dto.setCustomerExternalId(PGPUtils.setNullToEmpty((String) objects[8]));
                    dto.setCustomerName(PGPUtils.setNullToEmpty((String) objects[9]));
                    dto.setCustomerEmail(PGPUtils.setNullToEmpty((String) objects[10]));
                    dto.setCustomerPhone(PGPUtils.setNullToEmpty((String) objects[11]));
                    dto.setShowStatus(PGPUtils.setComletedStatusForEmpty((String) objects[12]));
                    dto.setComments(PGPUtils.hasRemarks((String) objects[13]));
                    dto.setStudentNote(PGPUtils.hasNotes((String) objects[14]));

                    instructorAppointmentStatusDTOList.add(dto);
                }

                return instructorAppointmentStatusDTOList;
            }
        };
    }


    //--363 Changes.


    public static Criterion<Appointment, InstructorLessonLinkDTO> findInstructorScheduleByLocationIdAndInstructorId(
            final List<String> personIdList, final Date startTime, final Date endTime) {

        return new AppointmentCriterion<InstructorLessonLinkDTO>() {

            @Override
            public List<InstructorLessonLinkDTO> search(EntityManager entityManager, int pFetchMode) {


                String sb = " SELECT "+
                        " OM.ID AS ZOOM_MEETINGS_ID, "+
                        " T.APPOINTMENT_ID AS APPOINTMENT_ID, "+
                        " T.CANCELED AS CANCELED, "+
                        "  (EXTRACT(DAY FROM ((T.END_TIME - T.START_TIME) * 24 * 60))) AS DURATION, "+
                        "  A.ACTIVITY_NAME AS ACTIVITY_NAME, "+
                        "  I.INSTRUCTOR_ID AS INSTRUCTOR_ID, "+
                        "  P_I.FIRST_NAME AS INSTRUCTOR_FIRST_NAME, "+
                        "  P_I.LAST_NAME AS INSTRUCTOR_LAST_NAME, "+
                        "  C.CUSTOMER_ID AS CUSTOMER_ID, "+
                        "  P_C.FIRST_NAME AS CUSTOMER_FIRST_NAME, "+
                        "   P_C.LAST_NAME AS CUSTOMER_LAST_NAME, "+
                        "  I.EXTERNAL_ID AS INSTRUCTOR_EXTERNAL_ID, "+
                        "  L.EXTERNAL_ID LOCATION_EXTERNAL_ID, "+
                        "  L.LOCATION_NAME LOCATION_NAME, "+
                        "   OM.START_URL ZOOM_MEETINGS_URL, "+
                        "  IAS.SHOW_STATUS SHOW_STATUS, "+
                        "  IAS.COMMENTS COMMENTS , "+
                        "  T.START_TIME AS  START_DATE , "+
                        "  TO_CHAR(T.START_TIME, 'HH:MI AM') AS START_TIME , "+
                        "   T.END_TIME AS END_TIME, "+
                        "  P_C.EMAIL AS C_EMAIL , "+
                        "   P_C.PHONE AS C_PHONE, "+
                        "  OM.NORMALIZED_UTC_END_TIME AS UTC_END_TIME , " +
                        "  IAS.STUDENT_NOTE STUDENT_NOTE ," +
                        "  OM.JOIN_URL JOIN_URL ," +
                        "  OM.NORMALIZED_UTC_START_TIME NORMALIZED_UTC_START_TIME , " +
                        "  LP.TZ TZ,  " +
                        "  a.SERVICE_ID AS SERVICE_ID, " +
                        "  r.ROOM_NUMBER_ID AS ROOM_NUMBER_ID, " +
                        "  t.Room_id AS Appointment_room_id, " +
                        "  rn.ROOM_NUMBER AS ROOM_NUMBER_Name, " +
                        " IAS.ASSIGNMENT ASSIGNMENT,IAS.PRACTICE_NOTES PRACTICE_NOTES,IAS.REMARKS REMARKS,  " +
                        "  C.EXTERNAL_ID AS EXTERNAL_ID "+
                        "  FROM APPOINTMENT T "+
                        "  LEFT JOIN APP_OLO.ZOOM_MEETINGS OM ON to_char(T.APPOINTMENT_ID) = OM.LESSON_APPOINTMENT_ID "+
                        " LEFT JOIN LOCATION L ON T.PROFILE_ID = L.PROFILE_ID "+
                        " LEFT JOIN LOCATION_PROFILE LP ON T.PROFILE_ID = LP.PROFILE_ID "+
                        " LEFT JOIN Room r ON t.room_id = r.room_id  "+
                        " LEFT JOIN   ROOM_NUMBER  rn ON rn.ROOM_NUMBER_ID = r.ROOM_NUMBER_ID "+
                        "  LEFT JOIN APPOINTMENT_CUSTOMERS A_C ON T.APPOINTMENT_ID = A_C.APPOINTMENT_ID "+
                        "  LEFT JOIN CUSTOMER C ON A_C.CUSTOMER_ID = C.CUSTOMER_ID "+
                        "  LEFT JOIN CUSTOMER_STATUS C_S ON C.CUSTOMER_STATUS_ID = C_S.CUSTOMER_STATUS_ID "+
                        "   LEFT JOIN PERSON P_C ON C.PERSON_ID = P_C.PERSON_ID "+
                        "  LEFT JOIN INSTRUCTOR I ON T.INSTRUCTOR_ID = I.INSTRUCTOR_ID "+
                        "  LEFT JOIN PERSON P_I ON I.PERSON_ID = P_I.PERSON_ID "+
                        "  LEFT JOIN ACTIVITY A ON T.ACTIVITY_ID = A.ACTIVITY_ID "+
                        "  LEFT JOIN INSTRUCTOR_APPOINTMENT_STATUS IAS ON T.APPOINTMENT_ID = IAS.APPOINTMENT_ID "+
                        "  WHERE  "+
                        "  T.INSTRUCTOR_ID IS NOT NULL "+
                        " AND A.ACTIVITY_ID NOT IN ('200','100','0','1','160','20','120','6','684','360','400','401') "+

                        "  AND T.START_TIME > :startTime " +
                        "  AND T.START_TIME <= :endTime " +
                        "   AND I.PERSON_ID in (:person_Id) " +
                        "   AND DECODE(C_S.EXTERNAL_ID, NULL, '_', C_S.EXTERNAL_ID) != 'C' "+
                        "  AND DECODE(T.CANCELED, NULL, 'N', T.CANCELED) != 'Y' "+
                        "  ORDER BY TO_CHAR(T.START_TIME, 'DD-MON-YYYY'), P_I.FIRST_NAME, P_I.LAST_NAME, T.START_TIME, P_C.FIRST_NAME, P_C.LAST_NAME";

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb);
                query.setParameterList("person_Id", personIdList);
                query.setParameter("startTime", startTime);
                query.setParameter("endTime", endTime);

                query.addScalar("zoom_meetings_id", StandardBasicTypes.STRING);
                query.addScalar("appointment_id", StandardBasicTypes.STRING);
                query.addScalar("canceled", StandardBasicTypes.STRING);
                query.addScalar("duration", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("instructor_id", StandardBasicTypes.STRING);
                query.addScalar("instructor_first_name", StandardBasicTypes.STRING);
                query.addScalar("instructor_last_name", StandardBasicTypes.STRING);
                query.addScalar("customer_id", StandardBasicTypes.STRING);
                query.addScalar("customer_first_name", StandardBasicTypes.STRING);
                query.addScalar("customer_last_name", StandardBasicTypes.STRING);
                query.addScalar("instructor_external_id", StandardBasicTypes.STRING);
                query.addScalar("location_external_id", StandardBasicTypes.STRING);
                query.addScalar("location_name", StandardBasicTypes.STRING);
                query.addScalar("zoom_meetings_url", StandardBasicTypes.STRING);
                query.addScalar("show_status", StandardBasicTypes.STRING);
                query.addScalar("comments", StandardBasicTypes.STRING);
                query.addScalar("start_date", StandardBasicTypes.TIMESTAMP);
                query.addScalar("start_time", StandardBasicTypes.STRING);
                query.addScalar("end_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("c_email", StandardBasicTypes.STRING);
                query.addScalar("c_phone", StandardBasicTypes.STRING);
                query.addScalar("utc_end_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("student_note", StandardBasicTypes.STRING);
                query.addScalar("join_url", StandardBasicTypes.STRING);
                query.addScalar("normalized_utc_start_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("tz", StandardBasicTypes.STRING);
                query.addScalar("service_id", StandardBasicTypes.STRING);
                query.addScalar("room_number_id", StandardBasicTypes.STRING);
                query.addScalar("appointment_room_id", StandardBasicTypes.STRING);
                query.addScalar("room_number_name", StandardBasicTypes.STRING);
                query.addScalar("assignment", StandardBasicTypes.STRING);
                query.addScalar("practice_notes", StandardBasicTypes.STRING);
                query.addScalar("remarks", StandardBasicTypes.STRING);
                query.addScalar("external_id", StandardBasicTypes.STRING);

                List<Object[]> results = query.getResultList();
                List<InstructorLessonLinkDTO> instructorScheduleDTOs = new ArrayList<>();

                for (Object[] objects : results) {
                    InstructorLessonLinkDTO dto = new InstructorLessonLinkDTO();
                    dto.setZoomMeetingID(PGPUtils.setNullToEmpty((String) objects[0]));
                    dto.setAppointmentId((String) objects[1]);
                    dto.setCanceled(PGPUtils.setNullToEmpty((String) objects[2]));
                    dto.setDuration(PGPUtils.setNullToEmpty((String) objects[3]));
                    dto.setActivityName(PGPUtils.setNullToEmpty((String) objects[4]));
                    dto.setInstructorId((String) objects[5]);
                    dto.setInstructorFirstName(PGPUtils.setNullToEmpty((String) objects[6]));
                    dto.setInstructorLastName(PGPUtils.setNullToEmpty((String) objects[7]));
                    dto.setCustomerId((String) objects[8]);
                    dto.setCustomerFirstName(PGPUtils.setNullToEmpty((String) objects[9]));
                    dto.setCustomerLastName(PGPUtils.setNullToEmpty((String) objects[10]));
                    dto.setInstructorExternalId(PGPUtils.setNullToEmpty((String) objects[11]));
                    dto.setLocationExternalId(PGPUtils.setNullToEmpty((String) objects[12]));
                    dto.setLocationName(PGPUtils.setNullToEmpty((String) objects[13]));
                    dto.setZoomMeetingUrl(PGPUtils.setNullToEmpty((String) objects[14]));
                    dto.setShowStatus(PGPUtils.setNullToEmpty((String) objects[15]));
                    dto.setComments(PGPUtils.setNullToEmpty((String) objects[16]));
                    dto.setStartDate(nullCheckDate(objects[17]));
                    dto.setStartTime(PGPUtils.setNullToEmpty((String) objects[18]));
                    dto.setEndTime(nullCheckDate(objects[19]));
                    dto.setCustomerEmail(PGPUtils.setNullToEmpty((String) objects[20]));
                    dto.setCustomerPhone(PGPUtils.setNullToEmpty((String) objects[21]));
                    dto.setUtcEndTime(nullCheckDate(objects[22]));
                    dto.setStudentNote(PGPUtils.setNullToEmpty((String) objects[23]));
                    dto.setJoinURL(PGPUtils.setNullToEmpty((String) objects[24]));
                    dto.setUtcStartTime(nullCheckDate(objects[25]));
                    dto.setLocationTimezone(PGPUtils.setNullToEmpty((String) objects[26]));
                    dto.setServiceId(PGPUtils.setNullToEmpty((String) objects[27]));
                    dto.setRoomNumberId(PGPUtils.setNullToEmpty((String) objects[28]));
                    dto.setAppointmentRoomId(PGPUtils.setNullToEmpty((String) objects[29]));
                    dto.setRoomNumberName(PGPUtils.setNullToEmpty((String) objects[30]));
                    dto.setAssignment(PGPUtils.setNullToEmpty((String) objects[31]));
                    dto.setPracticeNotes(PGPUtils.setNullToEmpty((String) objects[32]));
                    dto.setRemarks(PGPUtils.setNullToEmpty((String) objects[33]));
                    dto.setCustomerExternalId(PGPUtils.setNullToEmpty((String) objects[34]));

                    instructorScheduleDTOs.add(dto);
                }

                return instructorScheduleDTOs;
            }
        };
    }


    public static Date nullCheckDate(Object inputObj)
    {
        if(inputObj ==null)return null;
        Date outputStr = (Date)inputObj;
        return outputStr;

    }

    // -- 368 Changes


    public static Criterion<Appointment, AppointmentBookDTO> findAppointmentBookByDate(
            final Long pLocationId, final Date startTime, final Date endTime) {

        return new AppointmentCriterion<AppointmentBookDTO>() {

            @Override
            public List<AppointmentBookDTO> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder( "select distinct " +
                        " AB.APPOINTMENT_ID " +
                        ", to_char(A.START_TIME,'MM/DD/YYYY') as START_DATE " +
                        ", to_char(A.START_TIME, 'HH:MI AM') as START_TIME " +
                        ", to_char(A.END_TIME, 'HH:MI AM') as END_TIME " +
                        ", L.EXTERNAL_ID LOCATION_EXTERNAL_ID " +
                        ", L.LOCATION_NAME " +
                        ", AB.BOOK_FLAG " +
                        ", AB.SITE  " +
                        ", ABT.DURATION " +
                        ", ABT.ORDER_ID " +
                        ", ABT.CUSTOMER_EXTERNAL_ID " +
                        ", CUP.FIRST_NAME CUSTOMER_FIRST_NAME " +
                        ", CUP.LAST_NAME CUSTOMER_LAST_NAME " +
                        ", ABT.INSTRUCTOR_EXTERNAL_ID " +
                        ", INJ.FIRST_NAME INSTRUCTOR_FIRST_NAME " +
                        ", INJ.LAST_NAME INSTRUCTOR_LAST_NAME " +
                        ", ABT.POS_REFERENCE_NUMBER " +
                        ", ABT.STATUS " +
                        ", ABT.ERROR_INFO " +
                        " from APPOINTMENT_BOOK AB " +
                        " INNER JOIN APPOINTMENT_BOOK_TRANSACTION ABT ON AB.ORDER_ID = ABT.ORDER_ID " +
                        " LEFT OUTER JOIN APPOINTMENT A ON A.APPOINTMENT_ID = AB.APPOINTMENT_ID " +
                        " LEFT OUTER JOIN INSTRUCTOR I ON I.EXTERNAL_ID = ABT.INSTRUCTOR_EXTERNAL_ID " +
                        " LEFT OUTER JOIN PERSON INJ ON INJ.PERSON_ID = I.PERSON_ID " +
                        " LEFT OUTER JOIN CUSTOMER C ON C.EXTERNAL_ID = ABT.CUSTOMER_EXTERNAL_ID " +
                        " LEFT OUTER JOIN PERSON CUP ON CUP.PERSON_ID = C.PERSON_ID " +
                        " LEFT OUTER JOIN LOCATION L ON L.PROFILE_ID = A.PROFILE_ID " +
                        " WHERE " +
                        " AB.BOOK_FLAG = 'N' " +
                        " AND DECODE(A.CANCELED, NULL, 'N', A.CANCELED) != 'Y' " +
                        " AND L.LOCATION_ID = :locationId " +
                        " AND A.START_TIME >= :startTime " +
                        " AND A.START_TIME <= :endTime " +

                        //      " and A.START_TIME >= :startTime " +
                        //      " and A.START_TIME <= :endTime " +
                        " order by to_char(A.START_TIME, 'MM/DD/YYYY') ");

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb.toString());
                query.setParameter("locationId", pLocationId);
                query.setParameter("startTime", startTime);
                query.setParameter("endTime", endTime);

                query.addScalar("APPOINTMENT_ID", StandardBasicTypes.STRING);
                query.addScalar("START_DATE", StandardBasicTypes.STRING);
                query.addScalar("START_TIME", StandardBasicTypes.STRING);
                query.addScalar("END_TIME", StandardBasicTypes.STRING);
                query.addScalar("LOCATION_EXTERNAL_ID", StandardBasicTypes.STRING);
                query.addScalar("LOCATION_NAME", StandardBasicTypes.STRING);
                query.addScalar("BOOK_FLAG", StandardBasicTypes.STRING);
                query.addScalar("SITE", StandardBasicTypes.STRING);
                query.addScalar("DURATION", StandardBasicTypes.STRING);
                query.addScalar("ORDER_ID", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_EXTERNAL_ID", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_FIRST_NAME", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_LAST_NAME", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_EXTERNAL_ID", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_FIRST_NAME", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_LAST_NAME", StandardBasicTypes.STRING);
                query.addScalar("POS_REFERENCE_NUMBER", StandardBasicTypes.STRING);
                query.addScalar("STATUS", StandardBasicTypes.STRING);
                query.addScalar("ERROR_INFO", StandardBasicTypes.STRING);

                List<Object[]> results = query.getResultList();
                List<AppointmentBookDTO> appointmentBookDTOs = new ArrayList<>();

                for (Object[] objects : results) {
                    AppointmentBookDTO dto = new AppointmentBookDTO();
                    dto.setAppointmentId((String) objects[0]);
                    dto.setStartDate((String) objects[1]);
                    dto.setStartTime((String) objects[2]);
                    dto.setEndTime((String) objects[3]);
                    dto.setLocationId((String) objects[4]);
                    dto.setLocation((String) objects[5]);
                    dto.setBookFlag((String) objects[6]);
                    dto.setSite((String) objects[7]);
                    dto.setDuration((String) objects[8]);
                    dto.setOrderId((String) objects[9]);
                    dto.setCustomerId((String) objects[10]);
                    dto.setCustomerFirstName((String) objects[11]);
                    dto.setCustomerLastName((String) objects[12]);
                    dto.setInstructorId((String) objects[13]);
                    dto.setInstructorFirstName((String) objects[14]);
                    dto.setInstructorLastName((String) objects[15]);
                    dto.setPosRefNumber((String) objects[16]);
                    dto.setStatus((String) objects[17]);
                    dto.setErrorInfo((String) objects[18]);
                    dto.setShowResubmit("Y".equalsIgnoreCase(dto.getBookFlag()));
                    appointmentBookDTOs.add(dto);
                }

                return appointmentBookDTOs;
            }
        };
    }



    //LES-624


    public static Criterion<Appointment, CustomerAppointmentDTO> findCustomerAppointmentsByCustomerIdAndDate(
            final Long customerId, final Date startTime, final Date endTime) {

        return new AppointmentCriterion<CustomerAppointmentDTO>() {

            @Override
            public List<CustomerAppointmentDTO> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sql  = new StringBuilder( "SELECT " +
                        " T.APPOINTMENT_ID , " +
                        " to_char(T.START_TIME,'MM/DD/YYYY') as START_DATE , " +
                        " to_char(T.START_TIME, 'HH:MI AM') as START_TIME , " +
                        " to_char(T.END_TIME, 'HH:MI AM') as END_TIME , " +
                        " L.EXTERNAL_ID LOCATION_EXTERNAL_ID , " +
                        " L.LOCATION_NAME , " +
                        " A.ACTIVITY_NAME AS ACTIVITY_NAME, " +
                        " (EXTRACT(DAY FROM ((T.END_TIME - T.START_TIME) * 24 * 60))) AS DURATION , " +
                        " P_C.EXTERNAL_ID  as CUSTOMER_EXTERNAL_ID, " +
                        " P_C.FIRST_NAME CUSTOMER_FIRST_NAME , " +
                        " P_C.LAST_NAME CUSTOMER_LAST_NAME , " +
                        " P_I.EXTERNAL_ID AS INSTRUCTOR_EXTERNAL_ID , " +
                        " P_I.FIRST_NAME INSTRUCTOR_FIRST_NAME , " +
                        " P_I.LAST_NAME INSTRUCTOR_LAST_NAME , " +
                        " IAS.SHOW_STATUS STATUS, " +
                        " IAS.COMMENTS , " +
                        " IAS.STUDENT_NOTE, " +
                        " A.SERVICE_ID AS SERVICE_ID, " +
                        " OM.JOIN_URL JOIN_URL, " +
                        " OM.ID AS ZOOM_MEETINGS_ID, "+
                        " OM.NORMALIZED_UTC_START_TIME NORMALIZED_UTC_START_TIME , " +
                        " OM.NORMALIZED_UTC_END_TIME AS UTC_END_TIME , " +
                        " LP.TZ TZ,  " +
                        " T.END_TIME AS END_TIME_DATE, "+
                        " T.START_TIME AS START_TIME_DATE "+
                        ", to_Char(c.customer_id) AS CUSTOMER_ID" +
                        " FROM APPOINTMENT T " +
                        " LEFT JOIN APP_OLO.ZOOM_MEETINGS OM ON to_char(T.APPOINTMENT_ID) = OM.LESSON_APPOINTMENT_ID " +
                        " LEFT JOIN ACTIVITY A ON T.ACTIVITY_ID = A.ACTIVITY_ID "+
                        " LEFT JOIN LOCATION L ON T.PROFILE_ID = L.PROFILE_ID " +
                        " LEFT JOIN LOCATION_PROFILE LP ON T.PROFILE_ID = LP.PROFILE_ID " +
                        " LEFT JOIN APPOINTMENT_CUSTOMERS A_C ON T.APPOINTMENT_ID = A_C.APPOINTMENT_ID " +
                        " LEFT JOIN CUSTOMER C ON A_C.CUSTOMER_ID = C.CUSTOMER_ID " +
                        " LEFT JOIN CUSTOMER_STATUS C_S ON C.CUSTOMER_STATUS_ID = C_S.CUSTOMER_STATUS_ID " +
                        " LEFT JOIN PERSON P_C ON C.PERSON_ID = P_C.PERSON_ID " +
                        " LEFT JOIN INSTRUCTOR I ON T.INSTRUCTOR_ID = I.INSTRUCTOR_ID " +
                        " LEFT JOIN PERSON P_I ON I.PERSON_ID = P_I.PERSON_ID " +
                        " LEFT JOIN ACTIVITY A ON T.ACTIVITY_ID = A.ACTIVITY_ID " +
                        " LEFT JOIN INSTRUCTOR_APPOINTMENT_STATUS IAS ON T.APPOINTMENT_ID = IAS.APPOINTMENT_ID " +
                        " WHERE " +
                        " C.CUSTOMER_ID = :customerId " +
                        " AND T.INSTRUCTOR_ID IS NOT NULL " +
                        " AND A.ACTIVITY_ID NOT IN ('200','100','0','1','160','20','120','6','684','380') " +
                        " AND T.START_TIME >= :startTime " +
                        " AND T.START_TIME <= :endTime " +
                        " AND DECODE(C_S.EXTERNAL_ID, NULL, '_', C_S.EXTERNAL_ID) != 'C' " +
                        " AND DECODE(T.CANCELED, NULL, 'N', T.CANCELED) != 'Y' " +
                        //" order by to_char(T.START_TIME, 'dd-mon-yyyy:hh24:mm') ");
                        " order by T.START_TIME ");

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sql.toString());
                query.setParameter("customerId", customerId);
                query.setParameter("startTime", startTime);
                query.setParameter("endTime", endTime);

                query.addScalar("APPOINTMENT_ID", StandardBasicTypes.STRING);
                query.addScalar("START_DATE", StandardBasicTypes.STRING);
                query.addScalar("START_TIME", StandardBasicTypes.STRING);
                query.addScalar("END_TIME", StandardBasicTypes.STRING);
                query.addScalar("LOCATION_EXTERNAL_ID", StandardBasicTypes.STRING);
                query.addScalar("LOCATION_NAME", StandardBasicTypes.STRING);
                query.addScalar("ACTIVITY_NAME", StandardBasicTypes.STRING);
                query.addScalar("DURATION", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_EXTERNAL_ID", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_FIRST_NAME", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_LAST_NAME", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_EXTERNAL_ID", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_FIRST_NAME", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_LAST_NAME", StandardBasicTypes.STRING);
                query.addScalar("STATUS", StandardBasicTypes.STRING);
                query.addScalar("COMMENTS", StandardBasicTypes.STRING);
                query.addScalar("STUDENT_NOTE", StandardBasicTypes.STRING);
                query.addScalar("SERVICE_ID", StandardBasicTypes.STRING);
                query.addScalar("JOIN_URL", StandardBasicTypes.STRING);
                query.addScalar("ZOOM_MEETINGS_ID", StandardBasicTypes.STRING);
                query.addScalar("NORMALIZED_UTC_START_TIME", StandardBasicTypes.TIMESTAMP);
                query.addScalar("UTC_END_TIME", StandardBasicTypes.TIMESTAMP);
                query.addScalar("TZ", StandardBasicTypes.STRING);
                query.addScalar("END_TIME_DATE", StandardBasicTypes.TIMESTAMP);
                query.addScalar("START_TIME_DATE", StandardBasicTypes.TIMESTAMP);
                query.addScalar("CUSTOMER_ID", StandardBasicTypes.STRING);

                List<Object[]> results = query.getResultList();
                List<CustomerAppointmentDTO> customerAppointmentDTOS = new ArrayList<>();

                for (Object[] objects : results) {
                    CustomerAppointmentDTO dto = new CustomerAppointmentDTO();
                    dto.setAppointmentId((String) objects[0]);
                    dto.setStartDate((String) objects[1]);
                    dto.setStartTime((String) objects[2]);
                    dto.setEndTime((String) objects[3]);
                    dto.setLocationId((String) objects[4]);
                    dto.setLocation((String) objects[5]);
                    dto.setActivityName((String) objects[6]);
                    dto.setDuration((String) objects[7]);
                    dto.setCustomerId((String) objects[8]);
                    dto.setCustomerFirstName((String) objects[9]);
                    dto.setCustomerLastName((String) objects[10]);
                    dto.setInstructorId((String) objects[11]);
                    dto.setInstructorFirstName((String) objects[12]);
                    dto.setInstructorLastName((String) objects[13]);
                    dto.setStatus((String) objects[14]);
                    dto.setComments((String) objects[15]);
                    dto.setStudentNotes((String) objects[16]);
                    dto.setServiceId((String) objects[17]);
                    dto.setJoinURL((String) objects[18]);
                    dto.setZoomMeetingID(PGPUtils.setNullToEmpty((String) objects[19]));
                    dto.setUtcStartTime(nullCheckDate(objects[20]));
                    dto.setUtcEndTime(nullCheckDate(objects[21]));
                    dto.setLocationTimezone((String) objects[22]);
                    dto.setEndTimeDate(nullCheckDate(objects[23]));
                    dto.setStartTimeDate(nullCheckDate(objects[24]));
                    dto.setCustomerId((String) objects[25]);

                    dto.setEnabledComments(!StringUtils.isEmpty(dto.getComments()));
                    dto.setEnabledNotes(!StringUtils.isEmpty(dto.getStudentNotes()));
                    dto.setEnabledStatus(StringUtils.isNotEmpty(dto.getStatus()));

                    if (StringUtils.isEmpty(dto.getStatus())) {
                        dto.setStatus("Pending Instructor Response");
                    }

                    customerAppointmentDTOS.add(dto);
                }

                return customerAppointmentDTOS;
            }
        };
    }


    //-----------------For Appointment Status API  by Customer Email ID----------------



    public static Criterion<Appointment, AppointmentStatusDTO> findCustomerAppointmentStatusByEmailId(
            final List<String> emailId, final Date startTime) {

        return new AppointmentCriterion<AppointmentStatusDTO>() {

            @Override
            public List<AppointmentStatusDTO> search(EntityManager entityManager, int pFetchMode) {
                String sql =  "  SELECT T.APPOINTMENT_ID APPOINTMENT_ID," +
                        " L.EXTERNAL_ID || ' ' || L.LOCATION_NAME LOCATION," +
                        " I.EXTERNAL_ID AS INSTRUCTOR_EXTERNALID," +
                        "  P_I.FIRST_NAME  || ' ' || P_I.LAST_NAME AS INSTRUCTOR_NAME," +
                        "  TO_CHAR(T.START_TIME, 'DD-MON-YYYY') AS DATE_OF_APPOINTMENT ," +
                        "  TO_CHAR( T.START_TIME, 'HH:MI:SS AM') || '-' ||TO_CHAR( T.END_TIME, 'HH:MI:SS AM') AS TIME_FRAME," +
                        "  (EXTRACT(DAY FROM ((T.END_TIME - T.START_TIME)*24*60))) AS DURATION," +
                        "  A.ACTIVITY_NAME AS ACTIVITY_NAME," +
                        "  C.EXTERNAL_ID AS CUSTOMER," +
                        "  P_C.FIRST_NAME || ' ' || P_C.LAST_NAME AS CUSTOMER_NAME," +
                        "  P_C.EMAIL CUSTOMER_EMAIL," +
                        "  P_C.PHONE CUSTOMER_PHONE," +
                        "  IAS.SHOW_STATUS APPOINTMENT_COMPLETED_STATUS," +
                        "  IAS.COMMENTS INSTRUCTOR_INTERNAL_REMARKS," +
                        "  IAS.STUDENT_NOTE STUDENT_NOTES" +
                        "  FROM APPOINTMENT T" +
                        "  JOIN APPOINTMENT_CUSTOMERS A_C ON T.APPOINTMENT_ID = A_C.APPOINTMENT_ID" +
                        "  LEFT JOIN ACTIVITY A ON T.ACTIVITY_ID = A.ACTIVITY_ID" +
                        "  LEFT JOIN CUSTOMER C ON A_C.CUSTOMER_ID = C.CUSTOMER_ID  " +
                        "  LEFT JOIN LOCATION L  ON T.PROFILE_ID = L.PROFILE_ID  " +
                        "  LEFT JOIN PERSON P_C ON C.PERSON_ID = P_C.PERSON_ID" +
                        "  LEFT JOIN INSTRUCTOR I ON T.INSTRUCTOR_ID = I.INSTRUCTOR_ID" +
                        "  LEFT JOIN PERSON P_I ON I.PERSON_ID = P_I.PERSON_ID" +
                        "  LEFT JOIN INSTRUCTOR_APPOINTMENT_STATUS IAS  ON T.APPOINTMENT_ID = IAS.APPOINTMENT_ID" +
                        "  WHERE " +
                        //"  T.START_TIME <= :startTime AND " +
                        "   P_C.EMAIL IN (:email_Id)" +
                        "  AND DECODE(C.EXTERNAL_ID, NULL, '_', C.EXTERNAL_ID) != 'C' " +
                        "  AND DECODE(T.CANCELED, NULL, 'N', T.CANCELED) != 'Y' " +
                        "   " ;

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sql);
                query.setParameterList("emailId", emailId);

                query.addScalar("APPOINTMENT_ID", StandardBasicTypes.LONG);
                query.addScalar("LOCATION", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_EXTERNALID", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_NAME", StandardBasicTypes.STRING);
                query.addScalar("DATE_OF_APPOINTMENT", StandardBasicTypes.STRING);
                query.addScalar("TIME_FRAME", StandardBasicTypes.STRING);
                query.addScalar("DURATION", StandardBasicTypes.STRING);
                query.addScalar("ACTIVITY_NAME", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_NAME", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_EMAIL", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_PHONE", StandardBasicTypes.STRING);
                query.addScalar("APPOINTMENT_COMPLETED_STATUS", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_INTERNAL_REMARKS", StandardBasicTypes.STRING);
                query.addScalar("STUDENT_NOTES", StandardBasicTypes.STRING);

                List<Object[]> results = query.getResultList();
                List<AppointmentStatusDTO> appointmentStatusDTOList = new ArrayList<>();

                for (Object[] objects : results) {
                    AppointmentStatusDTO dto = new AppointmentStatusDTO();
                    dto.setAppointmentId((Long) objects[0]);
                    dto.setLocation(PGPUtils.setNullToEmpty((String) objects[1]));
                    dto.setInstructorExternalId(PGPUtils.setNullToEmpty((String) objects[2]));
                    dto.setInstructorName(PGPUtils.setNullToEmpty((String) objects[3]));
                    dto.setDateOfAppointment(PGPUtils.setNullToEmpty((String) objects[4]));
                    dto.setTimeFrame(PGPUtils.setNullToEmpty((String) objects[5]));
                    dto.setDuration(PGPUtils.setNullToEmpty((String) objects[6]));
                    dto.setActivityName(PGPUtils.setNullToEmpty((String) objects[7]));
                    dto.setCustomerExternalId(PGPUtils.setNullToEmpty((String) objects[8]));
                    dto.setCustomerName(PGPUtils.setNullToEmpty((String) objects[9]));
                    dto.setCustomerEmail(PGPUtils.setNullToEmpty((String) objects[10]));
                    dto.setCustomerPhone(PGPUtils.setNullToEmpty((String) objects[11]));
                    dto.setShowStatus(PGPUtils.setNullToEmpty((String) objects[12]));
                    dto.setComments(PGPUtils.setNullToEmptyAndNewLineToSpace((String) objects[13]));
                    dto.setStudentNote(PGPUtils.setNullToEmptyAndNewLineToSpace((String) objects[14]));

                    appointmentStatusDTOList.add(dto);
                }

                return appointmentStatusDTOList;
            }
        };
    }


    //Added for new Instructor Availabilty Service
    /**
     * Returns a Criterion that can be used to find  details of appointments passed from the UI
     *
     * @param appointmentId Long
     *
     * @return Criterion instance
     */


    public static Criterion<Appointment, LocationProfileInfoDTO> findProfileDetailByAppointmentIDInsAVL(
            final Long appointmentId) {

        return new AppointmentCriterion<LocationProfileInfoDTO>() {

            @Override
            public List<LocationProfileInfoDTO> search(EntityManager entityManager, int pFetchMode) {
                String sql = " select t.profile_id as profileId, t.appointment_series_id ,l.location_id, t.activity_id, " +
                        " l.location_name as locationName, l.phone  ,to_char(ap.series_start_time,'YYYY-MM-DD')as seriesStartDate, " +
                        " to_char(t.start_time, 'YYYY-MM-DD') as appointmentDate,  " +
                        " (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration, " +
                        " to_char(t.start_time,'HH:MI AM') as start_time, to_char(t.end_time,'HH:MI AM') as end_time, "+
                        " to_char(ap.series_end_time,'YYYY-MM-DD')as seriesEndDate ," +
                        " ap.is_recurring as isRecurring" +
                        " from appointment t  " +
                        " join appointment_series ap on t.appointment_series_id = ap.appointment_series_id "+
                        " join location_profile lp on t.profile_id = lp.profile_id "+
                        " join location l on lp.profile_id = l.profile_id " +
                        " where t.appointment_id=:appointmentId";

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sql);
                query.setParameter("appointmentId", appointmentId);

                query.addScalar("profileId", StandardBasicTypes.LONG);
                query.addScalar("appointmentSeriesId", StandardBasicTypes.LONG);
                query.addScalar("locationId", StandardBasicTypes.LONG);
                query.addScalar("activityId", StandardBasicTypes.LONG);
                query.addScalar("locationName", StandardBasicTypes.STRING);
                query.addScalar("phone", StandardBasicTypes.STRING);
                query.addScalar("seriesStartDate", StandardBasicTypes.STRING);
                query.addScalar("appointmentDate", StandardBasicTypes.STRING);
                query.addScalar("duration", StandardBasicTypes.LONG);
                query.addScalar("startTime", StandardBasicTypes.STRING);
                query.addScalar("endTime", StandardBasicTypes.STRING);
                query.addScalar("seriesEndDate", StandardBasicTypes.STRING);
                query.addScalar("isRecurring", StandardBasicTypes.STRING);

                List<Object[]> results = query.getResultList();
                List<LocationProfileInfoDTO> result = new ArrayList<>();

                for (Object[] objects : results) {
                    LocationProfileInfoDTO dto = new LocationProfileInfoDTO(
                            (Long) objects[0],
                            (String) objects[4],
                            (String) objects[5],
                            (String) objects[6],
                            (Long) objects[1],
                            (Long) objects[2],
                            (Long) objects[3],
                            (String) objects[7],
                            (Long) objects[8],
                            (String) objects[9],
                            (String) objects[10],
                            (String) objects[11],
                            (String) objects[12]
                    );
                    result.add(dto);
                }

                return result;
            }
        };
    }


    public static Criterion<Appointment, Appointment> findByActiveAppointmentbyAppSeries(final long pAppointmentSeriesId) {

        return new AppointmentCriterion<Appointment>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Appointment> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Appointment t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" where t.appointmentSeries.appointmentSeriesId = :appointmentSeriesId and t.canceled != 'Y' ");

                sb.append(" order by t.updated ");
                Query query = entityManager.createQuery(sb.toString());
                query.setParameter("appointmentSeriesId", pAppointmentSeriesId);

                return query.getResultList();
            }

        };
    }


    public static Criterion<Appointment, CustomerAppointmentDetailsResultDTO> findByCustomerAndDate(
            final String customerId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<CustomerAppointmentDetailsResultDTO>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<CustomerAppointmentDetailsResultDTO> search(EntityManager entityManager, int pFetchMode) {
                /*
                 * String sql = " SELECT T.APPOINTMENT_ID AS APPOINTMENTID, L.EXTERNAL_ID || ' '
                 * || L.LOCATION_NAME AS LOCATION, I.EXTERNAL_ID AS INSTRUCTOR_EXTERNALID,
                 * P_I.FIRST_NAME || ' ' || P_I.LAST_NAME AS INSTRUCTOR_NAME,
                 * TO_CHAR(T.START_TIME, 'DD-MON-YYYY') AS DATE_OF_APPOINTMENT,
                 * TO_CHAR(T.START_TIME, 'HH:MI AM') || '-' || TO_CHAR(T.END_TIME, 'HH:MI AM')
                 * AS TIME_FRAME, EXTRACT(DAY FROM (T.END_TIME - T.START_TIME)) AS DURATION,
                 * A.ACTIVITY_NAME AS ACTIVITY_NAME, C.EXTERNAL_ID AS CUSTOMER, P_C.FIRST_NAME
                 * || ' ' || P_C.LAST_NAME AS CUSTOMER_NAME, P_C.EMAIL AS CUSTOMER_EMAIL,
                 * P_C.PHONE AS CUSTOMER_PHONE FROM APPOINTMENT T JOIN APPOINTMENT_CUSTOMERS A_C
                 * ON T.APPOINTMENT_ID = A_C.APPOINTMENT_ID LEFT JOIN ACTIVITY A ON
                 * T.ACTIVITY_ID = A.ACTIVITY_ID LEFT JOIN CUSTOMER C ON A_C.CUSTOMER_ID =
                 * C.CUSTOMER_ID LEFT JOIN LOCATION L ON T.PROFILE_ID = L.PROFILE_ID LEFT JOIN
                 * PERSON P_C ON C.PERSON_ID = P_C.PERSON_ID LEFT JOIN INSTRUCTOR I ON
                 * T.INSTRUCTOR_ID = I.INSTRUCTOR_ID LEFT JOIN PERSON P_I ON I.PERSON_ID =
                 * P_I.PERSON_ID WHERE DECODE(T.CANCELED, NULL, 'N', T.CANCELED) != 'Y' AND
                 * T.START_TIME >= :startTime AND T.START_TIME <= :endTime AND C.EXTERNAL_ID =
                 * :customerId " ;
                 */

                String sql = "SELECT "
                        + "T.APPOINTMENT_ID AS APPOINTMENTID, "
                        + "L.EXTERNAL_ID || ' ' || L.LOCATION_NAME AS LOCATION, "
                        + "I.EXTERNAL_ID AS INSTRUCTOR_EXTERNALID, "
                        + "P_I.FIRST_NAME || ' ' || P_I.LAST_NAME AS INSTRUCTOR_NAME, "
                        + "TO_CHAR(T.START_TIME, 'DD-MON-YYYY') AS DATE_OF_APPOINTMENT, "
                        + "TO_CHAR(T.START_TIME, 'HH:MI AM') || '-' || TO_CHAR(T.END_TIME, 'HH:MI AM') AS TIME_FRAME, "
                        + "EXTRACT(DAY FROM (T.END_TIME - T.START_TIME)) AS DURATION, "
                        + "A.ACTIVITY_NAME AS ACTIVITY_NAME, "
                        + "C.EXTERNAL_ID AS CUSTOMER, "
                        + "P_C.FIRST_NAME || ' ' || P_C.LAST_NAME AS CUSTOMER_NAME, "
                        + "P_C.EMAIL AS CUSTOMER_EMAIL, "
                        + "P_C.PHONE AS CUSTOMER_PHONE, "
                        + "I.INSTRUCTOR_ID AS INSTRUCTOR_ID, "
                        + "A.ACTIVITY_ID AS ACTIVITY_ID, "
                        + "A.SERVICE_ID AS SERVICE_ID "

                        + "FROM APPOINTMENT T "
                        + "JOIN APPOINTMENT_CUSTOMERS A_C ON T.APPOINTMENT_ID = A_C.APPOINTMENT_ID "
                        + "LEFT JOIN ACTIVITY A ON T.ACTIVITY_ID = A.ACTIVITY_ID "
                        + "LEFT JOIN CUSTOMER C ON A_C.CUSTOMER_ID = C.CUSTOMER_ID "
                        + "LEFT JOIN LOCATION L ON T.PROFILE_ID = L.PROFILE_ID "
                        + "LEFT JOIN PERSON P_C ON C.PERSON_ID = P_C.PERSON_ID "
                        + "LEFT JOIN INSTRUCTOR I ON T.INSTRUCTOR_ID = I.INSTRUCTOR_ID "
                        + "LEFT JOIN PERSON P_I ON I.PERSON_ID = P_I.PERSON_ID "
                        + "WHERE "
                        + "DECODE(T.CANCELED, NULL, 'N', T.CANCELED) != 'Y' "
                        + "AND T.START_TIME >= :startTime "
                        + "AND T.START_TIME <= :endTime "
                        + "AND C.EXTERNAL_ID = :customerId";

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sql);
                query.setParameter("customerId", customerId);
                query.setParameter("startTime", pStartTime);
                query.setParameter("endTime", pEndTime);

                query.addScalar("APPOINTMENTID", StandardBasicTypes.LONG);
                query.addScalar("LOCATION", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_EXTERNALID", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_NAME", StandardBasicTypes.STRING);
                query.addScalar("DATE_OF_APPOINTMENT", StandardBasicTypes.STRING);
                query.addScalar("TIME_FRAME", StandardBasicTypes.STRING);
                query.addScalar("DURATION", StandardBasicTypes.STRING);
                query.addScalar("ACTIVITY_NAME", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_NAME", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_EMAIL", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_PHONE", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_ID", StandardBasicTypes.STRING);
                query.addScalar("ACTIVITY_ID", StandardBasicTypes.STRING);
                query.addScalar("SERVICE_ID", StandardBasicTypes.STRING);


                List<Object[]> results = query.getResultList();
                List<CustomerAppointmentDetailsResultDTO> resultList = new ArrayList<>();

                for (Object[] row : results) {
                    CustomerAppointmentDetailsResultDTO dto = new CustomerAppointmentDetailsResultDTO();
                    dto.setAppointmentId((Long) row[0]);
                    dto.setLocation((String) row[1]);
                    dto.setInstructorExternalId((String) row[2]);
                    dto.setInstructorName((String) row[3]);
                    dto.setDateOfAppointment((String) row[4]);
                    dto.setTimeFrame((String) row[5]);
                    dto.setDuration((String) row[6]);
                    dto.setActivityName((String) row[7]);
                    dto.setCustomerExternalId((String) row[8]);
                    dto.setCustomerName((String) row[9]);
                    dto.setCustomerEmail((String) row[10]);
                    dto.setCustomerPhone((String) row[11]);
                    dto.setInstructorInternalId((String) row[12]);
                    dto.setActivityId((String) row[13]);
                    dto.setServiceId((String) row[14]);

                    resultList.add(dto);
                }

                return resultList;
            }
        };
    }





    public static Criterion<Appointment, EmployeeScheduleDTO> findMasterEmployeeDetails(final String pStartTime, final String pEndTime) {

        return new AppointmentCriterion<EmployeeScheduleDTO>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<EmployeeScheduleDTO> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder();
                sb.append("SELECT ")
                        .append("i.EXTERNAL_ID AS Instructor_ID, ")
                        .append("l.external_id AS storeNumber, ")
                        .append("to_char(t.start_time, 'YYYY-MM-DD\"T\"HH24:MI:SS') AS START_DATE_OF_APPOINTMENT, ")
                        .append("to_char(t.end_time, 'YYYY-MM-DD\"T\"HH24:MI:SS') AS END_DATE_OF_APPOINTMENT, ")
                        .append("a.activity_name AS activity_name ")
                        .append("FROM appointment t ")
                        .append("JOIN location l ON t.profile_id = l.profile_id ")
                        .append("LEFT JOIN appointment_customers a_c ON t.appointment_id = a_c.appointment_id ")
                        .append("LEFT JOIN LOCATION_PROFILE lp ON t.profile_id = lp.profile_id ")
                        .append("LEFT JOIN customer c ON a_c.customer_id = c.customer_id ")
                        .append("LEFT JOIN person p_c ON c.person_id = p_c.person_id ")
                        .append("JOIN instructor i ON t.instructor_id = i.instructor_id ")
                        .append("LEFT JOIN person p_i ON i.person_id = p_i.person_id ")
                        .append("LEFT JOIN PERSON_PERSONAL_DETAILS ppd ON ppd.person_id = p_i.person_id ")
                        .append("LEFT JOIN room r ON t.room_id = r.room_id ")
                        .append("LEFT JOIN activity a ON t.activity_id = a.activity_id ")
                        .append("LEFT JOIN availability ay ON i.availability_id = ay.availability_id ")
                        .append("WHERE l.location_id IN (SELECT location_id FROM location l JOIN LOCATION_PROFILE lp ON l.PROFILE_ID = lp.PROFILE_ID WHERE lp.ENABLED = 'Y') ")
                        .append("AND t.start_time BETWEEN to_date(:startTime, 'YYYY-MM-DD HH24:MI:SS') AND to_date(:endTime, 'YYYY-MM-DD HH24:MI:SS') ")
                        .append("AND COALESCE(t.canceled, 'N') != 'Y' ")
                        .append("AND l.external_id IN ('101', '110') ")
                        .append("AND a.ACTIVITY_ID NOT IN ('200', '100', '140', '0', '1', '160', '6') ")
                        .append("ORDER BY l.external_id, to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS')");

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sb.toString());

                query.addScalar("storeNumber", StandardBasicTypes.STRING);
                query.addScalar("Instructor_ID", StandardBasicTypes.STRING);
                query.addScalar("START_DATE_OF_APPOINTMENT", StandardBasicTypes.STRING);
                query.addScalar("END_DATE_OF_APPOINTMENT", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);

                query.setParameter("startTime", pStartTime);
                query.setParameter("endTime", pEndTime);

                List<EmployeeScheduleDTO> result = new ArrayList<>();
                try (ScrollableResults scroll = query.scroll()) {
                    while (scroll.next()) {
                        Object[] objects = (Object[]) scroll.get();

                        EmployeeScheduleDTO dto = new EmployeeScheduleDTO();
                        dto.setStore((String) objects[1]);
                        dto.setInstructorId((String) objects[0]);
                        dto.setStartTime((String) objects[2]);
                        dto.setStartDateTime(getDateFromString((String) objects[2]));
                        dto.setEndTime((String) objects[3]);
                        dto.setEndDateTime(getDateFromString((String) objects[3]));
                        dto.setActvity((String) objects[4]);


                        result.add(dto);
                    }
                }
                return result;
            }

            // Assuming this method is defined elsewhere to convert date strings
            private Date getDateFromString(String dateString) {
                // Implement the conversion logic here
                // For example: return new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss").parse(dateString);
                return null;
            }
        };
    }

    private static Date getDateFromString(String dateStr){
        //dateStr = "2022-11-14T12:30:00";
        dateStr =dateStr.replace("T"," ");
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //String dateInString = "7-Jun-2013";
        Date date = new Date();

        try {

            date = formatter.parse(dateStr);

        } catch (ParseException e) {
            LOG.error("Caught {} when check getDateFromString Appointment", e);
        }
        return date;

    }




    public static Criterion<Appointment, InstructorAVLServiceResponseDTO> findLocationDistAndRegin() {

        return new AppointmentCriterion<InstructorAVLServiceResponseDTO>() {

            @Override
            public List<InstructorAVLServiceResponseDTO> search(EntityManager entityManager, int pFetchMode) {
                String sql = "  SELECT l.external_id AS external_id, l.LOCATION_NAME AS LOCATION_NAME, ldr.location_dst_desc AS location_dst_desc, ldr.LOCATION_REG_DESC AS LOCATION_REG_DESC FROM location l JOIN location_dst_reg ldr ON l.location_id = ldr.location_id ";

                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sql);

                query.addScalar("external_id", StandardBasicTypes.STRING);
                query.addScalar("LOCATION_NAME", StandardBasicTypes.STRING);
                query.addScalar("location_dst_desc", StandardBasicTypes.STRING);
                query.addScalar("LOCATION_REG_DESC", StandardBasicTypes.STRING);

                List<InstructorAVLServiceResponseDTO> result = new ArrayList<>();

                ScrollableResults scroll = query.scroll();
                try {
                    while (scroll.next()) {
                        Object[] objects = (Object[]) scroll.get();

                        InstructorAVLServiceResponseDTO dto = new InstructorAVLServiceResponseDTO();

                        dto.setStoreNumber((String) objects[0]);
                        dto.setStoreLocation((String) objects[1]);
                        dto.setStoreDistrict((String) objects[2]);
                        dto.setStoreRegion((String) objects[3]);

                        // Default empty strings for null values
                        if (dto.getStoreNumber() == null) dto.setStoreNumber("");
                        if (dto.getStoreLocation() == null) dto.setStoreLocation("");
                        if (dto.getStoreDistrict() == null) dto.setStoreDistrict("");
                        if (dto.getStoreRegion() == null) dto.setStoreRegion("");

                        result.add(dto);
                    }
                } finally {
                    scroll.close(); // Ensure the scroll is closed in case of exceptions
                }
                return result;
            }
        };
    }






/*
    public static Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> findInstructorApptsExcludingBreaks(
            final Long instructorId, final Date pStartTime, final Date pEndTime, final String pstartDate) {

        return new AppointmentCriterion<Map<String, List<InstructorAvailableHoursDTO>>>() {

            @Override
            public Map<String, List<InstructorAvailableHoursDTO>> get(EntityManager entityManager, int pFetchMode) {


                String sb = "select to_char(t.start_time, 'yyyy-MM-dd') as date1 , " +
                        "to_char(t.start_time,'HH24:Mi') as start_time, to_char(t.end_time,'HH24:Mi') as end_time from appointment t " +
                        "where  TO_CHAR(t.start_time, 'yyyy-mm-dd') = :pstartDate   and decode(t.canceled, null, 'N', t.canceled) != 'Y'  " +
                        "and t.ACTIVITY_ID not in ('20','120','300','400','360','401') " +
                        "and t.instructor_id in (:instructorId) " +
                        "minus " +
                        "select to_char(t.start_time, 'yyyy-MM-dd') as date1 , " +
                        "to_char(t.start_time,'HH24:Mi') as start_time, " +
                        "to_char(t.end_time,'HH24:Mi') as end_time " +
                        "from appointment t ,onetime ot " +
                        "where " +
                        "t.ACTIVITY_ID not in ('20','120','300','400','360','401') " +
                        "and t.instructor_id = ot.instructor_id " +
                        "and TO_CHAR(t.start_time, 'yyyy-mm-dd') = :pstartDate " +
                        //--"and t.end_time <= :pEndTime " +
                        "and t.start_time >= ot.start_time " +
                        "and t.end_time <= ot.end_time " +
                        "and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        "and t.instructor_id in (:instructorId) order by date1 desc " ;


                SQLQuery query = entityManager.createSQLQuery(sb);

                Long instructorId2 =20057l;
                query.setLong("instructorId", instructorId2);
                query.setString("pstartDate", pstartDate);
                //query.setDate("pEndTime", pEndTime);

                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("start_time", StandardBasicTypes.STRING);
                query.addScalar("end_time", StandardBasicTypes.STRING);

                Map<String, List<InstructorAvailableHoursDTO>> cache = new HashMap<String, List<InstructorAvailableHoursDTO>>();

                InstructorAvailableHoursDTO instructorAvailableHoursDTO = null;

                List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList = null;

                ScrollableResults scroll = query.scroll();

                while (scroll.next()) {

                    Object[] objects = scroll.get();
                    String instructorDate = (String) objects[0];

                    if (!cache.containsKey(instructorDate)) {

                        List<InstructorAvailableHoursDTO> list = new ArrayList<InstructorAvailableHoursDTO>();
                        cache.put(instructorDate, list);

                    }

                    instructorAvailableHoursDTOList = cache.get(instructorDate);

                    DateTimeFormatter df1 = DateTimeFormat.forPattern(DateTimeUtil.HOURS_PATTERN);

                    String startTime = (String) objects[1];
                    String endTime = (String) objects[2];
                    instructorAvailableHoursDTO = new InstructorAvailableHoursDTO(df1.parseLocalTime(startTime),df1.parseLocalTime(endTime)) ;

                    instructorAvailableHoursDTOList.add(instructorAvailableHoursDTO);

                }
                scroll.close();
                return cache;

            }
        };

    }*/



    public static Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> findInstructorApptsIncludingBreaks(
            final Long instructorId, final Date pStartTime, final Date pEndTime, final String pstartDate, final String pstartTime1, final String pstartTime2) {

        return new AppointmentCriterion<Map<String, List<InstructorAvailableHoursDTO>>>() {

            @Override
            public Map<String, List<InstructorAvailableHoursDTO>> get(EntityManager entityManager, int pFetchMode) {
                /*
                 * String sql = " SELECT TO_CHAR(t.start_time, 'yyyy-MM-dd') AS date1,
                 * TO_CHAR(t.start_time, 'HH24:MI') AS start_time, TO_CHAR(t.end_time,
                 * 'HH24:MI') AS end_time FROM appointment t WHERE TO_DATE(TO_CHAR(t.start_time,
                 * 'YYYY-MM-DD HH24:MI'), 'YYYY-MM-DD HH24:MI') >= TO_DATE(:pstartDate1,
                 * 'YYYY-MM-DD HH24:MI') AND TO_DATE(TO_CHAR(t.end_time, 'YYYY-MM-DD HH24:MI'),
                 * 'YYYY-MM-DD HH24:MI') <= TO_DATE(:pstartDate2, 'YYYY-MM-DD HH24:MI') AND
                 * DECODE(t.canceled, NULL, 'N', t.canceled) != 'Y' AND t.ACTIVITY_ID NOT IN
                 * ('300', '400', '401', '20', '360', '120') AND t.instructor_id = :instructorId
                 * MINUS SELECT TO_CHAR(t.start_time, 'yyyy-MM-dd') AS date1,
                 * TO_CHAR(t.start_time, 'HH24:MI') AS start_time, TO_CHAR(t.end_time,
                 * 'HH24:MI') AS end_time FROM appointment t JOIN onetime ot ON t.instructor_id
                 * = ot.instructor_id WHERE t.ACTIVITY_ID NOT IN ('300', '400', '401', '20',
                 * '360', '120') AND TO_CHAR(t.start_time, 'yyyy-MM-dd') = :pstartDate AND
                 * t.start_time >= ot.start_time AND t.end_time <= ot.end_time AND
                 * DECODE(t.canceled, NULL, 'N', t.canceled) != 'Y' AND t.instructor_id =
                 * :instructorId ORDER BY date1 DESC ";
                 */

                String sql = "SELECT "
                        + "TO_CHAR(t.start_time, 'yyyy-MM-dd') AS date1, "
                        + "TO_CHAR(t.start_time, 'HH24:MI') AS start_time, "
                        + "TO_CHAR(t.end_time, 'HH24:MI') AS end_time "
                        + "FROM appointment t "
                        + "WHERE "
                        + "TO_DATE(TO_CHAR(t.start_time, 'YYYY-MM-DD HH24:MI'), 'YYYY-MM-DD HH24:MI') >= TO_DATE(:pstartDate1, 'YYYY-MM-DD HH24:MI') "
                        + "AND TO_DATE(TO_CHAR(t.end_time, 'YYYY-MM-DD HH24:MI'), 'YYYY-MM-DD HH24:MI') <= TO_DATE(:pstartDate2, 'YYYY-MM-DD HH24:MI') "
                        + "AND DECODE(t.canceled, NULL, 'N', t.canceled) != 'Y' "
                        + "AND t.ACTIVITY_ID NOT IN ('300', '400', '401', '20', '360', '120') "
                        + "AND t.instructor_id = :instructorId "
                        + "MINUS "
                        + "SELECT "
                        + "TO_CHAR(t.start_time, 'yyyy-MM-dd') AS date1, "
                        + "TO_CHAR(t.start_time, 'HH24:MI') AS start_time, "
                        + "TO_CHAR(t.end_time, 'HH24:MI') AS end_time "
                        + "FROM appointment t "
                        + "JOIN onetime ot ON t.instructor_id = ot.instructor_id "
                        + "WHERE "
                        + "t.ACTIVITY_ID NOT IN ('300', '400', '401', '20', '360', '120') "
                        + "AND TO_CHAR(t.start_time, 'yyyy-MM-dd') = :pstartDate "
                        + "AND t.start_time >= ot.start_time "
                        + "AND t.end_time <= ot.end_time "
                        + "AND DECODE(t.canceled, NULL, 'N', t.canceled) != 'Y' "
                        + "AND t.instructor_id = :instructorId "
                        + "ORDER BY date1 DESC";


                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sql);
                query.setParameter("instructorId", instructorId);
                query.setParameter("pstartDate", pstartDate);
                query.setParameter("pstartDate1", pstartTime1);
                query.setParameter("pstartDate2", pstartTime2);

                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("start_time", StandardBasicTypes.STRING);
                query.addScalar("end_time", StandardBasicTypes.STRING);

                Map<String, List<InstructorAvailableHoursDTO>> result = new HashMap<>();

                DateTimeFormatter timeFormatter = DateTimeFormat.forPattern("HH:mm");

                ScrollableResults scroll = query.scroll();
                while (scroll.next()) {
                    Object[] row = (Object[]) scroll.get();
                    String date = (String) row[0];
                    String startTimeStr = (String) row[1];
                    String endTimeStr = (String) row[2];

                    InstructorAvailableHoursDTO dto = new InstructorAvailableHoursDTO(
                            timeFormatter.parseLocalTime(startTimeStr),
                            timeFormatter.parseLocalTime(endTimeStr)
                    );

                    result.computeIfAbsent(date, k -> new ArrayList<>()).add(dto);
                }
                scroll.close();
                return result;
            }
        };
    }



    public static Criterion<Appointment, List<InstructorServiceResponseDTO>> findInstructorMaxAndMinTime(
            final Long instructorId, final Date pStartTime, final Date pEndTime, final String pstartDate) {

        return new AppointmentCriterion<List<InstructorServiceResponseDTO>>() {

            @Override
            public List<InstructorServiceResponseDTO> get(EntityManager entityManager, int pFetchMode) {
                /*
                 * String sql = " SELECT TO_CHAR(MIN(start_time), 'YYYY-MM-DD HH24:MI') AS
                 * start_time, TO_CHAR(MAX(end_time), 'YYYY-MM-DD HH24:MI') AS end_time FROM
                 * appointment WHERE TO_CHAR(start_time, 'YYYY-MM-DD') = :pstartDate AND
                 * canceled = 'N' AND instructor_id = :instructorId AND activity_id NOT IN (20,
                 * 120, 360) GROUP BY TO_CHAR(start_time, 'YYYY-MM-DD') ";
                 */

                String sql = "SELECT "
                        + "TO_CHAR(MIN(start_time), 'YYYY-MM-DD HH24:MI') AS start_time, "
                        + "TO_CHAR(MAX(end_time), 'YYYY-MM-DD HH24:MI') AS end_time "
                        + "FROM appointment "
                        + "WHERE "
                        + "TO_CHAR(start_time, 'YYYY-MM-DD') = :pstartDate "
                        + "AND canceled = 'N' "
                        + "AND instructor_id = :instructorId "
                        + "AND activity_id NOT IN (20, 120, 360) "
                        + "GROUP BY TO_CHAR(start_time, 'YYYY-MM-DD')";


                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sql);
                query.setParameter("instructorId", instructorId);
                query.setParameter("pstartDate", pstartDate);

                query.addScalar("start_time", StandardBasicTypes.STRING);
                query.addScalar("end_time", StandardBasicTypes.STRING);

                List<InstructorServiceResponseDTO> result = new ArrayList<>();

                ScrollableResults scroll = query.scroll();
                DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm");

                while (scroll.next()) {
                    Object[] row = (Object[]) scroll.get();
                    String startTimeStr = (String) row[0];
                    String endTimeStr = (String) row[1];

                    InstructorServiceResponseDTO dto = new InstructorServiceResponseDTO();
                    dto.setSeriesStartDate(startTimeStr);
                    dto.setSeriesEndDate(endTimeStr);

                    result.add(dto);
                }

                scroll.close();
                return result;
            }
        };
    }


    public static Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> findBreakMoreThan30(
            final Long instructorId, final Date pStartTime, final Date pEndTime, final String pstartDate, final String pstartTime1, final String pstartTime2) {

        return new AppointmentCriterion<Map<String, List<InstructorAvailableHoursDTO>>>() {

            @Override
            public Map<String, List<InstructorAvailableHoursDTO>> get(EntityManager entityManager, int pFetchMode) {
                String sql = "SELECT "
                        + "TO_CHAR(t.start_time, 'yyyy-MM-dd') AS date1, "
                        + "TO_CHAR(t.start_time, 'HH24:mi') AS start_time, "
                        + "TO_CHAR(t.end_time, 'HH24:mi') AS end_time "
                        + "FROM appointment t "
                        + "WHERE "
                        + "TO_DATE(TO_CHAR(t.start_time, 'YYYY-MM-DD HH24:mi'), 'YYYY-MM-DD HH24:mi') >= TO_DATE(:pstartDate1, 'YYYY-MM-DD HH24:mi') "
                        + "AND TO_DATE(TO_CHAR(t.end_time, 'YYYY-MM-DD HH24:mi'), 'YYYY-MM-DD HH24:mi') <= TO_DATE(:pstartDate2, 'YYYY-MM-DD HH24:mi') "
                        + "AND DECODE(t.canceled, NULL, 'N', t.canceled) != 'Y' "
                        + "AND t.ACTIVITY_ID IN (20, 120, 360) "
                        + "AND t.instructor_id = :instructorId "
                        + "AND ROUND((CAST(t.end_time AS DATE) - CAST(t.start_time AS DATE)) * 1440) >= 30";


                NativeQuery<Object[]> query = (NativeQuery<Object[]>) entityManager.createNativeQuery(sql);

                query.setParameter("instructorId", instructorId);
                query.setParameter("pstartDate1", pstartTime1);
                query.setParameter("pstartDate2", pstartTime2);

                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("start_time", StandardBasicTypes.STRING);
                query.addScalar("end_time", StandardBasicTypes.STRING);

                Map<String, List<InstructorAvailableHoursDTO>> result = new HashMap<>();

                ScrollableResults scroll = query.scroll();
                DateTimeFormatter formatter = DateTimeFormat.forPattern("HH:mm");

                while (scroll.next()) {
                    Object[] row = (Object[]) scroll.get();

                    String date = (String) row[0];
                    String startTimeStr = (String) row[1];
                    String endTimeStr = (String) row[2];

                    org.joda.time.LocalTime startTime = formatter.parseLocalTime(startTimeStr);
                    org.joda.time.LocalTime endTime = formatter.parseLocalTime(endTimeStr);

                    InstructorAvailableHoursDTO dto = new InstructorAvailableHoursDTO(startTime, endTime);

                    result.computeIfAbsent(date, k -> new ArrayList<>()).add(dto);
                }

                scroll.close();
                return result;
            }
        };
    }


    private  static  BigDecimal convertToBigDecimal(Object obj) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof BigDecimal) {
            return (BigDecimal) obj;
        } else if (obj instanceof String) {
            return new BigDecimal((String) obj);
        } else if (obj instanceof Integer || obj instanceof Long) {
            return BigDecimal.valueOf(((Number) obj).longValue());
        } else if (obj instanceof Double || obj instanceof Float) {
            return BigDecimal.valueOf(((Number) obj).doubleValue());
        } else {
            throw new IllegalArgumentException("Unsupported type for conversion to BigDecimal");
        }
    }


}

