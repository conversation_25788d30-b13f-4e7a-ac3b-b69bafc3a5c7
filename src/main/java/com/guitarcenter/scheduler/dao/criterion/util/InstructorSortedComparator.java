package com.guitarcenter.scheduler.dao.criterion.util;

import java.io.Serializable;
import java.util.Comparator;

import com.guitarcenter.scheduler.dao.criterion.dto.InstructorSortedDTO;

public class InstructorSortedComparator implements Comparator<InstructorSortedDTO>, Serializable {

    private static final long serialVersionUID = 1L;

    @Override
    public int compare(InstructorSortedDTO o1, InstructorSortedDTO o2) {
        Long duration_o1 = o1.getDuration();
        Long duration_o2 = o2.getDuration();
        int compareTo = duration_o1.compareTo(duration_o2);
        if (compareTo != 0) return compareTo;
        Long roomId_o1 = o1.getInstructorId();
        Long roomId_o2 = o2.getInstructorId();
        return roomId_o1.compareTo(roomId_o2);
    }
}
