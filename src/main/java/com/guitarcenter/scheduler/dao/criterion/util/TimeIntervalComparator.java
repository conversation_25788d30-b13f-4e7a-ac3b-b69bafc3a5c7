package com.guitarcenter.scheduler.dao.criterion.util;

import java.io.Serializable;
import java.util.Comparator;

import org.joda.time.DateTime;

import com.guitarcenter.scheduler.dto.TimeIntervalDTO;

public class TimeIntervalComparator implements Comparator<TimeIntervalDTO>, Serializable{
	private static final long serialVersionUID = 1L;

	@Override
	public int compare(TimeIntervalDTO o1, TimeIntervalDTO o2) {
		DateTime startTime_o1=o1.getStartTime();
		DateTime startTime_o2=o2.getStartTime();
		return startTime_o1.compareTo(startTime_o2);
	}

}
