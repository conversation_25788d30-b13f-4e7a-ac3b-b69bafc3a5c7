package com.guitarcenter.scheduler.dao.criterion;


import java.util.List;

import jakarta.persistence.EntityManager;
import org.hibernate.query.Query;
import org.hibernate.Session;

import com.guitarcenter.scheduler.model.PersonPersonalDetails;
//New class created for GSSP-275
public abstract class PersonPersonalDetailsCriterion<E> extends AbstractCriterion<PersonPersonalDetails, E> implements
Criterion<PersonPersonalDetails, E>{

	

	private static final Criterion<PersonPersonalDetails, PersonPersonalDetails> DEFAULT_INSTANCE = new PersonPersonalDetailsCriterion<PersonPersonalDetails>() {};
	
	private PersonPersonalDetailsCriterion() {
		super(PersonPersonalDetails.class);
		
	}

	public static Criterion<PersonPersonalDetails, PersonPersonalDetails> getInstance() {
        return DEFAULT_INSTANCE;
    }
	
	
	@Override
	public E get(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}

	@Override
	public List<E> search(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}

	@Override
	protected String getFetchScript(int pFetchMode) {
		StringBuilder sb = new StringBuilder();
		
        return sb.toString();
	}
	
	public static Criterion<PersonPersonalDetails,PersonPersonalDetails> findByPersonId(long pPersonId)
	{
		return new PersonPersonalDetailsCriterion<PersonPersonalDetails>(){
			@SuppressWarnings("unchecked")
			@Override
            public List<PersonPersonalDetails> search(EntityManager entityManager, int pFetchMode) {
            	
                
                Query query = (Query) entityManager.createQuery(new StringBuilder(" from PersonPersonalDetails t ")
                        .append(" where t.personId = :personId").toString());
                
                query.setParameter("personId", pPersonId);
                
                
                return query.list();             
               
            }
		};
		
	}
	
	
	
	/*public static Criterion<PersonPersonalDetails,Long> updatePersonPersonalDetails(String email, long personId)
	{
		return new PersonPersonalDetailsCriterion<Long>(){
			@Override
            public Long get(EntityManager entityManager, int pFetchMode) {
            	
                StringBuilder sb = new StringBuilder("update person_Personal_Details u set u.personal_Email=:email");
                sb.append(" where u.person_Id = :personId");
                Query query = pSession.createQuery(sb.toString());
                query.setParameter("email", email);
                query.setLong("personId", personId);
                int result= query.executeUpdate();
                
                return (long) result ;
            }
		};
		
	}
	
*/	

}
