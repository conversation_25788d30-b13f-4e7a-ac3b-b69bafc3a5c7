package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_AVAILABILITY;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.Date;
import java.util.List;

import jakarta.persistence.EntityManager;
import org.hibernate.query.Query;
//import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.type.StandardBasicTypes;
import org.joda.time.DateTime;

import com.guitarcenter.scheduler.model.LocationProfile;

public abstract class LocationProfileCriterion<E> extends AbstractCriterion<LocationProfile, E> implements
        Criterion<LocationProfile, E> {

    private static final Criterion<LocationProfile, LocationProfile> DEFAULT_INSTANCE = new LocationProfileCriterion<LocationProfile>() {
    };


    private LocationProfileCriterion() {
        super(LocationProfile.class);
    }


    @Override
    protected String getFetchScript(int pFetchMode) {
        StringBuilder sb = new StringBuilder();
        sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
        sb.append(addFetchHQL(FETCH_AVAILABILITY, pFetchMode, "t.availability", true));
        sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
        return sb.toString();
    }


    @Override
    public List<E> search(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }


    @Override
    public E get(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }


    public static Criterion<LocationProfile, LocationProfile> getInstance() {
        return DEFAULT_INSTANCE;
    }
     
    
    public static Criterion<LocationProfile, LocationProfile> getByLocationProfileId(final long pProfileId) {
    	
    	
    	LocationProfileCriterion<LocationProfile> instance = new LocationProfileCriterion<LocationProfile>() {

			@Override
			public LocationProfile get(EntityManager entityManager, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from LocationProfile t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append("where t.profileId = :profileId");
				Query query = (Query) entityManager.createQuery(sb.toString());
				query.setParameter("profileId", pProfileId);
				query.setMaxResults(1);
				return (LocationProfile) query.uniqueResult();
			}

		};
		return instance;
	}

    public static Criterion<LocationProfile, Boolean> checkAppointmentDateTimeByProfileIdAndDateTime(final Long pProfileId, final Date pStartDate, final Date pEndDate, final Integer pDayOfWeek) {
        return new LocationProfileCriterion<Boolean>() {
            @Override
            public Boolean get(EntityManager entityManager, int pFetchMode) {
                Boolean result = true;
                DateTime startTime = new DateTime(pStartDate).withMillisOfSecond(0);
                DateTime endTime = new DateTime(pEndDate).withMillisOfSecond(0);
                String dayOfWeek = String.valueOf(pDayOfWeek);
                String sb = "select " +
                        "        min(to_char(t.start_time, 'HH24:MI:SS')) as min_time," +
                        "        max(to_char(t.end_time, 'HH24:MI:SS')) as max_time" +
                        " from appointment t" +
                        "    where to_char(t.start_time - 1, 'd') = :dayOfWeek" +
                        "      and t.profile_id = :profileId" +
                        "      and t.start_time >= sysdate" +
                        "      and (t.canceled is null or t.canceled = 'N')";
                Query query = entityManager.createNativeQuery(sb.toString())
                        .unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("min_time", StandardBasicTypes.TIME)
                .addScalar("max_time", StandardBasicTypes.TIME);
                query.setParameter("profileId", pProfileId);
                query.setParameter("dayOfWeek", dayOfWeek);

                Object[] colunms = (Object[]) query.uniqueResult();
                if (colunms[0] != null && colunms[1] != null) {
                    DateTime min_time = new DateTime(colunms[0]);
                    DateTime max_time = new DateTime(colunms[1]);
                    min_time = startTime.withTime(min_time.getHourOfDay(), min_time.getMinuteOfHour(), min_time.getSecondOfMinute(), min_time.getMillisOfSecond());
                    max_time = endTime.withTime(max_time.getHourOfDay(), max_time.getMinuteOfHour(), max_time.getSecondOfMinute(), max_time.getMillisOfSecond());
                    result = (startTime.isBefore(min_time) || startTime.isEqual(min_time)) && (endTime.isAfter(max_time) || endTime.isEqual(max_time));
                }
                return result;
            }
        };
    }
}
