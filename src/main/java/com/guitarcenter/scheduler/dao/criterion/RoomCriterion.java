package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.*;

import com.guitarcenter.scheduler.dao.criterion.dto.ExportDetailsDTO;
import com.guitarcenter.scheduler.model.*;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Tuple;
import jakarta.persistence.TypedQuery;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.Query;
import org.hibernate.ScrollableResults;
import org.hibernate.Session;
import org.hibernate.type.StandardBasicTypes;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.dao.criterion.dto.InstructorOpenAppointmentsDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.RoomDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.RoomSortedDTO;
import com.guitarcenter.scheduler.dao.criterion.util.RoomSortedComparator;
import com.guitarcenter.scheduler.dto.ConflictingAppointmentListDTO;
import com.guitarcenter.scheduler.model.enums.Enabled;

import java.math.BigDecimal;
import java.util.*;

public abstract class RoomCriterion<E> extends AbstractCriterion<Room, E> implements Criterion<Room, E> {

    private static final Logger LOG = LoggerFactory.getLogger(RoomCriterion.class);

    public static String newline = System.getProperty("line.separator");

    private static final Criterion<Room, Room> DEFAULT_INSTANCE = new RoomCriterion<Room>() {};

    private RoomCriterion() {
        super(Room.class);
    }

    @Override
    protected String getFetchScript(int pFetchMode) {
        StringBuilder sb = new StringBuilder();
        sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
        sb.append(addFetchHQL(FETCH_LOCATION_PROFILE, pFetchMode, "t.locationProfile", true));
        sb.append(addFetchHQL(FETCH_PARENT_ROOM, pFetchMode, "t.parentRoom", true));
        sb.append(addFetchHQL(FETCH_ROOM_NUMBER, pFetchMode, "t.roomNumber", true));
        sb.append(addFetchHQL(FETCH_ROOM_SIZE, pFetchMode, "t.roomSize", true));
        sb.append(addFetchHQL(FETCH_ROOM_TEMPLATE, pFetchMode, "t.Room", true));
        sb.append(addFetchHQL(FETCH_ROOM_TYPE, pFetchMode, "t.roomType", true));
        sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
        return sb.toString();
    }

    @Override
    public List<E> search(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object does not support this operation.");
    }

    @Override
    public E get(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object does not support this operation.");
    }

    public static Criterion<Room, Room> getInstance() {
        return DEFAULT_INSTANCE;
    }

    public static Criterion<Room, Room> findByLocationProfileId(final long pLocationProfileId) {
        return new RoomCriterion<Room>() {
            @Override
            public List<Room> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Room t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append("where t.locationProfile.profileId = :locationProfileId order by t.roomId");

                Query<Room> query = (Query<Room>) entityManager.createQuery(sb.toString(), Room.class);
                query.setParameter("locationProfileId", pLocationProfileId);
                return query.getResultList();
            }
        };
    }

    public static Criterion<Room, Room> findByProfileIdAndEnabled(final long pLocationProfileId, final Enabled pEnabled) {
        return new RoomCriterion<Room>() {
            @Override
            public List<Room> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Room t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append("where t.locationProfile.profileId = :locationProfileId");
                sb.append(" and t.enabled = :enabled");
                sb.append(" order by t.roomId");

                Query<Room> query = (Query<Room>) entityManager.createQuery(sb.toString(), Room.class);
                query.setParameter("locationProfileId", pLocationProfileId);
                query.setParameter("enabled", pEnabled.toString());
                return query.getResultList();
            }
        };
    }

    public static Criterion<Room, Room> findByRoomIds(final Long... pRoomIds) {
        return new RoomCriterion<Room>() {
            @Override
            public List<Room> search(EntityManager entityManager, int pFetchMode) {
                if (pRoomIds == null || pRoomIds.length == 0) {
                    return Collections.emptyList();
                }

                StringBuilder sb = new StringBuilder("from Room t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append("where t.roomId in(:roomIds) order by t.roomId");

                Query<Room> query = (Query<Room>) entityManager.createQuery(sb.toString(), Room.class);
                query.setParameterList("roomIds", Arrays.asList(pRoomIds));
                return query.getResultList();
            }
        };
    }

    public static Criterion<Room, Room> findByServiceId(final long pServiceId) {
        return new RoomCriterion<Room>() {
            @Override
            public List<Room> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Room t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" left join fetch t.services s ");
                sb.append("where s.serviceId = :serviceId order by t.roomId");

                Query<Room> query = (Query<Room>) entityManager.createQuery(sb.toString(), Room.class);
                query.setParameter("serviceId", pServiceId);
                return query.getResultList();
            }
        };
    }

    public static Criterion<Room, Room> findByActivityIdAndEnabled(final long pActivityId, final Enabled pEnabled) {
        return new RoomCriterion<Room>() {
            @Override
            public List<Room> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Room t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" left join fetch t.activities s ");
                sb.append("where s.activityId = :activityId");
                sb.append(" and t.enabled = :enabled");
                sb.append(" order by t.roomId");

                Query<Room> query = (Query<Room>) entityManager.createQuery(sb.toString(), Room.class);
                query.setParameter("activityId", pActivityId);
                query.setParameter("enabled", pEnabled.toString());
                return query.getResultList();
            }
        };
    }

  /*  public static Criterion<Room, Room> findByProfileIdAndActivityIds(final long pProfileId, final Long... pActivityIds) {
        return new RoomCriterion<Room>() {
            @Override
            public List<Room> search(EntityManager entityManager, int pFetchMode) {
                if (pActivityIds.length == 0) {
                    return Collections.emptyList();
                }
                StringBuilder sb = new StringBuilder("select distinct t from Room t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" left join fetch t.activities s ");
                sb.append("where s.activityId in (:activityIds)");
                sb.append(" and t.locationProfile.profileId = :profileId ");
                sb.append(" and t.enabled = :enabled");
                sb.append(" order by t.roomId");

                Query<Room> query = (Query<Room>) entityManager.createQuery(sb.toString(), Room.class);
                query.setParameter("profileId", pProfileId);
                query.setParameterList("activityIds", pActivityIds);
                query.setParameter("enabled", Enabled.Y);
                return query.getResultList();
            }
        };
    }*/

    public static RoomCriterion<Room> findByProfileIdAndActivityIds(final long pProfileId, final Long... pActivityIds) {
        return new RoomCriterion<Room>() {

            @Override
            public List<Room> search(EntityManager entityManager, int pFetchMode) {
                if (pActivityIds.length == 0) {
                    return Collections.emptyList();
                }

                StringBuilder sb = new StringBuilder("select distinct t from Room t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" left join fetch t.activities s ");
                sb.append(" where s.activityId in (:activityIds)");
                sb.append(" and t.locationProfile.profileId = :profileId ");
                sb.append(" and t.enabled = :enabled ");
                sb.append(" order by t.roomId");

                // Create the TypedQuery for type safety in Hibernate 6
                TypedQuery<Room> query = entityManager.createQuery(sb.toString(), Room.class);
                query.setParameter("profileId", pProfileId);
                // Replace setParameterList with setParameter in Hibernate 6
                query.setParameter("activityIds", Arrays.asList(pActivityIds));
                query.setParameter("enabled", Enabled.Y);

                return query.getResultList();
            }
        };
    }

    public static Criterion<Room, Room> findByRoomTemplateIdAndEnabled(final long pRoomTemplateId, final Enabled pEnabled) {
        return new RoomCriterion<Room>() {
            @Override
            public List<Room> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Room t ");
                sb.append(" left join fetch t.roomSize ");
                sb.append(" left join fetch t.site ");
                sb.append(" left join fetch t.roomType ");
                sb.append(" left join fetch t.roomNumber ");
                sb.append(" left join fetch t.activities ");
                sb.append(" left join fetch t.services ");
                sb.append("where t.roomTemplate.roomTemplateId = :roomTemplateId");
                sb.append(" and t.enabled = :enabled");
                sb.append(" order by t.roomId");

                Query<Room> query = (Query<Room>) entityManager.createQuery(sb.toString(), Room.class);
                query.setParameter("roomTemplateId", pRoomTemplateId);
                query.setParameter("enabled", pEnabled.toString());
                return query.getResultList();
            }
        };
    }

    public static Criterion<Room, Room> findByRoomTemplateId(final long pRoomTemplateId) {
        return new RoomCriterion<Room>() {
            @Override
            public List<Room> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Room t ");
                sb.append(" left join fetch t.roomSize ");
                sb.append(" left join fetch t.site ");
                sb.append(" left join fetch t.roomType ");
                sb.append(" left join fetch t.roomNumber ");
                sb.append(" left join fetch t.activities ");
                sb.append(" left join fetch t.services ");
                sb.append("where t.roomTemplate.roomTemplateId = :roomTemplateId");
                sb.append(" order by t.roomId");

                Query<Room> query = (Query<Room>) entityManager.createQuery(sb.toString(), Room.class);
                query.setParameter("roomTemplateId", pRoomTemplateId);
                return query.getResultList();
            }
        };
    }

    public static Criterion<Room, Room> findByProfileIdAndActivityIdAndDateTime(final long pProfileId, final long pActivityId, final Date pStartDate, final Date pEndDate, final Long pExcludeAppointmentId) {
        return new RoomCriterion<Room>() {
            @Override
            public List<Room> search(EntityManager entityManager, int pFetchMode) {
                //5055 we have to handle seperatly sub room .* is removed in the below query
                StringBuilder sb = new StringBuilder("SELECT t.*  ");
                sb.append(" FROM room t");
                sb.append(" LEFT JOIN room_activities r_s ON t.room_id = r_s.room_id");
                sb.append(" LEFT JOIN room sub_room ON t.room_id = sub_room.parent_id");
                sb.append(" WHERE t.profile_id = :profileId");
                sb.append(" AND r_s.activity_id = :activityId");
                sb.append(" AND (t.enabled = 'Y' OR t.enabled IS NULL)");

                List<Room> lr = new ArrayList<Room>();

                TypedQuery<Tuple> query = (TypedQuery) entityManager.createNativeQuery(sb.toString(), Tuple.class);
                query.setParameter("profileId", pProfileId);
                query.setParameter("activityId", pActivityId);

                List<Tuple> list  = query.getResultList();
                //separateTupleValuesAndAliases(list);
                Map<Long, RoomDTO> cache = new HashMap<>();

                for (Tuple tuple : list) {
                    BigDecimal room_id = tuple.get("room_id", BigDecimal.class);
                    //BigDecimal roomId2 = tuple.get("room_id", BigDecimal.class);


                    Room room = entityManager.find(Room.class, room_id);
                    //Room room2 = entityManager.find(Room.class, roomId2);
                    //Room room = rom1;
                    Room subRoom =new Room();
                    // Room subRoom = (Room) rooms[1];

                    // Room room = Long roomId = room.getRoomId();

                    RoomDTO roomDTO = cache.computeIfAbsent(room_id.toBigInteger().longValue(), id -> new RoomDTO(room, subRoom));
                    roomDTO.addSubRoom(subRoom);
                }
               /* for (Tuple rooms : list) {
                    // Assuming the order of the tuple matches the fields in Room

                    BigDecimal room_id  = rooms.get("room_id", BigDecimal.class);
                    BigDecimal parent_id  = rooms.get("parent_id", BigDecimal.class);

                    Room room = entityManager.find(Room.class, room_id);
                    Room subRoom = entityManager.find(Room.class, parent_id);
                    // Room subRoom = (Room) rooms[1];

                    // Room room = Long roomId = room.getRoomId();

                    RoomDTO roomDTO = cache.computeIfAbsent(room_id.toBigInteger().longValue(), id -> new RoomDTO(room, subRoom));
                    roomDTO.addSubRoom(subRoom);
                }*/

                Map<Long, Room> maps = new HashMap<>();

                for (Map.Entry<Long, RoomDTO> entry : cache.entrySet()) {
                    RoomDTO roomDTO = entry.getValue();
                    Room room = roomDTO.getRoom();
                    Set<Room> subRooms = roomDTO.getSubRooms();
                    Long[] subRoomIds = subRooms.stream().map(Room::getRoomId).toArray(Long[]::new);

                    StringBuilder sbCount = new StringBuilder("SELECT COUNT(*) AS counts");
                    sbCount.append(" FROM appointment t");
                    sbCount.append(" WHERE t.start_time BETWEEN :startTime AND :endTime");
                    sbCount.append(" AND t.profile_id = :profileId");
                    sbCount.append(" AND (t.canceled IS NULL OR t.canceled IN ('H', 'N'))");
                    if (pExcludeAppointmentId != null) {
                        sbCount.append(" AND t.appointment_id != :excludeAppId");
                    }

                    if (room.getParentRoom() == null && subRoomIds.length == 0) {
                        sbCount.append(" AND t.room_id = :roomId");
                    } else if (room.getParentRoom() != null && subRoomIds.length == 0) {
                        sbCount.append(" AND (t.room_id = :roomId OR t.room_id = :parentRoomId)");
                    } else if (room.getParentRoom() == null && subRoomIds.length > 0) {
                        sbCount.append(" AND (t.room_id = :roomId OR t.room_id IN (:subRoomIds))");
                    }

                    NativeQuery<Long> countQuery = (NativeQuery<Long>) entityManager.createNativeQuery(sbCount.toString(), Long.class);

                    countQuery.setParameter("startTime", pStartDate);
                    countQuery.setParameter("endTime", pEndDate);
                    countQuery.setParameter("profileId", pProfileId);
                    countQuery.setParameter("roomId", room.getRoomId());

                    if (room.getParentRoom() != null) {
                        countQuery.setParameter("parentRoomId", room.getParentRoom().getRoomId());
                    }

                    if (subRoomIds.length > 0) {
                        countQuery.setParameterList("subRoomIds", subRoomIds);
                    }

                    if (pExcludeAppointmentId != null) {
                        countQuery.setParameter("excludeAppId", pExcludeAppointmentId);
                    }

                    Long counts = countQuery.getSingleResult();
                    if (counts != null) {
                        maps.putIfAbsent(room.getRoomId(), room);
                    } else {
                        maps.remove(room.getRoomId());
                    }
                }

                if (maps.isEmpty()) {
                    return Collections.emptyList();
                }

                String sql = "select room_id, duration" +
                        " from (select t.room_id," +
                        "        abs(((to_date(:endTimeStr, 'YYYY-MM-DD HH24:MI:SS') -" +
                        "                to_date(to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS')," +
                        "                        'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 * 60)) as duration" +
                        "        from appointment t" +
                        "        where t.profile_id = :profileId" +
                        "        and t.room_id in (:rooms)" +
                        "        and (t.canceled is null or t.canceled = 'N' or t.canceled = 'H')" +
                        " and to_char(t.start_time, 'YYYY-MM-DD') >= :startDateStr" +
                        " and to_char(t.end_time, 'YYYY-MM-DD') <= :endDateStr" +
                        " union" +
                        " select t.room_id," +
                        "        abs(((to_date(:startTimeStr, 'YYYY-MM-DD HH24:MI:SS') -" +
                        "                to_date(to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS')," +
                        "                        'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 * 60)) as duration" +
                        " from appointment t" +
                        " where t.profile_id = :profileId" +
                        " and t.room_id in (:rooms)" +
                        " and (t.canceled is null or t.canceled = 'N' or t.canceled = 'H')" +
                        " and to_char(t.start_time, 'YYYY-MM-DD') >= :startDateStr" +
                        " and to_char(t.end_time, 'YYYY-MM-DD') <= :endDateStr)" +
                        " order by duration";

                NativeQuery<Object[]> durationQuery = entityManager.createNativeQuery(sql)
                        .unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("room_id", StandardBasicTypes.LONG)
                        .addScalar("duration", StandardBasicTypes.LONG);

                durationQuery.setParameter("profileId", pProfileId);
                durationQuery.setParameterList("rooms", maps.keySet());
                DateTime startDate = new DateTime(pStartDate);
                DateTime endDate = new DateTime(pEndDate);
                durationQuery.setParameter("startTimeStr", startDate.toString("yyyy-MM-dd HH:mm:ss"));
                durationQuery.setParameter("startDateStr", startDate.minusDays(7).toString("yyyy-MM-dd"));
                durationQuery.setParameter("endTimeStr", endDate.toString("yyyy-MM-dd HH:mm:ss"));
                durationQuery.setParameter("endDateStr", endDate.plusDays(7).toString("yyyy-MM-dd"));

                List<Object[]> durationList = durationQuery.getResultList();
                List<RoomSortedDTO> roomSortedDTOList = new LinkedList<>();
                Map<Long, RoomSortedDTO> roomSortedDTOCache = new HashMap<>();

                for (Object[] objects : durationList) {
                    Long roomId = (Long) objects[0];
                    Long duration = (Long) objects[1];

                    roomSortedDTOCache.computeIfAbsent(roomId, id -> {
                        RoomSortedDTO roomSortedDTO = new RoomSortedDTO(id, duration);
                        roomSortedDTOList.add(roomSortedDTO);
                        return roomSortedDTO;
                    }).setDuration(duration);
                }

                for (Long roomId : maps.keySet()) {
                    roomSortedDTOCache.computeIfAbsent(roomId, id -> {
                        RoomSortedDTO roomSortedDTO = new RoomSortedDTO(id, Long.MAX_VALUE);
                        roomSortedDTOList.add(roomSortedDTO);
                        return roomSortedDTO;
                    });
                }

                for (Map.Entry<Long, Room> entry : maps.entrySet()) {
                    Long roomId = entry.getKey();
                    RoomDTO roomDTO = cache.get(roomId);
                    RoomSortedDTO roomSortedDTO = roomSortedDTOCache.get(roomId);

                    if (roomDTO.getRoom().getParentRoom() == null && roomDTO.getSubRooms().isEmpty()) {
                        roomSortedDTO.setPriority(1L);
                    } else if (roomDTO.getRoom().getParentRoom() != null && roomDTO.getSubRooms().isEmpty()) {
                        roomSortedDTO.setPriority(0L);
                        if (roomSortedDTOCache.containsKey(roomDTO.getRoom().getParentRoom().getRoomId())){
                            roomSortedDTO.setPriority(2L);
                            roomSortedDTOCache.get(roomDTO.getRoom().getParentRoom().getRoomId()).setPriority(2L);
                        }
                    } else if (roomDTO.getRoom().getParentRoom() == null && !roomDTO.getSubRooms().isEmpty()) {
                        roomSortedDTO.setPriority(2L);
                        for (Room subRoom : roomDTO.getSubRooms()) {
                            if (roomSortedDTOCache.containsKey(subRoom.getRoomId())){
                                roomSortedDTO.setPriority(4L);
                                roomSortedDTOCache.get(subRoom.getRoomId()).setPriority(3L);
                            }
                        }
                    }
                }

                roomSortedDTOList.sort(new RoomSortedComparator());
                List<Room> rooms = new ArrayList<>();

                for (RoomSortedDTO roomSortedDTO : roomSortedDTOList) {
                    rooms.add(maps.get(roomSortedDTO.getRoomId()));
                }

                return rooms;
            }
        };
    }

    // Method to find Rooms based on profile and activity
    public static Criterion<Room, Map<Long, RoomDTO>> findByProfileIdAndActivityId(final long pProfileId, final long pActivityId) {
        return new RoomCriterion<Map<Long, RoomDTO>>() {
            @Override
            public Map<Long, RoomDTO> get(EntityManager entityManager, int pFetchMode) {

                StringBuilder sb = new StringBuilder("SELECT t.*, sub_room.*");
                sb.append(" FROM room t");
                sb.append(" LEFT JOIN room_activities r_s ON t.room_id = r_s.room_id");
                sb.append(" LEFT JOIN room sub_room ON t.room_id = sub_room.parent_id");
                sb.append(" WHERE t.profile_id = :profileId");
                sb.append(" AND r_s.activity_id = :activityId");
                sb.append(" AND (t.enabled = 'Y' OR t.enabled IS NULL)");

                TypedQuery<Object[]> query = (TypedQuery) entityManager.createNativeQuery(sb.toString(), Object[].class);
                query.setParameter("profileId", pProfileId);
                query.setParameter("activityId", pActivityId);

              /*  NativeQuery<Object[]> query = entityManager.createNativeQuery(sb.toString(), Object[].class)
                        .unwrap(org.hibernate.query.NativeQuery.class)
                        .addEntity("t", Room.class)
                        .addEntity("sub_room", Room.class);

                query.setParameter("profileId", pProfileId);
                query.setParameter("activityId", pActivityId);*/

                List<Object[]> list  = query.getResultList();
                Map<Long, RoomDTO> cache = new HashMap<>();

                for (Object[] rooms : list) {

                    BigDecimal room_id = (BigDecimal) rooms[0];
                    BigDecimal parent_id = (BigDecimal) rooms[1];
                    Room room = entityManager.find(Room.class, room_id);
                    Room subRoom = entityManager.find(Room.class, parent_id);
                    Long roomId = room.getRoomId();

                    RoomDTO roomDTO = cache.computeIfAbsent(roomId, id -> new RoomDTO(room, null));
                    roomDTO.addSubRoom(subRoom);

                  /*  BigDecimal room_id  = rooms.get("room_id", BigDecimal.class);
                    BigDecimal parent_id  = rooms.get("parent_id", BigDecimal.class);

                    Room room = entityManager.find(Room.class, room_id);
                    Room subRoom = entityManager.find(Room.class, parent_id);
                   // Room subRoom = (Room) rooms[1];

                   // Room room = Long roomId = room.getRoomId();

                    RoomDTO roomDTO = cache.computeIfAbsent(room_id.toBigInteger().longValue(), id -> new RoomDTO(room, subRoom));
                    roomDTO.addSubRoom(subRoom);

                    */
                }

                return cache;
            }
        };
    }

    public static Criterion<Room, Map<Long, RoomDTO>> findByProfileIdAndActivityIdInsAVL(final long pProfileId, final long pActivityId) {
        return new RoomCriterion<Map<Long, RoomDTO>>() {
            @Override
            public Map<Long, RoomDTO> get(EntityManager entityManager, int pFetchMode) {

                StringBuilder sb = new StringBuilder("SELECT t.*, sub_room.*");
                sb.append(" FROM room t");
                sb.append(" LEFT JOIN room_activities r_s ON t.room_id = r_s.room_id");
                sb.append(" LEFT JOIN room sub_room ON t.room_id = sub_room.parent_id");
                sb.append(" WHERE t.profile_id = :profileId");
                sb.append(" AND r_s.activity_id = :activityId");
                sb.append(" AND (t.enabled = 'Y' OR t.enabled IS NULL)");

                NativeQuery<Object[]> query = entityManager.createNativeQuery(sb.toString(), Object[].class)
                        .unwrap(org.hibernate.query.NativeQuery.class)
                        .addEntity("t", Room.class)
                        .addEntity("sub_room", Room.class);

                query.setParameter("profileId", pProfileId);
                query.setParameter("activityId", pActivityId);

                List<Object[]> list = query.getResultList();
                Map<Long, RoomDTO> cache = new HashMap<>();

                for (Object[] rooms : list) {
                    Room room = (Room) rooms[0];
                    Room subRoom = (Room) rooms[1];
                    Long roomId = room.getRoomId();

                    RoomDTO roomDTO = cache.computeIfAbsent(roomId, id -> new RoomDTO(room, null));
                    roomDTO.addSubRoom(subRoom);
                }

                return cache;
            }
        };
    }

    public static Criterion<Room, Boolean> findByProfileIdAndActivityIdAndDate(final Map<Long, RoomDTO> cache, final long pProfileId, final Date pStartDate, final Date pEndDate) {
        return new RoomCriterion<Boolean>() {
            @Override
            public Boolean get(EntityManager entityManager, int pFetchMode) {

                boolean roomAvailable = false;

                for (Map.Entry<Long, RoomDTO> entry : cache.entrySet()) {
                    RoomDTO roomDTO = entry.getValue();
                    Room room = roomDTO.getRoom();
                    Set<Room> subRooms = roomDTO.getSubRooms();
                    Long[] subRoomIds = subRooms.stream().map(Room::getRoomId).toArray(Long[]::new);

                    StringBuilder sb = new StringBuilder("SELECT COUNT(*) AS counts");
                    sb.append(" FROM appointment t");
                    sb.append(" WHERE TO_CHAR(t.start_time, 'YYYY-MM-DD') = TO_CHAR(:targetDate, 'YYYY-MM-DD')");
                    sb.append(" AND (t.canceled = 'H' OR t.canceled = 'N' OR t.canceled IS NULL)");
                    sb.append(" AND t.profile_id = :profileId");
                    sb.append(" AND ((TO_CHAR(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= TO_CHAR(:startTime, 'YYYY-MM-DD HH24:MI:SS') AND TO_CHAR(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= TO_CHAR(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                    sb.append("      OR (TO_CHAR(t.start_time, 'YYYY-MM-DD HH24:MI:SS') >= TO_CHAR(:startTime, 'YYYY-MM-DD HH24:MI:SS') AND TO_CHAR(t.end_time, 'YYYY-MM-DD HH24:MI:SS') <= TO_CHAR(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                    sb.append("      OR (TO_CHAR(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= TO_CHAR(:startTime, 'YYYY-MM-DD HH24:MI:SS') AND TO_CHAR(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= TO_CHAR(:startTime, 'YYYY-MM-DD HH24:MI:SS'))");
                    sb.append("      OR (TO_CHAR(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= TO_CHAR(:endTime, 'YYYY-MM-DD HH24:MI:SS') AND TO_CHAR(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= TO_CHAR(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");

                    if (room.getParentRoom() == null && subRoomIds.length == 0) {
                        sb.append(" AND t.room_id = :roomId");
                    } else if (room.getParentRoom() != null && subRoomIds.length == 0) {
                        sb.append(" AND (t.room_id = :roomId OR t.room_id = :parentRoomId)");
                    } else if (room.getParentRoom() == null && subRoomIds.length > 0) {
                        sb.append(" AND (t.room_id = :roomId OR t.room_id IN (:subRoomIds))");
                    }

                    NativeQuery<Long> query = (NativeQuery<Long>) entityManager.createNativeQuery(sb.toString(), Long.class);
                    query.setParameter("targetDate", pStartDate);
                    query.setParameter("profileId", pProfileId);
                    query.setParameter("startTime", pStartDate);
                    query.setParameter("endTime", pEndDate);
                    query.setParameter("roomId", room.getRoomId());

                    if (room.getParentRoom() != null) {
                        query.setParameter("parentRoomId", room.getParentRoom().getRoomId());
                    }
                    if (subRoomIds.length > 0) {
                        query.setParameterList("subRoomIds", subRoomIds);
                    }

                    Long counts = query.getSingleResult();
                    if (counts == 0) {
                        roomAvailable = true;
                        break;
                    }
                }

                return roomAvailable;
            }
        };
    }

  public static Criterion<Room, Room> findRoomByProfileIdAndActivityIdAndDate(final Map<Long, RoomDTO> cache, final long pProfileId, final Date pStartDate, final Date pEndDate) {
      return new RoomCriterion<Room>() {
          @Override
          public Room get(EntityManager entityManager, int pFetchMode) {
              Room availableRoom = new Room();

              try {
                  for (Map.Entry<Long, RoomDTO> entry : cache.entrySet()) {
                      RoomDTO roomDTO = entry.getValue();
                      Room room = roomDTO.getRoom();
                      Set<Room> subRooms = roomDTO.getSubRooms();
                      Long[] subRoomIds = subRooms.stream().map(Room::getRoomId).toArray(Long[]::new);

                      StringBuilder sb = new StringBuilder("SELECT COUNT(*) AS counts");
                      sb.append(" FROM appointment t");
                      sb.append(" WHERE TO_CHAR(t.start_time, 'YYYY-MM-DD') = TO_CHAR(:targetDate, 'YYYY-MM-DD')");
                      sb.append(" AND (t.canceled = 'H' OR t.canceled = 'N' OR t.canceled IS NULL)");
                      sb.append(" AND t.profile_id = :profileId");
                      sb.append(" AND (");
                      sb.append(" (TO_CHAR(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= TO_CHAR(:startTime, 'YYYY-MM-DD HH24:MI:SS') AND TO_CHAR(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= TO_CHAR(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                      sb.append(" OR (TO_CHAR(t.start_time, 'YYYY-MM-DD HH24:MI:SS') >= TO_CHAR(:startTime, 'YYYY-MM-DD HH24:MI:SS') AND TO_CHAR(t.end_time, 'YYYY-MM-DD HH24:MI:SS') <= TO_CHAR(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                      sb.append(" OR (TO_CHAR(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= TO_CHAR(:startTime, 'YYYY-MM-DD HH24:MI:SS') AND TO_CHAR(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= TO_CHAR(:startTime, 'YYYY-MM-DD HH24:MI:SS'))");
                      sb.append(" OR (TO_CHAR(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= TO_CHAR(:endTime, 'YYYY-MM-DD HH24:MI:SS') AND TO_CHAR(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= TO_CHAR(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                      sb.append(" )");

                      if (room.getParentRoom() == null && subRoomIds.length == 0) {
                          sb.append(" AND t.room_id = :roomId");
                      } else if (room.getParentRoom() != null && subRoomIds.length == 0) {
                          sb.append(" AND (t.room_id = :roomId OR t.room_id = :parentRoomId)");
                      } else if (room.getParentRoom() == null && subRoomIds.length > 0) {
                          sb.append(" AND (t.room_id = :roomId OR t.room_id IN (:subRoomIds))");
                      }

                      NativeQuery<Long> query = (NativeQuery<Long>) entityManager.createNativeQuery(sb.toString(), Long.class);
                      query.setParameter("targetDate", pStartDate);
                      query.setParameter("profileId", pProfileId);
                      query.setParameter("startTime", pStartDate);
                      query.setParameter("endTime", pEndDate);
                      query.setParameter("roomId", room.getRoomId());

                      if (room.getParentRoom() != null) {
                          query.setParameter("parentRoomId", room.getParentRoom().getRoomId());
                      }
                      if (subRoomIds.length > 0) {
                          query.setParameterList("subRoomIds", subRoomIds);
                      }

                      Long counts = query.getSingleResult();
                      if (counts == 0) {
                          availableRoom.setRoomId(room.getRoomId());
                          return availableRoom;
                      }
                  }
              } catch (Exception e) {
                  // Handle the exception (e.g., log it)
                  e.printStackTrace();
              }

              return null;
          }
      };
  }

    public static Criterion<Room, Map<String, List<String>>> findNonAvailableSlots(final long pProfileId, final Date pStartDate, final long activityID, final long duration) {
        return new RoomCriterion<Map<String, List<String>>>() {
            @Override
            public Map<String, List<String>> get(EntityManager entityManager, int pFetchMode) {
                Map<String, List<String>> availTimeSlots = new HashMap<>();
                double durations = duration / 60.0;

                NativeString concurrentString;

                String consolidatedSlots = "", concurrentSlots = "", dateTimeStrings = "'", date = "", time = "", parentChildSlots = "";

                NativeQuery<NativeString> query = (NativeQuery<NativeString>) entityManager.createNamedQuery("callNASlotsFunc", NativeString.class);
                query.setParameter(0, pStartDate);
                query.setParameter(2, pProfileId);
                query.setParameter(1, activityID);

                ScrollableResults scroll = query.scroll();

                while (scroll.next()) {
                    concurrentString = (NativeString) scroll.get();
                    if (concurrentString != null) {
                        concurrentSlots = concurrentString.getNoSlotsStr();
                    }
                }

                NativeQuery<NativeString> queryParentChild = (NativeQuery<NativeString>) entityManager.createNamedQuery("callSpltFunc", NativeString.class);
                queryParentChild.setParameter(0, pStartDate);
                queryParentChild.setParameter(2, pProfileId);
                queryParentChild.setParameter(1, activityID);

                ScrollableResults scrollParentChild = queryParentChild.scroll();

                while (scrollParentChild.next()) {
                    concurrentString = (NativeString) scrollParentChild.get();
                    if (concurrentString != null) {
                        parentChildSlots = concurrentString.getNoSlotsStr();
                    }
                }

                consolidatedSlots = !parentChildSlots.isEmpty() ? concurrentSlots.concat(parentChildSlots) : concurrentSlots;

                if (!consolidatedSlots.isEmpty()) {
                    StringTokenizer multiTokenizer = new StringTokenizer(consolidatedSlots, "~");
                    while (multiTokenizer.hasMoreTokens()) {
                        dateTimeStrings = multiTokenizer.nextToken();
                        StringTokenizer multiTokenizerDtTm = new StringTokenizer(dateTimeStrings, "*");
                        if (multiTokenizerDtTm.hasMoreTokens()) {
                            date = multiTokenizerDtTm.nextToken();
                            time = dateTimeStrings.substring(dateTimeStrings.indexOf("*") + 1);
                            StringTokenizer multiTokenizerTm = new StringTokenizer(time, "#");
                            while (multiTokenizerTm.hasMoreTokens()) {
                                String availSlot = multiTokenizerTm.nextToken();
                                availTimeSlots.computeIfAbsent(date, k -> new ArrayList<>()).add(availSlot);
                            }
                        }
                    }
                }

                return availTimeSlots;
            }
        };
    }

    public static Criterion<Room, ConflictingAppointmentListDTO> findConflictAppointmentsByRoom(final long pProfileId, final String pStartDate, final String pEndDate, final String pStartTime, final String pEndTime, final String pCanceled, final long proom_id) {
        return new RoomCriterion<ConflictingAppointmentListDTO>() {
            @Override
            public ConflictingAppointmentListDTO get(EntityManager entityManager, int pFetchMode) {
                String sb = "SELECT  to_char(a.start_time, 'DD-Mon-YYYY') as date1 ," +
                        "to_char(a.start_time, 'HH:MI AM') as start_time,to_char(a.end_time, 'HH:MI AM') as end_time," +
                        "(extract(day from ((a.end_time - a.start_time) * 24 * 60 * 60 * 60))) as duration," +
                        "r.profile_room_name as profile_room_name, t.activity_name as activity_name," +
                        "c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name " +
                        "FROM appointment a " +
                        "left join appointment_customers a_c on a.appointment_id = a_c.appointment_id " +
                        " left join customer c on a_c.customer_id = c.customer_id" +
                        " left join person p_c on c.person_id = p_c.person_id" +
                        " left join room r on r.room_id = a.room_id" +
                        " left join activity t on t.activity_id = a.activity_id" +
                        " WHERE TO_CHAR(start_time,'d') = TO_CHAR(to_date(:pStartDate,'yyyy/MM/dd'),'d')" +
                        "  AND TO_TIMESTAMP_TZ(TO_CHAR(start_time, 'yyyy/MM/dd'), 'yyyy/MM/dd') BETWEEN TO_TIMESTAMP_TZ(:pStartDate,'yyyy/MM/dd') AND TO_TIMESTAMP_TZ(:pEndDate,'yyyy/MM/dd')"+
                        "  AND (a.start_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,'yyyy/MM/dd')||' '||:pStartTime,'yyyy/MM/dd HH24:MI')"+
                        " 	   AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,'yyyy/MM/dd')||' '||:pEndTime,'yyyy/MM/dd HH24:MI')"+
                        " 	   OR a.end_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,'yyyy/MM/dd')||' '|| :pStartTime,'yyyy/MM/dd HH24:MI')"+
                        " 	   AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,'yyyy/MM/dd')||' '||:pEndTime,'yyyy/MM/dd HH24:MI')"+
                        " 	   OR TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,'yyyy/MM/dd')||' '||:pStartTime,'yyyy/MM/dd HH24:MI') BETWEEN a.start_time AND a.end_time"+
                        " 	   OR TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,'yyyy/MM/dd')||' '||:pEndTime,'yyyy/MM/dd HH24:MI') BETWEEN a.start_time AND a.end_time)"+
                        " 	   AND a.profile_id =:pProfileId"+
                        " 	   AND a.canceled = :pCanceled"+
                        " 	   AND a.room_id =:proom_id ORDER BY a.start_time";

                //jakarta.persistence.Query query1 = entityManager.createNativeQuery(sb);
                NativeQuery<Object[]> query = entityManager.createNativeQuery(sb)
                        .unwrap(org.hibernate.query.NativeQuery.class);
               // jakarta.persistence.Query query1 = entityManager.createNativeQuery(sb);
                      /*  .addScalar("date1", StandardBasicTypes.STRING)
                        .addScalar("start_time", StandardBasicTypes.STRING)
                        .addScalar("end_time", StandardBasicTypes.STRING)
                        .addScalar("duration", StandardBasicTypes.LONG)
                        .addScalar("profile_room_name", StandardBasicTypes.STRING)
                        .addScalar("activity_name", StandardBasicTypes.STRING)
                        .addScalar("customer_id", StandardBasicTypes.LONG)
                        .addScalar("c_first_name", StandardBasicTypes.STRING)
                        .addScalar("c_last_name", StandardBasicTypes.STRING);*/

                query.setParameter("pProfileId", pProfileId);
                query.setParameter("pStartDate", pStartDate);
                query.setParameter("pEndDate", pEndDate);
                query.setParameter("pStartTime", pStartTime);
                query.setParameter("pEndTime", pEndTime);
                query.setParameter("pCanceled", pCanceled);
                query.setParameter("proom_id", proom_id);



               // jakarta.persistence.Query query1 = entityManager.createNativeQuery(sb);
                List<Object[]> results = query.getResultList();
                for (Object[] row : results) {
                    String date1 = (String) row[0];
                }
               // ScrollableResults scroll = query.scroll();
                List<InstructorOpenAppointmentsDTO> result = new ArrayList<>();
                Map<Long, Customer> customers = new HashMap<>();


                //List<ExportDetailsDTO> result = new ArrayList<>();

                for (Object[] objects : results) {
                    String date1 = (String) objects[0];
                    Long pDuration = (Long) objects[3];
                    String roomName = (String) objects[4];
                    String pActivityName = (String) objects[5];

                    InstructorOpenAppointmentsDTO instructorOpenAppointmentsDTO = new InstructorOpenAppointmentsDTO(roomName, date1, (String) objects[1], (String) objects[2], pDuration, pActivityName);

                    Long customerId = (Long) objects[6];
                    if (customerId != null) {
                        Customer customer = customers.computeIfAbsent(customerId, id -> {
                            Customer cust = new Customer();
                            cust.setCustomerId(id);
                            Person person = new Person();
                            person.setFirstName((String) objects[7]);
                            person.setLastName((String) objects[8]);
                            cust.setPerson(person);
                            return cust;
                        });

                        instructorOpenAppointmentsDTO.addCustomer(customer);
                    }

                    result.add(instructorOpenAppointmentsDTO);
                }



                ConflictingAppointmentListDTO conflictingAppointmentListDTO = null;
                if (!result.isEmpty()) {
                    conflictingAppointmentListDTO = new ConflictingAppointmentListDTO(result.size(), result.subList(0, Math.min(result.size(), 5)));
                }

               // scroll.close();
                return conflictingAppointmentListDTO;
            }
        };
    }

    public static Criterion<Room, ConflictingAppointmentListDTO> findConflictAppointmentsByInstructor(final long pProfileId, final String pStartDate, final String pEndDate, final String pStartTime, final String pEndTime, final Long pinstructor_id) {
        return new RoomCriterion<ConflictingAppointmentListDTO>() {
            @Override
            public ConflictingAppointmentListDTO get(EntityManager entityManager, int pFetchMode) {
            	String sb =" SELECT TO_CHAR(a.start_time, 'DD-Mon-YYYY') AS date1 ," +
            		    " to_char(a.start_time, 'HH:MI AM') as start_time, to_char(a.end_time, 'HH:MI AM') as end_time, " +
            			" (extract(DAY FROM ((a.end_time - a.start_time) * 24 * 60 * 60 * 60))) AS duration," +
            			"  p_i.first_name AS i_first_name," +
            			" p_i.last_name AS i_last_name," +
            			" t.activity_name AS activity_name," +
            			" c.customer_id AS customer_id," +
            			" p_c.first_name AS c_first_name," +
            			" p_c.last_name AS c_last_name" +
            			" FROM appointment a left" +
            			" JOIN appointment_customers a_c" +
            			" ON a.appointment_id = a_c.appointment_id" +
            			" LEFT JOIN customer c" +
            			" ON a_c.customer_id = c.customer_id left" +
            			" JOIN person p_c" +
            			" ON c.person_id = p_c.person_id LEFT" +
            			" JOIN instructor i" +
            			" ON i.instructor_id = a.instructor_id left" +
            			" JOIN person p_i" +
            			" ON i.person_id = p_i.person_id left" +
            			" JOIN activity t" +
            			" ON t.activity_id = a.activity_id" +
            			" WHERE TO_CHAR(start_time,'d') = TO_CHAR(to_date(:pStartDate,'MM/dd/yyyy'),'d')" +
            			" AND TO_TIMESTAMP_TZ(TO_CHAR(start_time, 'MM/dd/yyyy'), 'MM/dd/yyyy') BETWEEN TO_TIMESTAMP_TZ(:pStartDate,'MM/dd/yyyy') AND TO_TIMESTAMP_TZ(:pEndDate,'MM/dd/yyyy')" +
            			" AND (a.start_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,'MM/dd/yyyy')||' '||:pStartTime,'MM/dd/yyyy HH24:MI')" +
            			" AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,'MM/dd/yyyy')||' '||:pEndTime,'MM/dd/yyyy HH24:MI')" +
            			" OR a.end_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,'MM/dd/yyyy')||' '||:pStartTime,'MM/dd/yyyy HH24:MI')" +
            			" AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,'MM/dd/yyyy')||' '||:pEndTime,'MM/dd/yyyy HH24:MI')" +
            			" OR TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,'MM/dd/yyyy')||' '||:pStartTime,'MM/dd/yyyy HH24:MI') BETWEEN a.start_time AND a.end_time" +
            			" OR TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,'MM/dd/yyyy')||' '||:pEndTime,'MM/dd/yyyy HH24:MI') BETWEEN a.start_time AND a.end_time)" +
            			" AND a.profile_id =:pProfileId" +
            			" AND a.canceled = 'N'" +
            			" AND a.instructor_id=:pinstructor_id  ORDER BY a.start_time" ; 

                NativeQuery<Object[]> query = entityManager.createNativeQuery(sb)
                        .unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("date1", StandardBasicTypes.STRING)
                        .addScalar("start_time", StandardBasicTypes.STRING)
                        .addScalar("end_time", StandardBasicTypes.STRING)
                        .addScalar("duration", StandardBasicTypes.LONG)
                        .addScalar("i_first_name", StandardBasicTypes.STRING)
                        .addScalar("i_last_name", StandardBasicTypes.STRING)
                        .addScalar("activity_name", StandardBasicTypes.STRING)
                        .addScalar("customer_id", StandardBasicTypes.LONG)
                        .addScalar("c_first_name", StandardBasicTypes.STRING)
                        .addScalar("c_last_name", StandardBasicTypes.STRING);

                query.setParameter("pProfileId", pProfileId);
                query.setParameter("pStartDate", pStartDate);
                query.setParameter("pEndDate", pEndDate);
                query.setParameter("pStartTime", pStartTime);
                query.setParameter("pEndTime", pEndTime);
                query.setParameter("pinstructor_id", pinstructor_id);

                ScrollableResults scroll = query.scroll();
                List<InstructorOpenAppointmentsDTO> result = new ArrayList<>();
                Map<Long, Customer> customers = new HashMap<>();

                while (scroll.next()) {
                    Object[] objects = (Object[]) scroll.get();
                    String date1 = (String) objects[0];
                    Long pDuration = (Long) objects[3];
                    String pActivityName = (String) objects[6];

                    InstructorOpenAppointmentsDTO instructorOpenAppointmentsDTO = new InstructorOpenAppointmentsDTO((String) objects[4] + ' ' + (String) objects[5], date1, (String) objects[1], (String) objects[2], pDuration, pActivityName);

                    Long customerId = (Long) objects[7];
                    if (customerId != null) {
                        Customer customer = customers.computeIfAbsent(customerId, id -> {
                            Customer cust = new Customer();
                            cust.setCustomerId(id);
                            Person person = new Person();
                            person.setFirstName((String) objects[8]);
                            person.setLastName((String) objects[9]);
                            cust.setPerson(person);
                            return cust;
                        });

                        instructorOpenAppointmentsDTO.addCustomer(customer);
                    }

                    result.add(instructorOpenAppointmentsDTO);
                }

                ConflictingAppointmentListDTO conflictingAppointmentListDTO = null;
                if (!result.isEmpty()) {
                    conflictingAppointmentListDTO = new ConflictingAppointmentListDTO(result.size(), result.subList(0, Math.min(result.size(), 5)));
                }

                scroll.close();
                return conflictingAppointmentListDTO;
            }
        };
    }
}
