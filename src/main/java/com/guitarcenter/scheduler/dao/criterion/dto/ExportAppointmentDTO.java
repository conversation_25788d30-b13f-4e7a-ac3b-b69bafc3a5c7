/**
 * @Title: ExportAppointmentDTO.java
 * @Package com.guitarcenter.scheduler.dao.criterion.dto
 * @Description: 
 * Copyright: Copyright (c) 2014 
 * Company:
 * 
 * <AUTHOR>
 * @date May 7, 2014 2:41:57 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.dao.criterion.dto;

/**
 * @ClassName: ExportAppointmentDTO
 * @Description: 
 * <AUTHOR>
 * @date May 7, 2014 2:41:57 PM
 *
 */
public class ExportAppointmentDTO {
	private String customerFirstName;
	private String customerLastName;
	private String customerGCID;
	private String statCode;
	private String date;
	private String dayOfAppointment;
	private String activityType;
	private String instructorName;
	private String appointmentCancelled;
	private String recurring;
	
	/**
	  * ExportAppointmentDTO. 
	  * <p>Title: </p>
	  * <p>Description: </p>
	  */
	public ExportAppointmentDTO() {
	}
	/**
	  * ExportAppointmentDTO. 
	  * <p>Title: </p>
	  * <p>Description: </p>
	  * @param customerFirstName
	  * @param customerLastName
	  * @param customerGCID
	  * @param statCode
	  * @param date
	  * @param dayOfAppointment
	  * @param activityType
	  * @param instructorName
	  * @param appointmentCancelled
	  * @param recurring
	  */
	public ExportAppointmentDTO(String customerFirstName,
			String customerLastName, String customerGCID, String statCode,
			String date, String dayOfAppointment, String activityType,
			String instructorName, String appointmentCancelled,
			String recurring) {
		super();
		this.customerFirstName = customerFirstName;
		this.customerLastName = customerLastName;
		this.customerGCID = customerGCID;
		this.statCode = statCode;
		this.date = date;
		this.dayOfAppointment = dayOfAppointment;
		this.activityType = activityType;
		this.instructorName = instructorName;
		this.appointmentCancelled = appointmentCancelled;
		this.recurring = recurring;
	}
	/**
	 * getter method
	 * @return the customerFirstName
	 */
	public String getCustomerFirstName() {
		return customerFirstName;
	}
	/**
	 * setter method
	 * @param customerFirstName the customerFirstName to set
	 */
	public void setCustomerFirstName(String customerFirstName) {
		this.customerFirstName = customerFirstName;
	}
	/**
	 * getter method
	 * @return the customerLastName
	 */
	public String getCustomerLastName() {
		return customerLastName;
	}
	/**
	 * setter method
	 * @param customerLastName the customerLastName to set
	 */
	public void setCustomerLastName(String customerLastName) {
		this.customerLastName = customerLastName;
	}
	/**
	 * getter method
	 * @return the customerGCID
	 */
	public String getCustomerGCID() {
		return customerGCID;
	}
	/**
	 * setter method
	 * @param customerGCID the customerGCID to set
	 */
	public void setCustomerGCID(String customerGCID) {
		this.customerGCID = customerGCID;
	}
	/**
	 * getter method
	 * @return the statCode
	 */
	public String getStatCode() {
		return statCode;
	}
	/**
	 * setter method
	 * @param statCode the statCode to set
	 */
	public void setStatCode(String statCode) {
		this.statCode = statCode;
	}
	/**
	 * getter method
	 * @return the date
	 */
	public String getDate() {
		return date;
	}
	/**
	 * setter method
	 * @param date the date to set
	 */
	public void setDate(String date) {
		this.date = date;
	}
	/**
	 * getter method
	 * @return the dayOfAppointment
	 */
	public String getDayOfAppointment() {
		return dayOfAppointment;
	}
	/**
	 * setter method
	 * @param dayOfAppointment the dayOfAppointment to set
	 */
	public void setDayOfAppointment(String dayOfAppointment) {
		this.dayOfAppointment = dayOfAppointment;
	}
	/**
	 * getter method
	 * @return the activityType
	 */
	public String getActivityType() {
		return activityType;
	}
	/**
	 * setter method
	 * @param activityType the activityType to set
	 */
	public void setActivityType(String activityType) {
		this.activityType = activityType;
	}
	/**
	 * getter method
	 * @return the instructorName
	 */
	public String getInstructorName() {
		return instructorName;
	}
	/**
	 * setter method
	 * @param instructorName the instructorName to set
	 */
	public void setInstructorName(String instructorName) {
		this.instructorName = instructorName;
	}
	/**
	 * getter method
	 * @return the appointmentCancelled
	 */
	public String getAppointmentCancelled() {
		return appointmentCancelled;
	}
	/**
	 * setter method
	 * @param appointmentCancelled the appointmentCancelled to set
	 */
	public void setAppointmentCancelled(String appointmentCancelled) {
		this.appointmentCancelled = appointmentCancelled;
	}
    /**
     * getter method
     * @return recurring
     */
    public String getRecurring() {
        return recurring;
    }
    /**
     * setter method
     * @param recurring
     */
    public void setRecurring(String recurring) {
        this.recurring = recurring;
    }

}
