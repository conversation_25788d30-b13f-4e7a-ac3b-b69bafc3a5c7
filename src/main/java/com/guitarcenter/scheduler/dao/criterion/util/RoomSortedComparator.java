package com.guitarcenter.scheduler.dao.criterion.util;

import java.io.Serializable;
import java.util.Comparator;

import com.guitarcenter.scheduler.dao.criterion.dto.RoomSortedDTO;

public class RoomSortedComparator implements Comparator<RoomSortedDTO>, Serializable {

    private static final long serialVersionUID = 1L;

    @Override
    public int compare(RoomSortedDTO o1, RoomSortedDTO o2) {
        Long priority_o1 = o1.getPriority();
        Long priority_o2 = o2.getPriority();
        int compareTo = priority_o1.compareTo(priority_o2);
        if (compareTo != 0) return compareTo;
        Long duration_o1 = o1.getDuration();
        Long duration_o2 = o2.getDuration();
        compareTo = duration_o1.compareTo(duration_o2);
        if (compareTo != 0) return compareTo;
        Long roomId_o1 = o1.getRoomId();
        Long roomId_o2 = o2.getRoomId();
        return roomId_o1.compareTo(roomId_o2);
    }
}
