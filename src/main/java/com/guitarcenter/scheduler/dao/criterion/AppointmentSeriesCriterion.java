package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ACTIVITY;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION_PROFILE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_MORE_CUSTOMERS;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.*;

import jakarta.persistence.EntityManager;
import org.hibernate.ScrollableResults;
import org.hibernate.Session;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.Query;
import org.hibernate.type.StandardBasicTypes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.dao.criterion.dto.AppointmentSeriesDTO;
import com.guitarcenter.scheduler.model.AppointmentSeries;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.enums.Canceled;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.IsRecurring;
import java.util.Map.Entry;

public abstract class AppointmentSeriesCriterion<E> extends AbstractCriterion<AppointmentSeries, E> implements
        Criterion<AppointmentSeries, E> {

    private static final Logger LOGGER_LACKING_SEARCH = LoggerFactory.getLogger("Appointment series lacking search:");
    // GSSP-224 Changes Start :: Constant added
    public static final String CUSTOMER_STATUS_CANCELLED_ID = "2";
    // GSSP-224 Changes end
    private static final Criterion<AppointmentSeries, AppointmentSeries> DEFAULT_INSTANCE = new AppointmentSeriesCriterion<AppointmentSeries>() {};

    private AppointmentSeriesCriterion() {
        super(AppointmentSeries.class);
    }

    @Override
    protected String getFetchScript(int pFetchMode) {
        StringBuilder sb = new StringBuilder();
        sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
        sb.append(addFetchHQL(FETCH_ACTIVITY, pFetchMode, "t.activity", true));
        sb.append(addFetchHQL(FETCH_LOCATION_PROFILE, pFetchMode, "t.locationProfile", true));
        sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
        sb.append(addFetchHQL(FETCH_MORE_CUSTOMERS, pFetchMode, "t.customers", true));
        return sb.toString();
    }

    @Override
    public List<E> search(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }

    @Override
    public E get(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }

    public static Criterion<AppointmentSeries, AppointmentSeries> getInstance() {
        return DEFAULT_INSTANCE;
    }

    public static Criterion<AppointmentSeries, AppointmentSeriesDTO> findLackingAppointmentSeries(
            final Date pBeginningDate) {

        final Long count = 52L; // GSSP-248

        AppointmentSeriesCriterion<AppointmentSeriesDTO> instance = new AppointmentSeriesCriterion<AppointmentSeriesDTO>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<AppointmentSeriesDTO> search(EntityManager entityManager, int pFetchMode) {
                Map<Long, AppointmentSeriesDTO> map = new HashMap<>();
                {
                    StringBuilder sb = new StringBuilder(
                            "select apptss.appointment_series_id, apptss.site_id, apptss.profile_id, apptss.activity_id, apptss.band_name, apptss.note, custappts.customer_id, appts.numbers");
                    sb.append(" from (select appts_inner.appointment_series_id,");
                    sb.append(" count(appts_inner.appointment_series_id) as numbers");
                    sb.append(" from appointment_series appts_inner");
                    sb.append(" left join appointment app_inner");
                    sb.append(" on app_inner.appointment_series_id = appts_inner.appointment_series_id");
                    sb.append(" where appts_inner.is_recurring = :recurring");
                    sb.append(" and appts_inner.series_end_time is null");
                    sb.append(" and to_char(app_inner.end_time, 'YYYY-MM-DD') >= to_char(:beginningDate, 'YYYY-MM-DD')");
                    sb.append(" group by appts_inner.appointment_series_id) appts");
                    sb.append(" left join appointment_series apptss");
                    sb.append(" on apptss.appointment_series_id = appts.appointment_series_id");
                    sb.append(" left join customer_appointment_series custappts");
                    sb.append(" on apptss.appointment_series_id = custappts.appointment_series_id");
                    sb.append(" left join customer cust");
                    sb.append(" on custappts.customer_id = cust.customer_id");
                    sb.append(" left join location_profile lp");
                    sb.append(" on apptss.profile_id = lp.profile_id");
                    sb.append(" where appts.numbers < :count");
                    sb.append(" and lp.enabled = :lp_enabled");
                    sb.append(" and (cust.customer_status_id != :customer_status or cust.customer_status_id is null)");
                    
                    NativeQuery<?> query = (NativeQuery<?>) entityManager.createNativeQuery(sb.toString());
                    query.setParameter("recurring", IsRecurring.Y.name());
                    query.setParameter("count", count);
                    query.setParameter("beginningDate", pBeginningDate);
                    query.setParameter("lp_enabled", Enabled.Y.name());
                    query.addScalar("appointment_series_id", StandardBasicTypes.LONG);
                    query.addScalar("site_id", StandardBasicTypes.LONG);
                    query.addScalar("profile_id", StandardBasicTypes.LONG);
                    query.addScalar("activity_id", StandardBasicTypes.LONG);
                    query.addScalar("band_name", StandardBasicTypes.STRING);
                    query.addScalar("note", StandardBasicTypes.STRING);
                    query.addScalar("customer_id", StandardBasicTypes.LONG);
                    query.addScalar("numbers", StandardBasicTypes.LONG);
                    query.setParameter("customer_status", Long.parseLong(CUSTOMER_STATUS_CANCELLED_ID));

                    List<Object[]> list = (List<Object[]>) query.list();
                    for (Object[] row : list) {
                        Long appointmentSeriesId = (Long) row[0];
                        AppointmentSeriesDTO obj = map.get(appointmentSeriesId);
                        if (obj == null) {
                            obj = new AppointmentSeriesDTO();
                            map.put(appointmentSeriesId, obj);
                            obj.setAppointmentSeriesId(appointmentSeriesId);
                            obj.setSiteId((Long) row[1]);
                            obj.setProfileId((Long) row[2]);
                            obj.setActivityId((Long) row[3]);
                            Object bandName = row[4];
                            if (bandName != null) {
                                obj.setBandName((String) bandName);
                            }
                            Object note = row[5];
                            // Change made for GSSP-234
                            if (note != null) {
                                obj.setNote((String) note);
                            }
                            // total of lacking appointment
                            obj.setNumbers(count - (Long) row[7]);
                            Set<Customer> customers = new HashSet<>();
                            obj.setCustomers(customers);
                        }
                        // To avoid not having customer situation
                        Object customerId = row[6];
                        if (customerId != null) {
                            Customer customer = new Customer();
                            customer.setCustomerId((Long) customerId);
                            obj.getCustomers().add(customer);
                        }
                    }
                }

                Set<Long> removeSeries = new HashSet<>();
                for (Entry<Long, AppointmentSeriesDTO> entry : map.entrySet()) {
                    StringBuilder sb = new StringBuilder(
                            "select tb1.appointment_series_id, tb1.instructor_id, tb1.room_id, tb1.duration, tb1.start_time, max(tb2.start_time) as latest_start_time, tb1.parent_id");
                    sb.append(" from (select ttt.appointment_series_id, ttt.instructor_id, ttt.room_id, (extract(day from((ttt.end_time - ttt.start_time) * 24 * 60 * 60 * 60))) as duration, ttt.start_time, r.parent_id");
                    sb.append(" from (select max(t.appointment_id) as appointmentId, t.appointment_series_id");
                    sb.append(" from appointment t");
                    sb.append(" where t.appointment_series_id = :seriesId");
                    sb.append(" and (t.canceled is null or t.canceled = :canceled)");
                    sb.append(" group by t.appointment_series_id) tt");
                    sb.append(" left join appointment ttt");
                    sb.append(" on tt.appointmentId = ttt.appointment_id");
                    sb.append(" left join room r");
                    sb.append(" on ttt.room_id = r.room_id");
                    sb.append(" ) tb1,");
                    sb.append(" appointment tb2");
                    sb.append(" where tb2.appointment_series_id = :seriesId");
                    sb.append(" and tb2.appointment_series_id = tb1.appointment_series_id");
                    sb.append(" group by tb1.appointment_series_id, tb1.instructor_id, tb1.room_id, tb1.duration, tb1.start_time, tb1.parent_id");
                    
                    NativeQuery<?> query = (NativeQuery<?>) entityManager.createNativeQuery(sb.toString());
                    query.setParameter("seriesId", entry.getKey());
                    query.setParameter("canceled", Canceled.N.name());
                    query.addScalar("appointment_series_id", StandardBasicTypes.LONG);
                    query.addScalar("instructor_id", StandardBasicTypes.LONG);
                    query.addScalar("room_id", StandardBasicTypes.LONG);
                    query.addScalar("duration", StandardBasicTypes.LONG);
                    query.addScalar("start_time", StandardBasicTypes.TIMESTAMP);
                    query.addScalar("latest_start_time", StandardBasicTypes.TIMESTAMP);
                    query.addScalar("parent_id", StandardBasicTypes.LONG);
                    query.setMaxResults(1);
                    
                    Object[] objects = (Object[]) query.uniqueResult();
                    if (objects == null) {
                        LOGGER_LACKING_SEARCH.error("Current series(id:{}) lacks end date, because it has been canceled. ", entry.getKey());
                        removeSeries.add(entry.getKey());
                        continue;
                    }
                    AppointmentSeriesDTO obj = entry.getValue();
                    Object instructorId = objects[1];
                    if (instructorId != null) {
                        obj.setInstructorId((Long) instructorId);
                    }
                    obj.setRoomId((Long) objects[2]);
                    // Set duration, in minutes
                    obj.setDuration(Math.round((Long) objects[3] / 60.0D / 60.0D));
                    obj.setStartTime((Date) objects[4]);
                    obj.setLatestAppintmentStartTime((Date) objects[5]);
                    Object parentRoomId = objects[6];
                    if (parentRoomId != null) {
                        obj.setParentRoomId((Long) parentRoomId);
                    }
                    obj.buildNextAppointmentDateTime();
                }

                for (Long removeKey : removeSeries) {
                    map.remove(removeKey);
                }

                return new ArrayList<>(map.values());
            }

        };
        return instance;
    }

    /**
     * Returns a criterion that will return all AppointmentSeries entities with
     * an end time that is after the cutoff provided and associated with the
     * customer matching a supplied identifier.
     *
     * @param customerId long integer containing the unique identifier of the
     *                   customer
     * @param cutoff     timestamp to use as a cutoff, inclusive.
     * @return a Criterion that can be used for searching
     */
    public static Criterion<AppointmentSeries, AppointmentSeries> findSeriesForCustomerEndingAfter(final long customerId, final Date cutoff) {
        return new AppointmentSeriesCriterion<AppointmentSeries>() {
            @SuppressWarnings("unchecked")
            @Override
            public List<AppointmentSeries> search(EntityManager entityManager, int pFetchMode) {
                Query<AppointmentSeries> query = (Query<AppointmentSeries>) entityManager.createQuery(new StringBuilder("from AppointmentSeries t ")
                        .append(getFetchScript(pFetchMode))
                        .append(" where coalesce(t.seriesEndTime, :cutoff) >= :cutoff ")
                        .append(" and exists(from t.customers c where c.id = :customerId) ")
                        .toString(), AppointmentSeries.class);
                query.setParameter("customerId", customerId);
                query.setParameter("cutoff", cutoff);
                return query.getResultList();
            }
        };
    }
}
