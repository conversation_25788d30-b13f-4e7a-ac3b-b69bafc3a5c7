package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.EntityManager;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.Query;
//import org.hibernate.SQLQuery;
import org.hibernate.Session;

import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.Service;
import com.guitarcenter.scheduler.model.enums.Enabled;

public abstract class ServiceCriterion<E> extends AbstractCriterion<Service, E> implements Criterion<Service, E> {

    private static final Criterion<Service, Service> DEFAULT_INSTANCE = new ServiceCriterion<Service>() {
    };


    private ServiceCriterion() {
        super(Service.class);
    }


    @Override
    protected String getFetchScript(int pFetchMode) {
        StringBuilder sb = new StringBuilder();
        sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
        sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
        return sb.toString();
    }


    @Override
    public List<E> search(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object not has support this operation.");
    }


    @Override
    public E get(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object not has support this operation.");
    }


    public static Criterion<Service, Service> getInstance() {
        return DEFAULT_INSTANCE;
    }


    public static Criterion<Service, Service> findBySiteId(final long pSiteId) {

        ServiceCriterion<Service> instance = new ServiceCriterion<Service>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Service> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Service t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append("where t.site.siteId = :siteId order by t.serviceId");
                Query query = (Query) entityManager.createQuery(sb.toString());
                query.setParameter("siteId", pSiteId);
                return query.list();
            }

        };
        return instance;
    }


    public static Criterion<Service, Service> findByProfileIdAndRoomIdAndEnabled(final long pProfileId, final long pRoomId, final Enabled pEnabled) {

        return new ServiceCriterion<Service>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Service> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select t.service_id, t.version, t.updated, t.service_name, t.updated_by, t.site_id, t.enabled, t.requires_instructor, t.allow_band_name, t.external_id");
                sb.append("  from service t");
                sb.append("  left join room_services r_s");
                sb.append("    on t.service_id = r_s.service_id");
                sb.append("  left join profile_service p_s");
                sb.append("    on t.service_id = p_s.service_id");
                sb.append(" where r_s.room_id = :roomId");
                sb.append("   and p_s.enabled = :enabled");
                sb.append("   and p_s.profile_id = :profileId");
                sb.append(" order by t.service_id");
                //SQLQuery query = entityManager.createSQLQuery(sb.toString());

                Query query = (NativeQuery<Service>) entityManager.createNativeQuery(sb.toString(), Service.class);
                //query.addEntity(Service.class);
                query.setParameter("roomId", pRoomId);
                query.setParameter("profileId", pProfileId);
                query.setParameter("enabled", pEnabled.toString());
                return query.list();
            }
        };
    }
    //GSSP-211 CHANGES
    public static Criterion<Service, Location> findLocation() {

        return new ServiceCriterion<Location>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Location> search(EntityManager entityManager, int pFetchMode) {
                String sql = "SELECT location_id, location_name FROM location WHERE profile_id IS NOT NULL ORDER BY location_name ";
                List<Object[]> result = entityManager.createNativeQuery(sql).getResultList();
                List<Location> locations = new ArrayList<>();

                for (Object[] row : result) {
                    Location location = new Location();
                    location.setLocationId(((BigDecimal) row[0]).longValue());
                    location.setLocationName((String) row[1]);
                    locations.add(location);
                }

                return locations;
            }
        };
    }

    public static Criterion<Service, Service> findByProfileIdAndInstructorIdAndEnabled(final long pProfileId, final long pInstructorId, final Enabled pEnabled) {

        return new ServiceCriterion<Service>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Service> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select  t.* ");
                sb.append("  from service t");
                sb.append("  left join activity a");
                sb.append("    on t.service_id = a.service_id");
                sb.append("  left join instructor_activities i_a");
                sb.append("    on t.service_id = a.service_id");
                sb.append("  left join profile_service p_s");
                sb.append("    on a.activity_id = i_a.activity_id");
                sb.append(" where i_a.instructor_id = :instructorId");
                sb.append("   and p_s.enabled = :enabled");
                sb.append("   and p_s.profile_id = :profileId");
                sb.append(" order by t.service_id");
                Query query = (Query) entityManager.createNativeQuery(sb.toString(), Service.class);
                // query.addEntity("t",Service.class);
                query.setParameter("instructorId", pInstructorId);
                query.setParameter("profileId", pProfileId);
                query.setParameter("enabled", pEnabled.Y.name());
                return query.list();
            }
        };
    }
}
