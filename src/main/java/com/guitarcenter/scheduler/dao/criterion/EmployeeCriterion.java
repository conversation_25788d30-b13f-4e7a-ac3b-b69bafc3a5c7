package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;
import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.EntityManager;import java.math.BigDecimal;
import org.hibernate.query.Query;
//import org.hibernate.SQLQuery;
import org.hibernate.ScrollableResults;
import org.hibernate.Session;
import org.hibernate.type.StandardBasicTypes;
import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.dto.StaffDTO;
import com.guitarcenter.scheduler.model.Employee;
import java.math.BigDecimal;
public abstract class EmployeeCriterion<E> extends AbstractCriterion<Employee, E> implements Criterion<Employee, E> {

	private static final Criterion<Employee, Employee>	DEFAULT_INSTANCE	= new EmployeeCriterion<Employee>() {
																			};



	private EmployeeCriterion() {
		super(Employee.class);
	}



	@Override
	protected String getFetchScript(int pFetchMode) {
		StringBuilder sb = new StringBuilder();
		sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
		sb.append(addFetchHQL(FETCH_LOCATION, pFetchMode, "t.location", true));
		sb.append(addFetchHQL(FETCH_PERSON, pFetchMode, "t.person", true));
		sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
		return sb.toString();
	}



	@Override
	public List<E> search(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	@Override
	public E get(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	public static Criterion<Employee, Employee> getInstance() {
		return DEFAULT_INSTANCE;
	}

    /**
     * Returns a Criterion that can be used to find any existing employees in
     * the supplied site with a matching external id.
     * 
     * @param pSiteId site identifier
     * @param pExternalId String containing the external id to match
     * @return Criterion instance
     */
    public static Criterion<Employee, Employee> findByExternalId(final long pSiteId, final String pExternalId) {
        return new EmployeeCriterion<Employee>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Employee> search(EntityManager entityManager, int pFetchMode) {
                Query query = (Query) entityManager.createQuery(new StringBuilder(" from Employee t ")
                                                       .append(getFetchScript(pFetchMode))
                                                       .append(" where t.site.siteId = :siteId ")
                                                       .append("   and t.externalId = :externalId ")
                                                       .toString());
                query.setParameter("siteId", pSiteId);
                query.setParameter("externalId", pExternalId);
                return query.list();
            }
        };
    }

    /**
     * Returns a Criterion that can be used to find any existing employees in
     * the supplied site.
     * 
     * @param pSiteId site identifier
     * @return Criterion instance
     */
    //Finding employees by site id
    public static Criterion<Employee, Employee> findBySiteId(final long pSiteId) {
        return new EmployeeCriterion<Employee>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Employee> search(EntityManager entityManager, int pFetchMode) {
                Query query = (Query) entityManager.createQuery(new StringBuilder(" from Employee t ")
                                                       .append(getFetchScript(pFetchMode))
                                                       .append(" where t.site.siteId = :siteId ")
                                                       .append(" and t.enterpriseStatus != :enterpriseStatus ")//changes made for GSSP-146
                                                       .toString());
                query.setParameter("siteId", pSiteId);
                query.setParameter("enterpriseStatus", "T");
                return query.list();
            }
        };
    }
    
    //GSSP-288 finding employees based on location
    public static Criterion<Employee, StaffDTO> findStaffBasedOnLocation(final long pSiteId, final String locationId){
    	return new EmployeeCriterion<StaffDTO>(){
    		@Override
    		public List<StaffDTO> search(EntityManager entityManager, int pFetchMode) {

					Query query = null ;
					StringBuilder sb = new StringBuilder ("select p.first_name,p.last_name,t.employee_id, t.status, p.email, r.role_name, l.external_id  from Employee t,PERSON_ROLE pr,PERSON p, ROLE r, LOCATION l ")
					.append(" where pr.person_id = t.person_id ")
					.append("and  p.person_id = pr.person_id ")
					.append("and pr.role_id = r.role_id ")
					.append(" and l.location_id = pr.location_id ")
					.append("and t.site_id = :siteId ")
					.append("and t.ENTERPRISE_STATUS != 'T' ")
					.append("and pr.location_id = :locationId ");
					query = (Query) entityManager.createNativeQuery(sb.toString());
					query.setParameter("siteId", pSiteId);
					query.setParameter("locationId",locationId);
//					query.setParameter("first_name", StandardBasicTypes.STRING);
//					query.setParameter("last_name", StandardBasicTypes.STRING);
//					query.setParameter("employee_id", StandardBasicTypes.INTEGER);
//					query.setParameter("status", StandardBasicTypes.STRING);
//					query.setParameter("email", StandardBasicTypes.STRING);
//					query.setParameter("role_name", StandardBasicTypes.STRING);
//					query.setParameter("external_id", StandardBasicTypes.STRING);
					ScrollableResults scroll = query.scroll();
					List<StaffDTO> resultList = new ArrayList<StaffDTO>();
					while (scroll.next()) {
						Object[] objects = (Object[]) scroll.get();
						String staff_name = (String) objects[0] + " " + (String) objects[1];
						//int employee_id=(int) objects[2];
						BigDecimal bigDecimal =(BigDecimal) objects[2];
						int employee_id = bigDecimal.intValue();
						String email=(String) objects[4];
						String roleLocationname = (String) objects[6] + AppConstants.SPLITOR_DASH + (String) objects[5];
						Boolean status = null;
						if ((String) objects[3]!= null &&
								AppConstants.STAFF_STATUS.equals((String) objects[3])) {
							status = true;
						} else {
							status = false;
						}
						StaffDTO dto = new StaffDTO(employee_id,staff_name,email,roleLocationname,status);
						resultList.add(dto);
					}
					scroll.close();
					return resultList;

			}
    	};
    }

    /**
     * Returns a Criterion that can be used to find any existing employees that
     * match the supplied person id.
     * 
     * @param pPersonId person identifier
     * @return Criterion instance
     */
    public static Criterion<Employee, Employee> findByPersonId(final long pPersonId) {
        return new EmployeeCriterion<Employee>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Employee> search(EntityManager entityManager, int pFetchMode) {
                Query query = (Query) entityManager.createQuery(new StringBuilder(" from Employee t ")
                                                       .append(getFetchScript(pFetchMode))
                                                       .append(" where t.person.personId = :personId ")
                                                       .toString());
                query.setParameter("personId", pPersonId);
                return query.list();
            }
        };
    }
}
