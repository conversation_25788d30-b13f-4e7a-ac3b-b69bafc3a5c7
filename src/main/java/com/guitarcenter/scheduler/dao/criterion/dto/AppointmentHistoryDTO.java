package com.guitarcenter.scheduler.dao.criterion.dto;

public class AppointmentHistoryDTO {
	
	private String appointmentId;
	private String updatedBy;
	private String updatedTimestamp;
	private String customerName;
	private String serviceType;
	private String lessonType;
	private String startDate;
	private String endDate;
	private String isRecurring;
	private String Time;
	private Long duration;
	private String instructorName;
	private String room;
	private String note;
	private String isCancelled;
		
	
	public String getCustomerName() {
		return customerName;
	}


	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}


	public String getInstructorName() {
		return instructorName;
	}


	public void setInstructorName(String instructorName) {
		this.instructorName = instructorName;
	}


	public AppointmentHistoryDTO(String appointmentId, String  updatedBy, String updatedTimestamp, String customerName, String serviceType, String  lessonType
			,String startDate,String endDate, String isRecurring,String time, Long duration,String instructorName, String room, String note, String isCanceled )
	
	{
		this.appointmentId = appointmentId;
		this.updatedBy = updatedBy;
		this.updatedTimestamp = updatedTimestamp;
		this.customerName = customerName;
		this.serviceType = serviceType;
		this.lessonType = lessonType;
		this.startDate = startDate;
		this.endDate = endDate;
		this.isRecurring = isRecurring;
		this.Time = time;
		this.duration = duration;
		this.instructorName = instructorName;
		this.room = room;
		this.note = note;
		this.isCancelled = isCanceled;
	}
	
	
	public String getAppointmentId() {
		return appointmentId;
	}
	public void setAppointmentId(String appointmentId) {
		this.appointmentId = appointmentId;
	}
	public String getUpdatedBy() {
		return updatedBy;
	}
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}
	public String getUpdatedTimestamp() {
		return updatedTimestamp;
	}
	public void setUpdatedTimestamp(String updatedTimestamp) {
		this.updatedTimestamp = updatedTimestamp;
	}
	
	public String getServiceType() {
		return serviceType;
	}
	public void setServiceType(String serviceType) {
		this.serviceType = serviceType;
	}
	public String getLessonType() {
		return lessonType;
	}
	public void setLessonType(String lessonType) {
		this.lessonType = lessonType;
	}
	public String getStartDate() {
		return startDate;
	}
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public String getIsRecurring() {
		return isRecurring;
	}
	public void setIsRecurring(String isRecurring) {
		this.isRecurring = isRecurring;
	}
	public String getTime() {
		return Time;
	}
	public void setTime(String time) {
		Time = time;
	}
	public Long getDuration() {
		return duration;
	}
	public void setDuration(Long duration) {
		this.duration = duration;
	}

	public String getRoom() {
		return room;
	}
	public void setRoom(String room) {
		this.room = room;
	}
	public String getNote() {
		return note;
	}
	public void setNote(String note) {
		this.note = note;
	}
	public String getIsCancelled() {
		return isCancelled;
	}
	public void setIsCancelled(String isCancelled) {
		this.isCancelled = isCancelled;
	}
	
}
