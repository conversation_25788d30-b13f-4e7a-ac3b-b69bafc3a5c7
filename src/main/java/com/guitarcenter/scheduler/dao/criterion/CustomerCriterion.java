package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_CUSTOMER_STATUS;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.EntityManager;
import org.hibernate.ScrollableResults;
import org.hibernate.Session;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.Query;
import org.hibernate.type.StandardBasicTypes;

import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.webservice.dto.CustomerInformationDTO;

public abstract class CustomerCriterion<E> extends AbstractCriterion<Customer, E> implements Criterion<Customer, E> {

    private static final Criterion<Customer, Customer> DEFAULT_INSTANCE = new CustomerCriterion<Customer>() {};

    private CustomerCriterion() {
        super(Customer.class);
    }

    @Override
    protected String getFetchScript(int pFetchMode) {
        StringBuilder sb = new StringBuilder();
        sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
        sb.append(addFetchHQL(FETCH_CUSTOMER_STATUS, pFetchMode, "t.customerStatus", true));
        sb.append(addFetchHQL(FETCH_PERSON, pFetchMode, "t.person", true));
        sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
        return sb.toString();
    }

    @Override
    public List<E> search(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }

    @Override
    public E get(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }

    public static Criterion<Customer, Customer> getInstance() {
        return DEFAULT_INSTANCE;
    }

    public static Criterion<Customer, Customer> likeByName(final String pName) {
        CustomerCriterion<Customer> instance = new CustomerCriterion<Customer>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Customer> search(EntityManager entityManager, int pFetchMode) {
                String[] split = pName.split(" ");
                StringBuilder sb = new StringBuilder("from Customer t ");
                sb.append(getFetchScript(pFetchMode));
                if (split.length > 1) {
                    sb.append("where lower(t.person.firstName) like :firstName and lower(t.person.lastName) like :lastName");
                } else {
                    sb.append("where lower(t.person.firstName) like :name or lower(t.person.lastName) like :name");
                }
                Query<Customer> query = (Query<Customer>) entityManager.createQuery(sb.toString(), Customer.class);
                if (split.length > 1) {
                    query.setParameter("firstName", split[0].toLowerCase() + "%");
                    query.setParameter("lastName", split[1].toLowerCase() + "%");
                } else {
                    query.setParameter("name", pName.toLowerCase() + "%");
                }
                return query.getResultList();
            }

        };
        return instance;
    }

    public static Criterion<Customer, Customer> findAll(final long pSiteId) {
        CustomerCriterion<Customer> instance = new CustomerCriterion<Customer>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Customer> search(EntityManager entityManager, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Customer t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" where t.site.siteId = :siteId ");
                sb.append(" order by t.customerId");
                Query<Customer> query = (Query<Customer>) entityManager.createQuery(sb.toString(), Customer.class);
                query.setParameter("siteId", pSiteId);
                return query.getResultList();
            }

        };
        return instance;
    }

    public static Criterion<Customer, Customer> getByExternalId(final long pSiteId, final String pExternalId) {
        return new CustomerCriterion<Customer>() {
            @Override
            public Customer get(EntityManager entityManager, int pFetchMode) {
                Query<Customer> query = (Query<Customer>) entityManager.createQuery(new StringBuilder(" from Customer t ")
                        .append(getFetchScript(pFetchMode))
                        .append(" where t.site.siteId = :siteId ")
                        .append("   and t.externalId = :externalId ")
                        .toString(), Customer.class);
                query.setParameter("siteId", pSiteId);
                query.setParameter("externalId", pExternalId);
                return query.uniqueResult();
            }
        };
    }

    public static Criterion<Customer, String> getCustomerDetails(
            final String badgeNumber, final String lastName, final String phoneNumber) {
        return new CustomerCriterion<String>() {

            @Override
            public String get(EntityManager entityManager, int pFetchMode) {
                String sb = " select c.CUSTOMER_ID as custID ,c.EXTERNAL_ID as externalID from customer c " +
                        " join person p on c.PERSON_ID = p.PERSON_ID" +
                        " where c.BADGE_NUMBER = :badgeNumber and UPPER(p.LAST_NAME) = UPPER(:lastName) and p.PHONE = :phoneNumber";

                NativeQuery<?> query = (NativeQuery<?>) entityManager.createNativeQuery(sb);
                query.setParameter("badgeNumber", badgeNumber);
                query.setParameter("lastName", lastName);
                query.setParameter("phoneNumber", phoneNumber);

                query.addScalar("custID", StandardBasicTypes.STRING);
                query.addScalar("externalID", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();

                String custID_externalID = "";

                while (scroll.next()) {
                    Object[] objects = (Object[]) scroll.get();
                    custID_externalID = (String) objects[0] + "_" + (String) objects[1];
                }

                return custID_externalID;
            }
        };
    }


    public static Criterion<Customer, Boolean> getUpdatedByCustomeFlag(final long personId) {
        return new CustomerCriterion<Boolean>() {

            @Override
            public Boolean get(EntityManager entityManager, int pFetchMode) {
                String sb = " select count(customer_id) as total from customer where person_id = :personId";
                boolean flag = false;

                NativeQuery<?> query = (NativeQuery<?>) entityManager.createNativeQuery(sb);
                query.setParameter("personId", personId);
                query.addScalar("total", StandardBasicTypes.INTEGER);

                // Execute the query and get the result
                Object result = query.getSingleResult();
                int count = 0;

                if (result != null) {
                    count = ((Number) result).intValue(); // Cast to Number and then to int
                }

                // Check if count is greater than 0
                if (count > 0) {
                    flag = true;
                }

                return flag;
            }
        };
    }


    public static Criterion<Customer, Boolean> isEmailMatchingWithExistingEmail(
            final String[] externalIDs, final String emailID) {
        return new CustomerCriterion<Boolean>() {

            @Override
            public Boolean get(EntityManager entityManager, int pFetchMode) {
                String sb = " select p.email as email from person p  join customer c " +
                        " on c.PERSON_ID = p.PERSON_ID " +
                        " where c.EXTERNAL_ID in (:externalIDs) and UPPER(p.email) = UPPER(:emailID)" +
                        " Union" +
                        " select ce.CUSTOMER_EMAIL from CUSTOMER_EMAIL ce " +
                        " where ce.EXTERNAL_CUSTOMER_ID in (:externalIDs) " +
                        " and UPPER(ce.customer_email) = UPPER(:emailID)";

                NativeQuery<?> query = (NativeQuery<?>) entityManager.createNativeQuery(sb);
                query.setParameter("emailID", emailID);
                query.setParameterList("externalIDs", externalIDs);

                query.addScalar("email", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();

                Boolean emailMatch = false;

                while (scroll.next()) {
                    Object[] objects = (Object[]) scroll.get();
                    String matchingEmail = (String) objects[0];

                    if (null != matchingEmail)
                        return true;
                }

                return emailMatch;
            }
        };
    }

    public static Criterion<Customer, List<CustomerInformationDTO>> validateWithBadgeNumber(
            final String badgeNumber, final String lastName, String emailID) {
        return new CustomerCriterion<List<CustomerInformationDTO>>() {

            @Override
            public List<CustomerInformationDTO> get(EntityManager entityManager, int pFetchMode) {
                String sb = " select c.CUSTOMER_ID as customerId,c.EXTERNAL_ID as externalId from customer c " +
                        " join person p on c.PERSON_ID = p.PERSON_ID " +
                        " where c.BADGE_NUMBER = :badgeNumber and " +
                        " UPPER(p.LAST_NAME) = UPPER(:lastName) and p.PHONE = :emailID";

                List<CustomerInformationDTO> customerList = new ArrayList<>();

                NativeQuery<?> query = (NativeQuery<?>) entityManager.createNativeQuery(sb);
                query.setParameter("badgeNumber", badgeNumber);
                query.setParameter("lastName", lastName);
                query.setParameter("emailID", emailID);

                query.addScalar("customerId", StandardBasicTypes.STRING);
                query.addScalar("externalId", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();

                String customerId = "", externalId = "";

                while (scroll.next()) {
                    Object[] objects = (Object[]) scroll.get();

                    customerId = (String) objects[0];
                    externalId = (String) objects[1];

                    CustomerInformationDTO customer = new CustomerInformationDTO(customerId, externalId);

                    customerList.add(customer);
                }

                return customerList;
            }
        };
    }

    public static Criterion<Customer, List<CustomerInformationDTO>> validateWithExternalID(
            final String externalId, final String lastName, String emailID) {
        return new CustomerCriterion<List<CustomerInformationDTO>>() {

            @Override
            public List<CustomerInformationDTO> get(EntityManager entityManager, int pFetchMode) {
                String sb = " select c.CUSTOMER_ID as customerId,c.EXTERNAL_ID as externalId from customer c " +
                        " join person p on c.PERSON_ID = p.PERSON_ID " +
                        " where c.EXTERNAL_ID = :externalId and " +
                        " UPPER(p.LAST_NAME) = UPPER(:lastName) and p.PHONE = :emailID";

                List<CustomerInformationDTO> customerList = new ArrayList<>();

                NativeQuery<?> query = (NativeQuery<?>) entityManager.createNativeQuery(sb);
                query.setParameter("externalId", externalId);
                query.setParameter("lastName", lastName);
                query.setParameter("emailID", emailID);

                query.addScalar("customerId", StandardBasicTypes.STRING);
                query.addScalar("externalId", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();

                while (scroll.next()) {
                    Object[] objects = (Object[]) scroll.get();

                    String customerId = (String) objects[0];
                    String externalIdResult = (String) objects[1];

                    CustomerInformationDTO customer = new CustomerInformationDTO(customerId, externalIdResult);

                    customerList.add(customer);
                }

                return customerList;
            }
        };
    }

    public static Criterion<Customer, List<CustomerInformationDTO>> getCustomerDetails(final Long[] customerIDs) {
        return new CustomerCriterion<List<CustomerInformationDTO>>() {

            @Override
            public List<CustomerInformationDTO> get(EntityManager entityManager, int pFetchMode) {
                String sb = " select c.external_id as externalId,c.badge_number as badgeNumber,c.customer_ID as customerId, " +
                        " p.first_name as firstName,p.last_name as lastName" +
                        " from person p " +
                        " join customer c on p.person_id = c.person_id " +
                        " where c.customer_id in (:customerIDs)";

                List<CustomerInformationDTO> customerList = new ArrayList<>();

                NativeQuery<?> query = (NativeQuery<?>) entityManager.createNativeQuery(sb);
                query.setParameterList("customerIDs", customerIDs);

                query.addScalar("externalId", StandardBasicTypes.STRING);
                query.addScalar("badgeNumber", StandardBasicTypes.STRING);
                query.addScalar("customerId", StandardBasicTypes.STRING);
                query.addScalar("firstName", StandardBasicTypes.STRING);
                query.addScalar("lastName", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();

                while (scroll.next()) {
                    Object[] objects = (Object[]) scroll.get();

                    CustomerInformationDTO customer = new CustomerInformationDTO(
                            (String) objects[0],
                            (String) objects[2],
                            (String) objects[1],
                            (String) objects[3],
                            (String) objects[4]
                    );

                    customerList.add(customer);
                }

                return customerList;
            }
        };
    }
}
