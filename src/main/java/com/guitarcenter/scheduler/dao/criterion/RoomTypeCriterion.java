package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.List;

import jakarta.persistence.EntityManager;
import org.hibernate.Session;

import com.guitarcenter.scheduler.model.RoomType;

public abstract class RoomTypeCriterion<E> extends AbstractCriterion<RoomType, E> implements Criterion<RoomType, E> {

	private static final Criterion<RoomType, RoomType>	DEFAULT_INSTANCE	= new RoomTypeCriterion<RoomType>() {
																			};



	private RoomTypeCriterion() {
		super(RoomType.class);
	}



	@Override
	protected String getFetchScript(int pFetchMode) {
		StringBuilder sb = new StringBuilder();
		sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
		sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
		return sb.toString();
	}



	@Override
	public List<E> search(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	@Override
	public E get(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	public static Criterion<RoomType, RoomType> getInstance() {
		return DEFAULT_INSTANCE;
	}

}
