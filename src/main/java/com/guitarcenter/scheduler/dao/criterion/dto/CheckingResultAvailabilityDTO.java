package com.guitarcenter.scheduler.dao.criterion.dto;

public class CheckingResultAvailabilityDTO {
	
	
	private String appointmentSeriesId; //GSSP-268 lack Appointment job issue
	private Boolean	result	= Boolean.TRUE;
	private String	erorrMessage;
	private String issueFlag; //GSSP-268 lack Appointment job issue


	
	//GSSP-268 lack Appointment job issue start
	public String getAppointmentSeriesId() {
		return appointmentSeriesId;
	}



	public void setAppointmentSeriesId(String appointmentSeriesId) {
		this.appointmentSeriesId = appointmentSeriesId;
	}
	//lack Appointment job issue end


	public Boolean getResult() {
		return result;
	}



	public void setResult(Boolean pResult) {
		result = pResult;
	}



	public String getErorrMessage() {
		return erorrMessage;
	}



	public void setErorrMessage(String pErorrMessage) {
		erorrMessage = pErorrMessage;
	}


	//GSSP-268 lack Appointment job issue start
	public String getIssueFlag() {
		return issueFlag;
	}



	public void setIssueFlag(String issueFlag) {
		this.issueFlag = issueFlag;
	}
	//lack Appointment job issue end
	

}
