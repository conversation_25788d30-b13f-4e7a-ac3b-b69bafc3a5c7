package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.List;

import jakarta.persistence.EntityManager;
import org.hibernate.query.Query;
import org.hibernate.Session;

import com.guitarcenter.scheduler.model.Person;

public abstract class PersonCriterion<E> extends AbstractCriterion<Person, E> implements Criterion<Person, E> {

	private static final Criterion<Person, Person>	DEFAULT_INSTANCE	= new PersonCriterion<Person>() {
																		};



	private PersonCriterion() {
		super(Person.class);
	}



	@Override
	protected String getFetchScript(int pFetchMode) {
		StringBuilder sb = new StringBuilder();
		sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
		return sb.toString();
	}



	@Override
	public List<E> search(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	@Override
	public E get(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	public static Criterion<Person, Person> getInstance() {
		return DEFAULT_INSTANCE;
	}
	
	  /**
     * Returns a Criterion that can be used to find any existing employees in
     * the supplied site with a matching external id.
     * 
     * @param pSiteId site identifier
     * @param pExternalId String containing the external id to match
     * @return Criterion instance
     */
    public static Criterion<Person, Person> findByAuthId(final long pSiteId, final String authId) {
        return new PersonCriterion<Person>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Person> search(EntityManager entityManager, int pFetchMode) {
                Query query = (Query) entityManager.createQuery(new StringBuilder(" from Person t ")
                                                       .append(getFetchScript(pFetchMode))
                                                       .append(" where t.authId = :authId ")                                                       
                                                       .toString());
                query.setParameter("authId", authId);
                return query.list();
            }
        };
    }

}
