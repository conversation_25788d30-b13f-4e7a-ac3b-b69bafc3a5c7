package com.guitarcenter.scheduler.dao.criterion.dto;

public class ExportDetailsDTO {
	
	private Long profileId;
	public Long getProfileId() {
		return profileId;
	}

	public void setProfileId(Long profileId) {
		this.profileId = profileId;
	}
	private Long activeAppointments;
	private Long activeStudents;
	private Long canceledAppointmentsCount;
	private String location;
	private Long storeNumber;
	private Long serviceCount;
	private Long roomCount;
	private Long activityCount;
	private String studioTiming ;
	private Long totalConflicts;
	private Long instructorCount;
	
	public Long getInstructorCount() {
		return instructorCount;
	}

	public void setInstructorCount(Long instructorCount) {
		this.instructorCount = instructorCount;
	}

	public String getStudioTiming() {
		return studioTiming;
	}

	public void setStudioTiming(String studioTiming) {
		this.studioTiming = studioTiming;
	}

	public Long getTotalConflicts() {
		return totalConflicts;
	}

	public void setTotalConflicts(Long totalConflicts) {
		this.totalConflicts = totalConflicts;
	}

	public ExportDetailsDTO(Long  profileId, Long activeAppointments,Long activeStudents,Long canceledAppointmentsCount,String location,
			Long storeNumber,Long serviceCount,Long roomCount,Long activityCount,String studioTiming ,Long totalConflicts,Long instructorCount) {
		
		this.profileId = profileId;
		this.activeAppointments=activeAppointments;
		this.activeStudents=activeStudents;
		this.canceledAppointmentsCount=canceledAppointmentsCount;
		this.location=location;
		this.storeNumber=storeNumber;
		this.serviceCount=serviceCount;
		this.roomCount=roomCount;
		this.activityCount=activityCount;
		this.studioTiming = studioTiming;
		this.totalConflicts = totalConflicts;
		this.instructorCount=instructorCount;
	}
		
	public Long getActiveAppointments() {
		return activeAppointments;
	}
	public void setActiveAppointments(Long activeAppointments) {
		this.activeAppointments = activeAppointments;
	}
	public Long getActiveStudents() {
		return activeStudents;
	}
	public void setActiveStudents(Long activeStudents) {
		this.activeStudents = activeStudents;
	}
	public Long getCanceledAppointmentsCount() {
		return canceledAppointmentsCount;
	}
	public void setCanceledAppointmentsCount(Long canceledAppointmentsCount) {
		this.canceledAppointmentsCount = canceledAppointmentsCount;
	}
	public String getLocation() {
		return location;
	}
	public void setLocation(String location) {
		this.location = location;
	}
	public Long getStoreNumber() {
		return storeNumber;
	}
	public void setStoreNumber(Long storeNumber) {
		this.storeNumber = storeNumber;
	}
	public Long getServiceCount() {
		return serviceCount;
	}
	public void setServiceCount(Long serviceCount) {
		this.serviceCount = serviceCount;
	}
	public Long getRoomCount() {
		return roomCount;
	}
	public void setRoomCount(Long roomCount) {
		this.roomCount = roomCount;
	}
	public Long getActivityCount() {
		return activityCount;
	}
	public void setActivityCount(Long activityCount) {
		this.activityCount = activityCount;
	}
	
}
