package com.guitarcenter.scheduler.dao.criterion.dto;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.enums.Canceled;

/**
 * Created by josedeng on 12/16/13.
 */
public class RehearsalBookingDTO {

    private Long appointmentId;
    private Date startTime;
    private Date endTime;
    private Canceled canceled;
    private Long duration;
    private String roomName;
    private String activityName;
    private String bandName;
    private List<Customer> customers = new ArrayList<Customer>();
    private Map<Long, Customer> customerMap = new HashMap<Long, Customer>();

    public RehearsalBookingDTO(Long pAppointmentId, Date pStartTime, Date pEndTime, String pCanceled, Long pDuration, String pRoomName, String pActivityName) {
        this.appointmentId = pAppointmentId;
        this.startTime = pStartTime;
        this.endTime = pEndTime;
        this.canceled = Canceled.valueOf(pCanceled == null ? "N" : pCanceled);
        this.duration = Math.round(pDuration / 60.0D / 60.0D);
        this.roomName = pRoomName;
        this.activityName = pActivityName;
    }

    public Long getAppointmentId() {
        return appointmentId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public Canceled getCanceled() {
        return canceled;
    }

    public Long getDuration() {
        return duration;
    }

    public String getRoomName() {
        return roomName;
    }

    public String getActivityName() {
        return activityName;
    }

    public List<Customer> getCustomers() {
        return customers;
    }

    public void addCustomer(Customer customer) {
        Long customerId = customer.getCustomerId();
        Customer customer1 = customerMap.get(customerId);
        if (customer1 == null) {
            customers.add(customer);
            customerMap.put(customerId, customer);
        }
    }

    public String getBandName() {
        return bandName;
    }

    public void setBandName(String bandName) {
        this.bandName = bandName;
    }
}
