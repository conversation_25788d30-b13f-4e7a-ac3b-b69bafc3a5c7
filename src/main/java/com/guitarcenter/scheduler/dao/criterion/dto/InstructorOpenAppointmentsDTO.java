package com.guitarcenter.scheduler.dao.criterion.dto;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.enums.Canceled;

/**
 * Added for NewInsAptReport _ June 2015 Enhancement
 */
public class InstructorOpenAppointmentsDTO {

    private String date1;
	private Long appointmentId;
	//For GSSP-170
	private String recurringStatus;
    private Date startTime;
    private Date endTime;
    private Canceled canceled;
    private Long duration;
    private String roomName;
    private String activityName;
    private Instructor instructor;
    private List<Customer> customers = new ArrayList<Customer>();
    private Map<Long, Customer> customerMap = new HashMap<Long, Customer>();
    
    //changes made for GSP-238
    private String storeName;
	
	 //Changes made for GSSP-241
    private String timeFrame;
    private String instructorName;
	
	
	  public String getInstructorName() {
		return instructorName;
	}

	public void setInstructorName(String instructorName) {
		this.instructorName = instructorName;
	}
	
	    public String getTimeFrame() {
		return timeFrame;
	}

	public void setTimeFrame(String timeFrame) {
		this.timeFrame = timeFrame;
	}
	
	//For GSSP-241 InstructorOpenAppointmentsDTO-Room
    public InstructorOpenAppointmentsDTO(String roomName,String date1, String pStartTime,String pEndTime,Long pDuration, String pActivityName ) {
        
    	this.roomName=roomName;
    	this.date1 = date1;
    	this.timeFrame = pStartTime + "-" + (pEndTime);			
	    this.duration = Math.round(pDuration / 60.0D / 60.0D);
        this.activityName = pActivityName;
        
      
    }
	
	
	//End of Changes made for GSSP-241
    
    
    public String getStoreName() {
		return storeName;
	}

	public void setStoreName(String storeName) {
		this.storeName = storeName;
	}

	public InstructorOpenAppointmentsDTO(String date1, Long pAppointmentId, Date pStartTime, Date pEndTime, String pCanceled, Long pDuration, String pRoomName, String pActivityName, Instructor pInstructor) {
        this.date1 = date1;
		this.appointmentId = pAppointmentId;
        this.startTime = pStartTime;
        this.endTime = pEndTime;
        this.canceled = Canceled.valueOf(pCanceled == null ? "N" : pCanceled);
        this.duration = Math.round(pDuration / 60.0D / 60.0D);
        this.roomName = pRoomName;
        this.activityName = pActivityName;
        this.instructor = pInstructor;
    }
    
    //For GSSP-170
    public InstructorOpenAppointmentsDTO(String date1, Long pAppointmentId, Date pStartTime, Date pEndTime, Long pDuration, String pActivityName, Instructor pInstructor, String pRecurringStatus) {
        this.date1 = date1;
		this.appointmentId = pAppointmentId;
		this.recurringStatus = pRecurringStatus;
        this.startTime = pStartTime;
        this.endTime = pEndTime;
        this.duration = Math.round(pDuration / 60.0D / 60.0D);
        this.activityName = pActivityName;
        this.instructor = pInstructor;
    }
    
    //For GSSP-170
    public InstructorOpenAppointmentsDTO(String date1, Long pAppointmentId, Date pStartTime, Date pEndTime, Long pDuration, String pRoomName, String pActivityName, String pRecurringStatus) {
        this.date1 = date1;
		this.appointmentId = pAppointmentId;
		this.recurringStatus = pRecurringStatus;
        this.startTime = pStartTime;
        this.endTime = pEndTime;
        this.duration = Math.round(pDuration / 60.0D / 60.0D);
        this.roomName = pRoomName;
        this.activityName = pActivityName;
    }   
    
    
    
  //Changes made for GSSP-238
	
    public InstructorOpenAppointmentsDTO(String date1, Long pAppointmentId, Date pStartTime, Date pEndTime, Long pDuration, String pRoomName, String pActivityName, String pRecurringStatus,String storeName) {
        this.date1 = date1;
		this.appointmentId = pAppointmentId;
		this.recurringStatus = pRecurringStatus;
        this.startTime = pStartTime;
        this.endTime = pEndTime;
        this.duration = Math.round(pDuration / 60.0D / 60.0D);
        this.roomName = pRoomName;
        this.activityName = pActivityName;
        this.storeName = storeName;
    }  
  	
    
    //Changes made for GSSP-238
    
    public InstructorOpenAppointmentsDTO(String date1, Long pAppointmentId, Date pStartTime, Date pEndTime, Long pDuration, String pActivityName, Instructor pInstructor, String pRecurringStatus,String storeName) {
        this.date1 = date1;
		this.appointmentId = pAppointmentId;
		this.recurringStatus = pRecurringStatus;
        this.startTime = pStartTime;
        this.endTime = pEndTime;
        this.duration = Math.round(pDuration / 60.0D / 60.0D);
        this.activityName = pActivityName;
        this.instructor = pInstructor;
        this.storeName = storeName;
    }
    
  
	
	
	public String getDate1() {
        return date1;
    }

    public Long getAppointmentId() {
        return appointmentId;
    }
    
    //For GSSP-170
    public String getRecurringStatus() {
        return recurringStatus;
    }

    public Date getStartTime() {
        return startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public Canceled getCanceled() {
        return canceled;
    }

    public Long getDuration() {
        return duration;
    }

    public String getRoomName() {
        return roomName;
    }

    public String getActivityName() {
        return activityName;
    }

    public Instructor getInstructor() {
        return instructor;
    }

    public void setInstructor(Instructor instructor) {
        this.instructor = instructor;
    }

    public List<Customer> getCustomers() {
        return customers;
    }

    public void addCustomer(Customer customer) {
        Long customerId = customer.getCustomerId();
        Customer customer1 = customerMap.get(customerId);
        if (customer1 == null) {
            customers.add(customer);
            customerMap.put(customerId, customer);
        }
    }
	
	/* (non-Javadoc)
	 * @see java.lang.Object#hashCode()
	 */
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((date1 == null) ? 0 : date1.hashCode());
		result = prime * result
				+ ((instructor == null) ? 0 : instructor.hashCode());
		return result;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#equals(java.lang.Object)
	 */
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		InstructorOpenAppointmentsDTO other = (InstructorOpenAppointmentsDTO) obj;
		if (date1 == null) {
			if (other.date1 != null)
				return false;
		} else if (!date1.equals(other.date1))
			return false;
		if (instructor == null) {
			if (other.instructor != null)
				return false;
		} else if (!instructor.equals(other.instructor))
			return false;
		return true;
	}

}