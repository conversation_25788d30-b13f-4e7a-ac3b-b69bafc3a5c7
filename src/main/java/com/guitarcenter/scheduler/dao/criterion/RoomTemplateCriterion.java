package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROOM_SIZE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROOM_TYPE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.List;

import jakarta.persistence.EntityManager;
import org.hibernate.query.Query;
import org.hibernate.Session;

import com.guitarcenter.scheduler.model.RoomTemplate;

public abstract class RoomTemplateCriterion<E> extends AbstractCriterion<RoomTemplate, E> implements
		Criterion<RoomTemplate, E> {

	private static final Criterion<RoomTemplate, RoomTemplate>	DEFAULT_INSTANCE	= new RoomTemplateCriterion<RoomTemplate>() {
																					};



	private RoomTemplateCriterion() {
		super(RoomTemplate.class);
	}



	@Override
	protected String getFetchScript(int pFetchMode) {
		StringBuilder sb = new StringBuilder();
		sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
		sb.append(addFetchHQL(FETCH_ROOM_SIZE, pFetchMode, "t.roomSize", true));
		sb.append(addFetchHQL(FETCH_ROOM_TYPE, pFetchMode, "t.roomType", true));
		sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
		return sb.toString();
	}



	@Override
	public List<E> search(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	@Override
	public E get(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	public static Criterion<RoomTemplate, RoomTemplate> getInstance() {
		return DEFAULT_INSTANCE;
	}



	public static Criterion<RoomTemplate, Boolean> hasByActivityId(final long pActivityId) {

		RoomTemplateCriterion<Boolean> instance = new RoomTemplateCriterion<Boolean>() {

			@Override
			public Boolean get(EntityManager entityManager, int pFetchMode) {
				StringBuilder sb = new StringBuilder("select count(t) from RoomTemplate t ");
				sb.append(" left join t.activities a ");
				sb.append(" where a.activityId = :activityId ");
				Query query = (Query) entityManager.createQuery(sb.toString());
				query.setMaxResults(1);
				query.setParameter("activityId", pActivityId);
				Long count = (Long) query.uniqueResult();
				return count > 0 ? Boolean.TRUE : Boolean.FALSE;
			}

		};
		return instance;
	}



	public static Criterion<RoomTemplate, Boolean> hasByServiced(final long pServiceId) {

		RoomTemplateCriterion<Boolean> instance = new RoomTemplateCriterion<Boolean>() {

			@Override
			public Boolean get(EntityManager entityManager, int pFetchMode) {
				StringBuilder sb = new StringBuilder("select count(t) from RoomTemplate t ");
				sb.append(" left join t.services a ");
				sb.append(" where a.serviceId = :serviceId ");
				Query query = (Query) entityManager.createQuery(sb.toString());
				query.setMaxResults(1);
				query.setParameter("serviceId", pServiceId);
				Long count = (Long) query.uniqueResult();
				return count > 0 ? Boolean.TRUE : Boolean.FALSE;
			}

		};
		return instance;
	}

}
