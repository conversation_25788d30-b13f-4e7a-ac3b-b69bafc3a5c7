package com.guitarcenter.scheduler.dao.criterion.dto;

import java.util.Date;
import java.util.List;

import com.guitarcenter.scheduler.model.Instructor;

/**
 * Created by josedeng on 12/13/13.
 */
public class InstructorAppointmentStatusListDTO {

    private String date;
    private List<InstructorAppointmentStatusDTO> list;
    private Instructor instructor;
    private Date startHours;
    private Date endHours;
    
    
    
    
    
  
	public InstructorAppointmentStatusListDTO() {
		super();
	}
	public String getDate() {
		return date;
	}
	public void setDate(String date) {
		this.date = date;
	}
	public List<InstructorAppointmentStatusDTO> getList() {
		return list;
	}
	public void setList(List<InstructorAppointmentStatusDTO> list) {
		this.list = list;
	}
	public Instructor getInstructor() {
		return instructor;
	}
	public void setInstructor(Instructor instructor) {
		this.instructor = instructor;
	}
	public Date getStartHours() {
		return startHours;
	}
	public void setStartHours(Date startHours) {
		this.startHours = startHours;
	}
	public Date getEndHours() {
		return endHours;
	}
	public void setEndHours(Date endHours) {
		this.endHours = endHours;
	}
  
	public InstructorAppointmentStatusListDTO(String date, List<InstructorAppointmentStatusDTO> list,
			Instructor instructor, Date startHours, Date endHours) {
		super();
		this.date = date;
		this.list = list;
		this.instructor = instructor;
		this.startHours = startHours;
		this.endHours = endHours;
	}
	@Override
	public String toString() {
		return "InstructorAppointmentStatusListDTO [date=" + date + ", list=" + list + ", instructor=" + instructor
				+ ", startHours=" + startHours + ", endHours=" + endHours + "]";
	}
    
    

	
	
}
