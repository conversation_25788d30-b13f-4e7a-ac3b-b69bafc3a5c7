package com.guitarcenter.scheduler.dao.criterion.dto;

/**
 * Created by joseden<PERSON> on 12/13/13.
 */
public class InstructorDateDTO {

    private String date1;
    private Long instructorId;

    public InstructorDateDTO(String date1, Long instructorId) {
        this.date1 = date1;
        this.instructorId = instructorId;
    }

    public String getDate1() {
        return date1;
    }

    public Long getInstructorId() {
        return instructorId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        InstructorDateDTO that = (InstructorDateDTO) o;

        if (!date1.equals(that.date1)) return false;
        if (!instructorId.equals(that.instructorId)) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = date1.hashCode();
        result = 31 * result + instructorId.hashCode();
        return result;
    }
}
