package com.guitarcenter.scheduler.dao.criterion.dto;

import java.util.Date;

/**
 * @Date 5/7/2020 11:33 AM
 * <AUTHOR>
 **/
public class CustomerAppointmentDTO {

    private String appointmentId;
    private String locationId;
    private String location;

    private String activityName;
    private String customerId;
    private String customerFirstName;
    private String instructorFirstName;
    private String instructorId;
    private String customerLastName;
    private String instructorLastName;
    private String duration;
    private String startDate;
    private String startTime;
    private String endTime;
    private String status;

    private String joinURL;
    private String serviceId;
    private boolean enabledSendRemainder;
    private String zoomMeetingID;
    private Date utcEndTime;
    private String timeZone;
    private Date utcStartTime;
    private String locationTimezone;
    private Date endTimeDate;
    private Date startTimeDate;

    private boolean enabledStatus;
    private String type;
    private String comments;
    private String studentNotes;

    private boolean enabledComments;
    private boolean enabledNotes;

    public boolean isEnabledStatus() {
        return enabledStatus;
    }

    public void setEnabledStatus(boolean enabledStatus) {
        this.enabledStatus = enabledStatus;
    }

    public boolean isEnabledComments() {
        return enabledComments;
    }

    public void setEnabledComments(boolean enabledComments) {
        this.enabledComments = enabledComments;
    }

    public boolean isEnabledNotes() {
        return enabledNotes;
    }

    public void setEnabledNotes(boolean enabledNotes) {
        this.enabledNotes = enabledNotes;
    }


    public String getAppointmentId() {
        return appointmentId;
    }

    public void setAppointmentId(String appointmentId) {
        this.appointmentId = appointmentId;
    }

    public String getLocationId() {
        return locationId;
    }

    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }


    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerFirstName() {
        return customerFirstName;
    }

    public void setCustomerFirstName(String customerFirstName) {
        this.customerFirstName = customerFirstName;
    }

    public String getInstructorFirstName() {
        return instructorFirstName;
    }

    public void setInstructorFirstName(String instructorFirstName) {
        this.instructorFirstName = instructorFirstName;
    }

    public String getInstructorId() {
        return instructorId;
    }

    public void setInstructorId(String instructorId) {
        this.instructorId = instructorId;
    }

    public String getCustomerLastName() {
        return customerLastName;
    }

    public void setCustomerLastName(String customerLastName) {
        this.customerLastName = customerLastName;
    }

    public String getInstructorLastName() {
        return instructorLastName;
    }

    public void setInstructorLastName(String instructorLastName) {
        this.instructorLastName = instructorLastName;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


    public String getStudentNotes() {
        return studentNotes;
    }

    public void setStudentNotes(String studentNotes) {
        this.studentNotes = studentNotes;
    }


    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getJoinURL() {
        return joinURL;
    }
    public void setJoinURL(String joinURL) {
        this.joinURL = joinURL;
    }

    public String getServiceId() {
        return serviceId;
    }
    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public boolean isEnabledSendRemainder() {
        return enabledSendRemainder;
    }
    public void setEnabledSendRemainder(boolean enabledSendRemainder) {
        this.enabledSendRemainder = enabledSendRemainder;
    }

    public String getZoomMeetingID() {
        return zoomMeetingID;
    }
    public void setZoomMeetingID(String zoomMeetingID) {
        this.zoomMeetingID = zoomMeetingID;
    }
    public Date getUtcStartTime() {
        return utcStartTime;
    }
    public void setUtcStartTime(Date utcStartTime) {
        this.utcStartTime = utcStartTime;
    }
    public Date getUtcEndTime() {
        return utcEndTime;
    }
    public void setUtcEndTime(Date utcEndTime) {
        this.utcEndTime = utcEndTime;
    }
    public String getTimeZone() {
        return timeZone;
    }
    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public String getLocationTimezone() {
        return locationTimezone;
    }
    public void setLocationTimezone(String locationTimezone) {
        this.locationTimezone = locationTimezone;
    }
    public Date getEndTimeDate() {
        return endTimeDate;
    }

    public void setEndTimeDate(Date endTimeDate) {
        this.endTimeDate = endTimeDate;
    }
    
    public Date getStartTimeDate() {
        return endTimeDate;
    }

    public void setStartTimeDate(Date startTimeDate) {
        this.startTimeDate = startTimeDate;
    }


    @Override
    public String toString() {
        return "CustomerAppointmentDTO{" +
                "appointmentId='" + appointmentId + '\'' +
                ", locationId='" + locationId + '\'' +
                ", location='" + location + '\'' +
                ", activityName='" + activityName + '\'' +
                ", customerId='" + customerId + '\'' +
                ", customerFirstName='" + customerFirstName + '\'' +
                ", instructorFirstName='" + instructorFirstName + '\'' +
                ", instructorId='" + instructorId + '\'' +
                ", customerLastName='" + customerLastName + '\'' +
                ", instructorLastName='" + instructorLastName + '\'' +
                ", duration='" + duration + '\'' +
                ", startDate='" + startDate + '\'' +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", status='" + status + '\'' +
                ", joinURL='" + joinURL + '\'' +
                ", serviceId='" + serviceId + '\'' +
                ", enabledSendRemainder=" + enabledSendRemainder +
                ", zoomMeetingID='" + zoomMeetingID + '\'' +
                ", utcEndTime=" + utcEndTime +
                ", timeZone='" + timeZone + '\'' +
                ", utcStartTime=" + utcStartTime +
                ", locationTimezone='" + locationTimezone + '\'' +
                ", endTimeDate=" + endTimeDate +
                ", startTimeDate=" + startTimeDate +
                ", enabledStatus=" + enabledStatus +
                ", type='" + type + '\'' +
                ", comments='" + comments + '\'' +
                ", studentNotes='" + studentNotes + '\'' +
                ", enabledComments=" + enabledComments +
                ", enabledNotes=" + enabledNotes +
                '}';
    }
}
