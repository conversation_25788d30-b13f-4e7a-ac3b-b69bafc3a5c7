package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.List;

import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import com.guitarcenter.scheduler.model.Site;

public abstract class SiteCriterion<E> extends AbstractCriterion<Site, E> implements Criterion<Site, E> {

	private static final Criterion<Site, Site> DEFAULT_INSTANCE = new SiteCriterion<Site>() {};

	private SiteCriterion() {
		super(Site.class);
	}

	@Override
	protected String getFetchScript(int fetchMode) {
		StringBuilder sb = new StringBuilder();
		sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, fetchMode, "t.updatedBy", true));
		return sb.toString();
	}

	@Override
	public List<E> search(EntityManager entityManager, int fetchMode) {
		throw new UnsupportedOperationException("Current criteria object doesn't support this operation.");
	}

	@Override
	public E get(EntityManager entityManager, int fetchMode) {
		throw new UnsupportedOperationException("Current criteria object doesn't support this operation.");
	}

	public static Criterion<Site, Site> getInstance() {
		return DEFAULT_INSTANCE;
	}

	/**
	 * Returns a Criterion that can be used to find Site entities matching a
	 * supplied external id.
	 *
	 * @param externalId String containing the external identifier to use for
	 *                   lookup
	 * @return a Criterion for lookup
	 */
	public static Criterion<Site, Site> findByExternalId(final String externalId) {
		return new SiteCriterion<Site>() {
			@Override
			public List<Site> search(EntityManager entityManager, int fetchMode) {
				String jpql = new StringBuilder("from Site t ")
						.append(getFetchScript(fetchMode))
						.append(" where t.externalId = :externalId")
						.toString();
				TypedQuery<Site> query = entityManager.createQuery(jpql, Site.class);
				query.setParameter("externalId", externalId);
				return query.getResultList();
			}
		};
	}
}
