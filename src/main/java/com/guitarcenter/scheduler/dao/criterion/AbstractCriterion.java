package com.guitarcenter.scheduler.dao.criterion;

import java.util.List;

import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class AbstractCriterion<T, E> implements Criterion<T, E> {

	private static final Logger LOGGER = LoggerFactory.getLogger(AbstractCriterion.class);
	private final Class<T> entityClass;

	AbstractCriterion(final Class<T> entityClass) {
		this.entityClass = entityClass;
	}

	protected Class<T> getEntityClass() {
		return this.entityClass;
	}

	public static <T> Criterion<T, T> getAbstractInstance(final Class<T> entityClass) {
		return new AbstractCriterion<T, T>(entityClass) {

			@Override
			public List<T> search(EntityManager entityManager, int fetchMode) {
				throw new UnsupportedOperationException("Current criteria object doesn't support this operation.");
			}

			@Override
			protected String getFetchScript(int fetchMode) {
				return "";
			}

			@Override
			public T get(EntityManager entityManager, int fetchMode) {
				throw new UnsupportedOperationException("Current criteria object doesn't support this operation.");
			}

		};
	}

	@Override
	public List<E> search(EntityManager entityManager) {
		return search(entityManager, 0);
	}

	@Override
	public List<T> searchAll(EntityManager entityManager) {
		return searchAll(entityManager, 0);
	}

	@Override
	public E get(EntityManager entityManager) {
		return get(entityManager, 0);
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<T> searchAll(EntityManager entityManager, int fetchMode) {
		StringBuilder sb = new StringBuilder("from " + getEntityClass().getName() + " t ");
		sb.append(getFetchScript(fetchMode));
		if (LOGGER.isDebugEnabled()) {
			LOGGER.debug("AbstractCriterion.searchAll : execute JPQL is [{}]", sb);
		}
		TypedQuery<T> query = entityManager.createQuery(sb.toString(), getEntityClass());
		return query.getResultList();
	}

	protected abstract String getFetchScript(int fetchMode);
}
