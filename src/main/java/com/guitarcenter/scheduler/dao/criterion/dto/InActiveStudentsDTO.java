package com.guitarcenter.scheduler.dao.criterion.dto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.guitarcenter.scheduler.model.Customer;
public class InActiveStudentsDTO {
    private String customerEmail;
	private String customerFirstName;
	private String customerLastName;
	private String customerPhone;
	private String externalId;
    
    private List<Customer> customers = new ArrayList<Customer>();
    private Map<Long, Customer> customerMap = new HashMap<Long, Customer>();

	
	public InActiveStudentsDTO(String customerFirstName,String customerLastName,String customerEmail,String customerPhone,String externalId) {
		    this.customerFirstName=customerFirstName;
		    this.customerLastName=customerLastName;
	        this.customerEmail=customerEmail;
	        this.externalId=externalId;
	        this.customerPhone=customerPhone;
	}
	
		public String getCustomerPhone() {
		return customerPhone;
	}

	public void setCustomerPhone(String customerPhone) {
		this.customerPhone = customerPhone;
	}

		public String getcustomerEmail() {
		return customerEmail;
	}
	public void setcustomerEmail(String customerEmail) {
		this.customerEmail = customerEmail;
	}
	
		public String getExternalId() {
		return externalId;
	}
	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}
		public List<Customer> getCustomers() {
		return customers;
	}
	public void setCustomers(List<Customer> customers) {
		this.customers = customers;
	}
	public void addCustomer(Customer customer) {
        Long customerId = customer.getCustomerId();
        Customer customer1 = customerMap.get(customerId);
        if (customer1 == null) {
            customers.add(customer);
            customerMap.put(customerId, customer);
        }
    }
	public Map<Long, Customer> getCustomerMap() {
		return customerMap;
	}
	public void setCustomerMap(Map<Long, Customer> customerMap) {
		this.customerMap = customerMap;
	}
	public String getCustomerFirstName() {
		return customerFirstName;
	}
	public void setCustomerFirstName(String customerFirstName) {
		this.customerFirstName = customerFirstName;
	}
	public String getCustomerLastName() {
		return customerLastName;
	}
	public void setCustomerLastName(String customerLastName) {
		this.customerLastName = customerLastName;
	}
	


}
