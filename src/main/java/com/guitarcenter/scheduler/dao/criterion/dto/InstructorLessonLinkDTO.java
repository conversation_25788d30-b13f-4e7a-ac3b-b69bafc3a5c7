package com.guitarcenter.scheduler.dao.criterion.dto;

import java.util.Date;

/**
 * Created by josedeng on 12/13/13.
 */
public class InstructorLessonLinkDTO {

	private String zoomMeetingID;
	private String appointmentId;
	private Date startDate;
	private String startTime;
	private Date endTime;
	private String canceled;
	private String duration;
	private String activityName;
	private String instructorId;
	private String instructorFirstName;
	private String instructorLastName;
	private String customerId;
	private String customerFirstName;
	private String customerLastName;
	private String instructorExternalId;
	private String locationExternalId;
	private String locationName;
	private String zoomMeetingUrl;
	private String showStatus;
	private String comments;
	private String timeFrame;
	private String customerEmail;
	private String customerPhone;
	private String formattedcustomerPhone;
	
	private boolean enabledStatus;
	private boolean enabledComments;
	private boolean enabledLaunch;
	private boolean enabledStudentNote;
	private String launchStatus;
	private Date utcEndTime;
	private String timeZone;
	private String studentNote;
	private String joinURL;
	private Date utcStartTime;
	private String locationTimezone;
	private boolean enabledSendRemainder;
	private String serviceId;
	private String roomNumberId;
	private String appointmentRoomId;
	private String roomNumberName;
	//There is no meaning for this its just for the UI.
	private String cid;
	private String customerExternalId;
	
	//private String				lessonStatus;
	//private String				nextLessonStatus;
	private String				assignment;
	private String				practiceNotes;
	private String				remarks;
	private String				submit;
	//private String				rate;
	
	
	public String getZoomMeetingID() {
		return zoomMeetingID;
	}
	public void setZoomMeetingID(String zoomMeetingID) {
		this.zoomMeetingID = zoomMeetingID;
	}
	public String getAppointmentId() {
		return appointmentId;
	}
	public void setAppointmentId(String appointmentId) {
		this.appointmentId = appointmentId;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String date) {
		this.startTime = date;
	}
	public Date getEndTime() {
		return endTime;
	}
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	public String getCanceled() {
		return canceled;
	}
	public void setCanceled(String canceled) {
		this.canceled = canceled;
	}
	public String getDuration() {
		return duration;
	}
	public void setDuration(String duration) {
		this.duration = duration;
	}
	public String getActivityName() {
		return activityName;
	}
	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}
	public String getInstructorId() {
		return instructorId;
	}
	public void setInstructorId(String instructorId) {
		this.instructorId = instructorId;
	}
	public String getInstructorFirstName() {
		return instructorFirstName;
	}
	public void setInstructorFirstName(String instructorFirstName) {
		this.instructorFirstName = instructorFirstName;
	}
	public String getInstructorLastName() {
		return instructorLastName;
	}
	public void setInstructorLastName(String instructorLastName) {
		this.instructorLastName = instructorLastName;
	}
	public String getCustomerId() {
		return customerId;
	}
	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}
	public String getCustomerFirstName() {
		return customerFirstName;
	}
	public void setCustomerFirstName(String customerFirstName) {
		this.customerFirstName = customerFirstName;
	}
	public String getCustomerLastName() {
		return customerLastName;
	}
	public void setCustomerLastName(String customerLastName) {
		this.customerLastName = customerLastName;
	}
	public String getInstructorExternalId() {
		return instructorExternalId;
	}
	public void setInstructorExternalId(String instructorExternalId) {
		this.instructorExternalId = instructorExternalId;
	}
	public String getLocationExternalId() {
		return locationExternalId;
	}
	public void setLocationExternalId(String locationExternalId) {
		this.locationExternalId = locationExternalId;
	}
	public String getLocationName() {
		return locationName;
	}
	public void setLocationName(String locationName) {
		this.locationName = locationName;
	}
	public String getZoomMeetingUrl() {
		return zoomMeetingUrl;
	}
	public void setZoomMeetingUrl(String zoomMeetingUrl) {
		this.zoomMeetingUrl = zoomMeetingUrl;
	}
	public String getShowStatus() {
		return showStatus;
	}
	public void setShowStatus(String showStatus) {
		this.showStatus = showStatus;
	}
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public String getTimeFrame() {
		return timeFrame;
	}
	public void setTimeFrame(String timeFrame) {
		this.timeFrame = timeFrame;
	}
	
	public String getCustomerEmail() {
		return customerEmail;
	}
	public void setCustomerEmail(String customerEmail) {
		this.customerEmail = customerEmail;
	}
	public String getCustomerPhone() {
		return customerPhone;
	}
	public void setCustomerPhone(String customerPhone) {
		this.customerPhone = customerPhone;
	}
	public boolean isEnabledStatus() {
		return enabledStatus;
	}
	public void setEnabledStatus(boolean enabledStatus) {
		this.enabledStatus = enabledStatus;
	}
	public boolean isEnabledComments() {
		return enabledComments;
	}
	public void setEnabledComments(boolean enabledComments) {
		this.enabledComments = enabledComments;
	}
	
	public boolean isEnabledLaunch() {
		return enabledLaunch;
	}
	public void setEnabledLaunch(boolean enabledLaunch) {
		this.enabledLaunch = enabledLaunch;
	}
	public String getLaunchStatus() {
		return launchStatus;
	}
	public void setLaunchStatus(String launchStatus) {
		this.launchStatus = launchStatus;
	}
	
	
	public Date getUtcEndTime() {
		return utcEndTime;
	}
	public void setUtcEndTime(Date utcEndTime) {
		this.utcEndTime = utcEndTime;
	}
	
	
	
	
	
	public String getStudentNote() {
		return studentNote;
	}
	public void setStudentNote(String studentNote) {
		this.studentNote = studentNote;
	}
	public String getTimeZone() {
		return timeZone;
	}
	public void setTimeZone(String timeZone) {
		this.timeZone = timeZone;
	}
	
	public String getJoinURL() {
		return joinURL;
	}
	public void setJoinURL(String joinURL) {
		this.joinURL = joinURL;
	}
	
	
	
	
	public boolean isEnabledStudentNote() {
		return enabledStudentNote;
	}
	public void setEnabledStudentNote(boolean enabledStudentNote) {
		this.enabledStudentNote = enabledStudentNote;
	}
	public Date getUtcStartTime() {
		return utcStartTime;
	}
	public void setUtcStartTime(Date utcStartTime) {
		this.utcStartTime = utcStartTime;
	}
	
	
	public String getLocationTimezone() {
		return locationTimezone;
	}
	public void setLocationTimezone(String locationTimezone) {
		this.locationTimezone = locationTimezone;
	}
	public boolean isEnabledSendRemainder() {
		return enabledSendRemainder;
	}
	public void setEnabledSendRemainder(boolean enabledSendRemainder) {
		this.enabledSendRemainder = enabledSendRemainder;
	}
	
	
	
	public String getFormattedcustomerPhone() {
		return formattedcustomerPhone;
	}
	public void setFormattedcustomerPhone(String formattedcustomerPhone) {
		this.formattedcustomerPhone = formattedcustomerPhone;
	}
	public String getServiceId() {
		return serviceId;
	}
	public void setServiceId(String serviceId) {
		this.serviceId = serviceId;
	}
	public String getRoomNumberId() {
		return roomNumberId;
	}
	public void setRoomNumberId(String roomNumberId) {
		this.roomNumberId = roomNumberId;
	}
	public String getAppointmentRoomId() {
		return appointmentRoomId;
	}
	public void setAppointmentRoomId(String appointmentRoomId) {
		this.appointmentRoomId = appointmentRoomId;
	}
	public String getRoomNumberName() {
		return roomNumberName;
	}
	public void setRoomNumberName(String roomNumberName) {
		this.roomNumberName = roomNumberName;
	}
	public String getCid() {
		return cid;
	}
	public void setCid(String cid) {
		this.cid = cid;
	}
	
	
	
	
	public String getCustomerExternalId() {
		return customerExternalId;
	}
	public void setCustomerExternalId(String customerExternalId) {
		this.customerExternalId = customerExternalId;
	}
	/*public String getLessonStatus() {
		return lessonStatus;
	}
	public void setLessonStatus(String lessonStatus) {
		this.lessonStatus = lessonStatus;
	}
	public String getNextLessonStatus() {
		return nextLessonStatus;
	}
	public void setNextLessonStatus(String nextLessonStatus) {
		this.nextLessonStatus = nextLessonStatus;
	}*/
	public String getAssignment() {
		return assignment;
	}
	public void setAssignment(String assignment) {
		this.assignment = assignment;
	}
	public String getPracticeNotes() {
		return practiceNotes;
	}
	public void setPracticeNotes(String practiceNotes) {
		this.practiceNotes = practiceNotes;
	}
	public String getRemarks() {
		return remarks;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	public String getSubmit() {
		return submit;
	}
	public void setSubmit(String submit) {
		this.submit = submit;
	}
	/*public String getRate() {
		return rate;
	}
	public void setRate(String rate) {
		this.rate = rate;
	}*/
	@Override
	public String toString() {
		return "InstructorLessonLinkDTO [submit=" + submit + ", appointmentId=" + appointmentId
				+ ", startDate=" + startDate + ", startTime=" + startTime + ", endTime=" + endTime + ", canceled="
				+ canceled + ", duration=" + duration + ", activityName=" + activityName + ", instructorId="
				+ instructorId + ", instructorFirstName=" + instructorFirstName + ", instructorLastName="
				+ instructorLastName + ", customerId=" + customerId + ", customerFirstName=" + customerFirstName
				+ ", customerLastName=" + customerLastName + ", instructorExternalId=" + instructorExternalId
				+ ", locationExternalId=" + locationExternalId + ", locationName=" + locationName + ", zoomMeetingUrl="
				+ zoomMeetingUrl + ", showStatus=" + showStatus + ", comments=" + comments + ", timeFrame=" + timeFrame
				+ ", customerEmail=" + customerEmail + ", customerPhone=" + customerPhone + ", formattedcustomerPhone="
				+ formattedcustomerPhone + ", enabledStatus=" + enabledStatus + ", enabledComments=" + enabledComments
				+ ", enabledLaunch=" + enabledLaunch + ", enabledStudentNote=" + enabledStudentNote + ", launchStatus="
				+ launchStatus + ", utcEndTime=" + utcEndTime + ", timeZone=" + timeZone + ", studentNote="
				+ studentNote + ", joinURL=" + joinURL + ", utcStartTime=" + utcStartTime + ", locationTimezone="
				+ locationTimezone + ", enabledSendRemainder=" + enabledSendRemainder + ", serviceId=" + serviceId
				+ ", roomNumberId=" + roomNumberId + ", appointmentRoomId=" + appointmentRoomId + ", roomNumberName="
				+ roomNumberName + ", cid=" + cid + ",  assignment=" + assignment + ", practiceNotes=" + practiceNotes + ", remarks=" + remarks + "]";
	}
	
	
	

	
	
	
  

}