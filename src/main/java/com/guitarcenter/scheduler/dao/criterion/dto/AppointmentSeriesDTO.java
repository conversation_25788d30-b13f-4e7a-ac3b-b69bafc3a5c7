package com.guitarcenter.scheduler.dao.criterion.dto;

import java.util.Date;
import java.util.Set;

import org.joda.time.DateTime;

import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.model.Customer;

public class AppointmentSeriesDTO {

	private Long			appointmentSeriesId;
	private Long			siteId;
	private Long			profileId;
	private Long			activityId;
	private String			bandName;
	private String			note;
	private Set<Customer>	customers;
	private Long			numbers;
	private Long			instructorId;
	private Long			roomId;
	private Long			duration;
	private Date			latestAppintmentStartTime;
	private int				latestAppintmentDayOfWeek;
	private Date			nextAppointmentStartTime;
	private Date			nextAppointmentEndTime;
	private Date			startTime;
	private int				dayOfWeek;
	private Long			parentRoomId;



	public Long getAppointmentSeriesId() {
		return appointmentSeriesId;
	}



	public void setAppointmentSeriesId(Long pAppointmentSeriesId) {
		appointmentSeriesId = pAppointmentSeriesId;
	}



	public Long getSiteId() {
		return siteId;
	}



	public void setSiteId(Long pSiteId) {
		siteId = pSiteId;
	}



	public Long getProfileId() {
		return profileId;
	}



	public void setProfileId(Long pProfileId) {
		profileId = pProfileId;
	}



	public Long getActivityId() {
		return activityId;
	}



	public void setActivityId(Long pActivityId) {
		activityId = pActivityId;
	}



	public String getBandName() {
		return bandName;
	}



	public void setBandName(String pBandName) {
		bandName = pBandName;
	}



	public String getNote() {
		return note;
	}



	public void setNote(String pNote) {
		note = pNote;
	}



	public Set<Customer> getCustomers() {
		return customers;
	}



	public void setCustomers(Set<Customer> pCustomers) {
		customers = pCustomers;
	}



	/**
	 * Get lacking appointment numbers
	 * 
	 * @return
	 */
	public Long getNumbers() {
		return numbers;
	}



	public void setNumbers(Long pNumbers) {
		numbers = pNumbers;
	}



	public Long getInstructorId() {
		return instructorId;
	}



	public void setInstructorId(Long pInstructorId) {
		instructorId = pInstructorId;
	}



	public Long getRoomId() {
		return roomId;
	}



	public void setRoomId(Long pRoomId) {
		roomId = pRoomId;
	}



	/**
	 * Get duration, Minute of Unit
	 * 
	 * @return
	 */
	public Long getDuration() {
		return duration;
	}



	public void setDuration(Long pDuration) {
		duration = pDuration;
	}



	public Date getLatestAppintmentStartTime() {
		return new DateTime(latestAppintmentStartTime).toDate();
	}



	public void setLatestAppintmentStartTime(Date pLatestAppintmentStartTime) {
        if (pLatestAppintmentStartTime != null){
		    latestAppintmentStartTime = new DateTime(pLatestAppintmentStartTime).toDate();
        }
		if (latestAppintmentStartTime != null) {
			DateTime dateTime = new DateTime(latestAppintmentStartTime);
			latestAppintmentDayOfWeek = dateTime.getDayOfWeek();
		}
	}



	public int getLatestAppintmentDayOfWeek() {
		return latestAppintmentDayOfWeek;
	}



	public void setLatestAppintmentDayOfWeek(int pLatestAppintmentDayOfWeek) {
		latestAppintmentDayOfWeek = pLatestAppintmentDayOfWeek;
	}



	public Date getNextAppointmentStartTime() {
		return new DateTime(nextAppointmentStartTime).toDate();
	}



	public void setNextAppointmentStartTime(Date pNextAppointmentStartTime) {
        if (pNextAppointmentStartTime != null){
		    nextAppointmentStartTime = new DateTime(pNextAppointmentStartTime).toDate();
        }
	}



	public Date getNextAppointmentEndTime() {
		return new DateTime(nextAppointmentEndTime).toDate();
	}



	public void setNextAppointmentEndTime(Date pNextAppointmentEndTime) {
		nextAppointmentEndTime = pNextAppointmentEndTime;
	}



	public Date getStartTime() {
		return new DateTime(startTime).toDate();
	}



	public void setStartTime(Date pStartTime) {
		startTime = pStartTime;
		if (startTime != null) {
			DateTime dateTime = new DateTime(startTime);
			dayOfWeek = dateTime.getDayOfWeek();
		}
	}



	public int getDayOfWeek() {
		return dayOfWeek;
	}



	public void setDayOfWeek(int pDayOfWeek) {
		dayOfWeek = pDayOfWeek;
	}



	public Long getParentRoomId() {
		return parentRoomId;
	}



	public void setParentRoomId(Long pParentRoomId) {
		parentRoomId = pParentRoomId;
	}



	public void buildNextAppointmentDateTime() {
		if (duration != null && latestAppintmentStartTime != null && startTime != null) {
			DateTime startDateTime = new DateTime(startTime);
			DateTime latestAppintmentStartTimeDateTime = new DateTime(latestAppintmentStartTime).plusDays(
					dayOfWeek - latestAppintmentDayOfWeek + 7).withTime(startDateTime.getHourOfDay(),
					startDateTime.getMinuteOfHour(), startDateTime.getSecondOfMinute(),
					startDateTime.getMillisOfSecond());
			nextAppointmentStartTime = latestAppintmentStartTimeDateTime.toDate();
			//For GSSP-176
			nextAppointmentEndTime = SystemUtil.getEndTimeIfOnMidnight(latestAppintmentStartTimeDateTime.plusMinutes(duration.intValue()).toDate());
		}
	}

}
