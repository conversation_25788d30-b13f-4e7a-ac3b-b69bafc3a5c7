package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//import org.hibernate.SQLQuery;
import jakarta.persistence.EntityManager;
import org.hibernate.ScrollableResults;
import org.hibernate.Session;
import org.hibernate.query.Query;
import org.hibernate.type.StandardBasicTypes;
import org.joda.time.DateTime;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.dao.criterion.dto.CheckingResultAvailabilityDTO;
import com.guitarcenter.scheduler.dao.util.EntryKey;
import com.guitarcenter.scheduler.dto.AvailabilityValueDTO2;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.enums.Canceled;
import com.guitarcenter.scheduler.model.enums.Enabled;

public abstract class AvailabilityCriterion<E> extends AbstractCriterion<Availability, E> implements
        Criterion<Availability, E> {

    private static final Criterion<Availability, Availability> DEFAULT_INSTANCE = new AvailabilityCriterion<Availability>() {
    };
   
    private AvailabilityCriterion() {
        super(Availability.class);
    }


    @Override
    protected String getFetchScript(int pFetchMode) {
        StringBuilder sb = new StringBuilder();
        sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
        sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
        return sb.toString();
    }


    @Override
    public List<E> search(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }


    @Override
    public E get(EntityManager entityManager, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }


    public static Criterion<Availability, Availability> getInstance() {
        return DEFAULT_INSTANCE;
    }


    public static Criterion<Availability, CheckingResultAvailabilityDTO> checkProfileAndInstructorAndRoomAvailability(
            final Long pProfileId, final Long pInstructorId, final int pDayOfWeek,
            final Date pStartTime, final Date pEndTime, final Long pRoomId, final Long pParentRoomId, final String appointmentSeriesId) {

        AvailabilityCriterion<CheckingResultAvailabilityDTO> instance = new AvailabilityCriterion<CheckingResultAvailabilityDTO>() {

            @Override
            public CheckingResultAvailabilityDTO get(EntityManager entityManager, int pFetchMode) {
                List<Long> availabilityIds = new ArrayList<Long>();
                CheckingResultAvailabilityDTO result = new CheckingResultAvailabilityDTO();
                {
                    StringBuilder sb = new StringBuilder(
                            "select p.availability_id from location_profile p where p.profile_id = :profileId");
                    Query query =  entityManager.createNativeQuery(sb.toString())  .unwrap(org.hibernate.query.NativeQuery.class).addScalar("availability_id", StandardBasicTypes.LONG);
                    query.setParameter("profileId", pProfileId);
                    query.setMaxResults(1);
                    Long availabilityId = (Long) query.uniqueResult();
                    if (availabilityId == null) {
                    	//Changes for JIRA GSSP-152
                    	// GSSP-268 lack Appointment job issue start
                    	result.setAppointmentSeriesId(appointmentSeriesId);
                    	result.setIssueFlag(AppConstants.FLAG_1); //lack Appointment job issue end
                    	result.setResult(false);
                        result.setErorrMessage("This Profile_id( " + pProfileId + " ) is not corretly.");
                        return result;
                    } else {
                        availabilityIds.add(availabilityId);
                    }
                }
                //Changes for GSSP-224 ::  status conditions  and join with location added.
                if (pInstructorId != null) {
                    StringBuilder sb = new StringBuilder(
                            "select i.availability_id from instructor i join location l on l.location_id = i.location_id  "
                            + " where i.instructor_id = :instructorId and i.enabled = :enabled  and i.status = :status and l.profile_id =  :profileId" );
                    Query query = entityManager.createNativeQuery(sb.toString())
                            .unwrap(org.hibernate.query.NativeQuery.class)
                            .addScalar("availability_id", StandardBasicTypes.LONG);
                    query.setParameter("instructorId", pInstructorId);
                    query.setParameter("enabled", Enabled.Y.name());
                    //Changes for GSSP-224 ::  status conditions added and location profile added.
                    query.setParameter("profileId", pProfileId);
                    query.setParameter("status",  AppConstants.INSTRUCTOR_ENTERPRISE_STATUS);

                    query.setMaxResults(1);
                    Long availabilityId = (Long) query.uniqueResult();
                    if (availabilityId == null) {
                    	//Changes for JIRA GSSP-152
                    	// GSSP-268 lack Appointment job issue start
                    	result.setAppointmentSeriesId(appointmentSeriesId);
                    	result.setIssueFlag(AppConstants.FLAG_2); //lack Appointment job issue end
                    	result.setResult(false);
                        result.setErorrMessage("This Instructor_id( " + pInstructorId + " ) is not corretly.");
                        return result;
                    } else {
                        availabilityIds.add(availabilityId);
                    }
                }
                {
                    StringBuilder sb = new StringBuilder(
                            "select r.enabled from room r where r.room_id = :roomId");
                    Query query = entityManager.createNativeQuery(sb.toString())
                            .unwrap(org.hibernate.query.NativeQuery.class)
                            .addScalar("enabled", StandardBasicTypes.STRING);
                    query.setParameter("roomId", pRoomId);

                    query.setMaxResults(1);
                    String enabled = (String) query.uniqueResult();
                    if (!Enabled.Y.toString().equals(enabled)) {
                    	//Changes for JIRA GSSP-152
                    	// GSSP-268 lack Appointment job issue start
                    	result.setAppointmentSeriesId(appointmentSeriesId);
                    	result.setIssueFlag(AppConstants.FLAG_3); //lack Appointment job issue end
                    	result.setResult(false);
                        result.setErorrMessage("This room ( " + pRoomId + " ) is disabled.");
                        return result;
                    }
                }
                {
                    StringBuilder sb = new StringBuilder("select count(*) ");
                    sb.append("  from (select ");
                    switch (pDayOfWeek) {
                        case 1:
                            sb.append("               max(to_char(a.monday_start_time, 'HH24:MI:SS')) as st,");
                            sb.append("               min(to_char(a.monday_end_time, 'HH24:MI:SS')) as et");
                            break;
                        case 2:
                            sb.append("               max(to_char(a.tuesday_start_time, 'HH24:MI:SS')) as st,");
                            sb.append("               min(to_char(a.tuesday_end_time, 'HH24:MI:SS')) as et");
                            break;
                        case 3:
                            sb.append("               max(to_char(a.wednesday_start_time, 'HH24:MI:SS')) as st,");
                            sb.append("               min(to_char(a.wednesday_end_time, 'HH24:MI:SS')) as et");
                            break;
                        case 4:
                            sb.append("               max(to_char(a.thursday_start_time, 'HH24:MI:SS')) as st,");
                            sb.append("               min(to_char(a.thursday_end_time, 'HH24:MI:SS')) as et");
                            break;
                        case 5:
                            sb.append("               max(to_char(a.friday_start_time, 'HH24:MI:SS')) as st,");
                            sb.append("               min(to_char(a.friday_end_time, 'HH24:MI:SS')) as et");
                            break;
                        case 6:
                            sb.append("               max(to_char(a.saturday_start_time, 'HH24:MI:SS')) as st,");
                            sb.append("               min(to_char(a.saturday_end_time, 'HH24:MI:SS')) as et");
                            break;
                        case 7:
                            sb.append("               max(to_char(a.sunday_start_time, 'HH24:MI:SS')) as st,");
                            sb.append("               min(to_char(a.sunday_end_time, 'HH24:MI:SS')) as et");
                            break;
                        default:
                            result.setErorrMessage("The dayOfWeek ( " + pDayOfWeek + " )  is invalid.");
                            return result;
                    }
                    sb.append("          from availability a");
                    sb.append("         where a.availability_id in (:availabilityIds)) aa");
                    sb.append(" where (aa.st <= to_char(:startTime,'HH24:MI:SS') or aa.st is null)");
                    sb.append("   and (aa.et >= to_char(:endTime,'HH24:MI:SS') or aa.et is null)");
                    Query query = entityManager.createNativeQuery(sb.toString())
                            .unwrap(org.hibernate.query.NativeQuery.class)
                            .addScalar("count(*)", StandardBasicTypes.LONG);
                    query.setParameterList("availabilityIds", availabilityIds);
                    query.setParameter("startTime", pStartTime);
                    query.setParameter("endTime", pEndTime);

                    Long count = (Long) query.uniqueResult();
                    if (count <= 0) {
                    	//Changes for JIRA GSSP-152
                    	// GSSP-268 lack Appointment job issue start
                    	result.setAppointmentSeriesId(appointmentSeriesId);
                    	result.setIssueFlag(AppConstants.FLAG_4); //lack Appointment job issue end
                    	result.setResult(false);
                    	result.setErorrMessage("The start_time( " + pStartTime + " ) or the end_time( " + pEndTime
                                + " )  is invalid.");
                        return result;
                    }
                }
                {
                    StringBuilder sb = new StringBuilder("select count(*) ");
                    sb.append("  from appointment t");
                    sb.append(" where t.profile_id = :profileId");
                    sb.append("   and (t.room_id = :roomId");
                    if (pInstructorId != null) {
                        sb.append("    or t.instructor_id = :instructorId");
                    }
                    if (pParentRoomId != null) {
                        sb.append("    or t.room_id = :parentRoomId");
                    }
                    sb.append("                                                             )");
                    sb.append("   and (t.canceled is null or t.canceled = :canceled or t.canceled = :canceled1)");
                    sb.append("   and ((to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') >=");
                    sb.append("       to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and");
                    sb.append("       to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <=");
                    sb.append("       to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS')) or");
                    sb.append("       (to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >=");
                    sb.append("       to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and");
                    sb.append("       to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') <=");
                    sb.append("       to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS')) or");
                    sb.append("       (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <=");
                    sb.append("       to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and");
                    sb.append("       to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >=");
                    sb.append("       to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS')))");
                    Query query = entityManager.createNativeQuery(sb.toString())
                            .unwrap(org.hibernate.query.NativeQuery.class)
                            .addScalar("count(*)", StandardBasicTypes.LONG);
                    query.setParameter("profileId", pProfileId);
                    query.setParameter("roomId", pRoomId);
                    if (pParentRoomId != null) {
                        query.setParameter("parentRoomId", pParentRoomId);
                    }
                    if (pInstructorId != null) {
                        query.setParameter("instructorId", pInstructorId);
                    }
                    query.setParameter("canceled", Canceled.N.name());
                    query.setParameter("canceled1", Canceled.H.name());
                    query.setParameter("startTime", new DateTime(pStartTime).plusMillis(1000).toDate());//GSSP-276 lack Appointment job issue
                    query.setParameter("endTime", new DateTime(pEndTime).minusMillis(1000).toDate()); //GSSP-276 lack Appointment job issue , changed the "endTime" from pStartTime to pEndTime
                    query.setMaxResults(1);
                    Long count = (Long) query.uniqueResult();
                    if (count > 0) // GSSP-268 lack Appointment job issue, changed the condition from count<=0 to count>0 
                    {
                    	// GSSP-268 lack Appointment job issue start
                    	result.setAppointmentSeriesId(appointmentSeriesId);
                    	result.setIssueFlag(AppConstants.FLAG_5); 
                    	result.setResult(false);//lack Appointment job issue end, setResult was missing too previously
                        result.setErorrMessage("The room( " + pRoomId + " ) or The instructor ( " + pInstructorId
                                + " ) has conflict at " + pStartTime + " - " + pEndTime + ".");
                     }
                    return result;
                }
            }

        };
        return instance;
    }

    public static Criterion<Availability, Availability> getByProfileIdAndInstructorId(final long pProfileId, final long pInstructorId) {
        return new AvailabilityCriterion<Availability>() {

            @Override
            @SuppressWarnings("unchecked")
            public Availability get(EntityManager entityManager, int pFetchMode) {
                String sb = "select" +
                        "       to_char(ay.monday_start_time, 'HH24:MI:DD') as mon_s," +
                        "       to_char(ay.monday_end_time, 'HH24:MI:DD') as mon_e," +
                        "       to_char(ay.tuesday_start_time, 'HH24:MI:DD') as tue_s," +
                        "       to_char(ay.tuesday_end_time, 'HH24:MI:DD') as tue_e," +
                        "       to_char(ay.wednesday_start_time, 'HH24:MI:DD') as wed_s," +
                        "       to_char(ay.wednesday_end_time, 'HH24:MI:DD') as wed_e," +
                        "       to_char(ay.thursday_start_time, 'HH24:MI:DD') as thu_s," +
                        "       to_char(ay.thursday_end_time, 'HH24:MI:DD') as thu_e," +
                        "       to_char(ay.friday_start_time, 'HH24:MI:DD') as fri_s," +
                        "       to_char(ay.friday_end_time, 'HH24:MI:DD') as fri_e," +
                        "       to_char(ay.saturday_start_time, 'HH24:MI:DD') as sat_s," +
                        "       to_char(ay.saturday_end_time, 'HH24:MI:DD') as sat_e," +
                        "       to_char(ay.sunday_start_time, 'HH24:MI:DD') as sun_s," +
                        "       to_char(ay.sunday_end_time, 'HH24:MI:DD') as sun_e," +
                        "       l_p.profile_id as profile_id," +
                        "       i.instructor_id as instructor_id" +
                        "  from availability ay" +
                        "  left join location_profile l_p" +
                        "    on ay.availability_id = l_p.availability_id" +
                        "  left join instructor i" +
                        "    on ay.availability_id = i.availability_id" +
                        " where l_p.profile_id = :profileId" +
                        "    or i.instructor_id = :instructorId";
                Query query = entityManager.createNativeQuery(sb)
                        .unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("mon_s", StandardBasicTypes.TIME)
                .addScalar("mon_e", StandardBasicTypes.TIME)
                .addScalar("tue_s", StandardBasicTypes.TIME)
                .addScalar("tue_e", StandardBasicTypes.TIME)
                .addScalar("wed_s", StandardBasicTypes.TIME)
                .addScalar("wed_e", StandardBasicTypes.TIME)
                .addScalar("thu_s", StandardBasicTypes.TIME)
                .addScalar("thu_e", StandardBasicTypes.TIME)
                .addScalar("fri_s", StandardBasicTypes.TIME)
                .addScalar("fri_e", StandardBasicTypes.TIME)
                .addScalar("sat_s", StandardBasicTypes.TIME)
                .addScalar("sat_e", StandardBasicTypes.TIME)
                .addScalar("sun_s", StandardBasicTypes.TIME)
                .addScalar("sun_e", StandardBasicTypes.TIME)
                .addScalar("profile_id", StandardBasicTypes.LONG)
                .addScalar("instructor_id", StandardBasicTypes.LONG);
                query.setParameter("profileId", pProfileId);
                query.setParameter("instructorId", pInstructorId);
                List<Object[]> list = query.list();
                Map<EntryKey, Availability> cache = new HashMap<EntryKey, Availability>();
                for (Object[] obj : list) {
                    Availability ay = new Availability();
                    ay.setMondayStartTime((Date) obj[0]);
                    ay.setMondayEndTime((Date) obj[1]);
                    ay.setTuesdayStartTime((Date) obj[2]);
                    ay.setTuesdayEndTime((Date) obj[3]);
                    ay.setWednesdayStartTime((Date) obj[4]);
                    ay.setWednesdayEndTime((Date) obj[5]);
                    ay.setThursdayStartTime((Date) obj[6]);
                    ay.setThursdayEndTime((Date) obj[7]);
                    ay.setFridayStartTime((Date) obj[8]);
                    ay.setFridayEndTime((Date) obj[9]);
                    ay.setSaturdayStartTime((Date) obj[10]);
                    ay.setSaturdayEndTime((Date) obj[11]);
                    ay.setSundayStartTime((Date) obj[12]);
                    ay.setSundayEndTime((Date) obj[13]);
                    cache.put(new EntryKey((Long) obj[15], (Long) obj[14]), ay);
                }
                if (cache.size() > 1) {
                    Availability ay = new Availability();
                    Availability profile_ay = cache.get(new EntryKey(null, pProfileId));
                    Availability instructor_ay = cache.get(new EntryKey(pInstructorId, null));
                    ay.setMondayStartTime(getMaxTime(profile_ay.getMondayStartTime(), instructor_ay.getMondayStartTime()));
                    ay.setMondayEndTime(getMinTime(profile_ay.getMondayEndTime(), instructor_ay.getMondayEndTime()));
                    ay.setTuesdayStartTime(getMaxTime(profile_ay.getTuesdayStartTime(), instructor_ay.getTuesdayStartTime()));
                    ay.setTuesdayEndTime(getMinTime(profile_ay.getTuesdayEndTime(), instructor_ay.getTuesdayEndTime()));
                    ay.setWednesdayStartTime(getMaxTime(profile_ay.getWednesdayStartTime(), instructor_ay.getWednesdayStartTime()));
                    ay.setWednesdayEndTime(getMinTime(profile_ay.getWednesdayEndTime(), instructor_ay.getWednesdayEndTime()));
                    ay.setThursdayStartTime(getMaxTime(profile_ay.getThursdayStartTime(), instructor_ay.getThursdayStartTime()));
                    ay.setThursdayEndTime(getMinTime(profile_ay.getThursdayEndTime(), instructor_ay.getThursdayEndTime()));
                    ay.setFridayStartTime(getMaxTime(profile_ay.getFridayStartTime(), instructor_ay.getFridayStartTime()));
                    ay.setFridayEndTime(getMinTime(profile_ay.getFridayEndTime(), instructor_ay.getFridayEndTime()));
                    ay.setSaturdayStartTime(getMaxTime(profile_ay.getSaturdayStartTime(), instructor_ay.getSaturdayStartTime()));
                    ay.setSaturdayEndTime(getMinTime(profile_ay.getSaturdayEndTime(), instructor_ay.getSaturdayEndTime()));
                    ay.setSundayStartTime(getMaxTime(profile_ay.getSundayStartTime(), instructor_ay.getSundayStartTime()));
                    ay.setSundayEndTime(getMinTime(profile_ay.getSundayEndTime(), instructor_ay.getSundayEndTime()));
                    return ay;
                } else {
                    Availability ay = cache.get(new EntryKey(null, pProfileId));
                    if (ay != null) {
                        return ay;
                    }
                }
                return new Availability();
            }

            private Date getMaxTime(Date profileDateTime, Date instructorDateTime) {
                if (profileDateTime == null || instructorDateTime == null) {
                    return null;
                }
                return new DateTime(profileDateTime).getMillisOfDay() >= new DateTime(instructorDateTime).getMillisOfDay() ? profileDateTime : instructorDateTime;
            }

            private Date getMinTime(Date profileDateTime, Date instructorDateTime) {
                if (profileDateTime == null || instructorDateTime == null) {
                    return null;
                }
                return new DateTime(profileDateTime).getMillisOfDay() <= new DateTime(instructorDateTime).getMillisOfDay() ? profileDateTime : instructorDateTime;
            }

        };
    }
    
    // Phase2_LES-27 Changes
    public static Criterion<Availability, Availability> getByInstructorId(final long pInstructorId) {
        return new AvailabilityCriterion<Availability>() {

            @Override
            @SuppressWarnings("unchecked")
            public Availability get(EntityManager entityManager, int pFetchMode) {
                String sb = "select" +
                        "       to_char(ay.monday_start_time, 'HH24:MI:SS') as mon_s," +
                        "       to_char(ay.monday_end_time, 'HH24:MI:SS') as mon_e," +
                        "       to_char(ay.tuesday_start_time, 'HH24:MI:SS') as tue_s," +
                        "       to_char(ay.tuesday_end_time, 'HH24:MI:SS') as tue_e," +
                        "       to_char(ay.wednesday_start_time, 'HH24:MI:SS') as wed_s," +
                        "       to_char(ay.wednesday_end_time, 'HH24:MI:SS') as wed_e," +
                        "       to_char(ay.thursday_start_time, 'HH24:MI:SS') as thu_s," +
                        "       to_char(ay.thursday_end_time, 'HH24:MI:SS') as thu_e," +
                        "       to_char(ay.friday_start_time, 'HH24:MI:SS') as fri_s," +
                        "       to_char(ay.friday_end_time, 'HH24:MI:SS') as fri_e," +
                        "       to_char(ay.saturday_start_time, 'HH24:MI:SS') as sat_s," +
                        "       to_char(ay.saturday_end_time, 'HH24:MI:SS') as sat_e," +
                        "       to_char(ay.sunday_start_time, 'HH24:MI:SS') as sun_s," +
                        "       to_char(ay.sunday_end_time, 'HH24:MI:SS') as sun_e," +
                        "       l_p.profile_id as profile_id," +
                        "       i.instructor_id as instructor_id" +
                        "  from availability ay" +
                        "  left join location_profile l_p" +
                        "    on ay.availability_id = l_p.availability_id" +
                        "  left join instructor i" +
                        "    on ay.availability_id = i.availability_id" +
                        " where i.instructor_id = :instructorId";
                Query query = entityManager.createNativeQuery(sb)
                        .unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("mon_s", StandardBasicTypes.TIME)
                .addScalar("mon_e", StandardBasicTypes.TIME)
                .addScalar("tue_s", StandardBasicTypes.TIME)
                .addScalar("tue_e", StandardBasicTypes.TIME)
                .addScalar("wed_s", StandardBasicTypes.TIME)
                .addScalar("wed_e", StandardBasicTypes.TIME)
                .addScalar("thu_s", StandardBasicTypes.TIME)
                .addScalar("thu_e", StandardBasicTypes.TIME)
                .addScalar("fri_s", StandardBasicTypes.TIME)
                .addScalar("fri_e", StandardBasicTypes.TIME)
                .addScalar("sat_s", StandardBasicTypes.TIME)
                .addScalar("sat_e", StandardBasicTypes.TIME)
                .addScalar("sun_s", StandardBasicTypes.TIME)
                .addScalar("sun_e", StandardBasicTypes.TIME)
                .addScalar("profile_id", StandardBasicTypes.LONG)
                .addScalar("instructor_id", StandardBasicTypes.LONG);
          
                query.setParameter("instructorId", pInstructorId);
                List<Object[]> list = query.list();
                Map<EntryKey, Availability> cache = new HashMap<EntryKey, Availability>();
                Availability ay = null;
                for (Object[] obj : list) {
                	ay = new Availability();
                    ay.setMondayStartTime((Date) obj[0]);
                    ay.setMondayEndTime((Date) obj[1]);
                    ay.setTuesdayStartTime((Date) obj[2]);
                    ay.setTuesdayEndTime((Date) obj[3]);
                    ay.setWednesdayStartTime((Date) obj[4]);
                    ay.setWednesdayEndTime((Date) obj[5]);
                    ay.setThursdayStartTime((Date) obj[6]);
                    ay.setThursdayEndTime((Date) obj[7]);
                    ay.setFridayStartTime((Date) obj[8]);
                    ay.setFridayEndTime((Date) obj[9]);
                    ay.setSaturdayStartTime((Date) obj[10]);
                    ay.setSaturdayEndTime((Date) obj[11]);
                    ay.setSundayStartTime((Date) obj[12]);
                    ay.setSundayEndTime((Date) obj[13]);
                  
                }
            
                return ay;
            } 
          };
    }
       
    
    // Phase2_LES-27 Changes
    public static Criterion<Availability, Availability> getByInstructorIdInsAVL(final long pInstructorId) {
        return new AvailabilityCriterion<Availability>() {

            @Override
            @SuppressWarnings("unchecked")
            public Availability get(EntityManager entityManager, int pFetchMode) {
                String sb = "select" +
                        "       to_char(ay.monday_start_time, 'HH24:MI:SS') as mon_s," +
                        "       to_char(ay.monday_end_time, 'HH24:MI:SS') as mon_e," +
                        "       to_char(ay.tuesday_start_time, 'HH24:MI:SS') as tue_s," +
                        "       to_char(ay.tuesday_end_time, 'HH24:MI:SS') as tue_e," +
                        "       to_char(ay.wednesday_start_time, 'HH24:MI:SS') as wed_s," +
                        "       to_char(ay.wednesday_end_time, 'HH24:MI:SS') as wed_e," +
                        "       to_char(ay.thursday_start_time, 'HH24:MI:SS') as thu_s," +
                        "       to_char(ay.thursday_end_time, 'HH24:MI:SS') as thu_e," +
                        "       to_char(ay.friday_start_time, 'HH24:MI:SS') as fri_s," +
                        "       to_char(ay.friday_end_time, 'HH24:MI:SS') as fri_e," +
                        "       to_char(ay.saturday_start_time, 'HH24:MI:SS') as sat_s," +
                        "       to_char(ay.saturday_end_time, 'HH24:MI:SS') as sat_e," +
                        "       to_char(ay.sunday_start_time, 'HH24:MI:SS') as sun_s," +
                        "       to_char(ay.sunday_end_time, 'HH24:MI:SS') as sun_e," +
                        "       l_p.profile_id as profile_id," +
                        "       i.instructor_id as instructor_id" +
                        "  from availability ay" +
                        "  left join location_profile l_p" +
                        "    on ay.availability_id = l_p.availability_id" +
                        "  left join instructor i" +
                        "    on ay.availability_id = i.availability_id" +
                        " where i.instructor_id = :instructorId";
                Query query = entityManager.createNativeQuery(sb)
                        .unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("mon_s", StandardBasicTypes.TIME)
                .addScalar("mon_e", StandardBasicTypes.TIME)
                .addScalar("tue_s", StandardBasicTypes.TIME)
                .addScalar("tue_e", StandardBasicTypes.TIME)
                .addScalar("wed_s", StandardBasicTypes.TIME)
                .addScalar("wed_e", StandardBasicTypes.TIME)
                .addScalar("thu_s", StandardBasicTypes.TIME)
                .addScalar("thu_e", StandardBasicTypes.TIME)
                .addScalar("fri_s", StandardBasicTypes.TIME)
                .addScalar("fri_e", StandardBasicTypes.TIME)
                .addScalar("sat_s", StandardBasicTypes.TIME)
                .addScalar("sat_e", StandardBasicTypes.TIME)
                .addScalar("sun_s", StandardBasicTypes.TIME)
                .addScalar("sun_e", StandardBasicTypes.TIME)
                .addScalar("profile_id", StandardBasicTypes.LONG)
                .addScalar("instructor_id", StandardBasicTypes.LONG);
          
                query.setParameter("instructorId", pInstructorId);
                List<Object[]> list = query.list();
                Map<EntryKey, Availability> cache = new HashMap<EntryKey, Availability>();
                Availability ay = null;
                for (Object[] obj : list) {
                	ay = new Availability();
                    ay.setMondayStartTime((Date) obj[0]);
                    ay.setMondayEndTime((Date) obj[1]);
                    ay.setTuesdayStartTime((Date) obj[2]);
                    ay.setTuesdayEndTime((Date) obj[3]);
                    ay.setWednesdayStartTime((Date) obj[4]);
                    ay.setWednesdayEndTime((Date) obj[5]);
                    ay.setThursdayStartTime((Date) obj[6]);
                    ay.setThursdayEndTime((Date) obj[7]);
                    ay.setFridayStartTime((Date) obj[8]);
                    ay.setFridayEndTime((Date) obj[9]);
                    ay.setSaturdayStartTime((Date) obj[10]);
                    ay.setSaturdayEndTime((Date) obj[11]);
                    ay.setSundayStartTime((Date) obj[12]);
                    ay.setSundayEndTime((Date) obj[13]);
                  
                }
            
                return ay;
            } 
          };
    }   
   // 292 –GSSP Multi location instructor comparison
    public static Criterion<Availability, Availability> getByInstructorId(final long pavailabilityId, final long pInstructorId) {
        return new AvailabilityCriterion<Availability>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Availability> search(EntityManager entityManager, int pFetchMode) {
                String sb = "select" +
                        "       to_char(ay.monday_start_time, 'HH24:MI:SS') as mon_s," +
                        "       to_char(ay.monday_end_time, 'HH24:MI:SS') as mon_e," +
                        "       to_char(ay.tuesday_start_time, 'HH24:MI:SS') as tue_s," +
                        "       to_char(ay.tuesday_end_time, 'HH24:MI:SS') as tue_e," +
                        "       to_char(ay.wednesday_start_time, 'HH24:MI:SS') as wed_s," +
                        "       to_char(ay.wednesday_end_time, 'HH24:MI:SS') as wed_e," +
                        "       to_char(ay.thursday_start_time, 'HH24:MI:SS') as thu_s," +
                        "       to_char(ay.thursday_end_time, 'HH24:MI:SS') as thu_e," +
                        "       to_char(ay.friday_start_time, 'HH24:MI:SS') as fri_s," +
                        "       to_char(ay.friday_end_time, 'HH24:MI:SS') as fri_e," +
                        "       to_char(ay.saturday_start_time, 'HH24:MI:SS') as sat_s," +
                        "       to_char(ay.saturday_end_time, 'HH24:MI:SS') as sat_e," +
                        "       to_char(ay.sunday_start_time, 'HH24:MI:SS') as sun_s," +
                        "       to_char(ay.sunday_end_time, 'HH24:MI:SS') as sun_e," +
                        "       l_p.profile_id as profile_id," +
                        "       i.instructor_id as instructor_id," +
                        "       i.availability_id as availability_id" +
                        "  from availability ay" +
                        "  left join location_profile l_p" +
                        "    on ay.availability_id = l_p.availability_id" +
                        "  left join instructor i" +
                        "    on ay.availability_id = i.availability_id" +
                        " where i.external_id = :instructorId"+
                        " and i.enabled ='Y' and i.Status ='A'" ;
                Query query = entityManager.createNativeQuery(sb)
                        .unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("mon_s", StandardBasicTypes.TIME)
                .addScalar("mon_e", StandardBasicTypes.TIME)
                .addScalar("tue_s", StandardBasicTypes.TIME)
                .addScalar("tue_e", StandardBasicTypes.TIME)
                .addScalar("wed_s", StandardBasicTypes.TIME)
                .addScalar("wed_e", StandardBasicTypes.TIME)
                .addScalar("thu_s", StandardBasicTypes.TIME)
                .addScalar("thu_e", StandardBasicTypes.TIME)
                .addScalar("fri_s", StandardBasicTypes.TIME)
                .addScalar("fri_e", StandardBasicTypes.TIME)
                .addScalar("sat_s", StandardBasicTypes.TIME)
                .addScalar("sat_e", StandardBasicTypes.TIME)
                .addScalar("sun_s", StandardBasicTypes.TIME)
                .addScalar("sun_e", StandardBasicTypes.TIME)
                .addScalar("profile_id", StandardBasicTypes.LONG)
                .addScalar("instructor_id", StandardBasicTypes.LONG)
                .addScalar("availability_id", StandardBasicTypes.LONG);
          
                query.setParameter("instructorId", pInstructorId+"");
               //  List<Object[]> list = query.list();
              List<Availability> list = query.list();
                Map<EntryKey, Availability> cache = new HashMap<EntryKey, Availability>();
                List<Availability> ay = new  ArrayList<Availability>();
                Availability     Availability = null;
              ScrollableResults scroll = query.scroll();
               
                while (scroll.next()) {
                    
                    Object[] obj = (Object[]) scroll.get();
                	Date MondayStartTime=(Date)obj[0];
                	Date MondayEndTime=(Date)obj[1];
                	Date TuesdayStartTime =  (Date)obj[2];
                	Date TuesdayEndTime = (Date) obj[3];
                	Date WednesdayStartTime= (Date) obj[4];
                	Date WednesdayEndTime=(Date) obj[5];
                	Date ThursdayStartTime=(Date) obj[6];
                	Date ThursdayEndTime=(Date) obj[7];
                	Date FridayStartTime=(Date) obj[8];
                	Date FridayEndTime=(Date) obj[9];
                	Date SaturdayStartTime=(Date) obj[10];
                	Date SaturdayEndTime=(Date) obj[11];
                	Date SundayStartTime=(Date) obj[12];
                	Date SundayEndTime=(Date) obj[13];
                	Long availabilityId=(Long) obj[16];
                	if(pavailabilityId != availabilityId) {
                	Availability = new Availability(availabilityId,MondayStartTime,MondayEndTime,TuesdayStartTime,TuesdayEndTime,WednesdayStartTime,WednesdayEndTime,
                     		ThursdayStartTime,ThursdayEndTime,FridayStartTime,FridayEndTime,SaturdayStartTime,SaturdayEndTime,SundayStartTime,SundayEndTime);
                 	ay.add(Availability);
                	}
                  
                }
                scroll.close();
            
                return ay;
            } 
          };
    }
    
     
  //-- Phase  Changes GSSP-295 --Method for get Map of Instructor and Availabilty -------
    Map<String,  Availability> mp = null;
    public static Criterion<Availability,Map<String,  Availability>> getByInstructorId(final List<String> pInstructorId) {
        return new AvailabilityCriterion<Map<String,  Availability>>() {

            @Override
            @SuppressWarnings("unchecked")
            public Map<String,  Availability> get(EntityManager entityManager, int pFetchMode) {
            	
            	mp =   new HashMap<String,Availability>();
                String sb = "select" +
                        "       to_char(ay.monday_start_time, 'HH24:MI:SS') as mon_s," +
                        "       to_char(ay.monday_end_time, 'HH24:MI:SS') as mon_e," +
                        "       to_char(ay.tuesday_start_time, 'HH24:MI:SS') as tue_s," +
                        "       to_char(ay.tuesday_end_time, 'HH24:MI:SS') as tue_e," +
                        "       to_char(ay.wednesday_start_time, 'HH24:MI:SS') as wed_s," +
                        "       to_char(ay.wednesday_end_time, 'HH24:MI:SS') as wed_e," +
                        "       to_char(ay.thursday_start_time, 'HH24:MI:SS') as thu_s," +
                        "       to_char(ay.thursday_end_time, 'HH24:MI:SS') as thu_e," +
                        "       to_char(ay.friday_start_time, 'HH24:MI:SS') as fri_s," +
                        "       to_char(ay.friday_end_time, 'HH24:MI:SS') as fri_e," +
                        "       to_char(ay.saturday_start_time, 'HH24:MI:SS') as sat_s," +
                        "       to_char(ay.saturday_end_time, 'HH24:MI:SS') as sat_e," +
                        "       to_char(ay.sunday_start_time, 'HH24:MI:SS') as sun_s," +
                        "       to_char(ay.sunday_end_time, 'HH24:MI:SS') as sun_e," +
                        "       l_p.profile_id as profile_id," +
                        "       i.instructor_id as instructor_id" +
                        "  from availability ay" +
                        "  left join location_profile l_p" +
                        "    on ay.availability_id = l_p.availability_id" +
                        "  left join instructor i" +
                        "    on ay.availability_id = i.availability_id" +
                        " where i.instructor_id IN :instructorId";
                Query query = entityManager.createNativeQuery(sb)
                        .unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("mon_s", StandardBasicTypes.TIME)
                .addScalar("mon_e", StandardBasicTypes.TIME)
                .addScalar("tue_s", StandardBasicTypes.TIME)
                .addScalar("tue_e", StandardBasicTypes.TIME)
                .addScalar("wed_s", StandardBasicTypes.TIME)
                .addScalar("wed_e", StandardBasicTypes.TIME)
                .addScalar("thu_s", StandardBasicTypes.TIME)
                .addScalar("thu_e", StandardBasicTypes.TIME)
                .addScalar("fri_s", StandardBasicTypes.TIME)
                .addScalar("fri_e", StandardBasicTypes.TIME)
                .addScalar("sat_s", StandardBasicTypes.TIME)
                .addScalar("sat_e", StandardBasicTypes.TIME)
                .addScalar("sun_s", StandardBasicTypes.TIME)
                .addScalar("sun_e", StandardBasicTypes.TIME)
                .addScalar("profile_id", StandardBasicTypes.LONG)
                .addScalar("instructor_id", StandardBasicTypes.LONG);

                query.setParameterList("instructorId", pInstructorId);
 
                List<Object[]> list = query.list();
                Map<EntryKey, Availability> cache = new HashMap<EntryKey, Availability>();
                Availability ay = null;
                for (Object[] obj : list) {
                	ay = new Availability();
                    ay.setMondayStartTime((Date) obj[0]);
                    ay.setMondayEndTime((Date) obj[1]);
                    ay.setTuesdayStartTime((Date) obj[2]);
                    ay.setTuesdayEndTime((Date) obj[3]);
                    ay.setWednesdayStartTime((Date) obj[4]);
                    ay.setWednesdayEndTime((Date) obj[5]);
                    ay.setThursdayStartTime((Date) obj[6]);
                    ay.setThursdayEndTime((Date) obj[7]);
                    ay.setFridayStartTime((Date) obj[8]);
                    ay.setFridayEndTime((Date) obj[9]);
                    ay.setSaturdayStartTime((Date) obj[10]);
                    ay.setSaturdayEndTime((Date) obj[11]);
                    ay.setSundayStartTime((Date) obj[12]);
                    ay.setSundayEndTime((Date) obj[13]);
 
                    mp.put(obj[15]+"",ay);
                }
            
                return mp;
            } 
          };
    }
   
    
    //----GSSP 334 ---Front page date 2 -------------------------------------
    
    public static Criterion<Availability, Availability> getByProfileIdAndOnly(final long pProfileId) {
        return new AvailabilityCriterion<Availability>() {

            @Override
            @SuppressWarnings("unchecked")
            public Availability get(EntityManager entityManager, int pFetchMode) {
            	String sb = "select" +
                        "       to_char(ay.monday_start_time, 'HH24:MI:DD') as mon_s," +
                        "       to_char(ay.monday_end_time, 'HH24:MI:DD') as mon_e," +
                        "       to_char(ay.tuesday_start_time, 'HH24:MI:DD') as tue_s," +
                        "       to_char(ay.tuesday_end_time, 'HH24:MI:DD') as tue_e," +
                        "       to_char(ay.wednesday_start_time, 'HH24:MI:DD') as wed_s," +
                        "       to_char(ay.wednesday_end_time, 'HH24:MI:DD') as wed_e," +
                        "       to_char(ay.thursday_start_time, 'HH24:MI:DD') as thu_s," +
                        "       to_char(ay.thursday_end_time, 'HH24:MI:DD') as thu_e," +
                        "       to_char(ay.friday_start_time, 'HH24:MI:DD') as fri_s," +
                        "       to_char(ay.friday_end_time, 'HH24:MI:DD') as fri_e," +
                        "       to_char(ay.saturday_start_time, 'HH24:MI:DD') as sat_s," +
                        "       to_char(ay.saturday_end_time, 'HH24:MI:DD') as sat_e," +
                        "       to_char(ay.sunday_start_time, 'HH24:MI:DD') as sun_s," +
                        "       to_char(ay.sunday_end_time, 'HH24:MI:DD') as sun_e," +
                        "       l_p.profile_id as profile_id" +
                        "       " +
                        "  from availability ay" +
                        "  left join location_profile l_p" +
                        "    on ay.availability_id = l_p.availability_id" +
                        "  left join instructor i" +
                        "    on ay.availability_id = i.availability_id" +
                        " where l_p.profile_id = :profileId" +
                        "   ";
                Query query = entityManager.createNativeQuery(sb)
                        .unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("mon_s", StandardBasicTypes.TIME)
                .addScalar("mon_e", StandardBasicTypes.TIME)
                .addScalar("tue_s", StandardBasicTypes.TIME)
                .addScalar("tue_e", StandardBasicTypes.TIME)
                .addScalar("wed_s", StandardBasicTypes.TIME)
                .addScalar("wed_e", StandardBasicTypes.TIME)
                .addScalar("thu_s", StandardBasicTypes.TIME)
                .addScalar("thu_e", StandardBasicTypes.TIME)
                .addScalar("fri_s", StandardBasicTypes.TIME)
                .addScalar("fri_e", StandardBasicTypes.TIME)
                .addScalar("sat_s", StandardBasicTypes.TIME)
                .addScalar("sat_e", StandardBasicTypes.TIME)
                .addScalar("sun_s", StandardBasicTypes.TIME)
                .addScalar("sun_e", StandardBasicTypes.TIME)
                .addScalar("profile_id", StandardBasicTypes.LONG)
                .setParameter("profileId", pProfileId);
                List<Object[]> list = query.list();
                Map<EntryKey, Availability> cache = new HashMap<EntryKey, Availability>();
                for (Object[] obj : list) {
                    Availability ay = new Availability();
                    ay.setMondayStartTime((Date) obj[0]);
                    ay.setMondayEndTime((Date) obj[1]);
                    ay.setTuesdayStartTime((Date) obj[2]);
                    ay.setTuesdayEndTime((Date) obj[3]);
                    ay.setWednesdayStartTime((Date) obj[4]);
                    ay.setWednesdayEndTime((Date) obj[5]);
                    ay.setThursdayStartTime((Date) obj[6]);
                    ay.setThursdayEndTime((Date) obj[7]);
                    ay.setFridayStartTime((Date) obj[8]);
                    ay.setFridayEndTime((Date) obj[9]);
                    ay.setSaturdayStartTime((Date) obj[10]);
                    ay.setSaturdayEndTime((Date) obj[11]);
                    ay.setSundayStartTime((Date) obj[12]);
                    ay.setSundayEndTime((Date) obj[13]);
                    cache.put(new EntryKey((Long) 222333333L, (Long) obj[14]), ay);
                }
               
                if (cache.size() > 1) {
                    Availability ay = new Availability();
                    Availability profile_ay = cache.get(new EntryKey(null, pProfileId));
                    ay.setMondayStartTime(profile_ay.getMondayStartTime());
                    ay.setMondayEndTime(profile_ay.getMondayEndTime());
                    ay.setTuesdayStartTime(profile_ay.getTuesdayStartTime());
                    ay.setTuesdayEndTime(profile_ay.getTuesdayEndTime());
                    ay.setWednesdayStartTime(profile_ay.getWednesdayStartTime());
                    ay.setWednesdayEndTime(profile_ay.getWednesdayEndTime());
                    ay.setThursdayStartTime(profile_ay.getThursdayStartTime());
                    ay.setThursdayEndTime(profile_ay.getThursdayEndTime());
                    ay.setFridayStartTime(profile_ay.getFridayStartTime());
                    ay.setFridayEndTime(profile_ay.getFridayEndTime());
                    ay.setSaturdayStartTime(profile_ay.getSaturdayStartTime());
                    ay.setSaturdayEndTime(profile_ay.getSaturdayEndTime());
                    ay.setSundayStartTime(profile_ay.getSundayStartTime());
                    ay.setSundayEndTime(profile_ay.getSundayEndTime());
                    return ay;
                } else {
                    Availability ay = cache.get(new EntryKey((Long) 222333333L, pProfileId));
                    if (ay != null) {
                        return ay;
                    }

                }
                
                return new Availability();
            }

        

        };
    }
    public static Criterion<Availability, List<AvailabilityValueDTO2>> getByInstructorIdInsValAVL(final String[] locationExtId) {
        return new AvailabilityCriterion<List<AvailabilityValueDTO2>>() {

            @Override
           // @SuppressWarnings("unchecked")
            public  List<AvailabilityValueDTO2>  get(EntityManager entityManager, int pFetchMode) {
                String sb = " SELECT " +
                		"    TO_CHAR(ay.monday_start_time,'HH24:MI:SS') AS mon_s, " +
                		"    TO_CHAR(ay.monday_end_time,'HH24:MI:SS') AS mon_e, " +
                		"    TO_CHAR(ay.tuesday_start_time,'HH24:MI:SS') AS tue_s, " +
                		"    TO_CHAR(ay.tuesday_end_time,'HH24:MI:SS') AS tue_e, " +
                		"    TO_CHAR(ay.wednesday_start_time,'HH24:MI:SS') AS wed_s, " +
                		"    TO_CHAR(ay.wednesday_end_time,'HH24:MI:SS') AS wed_e, " +
                		"    TO_CHAR(ay.thursday_start_time,'HH24:MI:SS') AS thu_s, " +
                		"    TO_CHAR(ay.thursday_end_time,'HH24:MI:SS') AS thu_e, " +
                		"    TO_CHAR(ay.friday_start_time,'HH24:MI:SS') AS fri_s, " +
                		"    TO_CHAR(ay.friday_end_time,'HH24:MI:SS') AS fri_e, " +
                		"    TO_CHAR(ay.saturday_start_time,'HH24:MI:SS') AS sat_s, " +
                		"    TO_CHAR(ay.saturday_end_time,'HH24:MI:SS') AS sat_e, " +
                		"    TO_CHAR(ay.sunday_start_time,'HH24:MI:SS') AS sun_s, " +
                		"    TO_CHAR(ay.sunday_end_time,'HH24:MI:SS') AS sun_e, " +
                		"    TO_CHAR(l.profile_id) AS profile_id, " +
                		"    TO_CHAR(i.instructor_id) AS instructor_id, " +
                		"    TO_CHAR(l.external_id) AS location_id, " +
                		"    TO_CHAR(i.external_id) AS external_id " +
                		" FROM  availability ay " +
                		" LEFT JOIN instructor i   " +
                		" ON ay.availability_id = i.availability_id LEFT JOIN " +
                		"   location l  " +
                		" ON l.location_id = i.location_id WHERE  " +
                		// "      i.external_id not in ('svc_GCSSInstructor','075795','svc_GCSSInstructor','075795','085133','085133','085133','108758','118134','Rajkumar.Ganesan','095552','095552','Swati.Paste','095552','084294','084326','087305','077201','088432','116619','122002','122011','090801','091993','096024','104120','087991','118822','098620','107683','096534','105820','119286','107574','101586','126049','127554','110260','099017','114796','113210','093729','114986','115818','088239','116179','114888','105331','127231','116644','118547','123036','117552','117742','118198','118249','120016','121224','102090','120833','119910','121397','122527','122882','122808','123003','122407','125725','125771','126234','127284','127370','114373','126666') AND  " +
                		  "    i.status = 'A'   " +
                		
                		" AND i.enabled = 'Y'   " ;
                
                
                
                
                Query query = entityManager.createNativeQuery(sb)
                        .unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("mon_s", StandardBasicTypes.TIME)
                .addScalar("mon_e", StandardBasicTypes.TIME)
                .addScalar("tue_s", StandardBasicTypes.TIME)
                .addScalar("tue_e", StandardBasicTypes.TIME)
                .addScalar("wed_s", StandardBasicTypes.TIME)
                .addScalar("wed_e", StandardBasicTypes.TIME)
                .addScalar("thu_s", StandardBasicTypes.TIME)
                .addScalar("thu_e", StandardBasicTypes.TIME)
                .addScalar("fri_s", StandardBasicTypes.TIME)
                .addScalar("fri_e", StandardBasicTypes.TIME)
                .addScalar("sat_s", StandardBasicTypes.TIME)
                .addScalar("sat_e", StandardBasicTypes.TIME)
                .addScalar("sun_s", StandardBasicTypes.TIME)
                .addScalar("sun_e", StandardBasicTypes.TIME)
                .addScalar("profile_id", StandardBasicTypes.STRING)
                .addScalar("instructor_id", StandardBasicTypes.STRING)
                .addScalar("location_id", StandardBasicTypes.STRING)
                .addScalar("external_id", StandardBasicTypes.STRING);
          
                 //query.setParameterList("locationExtId", locationExtId);
                 
                List<Object[]> list = query.list();
                List<AvailabilityValueDTO2> avl = new ArrayList<AvailabilityValueDTO2>();
                
                Map<EntryKey, AvailabilityValueDTO2> cache = new HashMap<EntryKey, AvailabilityValueDTO2>();
                AvailabilityValueDTO2 ay = null;
                for (Object[] obj : list) {
                	ay = new AvailabilityValueDTO2();
                    ay.setMondayStartTime(""+(Date) obj[0]);
                    ay.setMondayEndTime(""+(Date) obj[1]);
                    ay.setTuesdayStartTime(""+(Date) obj[2]);
                    ay.setTuesdayEndTime(""+(Date) obj[3]);
                    ay.setWednesdayStartTime(""+(Date) obj[4]);
                    ay.setWednesdayEndTime(""+(Date) obj[5]);
                    ay.setThursdayStartTime(""+(Date) obj[6]);
                    ay.setThursdayEndTime(""+(Date) obj[7]);
                    ay.setFridayStartTime(""+(Date) obj[8]);
                    ay.setFridayEndTime(""+(Date) obj[9]);
                    ay.setSaturdayStartTime(""+(Date) obj[10]);
                    ay.setSaturdayEndTime(""+(Date) obj[11]);
                    ay.setSundayStartTime(""+(Date) obj[12]);
                    ay.setSundayEndTime(""+(Date) obj[13]);
                    
                    ay.setInstructor_id(""+(String) obj[15]);
                    ay.setProfilelId(""+(String) obj[14]);
                    ay.setLocationId(""+(String) obj[16]);
                    ay.setInsExternalId(""+(String) obj[17]);
                    avl.add(ay);
                }
            
                return avl;
            } 
          };
    }   
    
    public static Criterion<Availability, List<AvailabilityValueDTO2>> getInstructorIdByUpdated() {
        return new AvailabilityCriterion<List<AvailabilityValueDTO2>>() {

            @Override
           // @SuppressWarnings("unchecked")
            public  List<AvailabilityValueDTO2>  get(EntityManager entityManager, int pFetchMode) {
                String sb = "select  TO_CHAR( i.INSTRUCTOR_ID) AS INSTRUCTOR_ID,TO_CHAR( l.profile_id) as  profile_id,l.external_id as external_id,i.external_id as Ins_external_id from INSTRUCTOR i, location l where i.location_id = l.Location_id and  i.updated >= sysdate -5   ";
 
                Query query = entityManager.createNativeQuery(sb)
                        .unwrap(org.hibernate.query.NativeQuery.class)
                        .addScalar("profile_id", StandardBasicTypes.STRING)
                
                .addScalar("INSTRUCTOR_ID", StandardBasicTypes.STRING)
                
                .addScalar("external_id", StandardBasicTypes.STRING)
                
                .addScalar("Ins_external_id", StandardBasicTypes.STRING);
                
 
   
                //query.setLong("instructorId", pInstructorId);
                List<Object[]> list = query.list();
                List<AvailabilityValueDTO2> avl = new ArrayList<AvailabilityValueDTO2>();
                
                Map<EntryKey, AvailabilityValueDTO2> cache = new HashMap<EntryKey, AvailabilityValueDTO2>();
                AvailabilityValueDTO2 ay = null;
                for (Object[] obj : list) {
                	ay = new AvailabilityValueDTO2();
                	ay.setProfilelId(""+(String) obj[0]);
                    ay.setInstructor_id(""+(String) obj[1]);
                    ay.setLocationId(""+(String) obj[2]);
                    ay.setInsExternalId(""+(String) obj[3]);
                	
                    avl.add(ay);
                }
            
                return avl;
            } 
          };
    }     
}
