package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.List;

import jakarta.persistence.EntityManager;
import org.hibernate.Session;

import com.guitarcenter.scheduler.model.RoomNumber;

public abstract class RoomNumberCriterion<E> extends AbstractCriterion<RoomNumber, E> implements
		Criterion<RoomNumber, E> {

	private static final Criterion<RoomNumber, RoomNumber>	DEFAULT_INSTANCE	= new RoomNumberCriterion<RoomNumber>() {
																				};



	private RoomNumberCriterion() {
		super(RoomNumber.class);
	}



	@Override
	protected String getFetchScript(int pFetchMode) {
		StringBuilder sb = new StringBuilder();
		sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
		sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
		return sb.toString();
	}



	@Override
	public List<E> search(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	@Override
	public E get(EntityManager entityManager, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	public static Criterion<RoomNumber, RoomNumber> getInstance() {
		return DEFAULT_INSTANCE;
	}

}
