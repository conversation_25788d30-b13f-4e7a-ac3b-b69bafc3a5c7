package com.guitarcenter.scheduler.model;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Represents a CustomerHistory
 */
@Entity
@Table(name = "CUSTOMER_HISTORY")
@SequenceGenerator(name = "CUSTOMER_HISTORY_ID_SEQ", sequenceName = "CUSTOMER_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class CustomerHistory implements java.io.Serializable {

	
	
	private static final long serialVersionUID =   2085422946647504613L;
	
	private Long				customerHistoryId;
	private String  			location_external_id;
	private String  			lessonCounts;
	private Date 				lastBooked;
	private Long				updatedBy;		
	private Long				siteId;
	private String				customerStatus;
	private Date				updated;
	private String				externalId;
	private String				externalSource;	
	private String				ParentFullName;
	private String				firstName;
	private String				lastName;
	private String				email;
	private String				phone;
	private String              badgeNumber;
	private Date 				posUpdated;
	private String 				contractDay;
 


	public CustomerHistory() {
	}
	

	

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "CUSTOMER_HISTORY_ID_SEQ")
	@Column(name = "CUSTOMER_HISTORY_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getCustomerHistoryId() {
		return this.customerHistoryId;
	}



	public void setCustomerHistoryId(Long customerHistoryId) {
		this.customerHistoryId = customerHistoryId;
	}
 


	/**
	* @return the lessonCounts
	*/
	@Column(name = "LESSON_COUNT", length = 256)
	public String getLessonCounts() {
		return lessonCounts;
	}


 
	public void setLessonCounts(String lessonCounts) {
		this.lessonCounts = lessonCounts;
	}
		 
		@Column(name = "LAST_BOOKED")		 
		public Date getLastBooked() {
			return lastBooked;
		}



		public void setLastBooked(Date lastBooked) {
			this.lastBooked = lastBooked;
		}	
		
	
	@Column(name = "LOCATION_EXTERNAL_ID", length = 256)
	public String getLocation_external_id() {
		return location_external_id;
	}



	public void setLocation_external_id(String location_external_id) {
		this.location_external_id = location_external_id;
	}




	@Column(name = "UPDATED_BY", nullable = false)
	public Long getUpdatedBy() {
		return updatedBy;
	}




	public void setUpdatedBy(Long updatedBy) {
		this.updatedBy = updatedBy;
	}




	@Column(name = "SITE_ID")
	public Long getSiteId() {
		return siteId;
	}




	public void setSiteId(Long siteId) {
		this.siteId = siteId;
	}




	@Column(name = "CUSTOMER_STATUS_ID", nullable = false)
	public String getCustomerStatus() {
		return customerStatus;
	}




	public void setCustomerStatus(String customerStatus) {
		this.customerStatus = customerStatus;
	}
 

	@Column(name = "UPDATED")
	public Date getUpdated() {
		return updated;
	}




	public void setUpdated(Date updated) {
		this.updated = updated;
	}




	@Column(name = "EXTERNAL_ID", length = 256)
	public String getExternalId() {
		return externalId;
	}




	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}

 


	@Column(name = "EXTERNAL_SOURCE", length = 256)
	public String getExternalSource() {
		return externalSource;
	}




	public void setExternalSource(String externalSource) {
		this.externalSource = externalSource;
	}




	@Column(name = "PARENT_FULL_NAME", length = 256)
	public String getParentFullName() {
		return ParentFullName;
	}




	public void setParentFullName(String parentFullName) {
		ParentFullName = parentFullName;
	}




	@Column(name = "FIRST_NAME", length = 256)
	public String getFirstName() {
		return firstName;
	}




	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}




	@Column(name = "LAST_NAME", length = 256)
	public String getLastName() {
		return lastName;
	}




	public void setLastName(String lastName) {
		this.lastName = lastName;
	}




	@Column(name = "EMAIL", length = 256)
	public String getEmail() {
		return email;
	}




	public void setEmail(String email) {
		this.email = email;
	}




	@Column(name = "PHONE", length = 256)
	public String getPhone() {
		return phone;
	}




	public void setPhone(String phone) {
		this.phone = phone;
	}




	@Column(name = "BADGE_NUMBER", length = 256)
	public String getBadgeNumber() {
		return badgeNumber;
	}




	public void setBadgeNumber(String badgeNumber) {
		this.badgeNumber = badgeNumber;
	}




	@Column(name = "POS_UPDATED", length = 256)
	public Date getPosUpdated() {
		return posUpdated;
	}




	public void setPosUpdated(Date posUpdated) {
		this.posUpdated = posUpdated;
	}




	@Column(name = "CONTRACT_DAY", length = 256)
	public String getContractDay() {
		return contractDay;
	}




	public void setContractDay(String contractDay) {
		this.contractDay = contractDay;
	}



 


	 
	 

}
