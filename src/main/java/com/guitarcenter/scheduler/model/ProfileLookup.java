package com.guitarcenter.scheduler.model;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Represents an employee of a studio
 */
@Entity
@Table(name = "PROFILE_LOOKUP")
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class ProfileLookup implements java.io.Serializable {

    
    private static final long	serialVersionUID	= 1778070834197028604L;
	private Long				activityId;
	private Long				version;
	private Site				site;
	private String				serviceId;
	private Date				mondayStartTime;
	private Date				mondayEndTime;
	private Date				tuesdayStartTime;
	private Date				tuesdayEndTime;
	private Date				wednesdayStartTime;
	private Date				wednesdayEndTime;
	private Date				thursdayStartTime;
	private Date				thursdayEndTime;
	private Date				fridayStartTime;
	private Date				fridayEndTime;
	private Date				saturdayStartTime;
	private Date				saturdayEndTime;
	private Date				sundayStartTime;
	private Date				sundayEndTime;



	public ProfileLookup() {
	}
	
	
	@Id
	@Column(name = "ACTIVITY_ID", nullable = false, precision = 22, scale = 0)
	public Long getActivityId() {
		return this.activityId;
	}



	public void setActivityId(Long activityId) {
		this.activityId = activityId;
	}
	
	
	
	@Column(name = "SERVICE_ID")
	public String getServiceId() {
		return this.serviceId;
	}



	public void setServiceId(String serviceId) {
		this.serviceId = serviceId;
	}
	
	
	@Version
	@Column(name = "VERSION")
	public Long getVersion() {
		return this.version;
	}



	public void setVersion(Long version) {
		this.version = version;
	}
	
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}

	@Column(name = "MONDAY_START_TIME")
	public Date getMondayStartTime() {
		return this.mondayStartTime;
	}



	public void setMondayStartTime(Date mondayStartTime) {
		this.mondayStartTime = mondayStartTime;
	}



	@Column(name = "MONDAY_END_TIME")
	public Date getMondayEndTime() {
		return this.mondayEndTime;
	}



	public void setMondayEndTime(Date mondayEndTime) {
		this.mondayEndTime = mondayEndTime;
	}



	@Column(name = "TUESDAY_START_TIME")
	public Date getTuesdayStartTime() {
		return this.tuesdayStartTime;
	}



	public void setTuesdayStartTime(Date tuesdayStartTime) {
		this.tuesdayStartTime = tuesdayStartTime;
	}



	@Column(name = "TUESDAY_END_TIME")
	public Date getTuesdayEndTime() {
		return this.tuesdayEndTime;
	}



	public void setTuesdayEndTime(Date tuesdayEndTime) {
		this.tuesdayEndTime = tuesdayEndTime;
	}



	@Column(name = "WEDNESDAY_START_TIME")
	public Date getWednesdayStartTime() {
		return this.wednesdayStartTime;
	}



	public void setWednesdayStartTime(Date wednesdayStartTime) {
		this.wednesdayStartTime = wednesdayStartTime;
	}



	@Column(name = "WEDNESDAY_END_TIME")
	public Date getWednesdayEndTime() {
		return this.wednesdayEndTime;
	}



	public void setWednesdayEndTime(Date wednesdayEndTime) {
		this.wednesdayEndTime = wednesdayEndTime;
	}



	@Column(name = "THURSDAY_START_TIME")
	public Date getThursdayStartTime() {
		return this.thursdayStartTime;
	}



	public void setThursdayStartTime(Date thursdayStartTime) {
		this.thursdayStartTime = thursdayStartTime;
	}



	@Column(name = "THURSDAY_END_TIME")
	public Date getThursdayEndTime() {
		return this.thursdayEndTime;
	}



	public void setThursdayEndTime(Date thursdayEndTime) {
		this.thursdayEndTime = thursdayEndTime;
	}



	@Column(name = "FRIDAY_START_TIME")
	public Date getFridayStartTime() {
		return this.fridayStartTime;
	}



	public void setFridayStartTime(Date fridayStartTime) {
		this.fridayStartTime = fridayStartTime;
	}



	@Column(name = "FRIDAY_END_TIME")
	public Date getFridayEndTime() {
		return this.fridayEndTime;
	}



	public void setFridayEndTime(Date fridayEndTime) {
		this.fridayEndTime = fridayEndTime;
	}



	@Column(name = "SATURDAY_START_TIME")
	public Date getSaturdayStartTime() {
		return this.saturdayStartTime;
	}



	public void setSaturdayStartTime(Date saturdayStartTime) {
		this.saturdayStartTime = saturdayStartTime;
	}



	@Column(name = "SATURDAY_END_TIME")
	public Date getSaturdayEndTime() {
		return this.saturdayEndTime;
	}



	public void setSaturdayEndTime(Date saturdayEndTime) {
		this.saturdayEndTime = saturdayEndTime;
	}



	@Column(name = "SUNDAY_START_TIME")
	public Date getSundayStartTime() {
		return this.sundayStartTime;
	}



	public void setSundayStartTime(Date sundayStartTime) {
		this.sundayStartTime = sundayStartTime;
	}



	@Column(name = "SUNDAY_END_TIME")
	public Date getSundayEndTime() {
		return this.sundayEndTime;
	}



	public void setSundayEndTime(Date sundayEndTime) {
		this.sundayEndTime = sundayEndTime;
	}




}	