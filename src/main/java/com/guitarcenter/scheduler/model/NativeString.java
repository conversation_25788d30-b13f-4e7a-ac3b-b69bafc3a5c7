package com.guitarcenter.scheduler.model;

import jakarta.persistence.*;

//Added for LES_27
	@Entity
	@NamedStoredProcedureQueries({
			@NamedStoredProcedureQuery(
					name = "callNASlotsFunc",
					procedureName = "GET_RM_SLTS_FNC",
					parameters = {
							@StoredProcedureParameter(mode = ParameterMode.REF_CURSOR, name = "result", type = void.class),
							@StoredProcedureParameter(mode = ParameterMode.IN, name = "param1", type = String.class),
							@StoredProcedureParameter(mode = ParameterMode.IN, name = "param2", type = String.class),
							@StoredProcedureParameter(mode = ParameterMode.IN, name = "param3", type = String.class)
					}
			),
			@NamedStoredProcedureQuery(
					name = "callAppTabRecProcedure",
					procedureName = "GET_APPNT_BKP_FNC",
					parameters = {
							@StoredProcedureParameter(mode = ParameterMode.REF_CURSOR, name = "result", type = void.class),
							@StoredProcedureParameter(mode = ParameterMode.IN, name = "param", type = Long.class)
					}
			),
			@NamedStoredProcedureQuery(
					name = "callAppTabDropProcedure",
					procedureName = "GET_APPNT_BKP_TBL_DRP_FNC",
					parameters = {
							@StoredProcedureParameter(mode = ParameterMode.REF_CURSOR, name = "result", type = void.class),
							@StoredProcedureParameter(mode = ParameterMode.IN, name = "param", type = Long.class)
					}
			),
			@NamedStoredProcedureQuery(
					name = "callAppCustTabRecProcedure",
					procedureName = "GET_APPNT_CUST_BKP_FNC",
					parameters = {
							@StoredProcedureParameter(mode = ParameterMode.REF_CURSOR, name = "result", type = void.class),
							@StoredProcedureParameter(mode = ParameterMode.IN, name = "param", type = Long.class)
					}
			),
			@NamedStoredProcedureQuery(
					name = "callAppCustTabDropProcedure",
					procedureName = "GET_APPNT_CUST_BKP_TBL_DRP_FNC",
					parameters = {
							@StoredProcedureParameter(mode = ParameterMode.REF_CURSOR, name = "result", type = void.class),
							@StoredProcedureParameter(mode = ParameterMode.IN, name = "param", type = Long.class)
					}
			),
			@NamedStoredProcedureQuery(
					name = "callSpltFunc",
					procedureName = "GET_RM_SLTS_SPLRMS_FNC",
					parameters = {
							@StoredProcedureParameter(mode = ParameterMode.REF_CURSOR, name = "result", type = void.class),
							@StoredProcedureParameter(mode = ParameterMode.IN, name = "param1", type = String.class),
							@StoredProcedureParameter(mode = ParameterMode.IN, name = "param2", type = String.class),
							@StoredProcedureParameter(mode = ParameterMode.IN, name = "param3", type = String.class)
					}
			)
	})
	public class NativeString {
	    private String noSlotsStr;

	    @Id
	    @Column(name = "noSlotsStr")
	    public String getNoSlotsStr() {
	        return noSlotsStr;
	    }

	    public void setNoSlotsStr(String noSlotsStr) {
	        this.noSlotsStr = noSlotsStr;
	    }
	}

