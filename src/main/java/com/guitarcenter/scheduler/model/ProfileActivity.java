package com.guitarcenter.scheduler.model;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.persistence.Version;



import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.guitarcenter.scheduler.model.enums.Enabled;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * Mapping of activities that are available at a profile
 */
@Entity
@Table(name = "PROFILE_ACTIVITY", uniqueConstraints = @UniqueConstraint(columnNames = { "PROFILE_ID", "ACTIVITY_ID" }))
@SequenceGenerator(name = "PROFILE_ACTIVITY_ID_SEQ", sequenceName = "PROFILE_ACTIVITY_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class ProfileActivity implements java.io.Serializable {

	private static final long	serialVersionUID	= -6818290075171383469L;
	private Long				profileActivityId;
	private long				version;
	private Site				site;
	private Person				updatedBy;
	private Date				updated;
	private LocationProfile		locationProfile;
	private Activity			activity;
	private Enabled				enabled;



	public ProfileActivity() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PROFILE_ACTIVITY_ID_SEQ")
	@Column(name = "PROFILE_ACTIVITY_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getProfileActivityId() {
		return this.profileActivityId;
	}



	public void setProfileActivityId(Long profileActivityId) {
		this.profileActivityId = profileActivityId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person personByUpdatedBy) {
		this.updatedBy = personByUpdatedBy;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PROFILE_ID", nullable = false)
	public LocationProfile getLocationProfile() {
		return this.locationProfile;
	}



	public void setLocationProfile(LocationProfile locationProfile) {
		this.locationProfile = locationProfile;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ACTIVITY_ID", nullable = false)
	public Activity getActivity() {
		return this.activity;
	}



	public void setActivity(Activity activity) {
		this.activity = activity;
	}



	@Column(name = "ENABLED", length = 1)
	@Enumerated(EnumType.STRING)
	public Enabled getEnabled() {
		return this.enabled;
	}



	public void setEnabled(Enabled enabled) {
		this.enabled = enabled;
	}
}
