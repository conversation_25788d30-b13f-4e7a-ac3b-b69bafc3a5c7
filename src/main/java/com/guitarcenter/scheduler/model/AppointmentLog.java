package com.guitarcenter.scheduler.model;

import java.util.Calendar;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Formula;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.guitarcenter.scheduler.model.enums.Canceled;



	@Entity
	@Table(name = "APPOINTMENT_LOG")
	@JsonAutoDetect
	@JsonIgnoreProperties({ "updatedBy"})
	@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
	public class AppointmentLog implements java.io.Serializable {

		
		private static final long serialVersionUID = 1L;
		
		private Long				appointmentId;
		private Long				updatedBy;		
		private Long				siteId;	
		private Long				instructorId;			
		private Long				roomId;
		private Long				activityId;		
		private Date				startTime;
		private Date				endTime;		
		private Canceled			canceled;		
		private String				note;
		private String				customerId;			
		private int					duration;

public AppointmentLog(){
	
}
@Id
@Column(name = "APPOINTMENT_ID", nullable = false, precision = 22, scale = 0)
public Long getAppointmentId() {
	return appointmentId;
}

public void setAppointmentId(Long appointmentId) {
	this.appointmentId = appointmentId;
}
@Id
@Column(name = "UPDATED_BY", nullable = false)
public Long getUpdatedBy() {
	return updatedBy;
}

public void setUpdatedBy(Long updatedBy) {
	this.updatedBy = updatedBy;
}


@Column(name = "INSTRUCTOR_ID")
public Long getInstructorID() {
	return this.instructorId;
}



public void setInstructorID(Long instructorId ) {
	this.instructorId = instructorId;
}


@Column(name = "SITE_ID")
public Long getSiteID() {
	return this.siteId;
}

public void setSiteID(Long siteId) {
	this.siteId = siteId;
}


@Column(name = "ROOM_ID")
public Long getRoomID() {
	return this.roomId;
}

public void setRoomID(Long roomId) {
	this.roomId = roomId;
}

@Column(name = "ACTIVITY_ID")
public Long getActivityID() {
	return this.activityId;
}

public void setActivityID(Long activityId) {
	this.activityId = activityId;
}


@Column(name = "START_TIME")
public Date getStartTime() {
	return this.startTime;
}



public void setStartTime(Date startTime) {
	this.startTime = startTime;
}



@Column(name = "END_TIME", nullable = false)
public Date getEndTime() {
	return this.endTime;
}



public void setEndTime(Date endTime) {
	this.endTime = endTime;
}

@Column(name = "CANCELED", length = 1)
@Enumerated(EnumType.STRING)
public Canceled getCanceled() {
	return this.canceled;
}



public void setCanceled(Canceled canceled) {
	this.canceled = canceled;
}


@Column(name = "NOTE", length = 512)
public String getNote() {
	return this.note;
}



public void setNote(String note) {
	this.note = note;
}

@Column(name = "CUSTOMER_ID")
public String getCustomerId() {
	return this.customerId;
}

public void setCustomerId(String customerId) {
	this.customerId = customerId;
}



@Formula(" (extract(day from ((END_TIME - START_TIME) * 24 * 60 * 60 * 60))) ")
public int getDuration() {
	return (int) Math.round(duration / 60.0D / 60.0D);
}



public void setDuration(int duration) {
	this.duration = duration;
}



@Transient
public String getStartTimeStr() {
	Calendar c = Calendar.getInstance();
	c.setTime(getStartTime());
	int month = c.get(Calendar.MONTH) + 1;
	int date = c.get(Calendar.DATE);
	String nyr = c.get(Calendar.YEAR) + "-" + (month < 10 ? ("0" + month) : month) + "-"
			+ (date < 10 ? ("0" + date) : date);
	String space = " ";
	int hour = c.get(Calendar.HOUR_OF_DAY);
	int minute = c.get(Calendar.MINUTE);
	String sfm = (hour < 10 ? ("0" + hour) : hour) + ":" + (minute < 10 ? ("0" + minute) : minute) + ":" + "00";
	return nyr + space + sfm;
}



@Transient
public String getEndTimeStr() {
	Calendar c = Calendar.getInstance();
	c.setTime(getEndTime());
	int month = c.get(Calendar.MONTH) + 1;
	int date = c.get(Calendar.DATE);
	String nyr = c.get(Calendar.YEAR) + "-" + (month < 10 ? ("0" + month) : month) + "-"
			+ (date < 10 ? ("0" + date) : date);
	String space = " ";
	int hour = c.get(Calendar.HOUR_OF_DAY);
	int minute = c.get(Calendar.MINUTE);
	String sfm = (hour < 10 ? ("0" + hour) : hour) + ":" + (minute < 10 ? ("0" + minute) : minute) + ":" + "00";
	return nyr + space + sfm;
}


		}
	

