package com.guitarcenter.scheduler.model;

import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Formula;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.guitarcenter.scheduler.model.enums.Canceled;

/**
 * Represents appointments in the scheduler
 */

@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class Appointment2 implements java.io.Serializable {

	private static final long	serialVersionUID	= -3273105690730365835L;
	private Long				appointmentId;
	private long				version;
	private Instructor			instructor;
	private Site				site;
	private Person				updatedBy;
	private Room				room;
	private LocationProfile		locationProfile;
	private AppointmentSeries	appointmentSeries;
	private Activity			activity;
	private Date				updated;
	private String				externalId;
	private Date				startTime;
	private Date				endTime;
	private Date				createTime;
	private Canceled			canceled;
	private String				bandName;
	private String				note;
	private Set<Customer>		customers			= new HashSet<Customer>(0);
	private int					duration;
	//GSSP-249 Changes
	private Boolean				isUpdatedByCustomer;


	public Appointment2() {
	}


 
	public Long getAppointmentId() {
		return this.appointmentId;
	}



	public void setAppointmentId(Long appointmentId) {
		this.appointmentId = appointmentId;
	}


 
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}


 
	public Instructor getInstructor() {
		return this.instructor;
	}



	public void setInstructor(Instructor instructor) {
		this.instructor = instructor;
	}


 
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}


 
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}

 
	public Room getRoom() {
		return this.room;
	}



	public void setRoom(Room room) {
		this.room = room;
	}



 
	public LocationProfile getLocationProfile() {
		return this.locationProfile;
	}



	public void setLocationProfile(LocationProfile locationProfile) {
		this.locationProfile = locationProfile;
	}


 
	public AppointmentSeries getAppointmentSeries() {
		return this.appointmentSeries;
	}



	public void setAppointmentSeries(AppointmentSeries appointmentSeries) {
		this.appointmentSeries = appointmentSeries;
	}


 
	public Activity getActivity() {
		return this.activity;
	}



	public void setActivity(Activity activity) {
		this.activity = activity;
	}

 
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}

 
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}

 
	public Date getStartTime() {
		return this.startTime;
	}



	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}


 
	public Date getEndTime() {
		return this.endTime;
	}



	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

 
	public Date getCreateTime() {
		return this.createTime;
	}



	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}


 
	public Canceled getCanceled() {
		return this.canceled;
	}



	public void setCanceled(Canceled canceled) {
		this.canceled = canceled;
	}

 
	public String getBandName() {
		return this.bandName;
	}



	public void setBandName(String bandName) {
		this.bandName = bandName;
	}



 public String getNote() {
		return this.note;
	}



	public void setNote(String note) {
		this.note = note;
	}


 public Set<Customer> getCustomers() {
		return this.customers;
	}



	public void setCustomers(Set<Customer> customers) {
		this.customers = customers;
	}


 
	public int getDuration() {
		return this.duration;
	}



	public void setDuration(int duration) {
		this.duration = duration;
	}


 
	public String getStartTimeStr() {
		Calendar c = Calendar.getInstance();
		c.setTime(getStartTime());
		int month = c.get(Calendar.MONTH) + 1;
		int date = c.get(Calendar.DATE);
		String nyr = c.get(Calendar.YEAR) + "-" + (month < 10 ? ("0" + month) : month) + "-"
				+ (date < 10 ? ("0" + date) : date);
		String space = " ";
		int hour = c.get(Calendar.HOUR_OF_DAY);
		int minute = c.get(Calendar.MINUTE);
		String sfm = (hour < 10 ? ("0" + hour) : hour) + ":" + (minute < 10 ? ("0" + minute) : minute) + ":" + "00";
		return nyr + space + sfm;
	}

 
	public String getEndTimeStr() {
		Calendar c = Calendar.getInstance();
		c.setTime(getEndTime());
		int month = c.get(Calendar.MONTH) + 1;
		int date = c.get(Calendar.DATE);
		String nyr = c.get(Calendar.YEAR) + "-" + (month < 10 ? ("0" + month) : month) + "-"
				+ (date < 10 ? ("0" + date) : date);
		String space = " ";
		int hour = c.get(Calendar.HOUR_OF_DAY);
		int minute = c.get(Calendar.MINUTE);
		String sfm = (hour < 10 ? ("0" + hour) : hour) + ":" + (minute < 10 ? ("0" + minute) : minute) + ":" + "00";
		return nyr + space + sfm;
	}

 
	public Boolean getIsUpdatedByCustomer() {
		return isUpdatedByCustomer;
	}



	public void setIsUpdatedByCustomer(Boolean isUpdatedByCustomer) {
		this.isUpdatedByCustomer = isUpdatedByCustomer;
	}
	
	

}
