package com.guitarcenter.scheduler.model;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;


//Added for Phase2 Get Lesson Service
/**
 * Represents a customer
 */
@Entity
@Table(name = "CUSTOMER_EMAIL")
@SequenceGenerator(name = "CUSTOMER_EMAIL_ID_SEQ", sequenceName = "CUSTOMER_ID_SEQ", allocationSize = 1)
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class CustomerEmail implements java.io.Serializable {

	
	
	private static final long serialVersionUID =   2085422946647504699L;
	
	private Long				customerEmaiId;
	private String				externalCustomerId;
	private String				customerEmail;
	private String				externalSource;
	private Person				updatedBy;
	private Date				updated;

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "CUSTOMER_EMAIL_ID_SEQ")
	@Column(name = "CUSTOMER_EMAIL_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getCustomerEmaiId() {
		return this.customerEmaiId;
	}



	public void setCustomerEmaiId(Long customerEmaiId) {
		this.customerEmaiId = customerEmaiId;
	}


	@Column(name = "EXTERNAL_CUSTOMER_ID", nullable = false, length = 512)
	public String getExternalCustomerId() {
		return this.externalCustomerId;
	}



	public void setExternalCustomerId(String externalCustomerId) {
		this.externalCustomerId = externalCustomerId;
	}


	@Column(name = "EXTERNAL_SOURCE", length = 512)
	public String getExternalSource() {
		return this.externalSource;
	}

	public void setExternalSource(String externalSource) {
		this.externalSource = externalSource;
	}
	
	
	@Column(name = "CUSTOMER_EMAIL", length = 512)
	public String getCustomerEmail() {
		return this.customerEmail;
	}

	public void setCustomerEmail(String customerEmail) {
		this.customerEmail = customerEmail;
	}
	
	/**
	 * getter method
	 * @return the updatedBy
	 */

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return updatedBy;
	}
	
	/**
	 * setter method
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(Person updatedBy) {
		this.updatedBy = updatedBy;
	}
	
	/**
	 * getter method
	 * @return the updated
	 */
	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return updated;
	}
	
	/**
	 * setter method
	 * @param updated the updated to set
	 */
	public void setUpdated(Date updated) {
		this.updated = updated;
	}

}

