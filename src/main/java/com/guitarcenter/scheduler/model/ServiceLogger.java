package com.guitarcenter.scheduler.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

@Entity
@Table(name = "SERVICE_LOGGER")

@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class ServiceLogger  implements java.io.Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
		
	private String	serviceName;
	private String appointmentSeriesId;
	private String appointmentId;
	private String customerMemberId;
	
	@Id
	@Column(name = "SERVICE_NAME")
	public String getServiceName() {
		return serviceName;
	}
	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}
	@Column(name = "APPOINTMENT_SERIES_ID")
	public String getAppointmentSeriesId() {
		return appointmentSeriesId;
	}
	public void setAppointmentSeriesId(String appointmentSeriesId) {
		this.appointmentSeriesId = appointmentSeriesId;
	}
	@Column(name = "APPOINTMENT_ID")
	public String getAppointmentId() {
		return appointmentId;
	}
	public void setAppointmentId(String appointmentId) {
		this.appointmentId = appointmentId;
	}
	@Column(name = "CUSTOMER_MEMBER_ID")
	public String getCustomerMemberId() {
		return customerMemberId;
	}
	public void setCustomerMemberId(String customerMemberId) {
		this.customerMemberId = customerMemberId;
	}
	
	
	
	
	

}
