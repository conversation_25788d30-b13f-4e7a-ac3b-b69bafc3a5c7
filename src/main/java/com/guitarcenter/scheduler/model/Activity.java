package com.guitarcenter.scheduler.model;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.*;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.RequiresInstructor;

/**
 * Represents activities that are known to the system
 */


@Entity
@Table(name = "ACTIVITY")
@SequenceGenerator(name = "ACTIVITY_ID_SEQ", sequenceName = "ACTIVITY_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler"})
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@NamedNativeQueries({
		@NamedNativeQuery(
				name = "activity.getActivityByAppointmentTime",
				query = "SELECT {ac.*} FROM activity ac WHERE NOT EXISTS " +
						"(SELECT * FROM appointment a WHERE " +
						"(a.start_time BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?) " +
						"OR a.end_time BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?) " +
						"OR TO_TIMESTAMP_TZ(?,?) BETWEEN a.start_time AND a.end_time " +
						"OR TO_TIMESTAMP_TZ(?,?) BETWEEN a.start_time AND a.end_time) " +
						"AND a.profile_id = ? AND a.canceled in (? , ?) AND ac.activity_id = a.activity_id(+)) " +
						"AND ac.activity_id = ?",
				resultClass = Activity.class
		),
		@NamedNativeQuery(
				name = "activity.getActivityByAppointmentRecurringTime",
				query = "SELECT {ac.*} FROM activity ac WHERE NOT EXISTS " +
						"(SELECT * FROM appointment a WHERE " +
						"TO_CHAR(start_time,'d') = TO_CHAR(to_date(?,?),'d') " +
						"AND TO_TIMESTAMP_TZ(TO_CHAR(start_time, ?), ?) BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?) " +
						"AND (a.start_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?,?) " +
						"AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '||?,?) " +
						"OR a.end_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?,?) " +
						"AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '||?,?) " +
						"OR TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?,?) BETWEEN a.start_time AND a.end_time " +
						"OR TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '||?,?) BETWEEN a.start_time AND a.end_time) " +
						"AND a.profile_id = ? AND a.canceled in (? , ?) AND ac.activity_id = a.activity_id(+)) " +
						"AND ac.activity_id = ?",
				resultClass = Activity.class
		)
})
public class Activity implements java.io.Serializable {

	private static final long	serialVersionUID	= 3166026907095259951L;
	private Long				activityId;
	private long				version;
	private Site				site;
	private Person				updatedBy;
	private Service				service;
	private Date				updated;
	private String				activityName;
	private Long				minimumAttendees;
	private Long				maximumAttendees;
	private Long				minimumDuration;
	private Long				maximumDuration;
	private RequiresInstructor	requiresInstructor;
	private Enabled				enabled;
	private String				externalId;



	public Activity() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ACTIVITY_ID_SEQ")
	@Column(name = "ACTIVITY_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getActivityId() {
		return this.activityId;
	}



	public void setActivityId(Long activityId) {
		this.activityId = activityId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	@JsonInclude
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	@JsonInclude
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SERVICE_ID", nullable = false)
	@JsonInclude
	public Service getService() {
		return this.service;
	}



	public void setService(Service service) {
		this.service = service;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "ACTIVITY_NAME", nullable = false, length = 512)
	public String getActivityName() {
		return this.activityName;
	}



	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}



	@Column(name = "MINIMUM_ATTENDEES", nullable = false, precision = 22, scale = 0)
	public Long getMinimumAttendees() {
		return this.minimumAttendees;
	}



	public void setMinimumAttendees(Long minimumAttendees) {
		this.minimumAttendees = minimumAttendees;
	}



	@Column(name = "MAXIMUM_ATTENDEES", precision = 22, scale = 0)
	public Long getMaximumAttendees() {
		return this.maximumAttendees;
	}



	public void setMaximumAttendees(Long maximumAttendees) {
		this.maximumAttendees = maximumAttendees;
	}



	@Column(name = "MINIMUM_DURATION", nullable = false, precision = 22, scale = 0)
	public Long getMinimumDuration() {
		return this.minimumDuration;
	}



	public void setMinimumDuration(Long minimumDuration) {
		this.minimumDuration = minimumDuration;
	}
	
	
	@Column(name = "MAXIMUM_DURATION", nullable = false, precision = 22, scale = 0)
	public Long getMaximumDuration() {
		return this.maximumDuration;
	}
	
	
	
	public void setMaximumDuration(Long maximumDuration) {
		this.maximumDuration = maximumDuration;
	}



	@Column(name = "REQUIRES_INSTRUCTOR", length = 1)
	@Enumerated(EnumType.STRING)
	public RequiresInstructor getRequiresInstructor() {
		return this.requiresInstructor;
	}



	public void setRequiresInstructor(RequiresInstructor requiresInstructor) {
		this.requiresInstructor = requiresInstructor;
	}



	@Column(name = "ENABLED", length = 1)
	@Enumerated(EnumType.STRING)
	public Enabled getEnabled() {
		return this.enabled;
	}



	public void setEnabled(Enabled enabled) {
		this.enabled = enabled;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}



	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37).append(activityId).toHashCode();
	}



	@Override
	public boolean equals(Object obj) {
		boolean flag = false;
		if (obj != null && Activity.class.isAssignableFrom(obj.getClass())) {
			Activity f = (Activity) obj;
			flag = new EqualsBuilder().append(activityId, f.getActivityId()).isEquals();
		}
		return flag;
	}
}
