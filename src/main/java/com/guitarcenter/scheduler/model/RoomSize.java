package com.guitarcenter.scheduler.model;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Implement a standard set of room sizes for profiles
 */
@Entity
@Table(name = "ROOM_SIZE")
@SequenceGenerator(name = "ROOM_SIZE_ID_SEQ", sequenceName = "ROOM_SIZE_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler" })
@Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
public class RoomSize implements java.io.Serializable {

	private static final long	serialVersionUID	= 3364320603786371017L;
	private Long				roomSizeId;
	private long				version;
	private Site				site;
	private RoomType			roomType;
	private Person				updatedBy;
	private Date				updated;
	private String				roomSizeName;
	private String				externalId;



	public RoomSize() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ROOM_SIZE_ID_SEQ")
	@Column(name = "ROOM_SIZE_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getRoomSizeId() {
		return this.roomSizeId;
	}



	public void setRoomSizeId(Long roomSizeId) {
		this.roomSizeId = roomSizeId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ROOM_TYPE_ID", nullable = false)
	public RoomType getRoomType() {
		return this.roomType;
	}



	public void setRoomType(RoomType roomType) {
		this.roomType = roomType;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}



	@Column(name = "ROOM_SIZE_NAME", nullable = false, length = 512)
	public String getRoomSizeName() {
		return this.roomSizeName;
	}



	public void setRoomSizeName(String roomSizeName) {
		this.roomSizeName = roomSizeName;
	}

}
