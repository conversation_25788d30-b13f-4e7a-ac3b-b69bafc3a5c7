/**
 * @Title: Timeoff.java
 * @Package com.guitarcenter.scheduler.model
 * @Description: TODO
 * Copyright: Copyright (c) 2014 
 * Company:
 * 
 * <AUTHOR>
 * @date Mar 10, 2014 3:48:37 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.model;

import java.util.Date;

import jakarta.persistence.*;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * @ClassName: Timeoff
 * @Description: TODO
 * <AUTHOR>
 * @date Mar 10, 2014 3:48:37 PM
 *
 */
@Entity
@Table(name = "TIMEOFF")
@SequenceGenerator(name = "TIMEOFF_ID_SEQ", sequenceName = "TIMEOFF_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@NamedNativeQueries({
		@NamedNativeQuery(
				name = "timeoff.getTimeoffByTime",
				query = "SELECT * FROM timeoff t WHERE " +
						"(t.start_time BETWEEN TO_TIMESTAMP_TZ(:startTime1, :timezone1) AND TO_TIMESTAMP_TZ(:endTime1, :timezone2) " +
						"OR t.end_time BETWEEN TO_TIMESTAMP_TZ(:startTime2, :timezone3) AND TO_TIMESTAMP_TZ(:endTime2, :timezone4) " +
						"OR TO_TIMESTAMP_TZ(:startTime3, :timezone5) BETWEEN t.start_time AND t.end_time " +
						"OR TO_TIMESTAMP_TZ(:endTime3, :timezone6) BETWEEN t.start_time AND t.end_time) " +
						"AND t.instructor_id = :instructorId",
				resultClass = Timeoff.class
		),
		@NamedNativeQuery(
				name = "timeoff.getTimeoffByRecurringTime",
				query = "SELECT CASE WHEN EXISTS (" +
						"SELECT t.* " +
						"FROM timeoff t, ( &REPLACECONDITION ) temp " +
						"WHERE " +
						"(t.start_time BETWEEN TO_TIMESTAMP_TZ(temp.s, :timeformat) AND TO_TIMESTAMP_TZ(temp.e, :timeformat) " +
						"OR t.end_time BETWEEN TO_TIMESTAMP_TZ(temp.s, :timeformat) AND TO_TIMESTAMP_TZ(temp.e, :timeformat) " +
						"OR TO_TIMESTAMP_TZ(temp.s, :timeformat) BETWEEN t.start_time AND t.end_time " +
						"OR TO_TIMESTAMP_TZ(temp.e, :timeformat) BETWEEN t.start_time AND t.end_time) " +
						"AND t.instructor_id = :instructorId " +
						") THEN 'false' ELSE 'true' END flag FROM DUAL",
				resultSetMapping = "StringResult"
		),
		@NamedNativeQuery(
				name = "timeoff.getTimeoffDTOByAvailabilityTime",
				query = "WITH a AS (SELECT availability_id, 2 week_day, monday_start_time start_time, monday_end_time end_time " +
						"FROM availability UNION " +
						"SELECT availability_id, 3, TUESDAY_START_TIME, TUESDAY_end_TIME FROM availability UNION " +
						"SELECT availability_id, 4, WEDNESDAY_START_TIME, WEDNESDAY_end_TIME FROM availability UNION " +
						"SELECT availability_id, 5, THURSDAY_START_TIME, THURSDAY_end_TIME FROM availability UNION " +
						"SELECT availability_id, 6, FRIDAY_START_TIME, FRIDAY_end_TIME FROM availability UNION " +
						"SELECT availability_id, 7, SATURDAY_START_TIME, SATURDAY_end_TIME FROM availability UNION " +
						"SELECT availability_id, 1, SUNDAY_START_TIME, SUNDAY_end_TIME FROM availability) " +
						"SELECT a.start_time, a.end_time, i.instructor_id " +
						"FROM a, instructor i " +
						"WHERE i.availability_id = a.availability_id " +
						"AND a.week_day = TO_CHAR(TO_DATE(?, 'MM/dd/yyyy'), 'd') " +  // Hardcoded date
						"AND i.location_id =? ",
				resultSetMapping = "TimeoffDateDTOMapping"
		)

})
public class Timeoff implements java.io.Serializable {
	private static final long	serialVersionUID	= 6718026907095237892L;
	private Long				timeoffId;
	private Instructor			instructor;
	private Date				startTime;
	private Date				endTime;
	private long				version;
	private Person				updatedBy;
	private Date				updated;
	
	/**
	  * Timeoff. 
	  * <p>Title: </p>
	  * <p>Description: </p>
	  */
	public Timeoff() {
	}
	
	/**
	 * getter method
	 * @return the timeoffId
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TIMEOFF_ID_SEQ")
	@Column(name = "TIMEOFF_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getTimeoffId() {
		return timeoffId;
	}
	/**
	 * setter method
	 * @param timeoffId the timeoffId to set
	 */
	public void setTimeoffId(Long timeoffId) {
		this.timeoffId = timeoffId;
	}
	/**
	 * getter method
	 * @return the instructor
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "INSTRUCTOR_ID", nullable = false)
	public Instructor getInstructor() {
		return instructor;
	}
	/**
	 * setter method
	 * @param instructor the instructor to set
	 */
	public void setInstructor(Instructor instructor) {
		this.instructor = instructor;
	}
	/**
	 * getter method
	 * @return the startTime
	 */
	@Column(name = "START_TIME", nullable = false)
	public Date getStartTime() {
		return startTime;
	}
	/**
	 * setter method
	 * @param startTime the startTime to set
	 */
	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}
	/**
	 * getter method
	 * @return the endTime
	 */
	@Column(name = "END_TIME", nullable = false)
	public Date getEndTime() {
		return endTime;
	}
	/**
	 * setter method
	 * @param endTime the endTime to set
	 */
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	/**
	 * getter method
	 * @return the version
	 */
	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return version;
	}
	/**
	 * setter method
	 * @param version the version to set
	 */
	public void setVersion(long version) {
		this.version = version;
	}
	/**
	 * getter method
	 * @return the updatedBy
	 */

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return updatedBy;
	}
	/**
	 * setter method
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(Person updatedBy) {
		this.updatedBy = updatedBy;
	}
	/**
	 * getter method
	 * @return the updated
	 */
	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return updated;
	}
	/**
	 * setter method
	 * @param updated the updated to set
	 */
	public void setUpdated(Date updated) {
		this.updated = updated;
	}
	
}
