package com.guitarcenter.scheduler.model;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * These are the high-level serviceModes that will be available in the system
 * //---GSSP Instructor Mode update changes
 */
@Entity
@Table(name = "SERVICE_MODE")
@SequenceGenerator(name = "SERVICE_MODE_ID_SEQ", sequenceName = "SERVICE_MODE_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class ServiceMode implements java.io.Serializable {

	private static final long	serialVersionUID	= 5422781799411531670L;
	private Long				serviceModeId;
	private long				version;
	private Site				site;
	private Person				updatedBy;
	private Date				updated;
	private String				serviceModeName;
	private String				description;
	private String				externalId;



	public ServiceMode() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SERVICE_MODE_ID_SEQ")
	@Column(name = "SERVICE_MODE_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getServiceModeId() {
		return this.serviceModeId;
	}



	public void setServiceModeId(Long serviceModeId) {
		this.serviceModeId = serviceModeId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "SERVICE_MODE_NAME", nullable = false, length = 512)
	public String getServiceModeName() {
		return this.serviceModeName;
	}



	public void setServiceModeName(String serviceModeName) {
		this.serviceModeName = serviceModeName;
	}



	@Column(name = "DESCRIPTION", length = 512)
	public String getDescription() {
		return this.description;
	}



	public void setDescription(String description) {
		this.description = description;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}

}
