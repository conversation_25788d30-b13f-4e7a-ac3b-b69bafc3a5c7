package com.guitarcenter.scheduler.model;

import java.util.Date;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * @ClassName: ProfileTimeoff
 * @Description: Hibernate Table entity for PROFILE_TIMEOFF
 * GSSP-321-Prevent customers from scheduling a lesson online,
 *  when the stores are not offering lessons
 * <AUTHOR>
 * @date AUG 10, 2019
 *
 */

	@Entity
	@Table(name = "PROFILE_TIMEOFF")
	@SequenceGenerator(name = "PROFILETIMEOFF_ID_SEQ", sequenceName = "PROFILETIMEOFF_ID_SEQ", allocationSize = 1)
	@JsonAutoDetect
	@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler" })
	@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
	public class ProfileTimeoff implements java.io.Serializable {
		private static final long	serialVersionUID	= 6718026907095237892L;
		private Long				profiletimeoffId;
		private Long     			profileId;
		private Date				startTime;
		private Date				endTime;
		private Long				version;
		private Date				updated;
		private Long				updatedBy;
		
		
		public ProfileTimeoff() {
			
		}
		
		@Id
		@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PROFILETIMEOFF_ID_SEQ")
		@Column(name = "PROFILE_TIMEOFF_ID", unique = true, nullable = false, precision = 22, scale = 0)
		public Long getProfiletimeoffId() {
			return profiletimeoffId;
		}
		public void setProfiletimeoffId(Long profiletimeoffId) {
			this.profiletimeoffId = profiletimeoffId;
		}
		
		@Column(name = "PROFILE_ID", nullable = false)
		public Long getProfileId() {
			return profileId;
		}
		public void setProfileId(Long profileId) {
			this.profileId = profileId;
		}
		
		@Column(name = "START_TIME", nullable = false)
		public Date getStartTime() {
			return startTime;
		}
		public void setStartTime(Date startTime) {
			this.startTime = startTime;
		}
		
		@Column(name = "END_TIME", nullable = false)
		public Date getEndTime() {
			return endTime;
		}
		public void setEndTime(Date endTime) {
			this.endTime = endTime;
		}

		@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
		public long getVersion() {
			return version;
		}
		public void setVersion(long version) {
			this.version = version;
		}
		
		@Column(name = "UPDATED", nullable = false)
		public Date getUpdated() {
			return updated;
		}
		public void setUpdated(Date updated) {
			this.updated = updated;
		}
		
		@Column(name = "UPDATED_BY", nullable = false)
		public Long getUpdatedBy() {
			return updatedBy;
		}
		public void setUpdatedBy(Long updatedBy) {
			this.updatedBy = updatedBy;
		}
			
	}
	
		
		