package com.guitarcenter.scheduler.model;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.guitarcenter.scheduler.model.enums.AllowBandName;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.RequiresInstructor;

/**
 * Represents the services known to the system
 */
@Entity
@Table(name = "SERVICE")
@SequenceGenerator(name = "SERVICE_ID_SEQ", sequenceName = "SERVICE_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class Service implements java.io.Serializable {

	private static final long serialVersionUID = -745041578310347938L;
	private Long serviceId;
	private long version;
	private Site site;
	private Person updatedBy;
	private Date updated;
	private String serviceName;
	private Enabled enabled;
	private RequiresInstructor requiresInstructor;
	private AllowBandName allowBandName;
	private String externalId;

	public Service() {
	}

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SERVICE_ID_SEQ")
	@Column(name = "SERVICE_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getServiceId() {
		return this.serviceId;
	}

	public void setServiceId(Long serviceId) {
		this.serviceId = serviceId;
	}

	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}

	public void setVersion(long version) {
		this.version = version;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}

	public void setSite(Site site) {
		this.site = site;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}

	public void setUpdatedBy(Person updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}

	public void setUpdated(Date updated) {
		this.updated = updated;
	}

	@Column(name = "SERVICE_NAME", nullable = false, length = 512)
	public String getServiceName() {
		return this.serviceName;
	}

	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}

	@Column(name = "ENABLED", length = 1)
	@Enumerated(EnumType.STRING)
	public Enabled getEnabled() {
		return this.enabled;
	}

	public void setEnabled(Enabled enabled) {
		this.enabled = enabled;
	}

	@Column(name = "REQUIRES_INSTRUCTOR", length = 1)
	@Enumerated(EnumType.STRING)
	public RequiresInstructor getRequiresInstructor() {
		return this.requiresInstructor;
	}

	public void setRequiresInstructor(RequiresInstructor requiresInstructor) {
		this.requiresInstructor = requiresInstructor;
	}

	@Column(name = "ALLOW_BAND_NAME", length = 1)
	@Enumerated(EnumType.STRING)
	public AllowBandName getAllowBandName() {
		return this.allowBandName;
	}

	public void setAllowBandName(AllowBandName allowBandName) {
		this.allowBandName = allowBandName;
	}

	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}

	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37).append(serviceId).toHashCode();
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj) return true;
		if (obj == null || getClass() != obj.getClass()) return false;
		Service other = (Service) obj;
		return new EqualsBuilder().append(serviceId, other.getServiceId()).isEquals();
	}
}
