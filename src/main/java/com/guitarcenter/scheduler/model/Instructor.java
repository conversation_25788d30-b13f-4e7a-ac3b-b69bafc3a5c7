package com.guitarcenter.scheduler.model;

import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.*;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.guitarcenter.scheduler.integration.service.impl.EmployeeIntegrationServiceImpl;
import com.guitarcenter.scheduler.model.enums.Enabled;

/**
 * Represents an employee of a studio
 */
@Entity
@Table(name = "INSTRUCTOR")
@SequenceGenerator(name = "INSTRUCTOR_ID_SEQ", sequenceName = "INSTRUCTOR_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@NamedNativeQueries({
		@NamedNativeQuery(
				name = "instructor.checkInstructorTime",
				query = "WITH t AS (SELECT availability_id, 2 week_day, monday_start_time start_time, monday_end_time end_time FROM availability " +
						"UNION SELECT availability_id, 3, TUESDAY_START_TIME , TUESDAY_end_TIME FROM availability " +
						"UNION SELECT availability_id, 4, WEDNESDAY_START_TIME , WEDNESDAY_end_TIME FROM availability " +
						"UNION SELECT availability_id, 5, THURSDAY_START_TIME , THURSDAY_end_TIME FROM availability " +
						"UNION SELECT availability_id, 6, FRIDAY_START_TIME , FRIDAY_end_TIME FROM availability " +
						"UNION SELECT availability_id, 7, SATURDAY_START_TIME , SATURDAY_end_TIME FROM availability " +
						"UNION SELECT availability_id, 1, SUNDAY_START_TIME , SUNDAY_end_TIME FROM availability) " +
						"SELECT i.* FROM t, instructor i WHERE t.availability_id = i.availability_id " +
						"AND t.week_day = TO_CHAR(to_date(?,?),'d') " +
						"AND TO_TIMESTAMP_TZ(?, ?) BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(t.start_time,?),?) AND TO_TIMESTAMP_TZ(TO_CHAR(t.end_time,?),?) " +
						"AND TO_TIMESTAMP_TZ(?, ?) BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(t.start_time,?),?) AND TO_TIMESTAMP_TZ(TO_CHAR(t.end_time,?),?) " +
						"AND i.instructor_id = ?",
				resultClass = Instructor.class
		),
		@NamedNativeQuery(
				name = "instructor.checkInstructorAppointmentRecurringTime",
				query = "SELECT i.* FROM instructor i WHERE NOT EXISTS " +
						"(SELECT * FROM appointment a WHERE TO_CHAR(start_time,'d') = TO_CHAR(to_date(?,?),'d') " +
						"AND TO_TIMESTAMP_TZ(TO_CHAR(start_time, ?), ?) BETWEEN TO_TIMESTAMP_TZ(?, ?) AND TO_TIMESTAMP_TZ(?, ?) " +
						"AND (a.start_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?, ?) " +
						"AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '|| ?, ?) " +
						"OR a.end_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?, ?) " +
						"AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time, ?)||' '||?, ?) " +
						"OR TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?,?) BETWEEN a.start_time AND a.end_time " +
						"OR TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '||?,?) BETWEEN a.start_time AND a.end_time) " +
						"AND a.canceled in (? , ?) AND i.instructor_id = a.instructor_id(+)) " +
						"AND i.instructor_id = ?",
				resultClass = Instructor.class
		),
		@NamedNativeQuery(
				name = "instructor.checkInstructorAppointmentTime",
				query = "SELECT i.* FROM instructor i WHERE NOT EXISTS " +
						"(SELECT * FROM appointment a WHERE " +
						"(a.start_time BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?) " +
						"OR a.end_time BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?) " +
						"OR TO_TIMESTAMP_TZ(?,?) BETWEEN a.start_time AND a.end_time " +
						"OR TO_TIMESTAMP_TZ(?,?) BETWEEN a.start_time AND a.end_time) " +
						"AND a.canceled in (? , ?) AND i.instructor_id = a.instructor_id(+)) " +
						"AND i.instructor_id = ?",
				resultClass = Instructor.class
		),

		@NamedNativeQuery(
				name = "instructor.checkInstructorByProfileActivityId",
				query = "SELECT CASE WHEN EXISTS " +
						"(SELECT * " +
						"FROM instructor_activities ia, profile_activity pa " +
						"WHERE ia.activity_id = pa.activity_id " +
						"AND ia.instructor_id = ? " +
						"AND ia.activity_id = ? " +
						"AND pa.profile_id = ? " +
						"AND pa.enabled = ?) " +
						"THEN 'true' ELSE 'false' END flag FROM DUAL",
				resultClass = Boolean.class //
			//	resultSetMapping = "Boolean" // Optional: Define a result set mapping if needed

		),
		@NamedNativeQuery(
				name = "Instructor.checkUpdateInstructorAppointmentTime",
				query = """
        SELECT CASE WHEN EXISTS
          (SELECT *
          FROM appointment a
          WHERE (a.start_time BETWEEN TO_TIMESTAMP_TZ(?1, ?2) AND TO_TIMESTAMP_TZ(?3, ?4)
          OR a.end_time BETWEEN TO_TIMESTAMP_TZ(?5, ?6) AND TO_TIMESTAMP_TZ(?7, ?8)
          OR TO_TIMESTAMP_TZ(?9, ?10) BETWEEN a.start_time AND a.end_time
          OR TO_TIMESTAMP_TZ(?11, ?12) BETWEEN a.start_time AND a.end_time)
          AND a.canceled in (?13 , ?14) 
          AND a.instructor_id = ?15
          &REPLACECONDITION
          ) THEN 'false' ELSE 'true' END flag FROM DUAL
    """,
				resultClass = Boolean.class

		),

		@NamedNativeQuery(
				name = "instructor.checkUpdateInstructorAppointmentRecurringTime",
				query = "SELECT CASE WHEN EXISTS " +
						"(SELECT * " +
						"FROM appointment a " +
						"WHERE TO_CHAR(start_time,'d') = TO_CHAR(to_date(?1,?2),'d') " +
						"AND TO_TIMESTAMP_TZ(TO_CHAR(start_time, ?3), ?4) BETWEEN TO_TIMESTAMP_TZ(?5, ?6) AND TO_TIMESTAMP_TZ(?7, ?8) " +
						"AND (a.start_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?9)||' '||?10, ?11) " +
						"AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?12)||' '|| ?13, ?14) " +
						"OR a.end_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?15)||' '||?16, ?17) " +
						"AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time, ?18)||' '||?19, ?20) " +
						"OR TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?21)||' '||?22,?23) BETWEEN a.start_time AND a.end_time " +
						"OR TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?24)||' '||?25,?26) BETWEEN a.start_time AND a.end_time) " +
						"AND a.canceled IN (?27, ?28) " +
						"AND a.instructor_id = ?29 " +
						"&REPLACECONDITION " +
						") THEN 'false' ELSE 'true' END AS flag FROM DUAL",
				resultClass = String.class
		)



})
public class Instructor implements java.io.Serializable {

    /**
     * The job code for GCS employees that indicate they are an instructor.
     * 
     * @see EmployeeIntegrationServiceImpl
     */
    private static final Integer GCS_INSTRUCTOR_JOB_CODE = new Integer(1878);
    private static final Integer GCS_INSTRUCTOR_COORDINATOR_JOB_CODE =
        new Integer(2006);
    private static final Integer GCS_INSTRUCTOR_3016_JOB_CODE =
        new Integer(3016);
    private static final Integer GCS_LEAD_INSTRUCTOR_JOB_CODE =
        new Integer(3027);
    private static final Set<Integer> GCS_INSTRUCTOR_JOB_CODES =
        new HashSet<Integer>(Arrays.asList(GCS_INSTRUCTOR_JOB_CODE,
                                           GCS_INSTRUCTOR_COORDINATOR_JOB_CODE,
                                           GCS_INSTRUCTOR_3016_JOB_CODE,
                                           GCS_LEAD_INSTRUCTOR_JOB_CODE));
    
    private static final long	serialVersionUID	= 1778070834197028604L;
	private Long				instructorId;
	private long				version;
	private Person				updatedBy;
	private Availability		availability;
	private Location			location;
	private Person				person;
	private Date				updated;
	private String				status;
	private Enabled				enabled;
	private Site				site;
	private String				externalId;
	private String				externalSource;
	private Set<Activity>		activities			= new HashSet<Activity>(0);
	
	private Set<ServiceMode>		serviceMode			= new HashSet<ServiceMode>(0);
  

	


	

// changes made for GSSP-275

	public Instructor(Person person ,Site site) {
		super();
		this.person=person;
		this.site=site;
	}
//  GSSP-275 changes ends here


	public Instructor() {
		// TODO Auto-generated constructor stub
	}







	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "INSTRUCTOR_ID_SEQ")
	@Column(name = "INSTRUCTOR_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getInstructorId() {
		return this.instructorId;
	}



	public void setInstructorId(Long instructorId) {
		this.instructorId = instructorId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person personByUpdatedBy) {
		this.updatedBy = personByUpdatedBy;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "AVAILABILITY_ID", nullable = false)
	public Availability getAvailability() {
		return this.availability;
	}



	public void setAvailability(Availability availability) {
		this.availability = availability;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "LOCATION_ID", nullable = false)
	public Location getLocation() {
		return this.location;
	}



	public void setLocation(Location location) {
		this.location = location;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PERSON_ID", nullable = false)
	public Person getPerson() {
		return this.person;
	}



	public void setPerson(Person person) {
		this.person = person;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "STATUS", length = 512)
	public String getStatus() {
		return this.status;
	}



	public void setStatus(String status) {
		this.status = status;
	}


	//Changes made for GSSP-231
	@Column(name = "ENABLED", length = 1,insertable = false)
	@Enumerated(EnumType.STRING)
	public Enabled getEnabled() {
		return this.enabled;
	}



	public void setEnabled(Enabled enabled) {
		this.enabled = enabled;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}



	@Column(name = "EXTERNAL_SOURCE", length = 512)
	public String getExternalSource() {
		return this.externalSource;
	}



	public void setExternalSource(String externalSource) {
		this.externalSource = externalSource;
	}



	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "INSTRUCTOR_ACTIVITIES", joinColumns = { @JoinColumn(name = "INSTRUCTOR_ID", nullable = false, updatable = false) }, inverseJoinColumns = { @JoinColumn(name = "ACTIVITY_ID", nullable = false, updatable = false) })
	@Fetch(FetchMode.SUBSELECT)
	public Set<Activity> getActivities() {
		return this.activities;
	}



	public void setActivities(Set<Activity> activities) {
		this.activities = activities;
	}
	
	//---GSSP Instructor Mode update changes
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "INSTRUCTOR_MODE", joinColumns = { @JoinColumn(name = "INSTRUCTOR_ID") }, inverseJoinColumns = { @JoinColumn(name = "SERVICE_MODE_ID") })
	@Fetch(FetchMode.SUBSELECT)
	public Set<ServiceMode> getServiceMode() {
		return serviceMode;
	}


	public void setServiceMode(Set<ServiceMode> serviceMode) {
		this.serviceMode = serviceMode;
	}


	/**
	 * Tests the supplied job code to determine if it represents an employee
	 * that should be recognised as an instructor.
	 * 
	 * @param jobCode integer job code to test
	 * @return true if the job code is for an instructor, false otherwise
	 */
	public static boolean isInstructorJobCode(int jobCode) {
	    return GCS_INSTRUCTOR_JOB_CODES.contains(new Integer(jobCode));
	}

}