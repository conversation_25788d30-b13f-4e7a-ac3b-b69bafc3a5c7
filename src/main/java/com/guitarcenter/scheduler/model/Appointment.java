package com.guitarcenter.scheduler.model;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.*;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Formula;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.guitarcenter.scheduler.model.enums.Canceled;

/**
 * Represents appointments in the scheduler
 */
@Entity
@Table(name = "APPOINTMENT")
@SequenceGenerator(name = "APPOINTMENT_ID_SEQ", sequenceName = "APPOINTMENT_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@NamedNativeQueries({
		@NamedNativeQuery(
				name = "appointment.getAppointmentByRoomActivityProfile",
				query = "SELECT  a.APPOINTMENT_ID  FROM appointment a WHERE a.start_time BETWEEN sysdate AND add_months(sysdate,?) " +
						"AND a.room_id = ? AND a.activity_id = ? AND a.profile_id = ? AND a.canceled in (? , ?)",
				resultClass =BigDecimal.class
		),
		@NamedNativeQuery(
				name = "appointment.getAppointmentByRoomTemplateActivity",
				query = "SELECT {a.*} FROM appointment a, room r WHERE a.room_id = r.room_id " +
						"AND a.start_time BETWEEN sysdate AND add_months(sysdate,?) " +
						"AND r.room_template_id = ? AND a.activity_id = ?",
				resultClass = Appointment.class
		),
		@NamedNativeQuery(
				name = "appointment.getAppointmentByRoom",
				query = "SELECT a.APPOINTMENT_ID FROM appointment a WHERE a.start_time BETWEEN sysdate AND add_months(sysdate,?) " +
						"AND a.room_id = ? AND a.profile_id = ? AND a.canceled in (? , ?)",
				resultClass = BigDecimal.class
		),
		@NamedNativeQuery(
				name = "appointment.checkStartTime",
				query = "select case when TO_TIMESTAMP_TZ(?,?) < sysdate  then 'false' else 'true' end flag from dual",
				resultClass = Boolean.class
		),
		@NamedNativeQuery(
				name = "appointment.getAppointmentByProfile",
				query = "SELECT {a.*} FROM appointment a WHERE a.start_time BETWEEN sysdate AND add_months(sysdate,?) " +
						"AND a.profile_id = ? AND a.canceled in (? , ?)",
				resultClass = Appointment.class
		)
})

// 5055  changed named query  getAppointmentByRoomActivityProfile
public class Appointment implements java.io.Serializable {

	private static final long	serialVersionUID	= -3273105690730365834L;
	private Long				appointmentId;
	private long				version;
	private Instructor			instructor;
	private Site				site;
	private Person				updatedBy;
	private Room				room;
	private LocationProfile		locationProfile;
	private AppointmentSeries	appointmentSeries;
	private Activity			activity;
	private Date				updated;
	private String				externalId;
	private Date				startTime;
	private Date				endTime;
	private Date				createTime;
	private Canceled			canceled;
	private String				bandName;
	private String				note;
	private Set<Customer>		customers			= new HashSet<Customer>(0);
	private int					duration;
	//GSSP-249 Changes
	private Boolean				isUpdatedByCustomer;


	public Appointment() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "APPOINTMENT_ID_SEQ")
	@Column(name = "APPOINTMENT_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getAppointmentId() {
		return this.appointmentId;
	}



	public void setAppointmentId(Long appointmentId) {
		this.appointmentId = appointmentId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "INSTRUCTOR_ID")
	public Instructor getInstructor() {
		return this.instructor;
	}



	public void setInstructor(Instructor instructor) {
		this.instructor = instructor;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ROOM_ID", nullable = false)
	public Room getRoom() {
		return this.room;
	}



	public void setRoom(Room room) {
		this.room = room;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PROFILE_ID", nullable = false)
	public LocationProfile getLocationProfile() {
		return this.locationProfile;
	}



	public void setLocationProfile(LocationProfile locationProfile) {
		this.locationProfile = locationProfile;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "APPOINTMENT_SERIES_ID", nullable = false)
	public AppointmentSeries getAppointmentSeries() {
		return this.appointmentSeries;
	}



	public void setAppointmentSeries(AppointmentSeries appointmentSeries) {
		this.appointmentSeries = appointmentSeries;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ACTIVITY_ID", nullable = false)
	public Activity getActivity() {
		return this.activity;
	}



	public void setActivity(Activity activity) {
		this.activity = activity;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}



	@Column(name = "START_TIME", nullable = false)
	public Date getStartTime() {
		return this.startTime;
	}



	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}



	@Column(name = "END_TIME", nullable = false)
	public Date getEndTime() {
		return this.endTime;
	}



	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}



	@Column(name = "CREATE_TIME", nullable = false)
	public Date getCreateTime() {
		return this.createTime;
	}



	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}



	@Column(name = "CANCELED", length = 1)
	@Enumerated(EnumType.STRING)
	public Canceled getCanceled() {
		return this.canceled;
	}



	public void setCanceled(Canceled canceled) {
		this.canceled = canceled;
	}



	@Column(name = "BAND_NAME", length = 512)
	public String getBandName() {
		return this.bandName;
	}



	public void setBandName(String bandName) {
		this.bandName = bandName;
	}



	@Column(name = "NOTE", length = 512)
	public String getNote() {
		return this.note;
	}



	public void setNote(String note) {
		this.note = note;
	}



	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "APPOINTMENT_CUSTOMERS", joinColumns = { @JoinColumn(name = "APPOINTMENT_ID", nullable = false, updatable = false) }, inverseJoinColumns = { @JoinColumn(name = "CUSTOMER_ID", nullable = false, updatable = false) })
	public Set<Customer> getCustomers() {
		return this.customers;
	}



	public void setCustomers(Set<Customer> customers) {
		this.customers = customers;
	}



	@Formula(" (extract(day from ((END_TIME - START_TIME) * 24 * 60 * 60 * 60))) ")
	public int getDuration() {
		return (int) Math.round(duration / 60.0D / 60.0D);
	}



	public void setDuration(int duration) {
		this.duration = duration;
	}



	@Transient
	public String getStartTimeStr() {
		Calendar c = Calendar.getInstance();
		c.setTime(getStartTime());
		int month = c.get(Calendar.MONTH) + 1;
		int date = c.get(Calendar.DATE);
		String nyr = c.get(Calendar.YEAR) + "-" + (month < 10 ? ("0" + month) : month) + "-"
				+ (date < 10 ? ("0" + date) : date);
		String space = " ";
		int hour = c.get(Calendar.HOUR_OF_DAY);
		int minute = c.get(Calendar.MINUTE);
		String sfm = (hour < 10 ? ("0" + hour) : hour) + ":" + (minute < 10 ? ("0" + minute) : minute) + ":" + "00";
		return nyr + space + sfm;
	}



	@Transient
	public String getEndTimeStr() {
		Calendar c = Calendar.getInstance();
		c.setTime(getEndTime());
		int month = c.get(Calendar.MONTH) + 1;
		int date = c.get(Calendar.DATE);
		String nyr = c.get(Calendar.YEAR) + "-" + (month < 10 ? ("0" + month) : month) + "-"
				+ (date < 10 ? ("0" + date) : date);
		String space = " ";
		int hour = c.get(Calendar.HOUR_OF_DAY);
		int minute = c.get(Calendar.MINUTE);
		String sfm = (hour < 10 ? ("0" + hour) : hour) + ":" + (minute < 10 ? ("0" + minute) : minute) + ":" + "00";
		return nyr + space + sfm;
	}


	@Transient
	public Boolean getIsUpdatedByCustomer() {
		return isUpdatedByCustomer;
	}



	public void setIsUpdatedByCustomer(Boolean isUpdatedByCustomer) {
		this.isUpdatedByCustomer = isUpdatedByCustomer;
	}
	
	

}
