package com.guitarcenter.scheduler.model;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Most role associations will be to a specific location, but Site Admin role is
 * not restricted by location. Allow NULL in location_id for this reason
 */
@Entity
@Table(name = "PERSON_ROLE", uniqueConstraints = @UniqueConstraint(columnNames = { "PERSON_ID", "ROLE_ID",
		"LOCATION_ID" }))
@SequenceGenerator(name = "PERSON_ROLE_ID_SEQ", sequenceName = "PERSON_ROLE_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class PersonRole implements java.io.Serializable {

	private static final long	serialVersionUID	= -6818290075171383469L;
	private Long				personRoleId;
	private long				version;
	private Role				role;
	private Site				site;
	private Person				updatedBy;
	private Location			location;
	private Person				person;
	private Date				updated;



	public PersonRole() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PERSON_ROLE_ID_SEQ")
	@Column(name = "PERSON_ROLE_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getPersonRoleId() {
		return this.personRoleId;
	}



	public void setPersonRoleId(Long personRoleId) {
		this.personRoleId = personRoleId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ROLE_ID", nullable = false)
	public Role getRole() {
		return this.role;
	}



	public void setRole(Role role) {
		this.role = role;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person personByUpdatedBy) {
		this.updatedBy = personByUpdatedBy;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "LOCATION_ID")
	public Location getLocation() {
		return this.location;
	}



	public void setLocation(Location location) {
		this.location = location;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PERSON_ID", nullable = false)
	public Person getPerson() {
		return this.person;
	}



	public void setPerson(Person person) {
		this.person = person;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}
}
