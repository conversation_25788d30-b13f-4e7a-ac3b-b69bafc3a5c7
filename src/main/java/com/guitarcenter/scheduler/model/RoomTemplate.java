package com.guitarcenter.scheduler.model;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.SplitRoom;

/**
 * Represents a template created in centralised management that can be setup and
 * re-used by profiles
 */
@Entity
@Table(name = "ROOM_TEMPLATE")
@SequenceGenerator(name = "ROOM_TEMPLATE_ID_SEQ", sequenceName = "ROOM_TEMPLATE_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler"})
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@Tag(name = "Room", description = "APIs for managing RoomTemplate")
public class RoomTemplate implements java.io.Serializable {

	private static final long serialVersionUID = -8948150465817481392L;
	private Long roomTemplateId;
	private long version;
	private RoomSize roomSize;
	private Site site;
	private RoomType roomType;
	private Person updatedBy;
	private Date updated;
	private String roomTemplateName;
	private SplitRoom isSplitRoom;
	private Enabled enabled;
	private String externalId;
	private Set<Activity> activities = new HashSet<>(0);
	private Set<Service> services = new HashSet<>(0);

	public RoomTemplate() {
	}

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ROOM_TEMPLATE_ID_SEQ")
	@Column(name = "ROOM_TEMPLATE_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getRoomTemplateId() {
		return this.roomTemplateId;
	}

	public void setRoomTemplateId(Long roomTemplateId) {
		this.roomTemplateId = roomTemplateId;
	}

	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}

	public void setVersion(long version) {
		this.version = version;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ROOM_SIZE_ID")
	@JsonIgnore
	public RoomSize getRoomSize() {
		return this.roomSize;
	}

	public void setRoomSize(RoomSize roomSize) {
		this.roomSize = roomSize;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	@JsonIgnore
	public Site getSite() {
		return this.site;
	}

	public void setSite(Site site) {
		this.site = site;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ROOM_TYPE_ID", nullable = false)
	@JsonIgnore
	public RoomType getRoomType() {
		return this.roomType;
	}

	public void setRoomType(RoomType roomType) {
		this.roomType = roomType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	@JsonIgnore
	public Person getUpdatedBy() {
		return this.updatedBy;
	}

	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}

	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}

	public void setUpdated(Date updated) {
		this.updated = updated;
	}

	@Column(name = "ROOM_TEMPLATE_NAME", nullable = false, length = 512)
	public String getRoomTemplateName() {
		return this.roomTemplateName;
	}

	public void setRoomTemplateName(String roomTemplateName) {
		this.roomTemplateName = roomTemplateName;
	}

	@Column(name = "IS_SPLIT_ROOM", length = 1)
	@Enumerated(EnumType.STRING)
	public SplitRoom getIsSplitRoom() {
		return this.isSplitRoom;
	}

	public void setIsSplitRoom(SplitRoom isSplitRoom) {
		this.isSplitRoom = isSplitRoom;
	}

	@Column(name = "ENABLED", length = 1)
	@Enumerated(EnumType.STRING)
	public Enabled getEnabled() {
		return this.enabled;
	}

	public void setEnabled(Enabled enabled) {
		this.enabled = enabled;
	}

	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}

	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}

	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(
			name = "ROOM_TEMPLATE_ACTIVITIES",
			joinColumns = @JoinColumn(name = "ROOM_TEMPLATE_ID", nullable = false, updatable = true,insertable = true),
			inverseJoinColumns = @JoinColumn(name = "ACTIVITY_ID", nullable = false, updatable = true,insertable = true)
	)
	@Fetch(FetchMode.SUBSELECT)
	@JsonIgnore
	public Set<Activity> getActivities() {
		return this.activities;
	}

	public void setActivities(Set<Activity> activities) {
		this.activities = activities;
	}

	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(
			name = "ROOM_TEMPLATE_SERVICES",
			joinColumns = @JoinColumn(name = "ROOM_TEMPLATE_ID", nullable = false, updatable = true, insertable = true),
			inverseJoinColumns = @JoinColumn(name = "SERVICE_ID", nullable = false, updatable = true,insertable = true)
	)
	@Fetch(FetchMode.SUBSELECT)
	@JsonIgnore
	public Set<Service> getServices() {
		return this.services;
	}

	public void setServices(Set<Service> services) {
		this.services = services;
	}
}
