package com.guitarcenter.scheduler.model;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 

 * represents the person personal details.
 *
 */
@Entity
@Table(name = "PERSON_PERSONAL_DETAILS")
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
//New entity created for GSSP-275
public class PersonPersonalDetails implements java.io.Serializable {

	private static final long	serialVersionUID	= -3273105690730360020L;
	private Long				personId;
	private long				version;
	private Date				updated;
	private Site 				site;
	private Person				updatedBy;
	private String 				personalEmail;
	private String 				externalSource;
	
	@Id
	@Column(name = "PERSON_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getPersonId() {
		return personId;
	}
	public void setPersonId(Long personId) {
		this.personId = personId;
	}
	
	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return version;
	}
	public void setVersion(long version) {
		this.version = version;
	}
	
	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return updated;
	}
	public void setUpdated(Date updated) {
		this.updated = updated;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return site;
	}
	public void setSite(Site site) {
		this.site = site;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return updatedBy;
	}
	public void setUpdatedBy(Person updatedBy) {
		this.updatedBy = updatedBy;
	}
	
	@Column(name = "PERSONAL_EMAIL", length = 512)
	public String getPersonalEmail() {
		return personalEmail;
	}
	public void setPersonalEmail(String personalEmail) {
		this.personalEmail = personalEmail;
	}
	
	@Column(name = "EXTERNAL_SOURCE", length = 512)
	public String getExternalSource() {
		return externalSource;
	}
	public void setExternalSource(String externalSource) {
		this.externalSource = externalSource;
	}
	
	
	
}
