package com.guitarcenter.scheduler.model;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

/**
 * Represents appointments in the scheduler
 */
@Entity
@Table(name = "APPOINTMENT_CANCEL")
@JsonAutoDetect
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class AppointmentCancel implements java.io.Serializable {

	private static final long	serialVersionUID	= -3273105690730365834L;
	private Long				appointmentId;
	private AppointmentCancelReason appointmentCancelReason;
	

	public AppointmentCancel() {
	}

	@Id	
	@Column(name = "APPOINTMENT_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getAppointmentId() {
		return this.appointmentId;
	}



	public void setAppointmentId(Long appointmentId) {
		this.appointmentId = appointmentId;
	}


	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "APPOINTMENT_CANCEL_REASON_ID", nullable = false)
	public AppointmentCancelReason getAppointmentCancelReason() {
		return this.appointmentCancelReason;
	}



	public void setAppointmentCancelReason(AppointmentCancelReason appointmentCancelReason) {
		this.appointmentCancelReason = appointmentCancelReason;
	}


}
