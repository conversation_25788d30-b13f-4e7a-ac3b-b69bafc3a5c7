package com.guitarcenter.scheduler.model;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Represents a Location, as supplied by EDW
 */
@Entity
@Table(name = "LOCATION")
@SequenceGenerator(name = "LOCATION_ID_SEQ", sequenceName = "LOCATION_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class Location implements java.io.Serializable {

	//private static final long	serialVersionUID	= -8294925395190658793L;
	private Long				locationId;
	private long				version;
	private Site				site;
	private Person				updatedBy;
	private String				externalId;
	private String				externalSource;
	private Date				updated;
	private String				locationName;
	private String				address1;
	private String				address2;
	private String				city;
	private String				state;
	private String				zip;
	private String				country;
	private String				phone;
	private String				fax;
	private LocationProfile		locationProfile;



	public Location() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LOCATION_ID_SEQ")
	@Column(name = "LOCATION_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getLocationId() {
		return this.locationId;
	}



	public void setLocationId(Long locationId) {
		this.locationId = locationId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}



	@Column(name = "EXTERNAL_SOURCE", length = 512)
	public String getExternalSource() {
		return this.externalSource;
	}



	public void setExternalSource(String externalSource) {
		this.externalSource = externalSource;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "LOCATION_NAME", nullable = false, length = 512)
	public String getLocationName() {
		return this.locationName;
	}



	public void setLocationName(String locationName) {
		this.locationName = locationName;
	}



	@Column(name = "ADDRESS_1", nullable = false, length = 512)
	public String getAddress1() {
		return this.address1;
	}



	public void setAddress1(String address1) {
		this.address1 = address1;
	}



	@Column(name = "ADDRESS_2", length = 512)
	public String getAddress2() {
		return this.address2;
	}



	public void setAddress2(String address2) {
		this.address2 = address2;
	}



	@Column(name = "CITY", nullable = false, length = 512)
	public String getCity() {
		return this.city;
	}



	public void setCity(String city) {
		this.city = city;
	}



	@Column(name = "STATE", nullable = false, length = 512)
	public String getState() {
		return this.state;
	}



	public void setState(String state) {
		this.state = state;
	}



	@Column(name = "ZIP", nullable = false, length = 512)
	public String getZip() {
		return this.zip;
	}



	public void setZip(String zip) {
		this.zip = zip;
	}



	@Column(name = "COUNTRY", nullable = false, length = 512)
	public String getCountry() {
		return this.country;
	}



	public void setCountry(String country) {
		this.country = country;
	}



	@Column(name = "PHONE", length = 512)
	public String getPhone() {
		return this.phone;
	}



	public void setPhone(String phone) {
		this.phone = phone;
	}



	@Column(name = "FAX", length = 512)
	public String getFax() {
		return this.fax;
	}



	public void setFax(String fax) {
		this.fax = fax;
	}



	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PROFILE_ID")
	public LocationProfile getLocationProfile() {
		return this.locationProfile;
	}



	public void setLocationProfile(LocationProfile locationProfile) {
		this.locationProfile = locationProfile;
	}



	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37).append(locationId).toHashCode();
	}



	@Override
	public boolean equals(Object obj) {
		boolean flag = false;
		if (obj != null && Location.class.isAssignableFrom(obj.getClass())) {
			Location f = (Location) obj;
			flag = new EqualsBuilder().append(locationId, f.getLocationId()).isEquals();
		}
		return flag;
	}

}
