package com.guitarcenter.scheduler.model;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Impose a standardised naming scheme on profile rooms
 */
@Entity
@Table(name = "ROOM_NUMBER")
@SequenceGenerator(name = "ROOM_NUMBER_ID_SEQ", sequenceName = "ROOM_NUMBER_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler"})
public class RoomNumber implements java.io.Serializable {

	private static final long	serialVersionUID	= 8758143736003249584L;
	private Long				roomNumberId;
	private long				version;
	private Site				site;
	private Person				updatedBy;
	private Date				updated;
	private String				roomNumber;
	private String				externalId;



	public RoomNumber() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ROOM_NUMBER_ID_SEQ")
	@Column(name = "ROOM_NUMBER_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getRoomNumberId() {
		return this.roomNumberId;
	}



	public void setRoomNumberId(Long roomNumberId) {
		this.roomNumberId = roomNumberId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	@JsonIgnore
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	@JsonIgnore
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "ROOM_NUMBER", nullable = false, length = 512)
	public String getRoomNumber() {
		return this.roomNumber;
	}



	public void setRoomNumber(String roomNumber) {
		this.roomNumber = roomNumber;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}

}
