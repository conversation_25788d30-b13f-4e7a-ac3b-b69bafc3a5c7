package com.guitarcenter.scheduler.model.dto;

import java.util.Date;

public class InstructorAppointmentBusinessHours {

	private Date currentDate;
	private Long instructorId;
	private Date minimumTime;
	private Date maximumTime;
	private String firstName;
	private String lastName;
	private String startDateStr;
	private String startTime;
	private String endTime;
	// for pdf file
	private String fullName;
	private String scheduleTime;

	/**
	 * @return the scheduleTime
	 */
	public String getScheduleTime() {
		return scheduleTime;
	}

	/**
	 * @param scheduleTime
	 *            the scheduleTime to set
	 */
	public void setScheduleTime(String scheduleTime) {
		this.scheduleTime = scheduleTime;
	}

	/**
	 * @return the fullName
	 */
	public String getFullName() {
		return fullName;
	}

	/**
	 * @param fullName
	 *            the fullName to set
	 */
	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getStartDateStr() {
		return startDateStr;
	}

	public void setStartDateStr(String startDateStr) {
		this.startDateStr = startDateStr;
	}

	public Date getCurrentDate() {
		return currentDate;
	}

	public void setCurrentDate(Date pCurrentDate) {
		currentDate = pCurrentDate;
	}

	public Long getInstructorId() {
		return instructorId;
	}

	public void setInstructorId(Long pInstructorId) {
		instructorId = pInstructorId;
	}

	public Date getMinimumTime() {
		return minimumTime;
	}

	public void setMinimumTime(Date pMinimumTime) {
		minimumTime = pMinimumTime;
	}

	public Date getMaximumTime() {
		return maximumTime;
	}

	public void setMaximumTime(Date pMaximumTime) {
		maximumTime = pMaximumTime;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String pFirstName) {
		firstName = pFirstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String pLastName) {
		lastName = pLastName;
	}

}
