package com.guitarcenter.scheduler.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;



	@Entity
	@Table(name = "APPOINTMENT_TRANSACTIONS")
	@JsonAutoDetect
	@JsonIgnoreProperties({ "updatedBy"})
	@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
	public class AppointmentTransactions implements java.io.Serializable {

		
		private static final long serialVersionUID = 1L;
		
		private Long				appointmentId;		
		private String				transactionId;	
		private String				email;
		private String				phone;

public AppointmentTransactions(){
	
}

@Id
@Column(name = "APPOINTMENT_ID", nullable = false, precision = 22, scale = 0)
public Long getAppointmentId() {
	return appointmentId;
}

public void setAppointmentId(Long appointmentId) {
	this.appointmentId = appointmentId;
}

@Column(name = "TRANSACTION_ID", length = 256)
public String getTransactionId() {
	return transactionId;
}
public void setTransactionId(String transactionId) {
	this.transactionId = transactionId;
}

@Column(name = "EMAIL", length = 256)
public String getEmail() {
	return email;
}
public void setEmail(String email) {
	this.email = email;
}

@Column(name = "PHONE", length = 256)
public String getPhone() {
	return phone;
}

public void setPhone(String phone) {
	this.phone = phone;
}

}
