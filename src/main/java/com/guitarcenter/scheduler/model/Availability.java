package com.guitarcenter.scheduler.model;

import java.util.Date;
import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.joda.time.DateTimeConstants;

/**
 * Provides availability for related entities
 */
@Entity
@Table(name = "AVAILABILITY")
@SequenceGenerator(name = "AVAILABILITY_ID_SEQ", sequenceName = "AVAILABILITY_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class Availability implements java.io.Serializable {

	private static final long	serialVersionUID	= 6553481074225642599L;
	private Long				availabilityId;
	private long				version;
	private Site				site;
	private Person				updatedBy;
	private Date				updated;
	private Date				mondayStartTime;
	private Date				mondayEndTime;
	private Date				tuesdayStartTime;
	private Date				tuesdayEndTime;
	private Date				wednesdayStartTime;
	private Date				wednesdayEndTime;
	private Date				thursdayStartTime;
	private Date				thursdayEndTime;
	private Date				fridayStartTime;
	private Date				fridayEndTime;
	private Date				saturdayStartTime;
	private Date				saturdayEndTime;
	private Date				sundayStartTime;
	private Date				sundayEndTime;
	private String				externalId;



	public Availability() {
	}



	public Availability(Long availabilityId,Date mondayStartTime, Date mondayEndTime, Date tuesdayStartTime, Date tuesdayEndTime, Date wednesdayStartTime, Date wednesdayEndTime,
			Date thursdayStartTime, Date thursdayEndTime, Date fridayStartTime, Date fridayEndTime, Date saturdayStartTime, Date saturdayEndTime, Date sundayStartTime, Date sundayEndTime) {
		super();
		this.availabilityId = availabilityId;
		this.mondayStartTime = mondayStartTime;
		this.mondayEndTime = mondayEndTime;
		this.tuesdayStartTime = tuesdayStartTime;
		this.tuesdayEndTime = tuesdayEndTime;
		this.wednesdayStartTime = wednesdayStartTime;
		this.wednesdayEndTime = wednesdayEndTime;
		this.thursdayStartTime = thursdayStartTime;		
		this.thursdayEndTime = thursdayEndTime;
		this.fridayStartTime = fridayStartTime;
		this.fridayEndTime = fridayEndTime;
		this. saturdayStartTime= saturdayStartTime;
		this.saturdayEndTime = saturdayEndTime;		
		this.sundayStartTime = sundayStartTime;
		this.sundayEndTime = sundayEndTime;
		
		
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "AVAILABILITY_ID_SEQ")
	@Column(name = "AVAILABILITY_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getAvailabilityId() {
		return this.availabilityId;
	}



	public void setAvailabilityId(Long availabilityId) {
		this.availabilityId = availabilityId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "MONDAY_START_TIME")
	public Date getMondayStartTime() {
		return this.mondayStartTime;
	}



	public void setMondayStartTime(Date mondayStartTime) {
		this.mondayStartTime = mondayStartTime;
	}



	@Column(name = "MONDAY_END_TIME")
	public Date getMondayEndTime() {
		return this.mondayEndTime;
	}



	public void setMondayEndTime(Date mondayEndTime) {
		this.mondayEndTime = mondayEndTime;
	}



	@Column(name = "TUESDAY_START_TIME")
	public Date getTuesdayStartTime() {
		return this.tuesdayStartTime;
	}



	public void setTuesdayStartTime(Date tuesdayStartTime) {
		this.tuesdayStartTime = tuesdayStartTime;
	}



	@Column(name = "TUESDAY_END_TIME")
	public Date getTuesdayEndTime() {
		return this.tuesdayEndTime;
	}



	public void setTuesdayEndTime(Date tuesdayEndTime) {
		this.tuesdayEndTime = tuesdayEndTime;
	}



	@Column(name = "WEDNESDAY_START_TIME")
	public Date getWednesdayStartTime() {
		return this.wednesdayStartTime;
	}



	public void setWednesdayStartTime(Date wednesdayStartTime) {
		this.wednesdayStartTime = wednesdayStartTime;
	}



	@Column(name = "WEDNESDAY_END_TIME")
	public Date getWednesdayEndTime() {
		return this.wednesdayEndTime;
	}



	public void setWednesdayEndTime(Date wednesdayEndTime) {
		this.wednesdayEndTime = wednesdayEndTime;
	}



	@Column(name = "THURSDAY_START_TIME")
	public Date getThursdayStartTime() {
		return this.thursdayStartTime;
	}



	public void setThursdayStartTime(Date thursdayStartTime) {
		this.thursdayStartTime = thursdayStartTime;
	}



	@Column(name = "THURSDAY_END_TIME")
	public Date getThursdayEndTime() {
		return this.thursdayEndTime;
	}



	public void setThursdayEndTime(Date thursdayEndTime) {
		this.thursdayEndTime = thursdayEndTime;
	}



	@Column(name = "FRIDAY_START_TIME")
	public Date getFridayStartTime() {
		return this.fridayStartTime;
	}



	public void setFridayStartTime(Date fridayStartTime) {
		this.fridayStartTime = fridayStartTime;
	}



	@Column(name = "FRIDAY_END_TIME")
	public Date getFridayEndTime() {
		return this.fridayEndTime;
	}



	public void setFridayEndTime(Date fridayEndTime) {
		this.fridayEndTime = fridayEndTime;
	}



	@Column(name = "SATURDAY_START_TIME")
	public Date getSaturdayStartTime() {
		return this.saturdayStartTime;
	}



	public void setSaturdayStartTime(Date saturdayStartTime) {
		this.saturdayStartTime = saturdayStartTime;
	}



	@Column(name = "SATURDAY_END_TIME")
	public Date getSaturdayEndTime() {
		return this.saturdayEndTime;
	}



	public void setSaturdayEndTime(Date saturdayEndTime) {
		this.saturdayEndTime = saturdayEndTime;
	}



	@Column(name = "SUNDAY_START_TIME")
	public Date getSundayStartTime() {
		return this.sundayStartTime;
	}



	public void setSundayStartTime(Date sundayStartTime) {
		this.sundayStartTime = sundayStartTime;
	}



	@Column(name = "SUNDAY_END_TIME")
	public Date getSundayEndTime() {
		return this.sundayEndTime;
	}



	public void setSundayEndTime(Date sundayEndTime) {
		this.sundayEndTime = sundayEndTime;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}


    public Date getStartTimeByDayOfWeek(int dayOfWeek){
        Date result = null;
        switch (dayOfWeek) {
            case DateTimeConstants.MONDAY:
                result = getMondayStartTime();
                break;
            case DateTimeConstants.TUESDAY:
                result = getTuesdayStartTime();
                break;
            case DateTimeConstants.WEDNESDAY:
                result = getWednesdayStartTime();
                break;
            case DateTimeConstants.THURSDAY:
                result = getThursdayStartTime();
                break;
            case DateTimeConstants.FRIDAY:
                result = getFridayStartTime();
                break;
            case DateTimeConstants.SATURDAY:
                result = getSaturdayStartTime();
                break;
            case DateTimeConstants.SUNDAY:
                result = getSundayStartTime();
                break;
        }
        return result;
    }

    public Date getEndTimeByDayOfWeek(int dayOfWeek){
        Date result = null;
        switch (dayOfWeek) {
            case DateTimeConstants.MONDAY:
                result = getMondayEndTime();
                break;
            case DateTimeConstants.TUESDAY:
                result = getTuesdayEndTime();
                break;
            case DateTimeConstants.WEDNESDAY:
                result = getWednesdayEndTime();
                break;
            case DateTimeConstants.THURSDAY:
                result = getThursdayEndTime();
                break;
            case DateTimeConstants.FRIDAY:
                result = getFridayEndTime();
                break;
            case DateTimeConstants.SATURDAY:
                result = getSaturdayEndTime();
                break;
            case DateTimeConstants.SUNDAY:
                result = getSundayEndTime();
                break;
        }
        return result;
    }

}
