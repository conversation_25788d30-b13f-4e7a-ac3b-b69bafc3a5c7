/**
 * @Title: Onetime.java
 * @Package com.guitarcenter.scheduler.model
 * @Description:
 * Copyright: Copyright (c) 2014 
 * Company:
 *
 * <AUTHOR>
 * @date May 29, 2014 1:52:32 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.model;

import java.util.Date;

import jakarta.persistence.*;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * @ClassName: Onetime
 * @Description:
 * <AUTHOR>
 * @date May 29, 2014 1:52:32 PM
 *
 */
@Entity
@Table(name = "ONETIME")
@SequenceGenerator(name = "ONETIME_ID_SEQ", sequenceName = "ONETIME_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SqlResultSetMapping(
		name = "BooleanResult",
		columns = @ColumnResult(name = "flag", type = String.class)
)
@NamedNativeQueries({
		@NamedNativeQuery(
				name = "onetime.checkOnetimeAvailabilityByProfileId",
				query = "SELECT CASE WHEN EXISTS (WITH a AS " +
						"(SELECT availability_id, 2 week_day, monday_start_time start_time, monday_end_time end_time FROM availability UNION " +
						"SELECT availability_id, 3, TUESDAY_START_TIME , TUESDAY_end_TIME FROM availability UNION " +
						"SELECT availability_id, 4, WEDNESDAY_START_TIME , WEDNESDAY_end_TIME FROM availability UNION " +
						"SELECT availability_id, 5, THURSDAY_START_TIME , THURSDAY_end_TIME FROM availability UNION " +
						"SELECT availability_id, 6, FRIDAY_START_TIME , FRIDAY_end_TIME FROM availability UNION " +
						"SELECT availability_id, 7, SATURDAY_START_TIME , SATURDAY_end_TIME FROM availability UNION " +
						"SELECT availability_id, 1, SUNDAY_START_TIME , SUNDAY_end_TIME FROM availability) " +
						"SELECT a.start_time, a.end_time, a.week_day FROM a, location_profile l WHERE a.availability_id = l.availability_id " +
						"AND a.week_day = TO_CHAR(TO_DATE(?, ?), 'd') " +
						"AND TO_TIMESTAMP_TZ(?, ?) BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time, ?), ?) " +
						"AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time, ?), ?) " +
						"AND TO_TIMESTAMP_TZ(?,?) BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time, ?), ?) " +
						"AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time, ?), ?) " +
						"AND l.profile_id = ?) " +
						"THEN 'true' ELSE 'false' END AS flag FROM DUAL",
				resultClass = Boolean.class
		),
		@NamedNativeQuery(
				name = "onetime.getOnetimeByTime",
				query = "SELECT * FROM onetime t WHERE " +
						"TO_TIMESTAMP_TZ(:startTime1, :timezone1) BETWEEN t.start_time AND t.end_time " +
						"AND TO_TIMESTAMP_TZ(:startTime2, :timezone2) BETWEEN t.start_time AND t.end_time " +
						"AND t.instructor_id = :instructorId",
				resultClass = Onetime.class
		),
		@NamedNativeQuery(
				name = "onetime.checkOnetimeByTime",
				query = "SELECT t.* FROM onetime t WHERE " +
						"(t.start_time BETWEEN TO_TIMESTAMP_TZ(?, ?) AND TO_TIMESTAMP_TZ(?, ?) " +
						"OR t.end_time BETWEEN TO_TIMESTAMP_TZ(?, ?) AND TO_TIMESTAMP_TZ(?, ?) " +
						"OR TO_TIMESTAMP_TZ(?, ?) BETWEEN t.start_time AND t.end_time " +
						"OR TO_TIMESTAMP_TZ(?, ?) BETWEEN t.start_time AND t.end_time) " +
						"AND t.instructor_id = ? ",
				resultClass = Onetime.class
		)
})
public class Onetime {

	@SuppressWarnings("unused")
	private static final long	serialVersionUID	= 2123445809734350913L;
	private Long				onetimeId;
	private Instructor			instructor;
	private Date				startTime;
	private Date				endTime;
	private long				version;
	private Person				updatedBy;
	private Date				updated;

	/**
	 * Onetime.
	 * <p>Title: </p>
	 * <p>Description: </p>
	 */
	public Onetime() {
	}

	/**
	 * getter method
	 * @return the onetimeId
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ONETIME_ID_SEQ")
	@Column(name = "ONETIME_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getOnetimeId() {
		return onetimeId;
	}
	/**
	 * setter method
	 * @param onetimeId the onetimeId to set
	 */
	public void setOnetimeId(Long onetimeId) {
		this.onetimeId = onetimeId;
	}
	/**
	 * getter method
	 * @return the instructor
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "INSTRUCTOR_ID", nullable = false)
	public Instructor getInstructor() {
		return instructor;
	}
	/**
	 * setter method
	 * @param instructor the instructor to set
	 */
	public void setInstructor(Instructor instructor) {
		this.instructor = instructor;
	}
	/**
	 * getter method
	 * @return the startTime
	 */
	@Column(name = "START_TIME", nullable = false)
	public Date getStartTime() {
		return startTime;
	}
	/**
	 * setter method
	 * @param startTime the startTime to set
	 */
	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}
	/**
	 * getter method
	 * @return the endTime
	 */
	@Column(name = "END_TIME", nullable = false)
	public Date getEndTime() {
		return endTime;
	}
	/**
	 * setter method
	 * @param endTime the endTime to set
	 */
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	/**
	 * getter method
	 * @return the version
	 */
	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return version;
	}
	/**
	 * setter method
	 * @param version the version to set
	 */
	public void setVersion(long version) {
		this.version = version;
	}
	/**
	 * getter method
	 * @return the updatedBy
	 */

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return updatedBy;
	}
	/**
	 * setter method
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(Person updatedBy) {
		this.updatedBy = updatedBy;
	}
	/**
	 * getter method
	 * @return the updated
	 */
	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return updated;
	}
	/**
	 * setter method
	 * @param updated the updated to set
	 */
	public void setUpdated(Date updated) {
		this.updated = updated;
	}
}
