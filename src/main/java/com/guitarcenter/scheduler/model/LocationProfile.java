package com.guitarcenter.scheduler.model;

import java.util.Date;

import jakarta.persistence.*;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.guitarcenter.scheduler.model.enums.Enabled;

/**
 * Represents a Location Profile
 */
@Entity
@Table(name = "LOCATION_PROFILE")
@SequenceGenerator(name = "LOCATION_PROFILE_ID_SEQ", sequenceName = "LOCATION_PROFILE_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@NamedNativeQueries({
		@NamedNativeQuery(
				name = "locationProfile.checkLocationProfileTime",
				query = "WITH t AS (SELECT availability_id, 2 week_day, monday_start_time start_time, monday_end_time end_time FROM availability " +
						"UNION SELECT availability_id, 3, TUESDAY_START_TIME , TUESDAY_end_TIME FROM availability " +
						"UNION SELECT availability_id, 4, WEDNESDAY_START_TIME , WEDNESDAY_end_TIME FROM availability " +
						"UNION SELECT availability_id, 5, THURSDAY_START_TIME , THURSDAY_end_TIME FROM availability " +
						"UNION SELECT availability_id, 6, FRIDAY_START_TIME , FRIDAY_end_TIME FROM availability " +
						"UNION SELECT availability_id, 7, SATURDAY_START_TIME , SATURDAY_end_TIME FROM availability " +
						"UNION SELECT availability_id, 1, SUNDAY_START_TIME , SUNDAY_end_TIME FROM availability) " +
						"SELECT l.* FROM t, location_profile l WHERE t.availability_id = l.availability_id " +
						"AND t.week_day = TO_CHAR(to_date(?,?),'d') " +
						"AND TO_TIMESTAMP_TZ(?, ?) BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(t.start_time,?),?) AND TO_TIMESTAMP_TZ(TO_CHAR(t.end_time,?),?) " +
						"AND TO_TIMESTAMP_TZ(?, ?) BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(t.start_time,?),?) AND TO_TIMESTAMP_TZ(TO_CHAR(t.end_time,?),?) " +
						"AND l.profile_id = ?",
				resultClass = LocationProfile.class
		)
})
public class LocationProfile implements java.io.Serializable {

	private static final long	serialVersionUID	= -4969443001972286978L;
	private Long				profileId;
	private long				version;
	private Site				site;
	private Person				updatedBy;
	private Availability		availability;
	private String				externalId;
	private Date				updated;
	private Enabled				enabled;
	private String				timeZone;



	public LocationProfile() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LOCATION_PROFILE_ID_SEQ")
	@Column(name = "PROFILE_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getProfileId() {
		return this.profileId;
	}



	public void setProfileId(Long profileId) {
		this.profileId = profileId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "AVAILABILITY_ID", nullable = false)
	public Availability getAvailability() {
		return this.availability;
	}



	public void setAvailability(Availability availability) {
		this.availability = availability;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "ENABLED", length = 1)
	@Enumerated(EnumType.STRING)
	public Enabled getEnabled() {
		return this.enabled;
	}



	public void setEnabled(Enabled enabled) {
		this.enabled = enabled;
	}



	@Column(name = "TZ", nullable = false, length = 512)
	public String getTimeZone() {
		return this.timeZone;
	}



	public void setTimeZone(String timeZone) {
		this.timeZone = timeZone;
	}

}
