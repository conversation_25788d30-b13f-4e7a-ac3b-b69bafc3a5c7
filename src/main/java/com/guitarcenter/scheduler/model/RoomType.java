package com.guitarcenter.scheduler.model;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.SplitRoom;

/**
 * Impose a standard set of rooms for profiles
 */
@Entity
@Table(name = "ROOM_TYPE")
@SequenceGenerator(name = "ROOM_TYPE_ID_SEQ", sequenceName = "ROOM_TYPE_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler" })
@Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
public class RoomType implements java.io.Serializable {

	private static final long	serialVersionUID	= -2712439106562472985L;
	private Long				roomTypeId;
	private long				version;
	private Site				site;
	private Person				updatedBy;
	private Date				updated;
	private String				roomType;
	private SplitRoom			canSplitRoom;
	private Enabled				enabled;
	private String				externalId;



	public RoomType() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ROOM_TYPE_ID_SEQ")
	@Column(name = "ROOM_TYPE_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getRoomTypeId() {
		return this.roomTypeId;
	}



	public void setRoomTypeId(Long roomTypeId) {
		this.roomTypeId = roomTypeId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "ROOM_TYPE", nullable = false, length = 512)
	public String getRoomType() {
		return this.roomType;
	}



	public void setRoomType(String roomType) {
		this.roomType = roomType;
	}



	@Column(name = "CAN_SPLIT_ROOM", nullable = false, length = 1)
	@Enumerated(EnumType.STRING)
	public SplitRoom getCanSplitRoom() {
		return this.canSplitRoom;
	}



	public void setCanSplitRoom(SplitRoom canSplitRoom) {
		this.canSplitRoom = canSplitRoom;
	}



	@Column(name = "ENABLED", length = 1)
	@Enumerated(EnumType.STRING)
	public Enabled getEnabled() {
		return this.enabled;
	}



	public void setEnabled(Enabled enabled) {
		this.enabled = enabled;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}

}
