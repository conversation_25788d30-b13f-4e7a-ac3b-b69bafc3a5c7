package com.guitarcenter.scheduler.model;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Represents an individual person in the system; the person may also be a
 * customer, employee or instructor
 */
@Entity
@Table(name = "PERSON")
@SequenceGenerator(name = "PERSON_ID_SEQ", sequenceName = "PERSON_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated","hibernateLazyInitializer", "handler" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class Person implements java.io.Serializable {

	private static final long	serialVersionUID	= 630363237392565324L;
	private Long				personId;
	private long				version;
	private Person				updatedBy;
	private Date				updated;
	private String				externalId;
	private String				firstName;
	private String				lastName;
	private String				email;
	private String				phone;
    private String              authId;



	public Person() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PERSON_ID_SEQ")
	@Column(name = "PERSON_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getPersonId() {
		return this.personId;
	}



	public void setPersonId(Long personId) {
		this.personId = personId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}



	@Column(name = "FIRST_NAME", length = 512)
	public String getFirstName() {
		return this.firstName;
	}



	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}



	@Column(name = "LAST_NAME", length = 512)
	public String getLastName() {
		return this.lastName;
	}



	public void setLastName(String lastName) {
		this.lastName = lastName;
	}



	@Column(name = "EMAIL", length = 512)
	public String getEmail() {
		return this.email;
	}



	public void setEmail(String email) {
		this.email = email;
	}



	@Column(name = "PHONE", length = 512)
	public String getPhone() {
		return this.phone;
	}



	public void setPhone(String phone) {
		this.phone = phone;
	}



    @Column(name = "AUTH_ID", length = 512)
    public String getAuthId() {
        return this.authId;
    }



    public void setAuthId(String authId) {
        this.authId = authId;
    }

}
