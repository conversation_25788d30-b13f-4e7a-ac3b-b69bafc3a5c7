package com.guitarcenter.scheduler.data.repository;

import com.guitarcenter.scheduler.model.Activity;
import org.springframework.data.jpa.repository.JpaRepository;


import java.util.List;
import java.util.Optional;

public interface ActivityRepository extends JpaRepository<Activity, Long> {

    // Custom query method to find activities by name
    List<Activity> findByActivityName(String activityName);

    // You can add other custom queries as needed
}
