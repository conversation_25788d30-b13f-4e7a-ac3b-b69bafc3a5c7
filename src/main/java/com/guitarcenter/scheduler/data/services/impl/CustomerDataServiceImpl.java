package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.CustomerRepository;
import com.guitarcenter.scheduler.data.services.CustomerDataService;
import com.guitarcenter.scheduler.model.Customer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the CustomerDataService interface.
 * Provides methods to perform CRUD operations on Customer entities.
 */
@Service("customerDataService")
public class CustomerDataServiceImpl implements CustomerDataService {

    private final CustomerRepository customerRepository;

    @Autowired
    public CustomerDataServiceImpl(CustomerRepository customerRepository) {
        this.customerRepository = customerRepository;
    }

    @Override
    public Customer createCustomer(Customer customer) {
        customer.setUpdated(new Date());
        return customerRepository.save(customer);
    }

    @Override
    public Optional<Customer> getCustomerById(Long customerId) {
        return customerRepository.findById(customerId);
    }

    @Override
    public List<Customer> getAllCustomers() {
        return customerRepository.findAll();
    }

    @Override
    public Customer updateCustomer(Long customerId, Customer customer) {
        if (customerRepository.existsById(customerId)) {
            customer.setCustomerId(customerId);
            customer.setUpdated(new Date());
            return customerRepository.save(customer);
        } else {
            throw new RuntimeException("Customer not found");
        }
    }

    @Override
    public void deleteCustomer(Long customerId) {
        customerRepository.deleteById(customerId);
    }
}