package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.AvailabilityRepository;
import com.guitarcenter.scheduler.data.services.AvailabilityDataService;
import com.guitarcenter.scheduler.model.Availability;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the AvailabilityDataService interface.
 * Provides methods to perform CRUD operations on Availability entities.
 */
@Service("availabilityDataService")
public class AvailabilityDataServiceImpl implements AvailabilityDataService {

    private final AvailabilityRepository availabilityRepository;

    @Autowired
    public AvailabilityDataServiceImpl(AvailabilityRepository availabilityRepository) {
        this.availabilityRepository = availabilityRepository;
    }

    @Override
    public Availability createAvailability(Availability availability) {
        availability.setUpdated(new Date());
        return availabilityRepository.save(availability);
    }

    @Override
    public Optional<Availability> getAvailabilityById(Long availabilityId) {
        return availabilityRepository.findById(availabilityId);
    }

    @Override
    public List<Availability> getAllAvailabilities() {
        return availabilityRepository.findAll();
    }

    @Override
    public Availability updateAvailability(Long availabilityId, Availability availability) {
        if (availabilityRepository.existsById(availabilityId)) {
            availability.setAvailabilityId(availabilityId);
            availability.setUpdated(new Date());
            return availabilityRepository.save(availability);
        } else {
            throw new RuntimeException("Availability not found");
        }
    }

    @Override
    public void deleteAvailability(Long availabilityId) {
        availabilityRepository.deleteById(availabilityId);
    }
}