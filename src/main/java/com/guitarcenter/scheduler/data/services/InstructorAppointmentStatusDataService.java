package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.InstructorAppointmentStatus;

import java.util.List;
import java.util.Optional;

public interface InstructorAppointmentStatusDataService {
    InstructorAppointmentStatus createInstructorAppointmentStatus(InstructorAppointmentStatus instructorAppointmentStatus);

    Optional<InstructorAppointmentStatus> getInstructorAppointmentStatusById(Long appointmentId);

    List<InstructorAppointmentStatus> getAllInstructorAppointmentStatuses();

    InstructorAppointmentStatus updateInstructorAppointmentStatus(Long appointmentId, InstructorAppointmentStatus instructorAppointmentStatus);

    void deleteInstructorAppointmentStatus(Long appointmentId);
}