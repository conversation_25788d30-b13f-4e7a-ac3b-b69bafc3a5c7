package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.ProfileService;

import java.util.List;
import java.util.Optional;

public interface ProfileServiceDataService {
    ProfileService createProfileService(ProfileService profileService);

    Optional<ProfileService> getProfileServiceById(Long profileServiceId);

    List<ProfileService> getAllProfileServices();

    ProfileService updateProfileService(Long profileServiceId, ProfileService profileService);

    void deleteProfileService(Long profileServiceId);
}