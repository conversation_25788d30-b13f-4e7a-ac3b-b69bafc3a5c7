package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.InstructorRepository;
import com.guitarcenter.scheduler.data.services.InstructorDataService;
import com.guitarcenter.scheduler.model.Instructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the InstructorDataService interface.
 * Provides methods to perform CRUD operations on Instructor entities.
 */
@Service("instructorDataService")
public class InstructorDataServiceImpl implements InstructorDataService {

    private final InstructorRepository instructorRepository;

    @Autowired
    public InstructorDataServiceImpl(InstructorRepository instructorRepository) {
        this.instructorRepository = instructorRepository;
    }

    @Override
    public Instructor createInstructor(Instructor instructor) {
        instructor.setUpdated(new Date());
        return instructorRepository.save(instructor);
    }

    @Override
    public Optional<Instructor> getInstructorById(Long instructorId) {
        return instructorRepository.findById(instructorId);
    }

    @Override
    public List<Instructor> getAllInstructors() {
        return instructorRepository.findAll();
    }

    @Override
    public Instructor updateInstructor(Long instructorId, Instructor instructor) {
        if (instructorRepository.existsById(instructorId)) {
            instructor.setInstructorId(instructorId);
            instructor.setUpdated(new Date());
            return instructorRepository.save(instructor);
        } else {
            throw new RuntimeException("Instructor not found");
        }
    }

    @Override
    public void deleteInstructor(Long instructorId) {
        instructorRepository.deleteById(instructorId);
    }
}