package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.Availability;

import java.util.List;
import java.util.Optional;

public interface AvailabilityDataService {
    Availability createAvailability(Availability availability);

    Optional<Availability> getAvailabilityById(Long availabilityId);

    List<Availability> getAllAvailabilities();

    Availability updateAvailability(Long availabilityId, Availability availability);

    void deleteAvailability(Long availabilityId);
}