package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.RoomTypeRepository;
import com.guitarcenter.scheduler.data.services.RoomTypeDataService;
import com.guitarcenter.scheduler.model.RoomType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the RoomTypeDataService interface.
 * Provides methods to perform CRUD operations on RoomType entities.
 */
@Service("roomTypeDataService")
public class RoomTypeDataServiceImpl implements RoomTypeDataService {

    private final RoomTypeRepository roomTypeRepository;

    @Autowired
    public RoomTypeDataServiceImpl(RoomTypeRepository roomTypeRepository) {
        this.roomTypeRepository = roomTypeRepository;
    }

    @Override
    public RoomType createRoomType(RoomType roomType) {
        roomType.setUpdated(new Date());
        return roomTypeRepository.save(roomType);
    }

    @Override
    public Optional<RoomType> getRoomTypeById(Long roomTypeId) {
        return roomTypeRepository.findById(roomTypeId);
    }

    @Override
    public List<RoomType> getAllRoomTypes() {
        return roomTypeRepository.findAll();
    }

    @Override
    public RoomType updateRoomType(Long roomTypeId, RoomType roomType) {
        if (roomTypeRepository.existsById(roomTypeId)) {
            roomType.setRoomTypeId(roomTypeId);
            roomType.setUpdated(new Date());
            return roomTypeRepository.save(roomType);
        } else {
            throw new RuntimeException("RoomType not found");
        }
    }

    @Override
    public void deleteRoomType(Long roomTypeId) {
        roomTypeRepository.deleteById(roomTypeId);
    }
}