package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.RoomNumber;

import java.util.List;
import java.util.Optional;

public interface RoomNumberDataService {
    RoomNumber createRoomNumber(RoomNumber roomNumber);

    Optional<RoomNumber> getRoomNumberById(Long roomNumberId);

    List<RoomNumber> getAllRoomNumbers();

    RoomNumber updateRoomNumber(Long roomNumberId, RoomNumber roomNumber);

    void deleteRoomNumber(Long roomNumberId);
}