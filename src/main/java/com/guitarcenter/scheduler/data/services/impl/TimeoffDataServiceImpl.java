package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.TimeoffRepository;
import com.guitarcenter.scheduler.data.services.TimeoffDataService;
import com.guitarcenter.scheduler.model.Timeoff;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the TimeoffDataService interface.
 * Provides methods to perform CRUD operations on Timeoff entities.
 */
@Service("timeoffDataService")
public class TimeoffDataServiceImpl implements TimeoffDataService {

    private final TimeoffRepository timeoffRepository;

    @Autowired
    public TimeoffDataServiceImpl(TimeoffRepository timeoffRepository) {
        this.timeoffRepository = timeoffRepository;
    }

    @Override
    public Timeoff createTimeoff(Timeoff timeoff) {
        timeoff.setUpdated(new Date());
        return timeoffRepository.save(timeoff);
    }

    @Override
    public Optional<Timeoff> getTimeoffById(Long timeoffId) {
        return timeoffRepository.findById(timeoffId);
    }

    @Override
    public List<Timeoff> getAllTimeoffs() {
        return timeoffRepository.findAll();
    }

    @Override
    public Timeoff updateTimeoff(Long timeoffId, Timeoff timeoff) {
        if (timeoffRepository.existsById(timeoffId)) {
            timeoff.setTimeoffId(timeoffId);
            timeoff.setUpdated(new Date());
            return timeoffRepository.save(timeoff);
        } else {
            throw new RuntimeException("Timeoff not found");
        }
    }

    @Override
    public void deleteTimeoff(Long timeoffId) {
        timeoffRepository.deleteById(timeoffId);
    }
}