package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.ProfileActivityRepository;
import com.guitarcenter.scheduler.data.services.ProfileActivityDataService;
import com.guitarcenter.scheduler.model.ProfileActivity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the ProfileActivityDataService interface.
 * Provides methods to perform CRUD operations on ProfileActivity entities.
 */
@Service("profileActivityDataService")
public class ProfileActivityDataServiceImpl implements ProfileActivityDataService {

    private final ProfileActivityRepository profileActivityRepository;

    @Autowired
    public ProfileActivityDataServiceImpl(ProfileActivityRepository profileActivityRepository) {
        this.profileActivityRepository = profileActivityRepository;
    }

    @Override
    public ProfileActivity createProfileActivity(ProfileActivity profileActivity) {
        profileActivity.setUpdated(new Date());
        return profileActivityRepository.save(profileActivity);
    }

    @Override
    public Optional<ProfileActivity> getProfileActivityById(Long profileActivityId) {
        return profileActivityRepository.findById(profileActivityId);
    }

    @Override
    public List<ProfileActivity> getAllProfileActivities() {
        return profileActivityRepository.findAll();
    }

    @Override
    public ProfileActivity updateProfileActivity(Long profileActivityId, ProfileActivity profileActivity) {
        if (profileActivityRepository.existsById(profileActivityId)) {
            profileActivity.setProfileActivityId(profileActivityId);
            profileActivity.setUpdated(new Date());
            return profileActivityRepository.save(profileActivity);
        } else {
            throw new RuntimeException("ProfileActivity not found");
        }
    }

    @Override
    public void deleteProfileActivity(Long profileActivityId) {
        profileActivityRepository.deleteById(profileActivityId);
    }
}