package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.SiteRepository;
import com.guitarcenter.scheduler.data.services.SiteDataService;
import com.guitarcenter.scheduler.model.Site;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service("siteDataService")
public class SiteDataServiceImpl implements SiteDataService {

    private final SiteRepository siteRepository;

    @Autowired
    public SiteDataServiceImpl(SiteRepository siteRepository) {
        this.siteRepository = siteRepository;
    }

    @Override
    public Site createSite(Site site) {
        site.setUpdated(new Date());
        return siteRepository.save(site);
    }

    @Override
    public Optional<Site> getSiteById(Long siteId) {
        return siteRepository.findById(siteId);
    }

    @Override
    public List<Site> getAllSites() {
        return siteRepository.findAll();
    }

    @Override
    public Site updateSite(Long siteId, Site site) {
        if (siteRepository.existsById(siteId)) {
            site.setSiteId(siteId);
            site.setUpdated(new Date());
            return siteRepository.save(site);
        } else {
            throw new RuntimeException("Site not found");
        }
    }

    @Override
    public void deleteSite(Long siteId) {
        siteRepository.deleteById(siteId);
    }
}