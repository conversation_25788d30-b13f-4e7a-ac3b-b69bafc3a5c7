package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.LocationRepository;
import com.guitarcenter.scheduler.data.services.LocationDataService;
import com.guitarcenter.scheduler.model.Location;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the LocationDataService interface.
 * Provides methods to perform CRUD operations on Location entities.
 */
@Service("locationDataService")
public class LocationDataServiceImpl implements LocationDataService {

    private final LocationRepository locationRepository;

    @Autowired
    public LocationDataServiceImpl(LocationRepository locationRepository) {
        this.locationRepository = locationRepository;
    }

    @Override
    public Location createLocation(Location location) {
        location.setUpdated(new Date());
        return locationRepository.save(location);
    }

    @Override
    public Optional<Location> getLocationById(Long locationId) {
        return locationRepository.findById(locationId);
    }

    @Override
    public List<Location> getAllLocations() {
        return locationRepository.findAll();
    }

    @Override
    public Location updateLocation(Long locationId, Location location) {
        if (locationRepository.existsById(locationId)) {
            location.setLocationId(locationId);
            location.setUpdated(new Date());
            return locationRepository.save(location);
        } else {
            throw new RuntimeException("Location not found");
        }
    }

    @Override
    public void deleteLocation(Long locationId) {
        locationRepository.deleteById(locationId);
    }
}