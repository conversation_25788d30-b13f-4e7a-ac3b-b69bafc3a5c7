package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.Location;

import java.util.List;
import java.util.Optional;

public interface LocationDataService {
    Location createLocation(Location location);

    Optional<Location> getLocationById(Long locationId);

    List<Location> getAllLocations();

    Location updateLocation(Long locationId, Location location);

    void deleteLocation(Long locationId);
}