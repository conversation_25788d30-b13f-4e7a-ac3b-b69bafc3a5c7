package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.ProfileServiceRepository;
import com.guitarcenter.scheduler.data.services.ProfileServiceDataService;
import com.guitarcenter.scheduler.model.ProfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the ProfileServiceDataService interface.
 * Provides methods to perform CRUD operations on ProfileService entities.
 */
@Service("profileServiceDataService")
public class ProfileServiceDataServiceImpl implements ProfileServiceDataService {

    private final ProfileServiceRepository profileServiceRepository;

    @Autowired
    public ProfileServiceDataServiceImpl(ProfileServiceRepository profileServiceRepository) {
        this.profileServiceRepository = profileServiceRepository;
    }

    @Override
    public ProfileService createProfileService(ProfileService profileService) {
        profileService.setUpdated(new Date());
        return profileServiceRepository.save(profileService);
    }

    @Override
    public Optional<ProfileService> getProfileServiceById(Long profileServiceId) {
        return profileServiceRepository.findById(profileServiceId);
    }

    @Override
    public List<ProfileService> getAllProfileServices() {
        return profileServiceRepository.findAll();
    }

    @Override
    public ProfileService updateProfileService(Long profileServiceId, ProfileService profileService) {
        if (profileServiceRepository.existsById(profileServiceId)) {
            profileService.setProfileServiceId(profileServiceId);
            profileService.setUpdated(new Date());
            return profileServiceRepository.save(profileService);
        } else {
            throw new RuntimeException("ProfileService not found");
        }
    }

    @Override
    public void deleteProfileService(Long profileServiceId) {
        profileServiceRepository.deleteById(profileServiceId);
    }
}