package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.RoomRepository;
import com.guitarcenter.scheduler.data.services.RoomDataService;
import com.guitarcenter.scheduler.model.Room;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the RoomDataService interface.
 * Provides methods to perform CRUD operations on Room entities.
 */
@Service("roomDataService")
public class RoomDataServiceImpl implements RoomDataService {

    private final RoomRepository roomRepository;

    @Autowired
    public RoomDataServiceImpl(RoomRepository roomRepository) {
        this.roomRepository = roomRepository;
    }

    @Override
    public Room createRoom(Room room) {
        room.setUpdated(new Date());
        return roomRepository.save(room);
    }

    @Override
    public Optional<Room> getRoomById(Long roomId) {
        return roomRepository.findById(roomId);
    }

    @Override
    public List<Room> getAllRooms() {
        return roomRepository.findAll();
    }

    @Override
    public Room updateRoom(Long roomId, Room room) {
        if (roomRepository.existsById(roomId)) {
            room.setRoomId(roomId);
            room.setUpdated(new Date());
            return roomRepository.save(room);
        } else {
            throw new RuntimeException("Room not found");
        }
    }

    @Override
    public void deleteRoom(Long roomId) {
        roomRepository.deleteById(roomId);
    }
}