package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.RoomSizeRepository;
import com.guitarcenter.scheduler.data.services.RoomSizeDataService;
import com.guitarcenter.scheduler.model.RoomSize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the RoomSizeDataService interface.
 * Provides methods to perform CRUD operations on RoomSize entities.
 */
@Service("roomSizeDataService")
public class RoomSizeDataServiceImpl implements RoomSizeDataService {

    private final RoomSizeRepository roomSizeRepository;

    @Autowired
    public RoomSizeDataServiceImpl(RoomSizeRepository roomSizeRepository) {
        this.roomSizeRepository = roomSizeRepository;
    }

    @Override
    public RoomSize createRoomSize(RoomSize roomSize) {
        roomSize.setUpdated(new Date());
        return roomSizeRepository.save(roomSize);
    }

    @Override
    public Optional<RoomSize> getRoomSizeById(Long roomSizeId) {
        return roomSizeRepository.findById(roomSizeId);
    }

    @Override
    public List<RoomSize> getAllRoomSizes() {
        return roomSizeRepository.findAll();
    }

    @Override
    public RoomSize updateRoomSize(Long roomSizeId, RoomSize roomSize) {
        if (roomSizeRepository.existsById(roomSizeId)) {
            roomSize.setRoomSizeId(roomSizeId);
            roomSize.setUpdated(new Date());
            return roomSizeRepository.save(roomSize);
        } else {
            throw new RuntimeException("RoomSize not found");
        }
    }

    @Override
    public void deleteRoomSize(Long roomSizeId) {
        roomSizeRepository.deleteById(roomSizeId);
    }
}