package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.ServiceRepository;
import com.guitarcenter.scheduler.data.services.ServiceDataService;
import com.guitarcenter.scheduler.model.Service;
import org.springframework.beans.factory.annotation.Autowired;


import java.util.List;
import java.util.Optional;

@org.springframework.stereotype.Service("serviceDataService")
public class ServiceDataServiceImpl implements ServiceDataService {

    private final ServiceRepository serviceRepository;

    @Autowired
    public ServiceDataServiceImpl(ServiceRepository serviceRepository) {
        this.serviceRepository = serviceRepository;
    }

    @Override
    public Service createService(Service service) {
        return serviceRepository.save(service);
    }

    @Override
    public Optional<Service> getServiceById(Long serviceId) {
        return serviceRepository.findById(serviceId);
    }

    @Override
    public List<Service> getAllServices() {
        return serviceRepository.findAll();
    }

    @Override
    public Service updateService(Long serviceId, Service service) {
        if (serviceRepository.existsById(serviceId)) {
            service.setServiceId(serviceId);
            return serviceRepository.save(service);
        } else {
            throw new RuntimeException("Service not found");
        }
    }

    @Override
    public void deleteService(Long serviceId) {
        serviceRepository.deleteById(serviceId);
    }
}