package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.LocationProfileRepository;
import com.guitarcenter.scheduler.data.services.LocationProfileDataService;
import com.guitarcenter.scheduler.model.LocationProfile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the LocationProfileDataService interface.
 * Provides methods to perform CRUD operations on LocationProfile entities.
 */
@Service("locationProfileDataService")
public class LocationProfileDataServiceImpl implements LocationProfileDataService {

    private final LocationProfileRepository locationProfileRepository;

    @Autowired
    public LocationProfileDataServiceImpl(LocationProfileRepository locationProfileRepository) {
        this.locationProfileRepository = locationProfileRepository;
    }

    @Override
    public LocationProfile createLocationProfile(LocationProfile locationProfile) {
        locationProfile.setUpdated(new Date());
        return locationProfileRepository.save(locationProfile);
    }

    @Override
    public Optional<LocationProfile> getLocationProfileById(Long locationProfileId) {
        return locationProfileRepository.findById(locationProfileId);
    }

    @Override
    public List<LocationProfile> getAllLocationProfiles() {
        return locationProfileRepository.findAll();
    }

    @Override
    public LocationProfile updateLocationProfile(Long locationProfileId, LocationProfile locationProfile) {
        if (locationProfileRepository.existsById(locationProfileId)) {
            locationProfile.setProfileId(locationProfileId);
            locationProfile.setUpdated(new Date());
            return locationProfileRepository.save(locationProfile);
        } else {
            throw new RuntimeException("LocationProfile not found");
        }
    }

    @Override
    public void deleteLocationProfile(Long locationProfileId) {
        locationProfileRepository.deleteById(locationProfileId);
    }
}