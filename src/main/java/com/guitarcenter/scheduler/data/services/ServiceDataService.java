package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.Service;

import java.util.List;
import java.util.Optional;

public interface ServiceDataService {
    Service createService(Service service);

    Optional<Service> getServiceById(Long serviceId);

    List<Service> getAllServices();

    Service updateService(Long serviceId, Service service);

    void deleteService(Long serviceId);
}