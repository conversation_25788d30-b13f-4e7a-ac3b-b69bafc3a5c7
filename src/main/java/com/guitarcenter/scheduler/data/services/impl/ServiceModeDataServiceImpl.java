package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.ServiceModeRepository;
import com.guitarcenter.scheduler.data.services.ServiceModeDataService;
import com.guitarcenter.scheduler.model.ServiceMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service("serviceModeDataService")
public class ServiceModeDataServiceImpl implements ServiceModeDataService {

    private final ServiceModeRepository serviceModeRepository;

    @Autowired
    public ServiceModeDataServiceImpl(ServiceModeRepository serviceModeRepository) {
        this.serviceModeRepository = serviceModeRepository;
    }

    @Override
    public ServiceMode createServiceMode(ServiceMode serviceMode) {
        return serviceModeRepository.save(serviceMode);
    }

    @Override
    public Optional<ServiceMode> getServiceModeById(Long serviceModeId) {
        return serviceModeRepository.findById(serviceModeId);
    }

    @Override
    public List<ServiceMode> getAllServiceModes() {
        return serviceModeRepository.findAll();
    }

    @Override
    public ServiceMode updateServiceMode(Long serviceModeId, ServiceMode serviceMode) {
        if (serviceModeRepository.existsById(serviceModeId)) {
            serviceMode.setServiceModeId(serviceModeId);
            return serviceModeRepository.save(serviceMode);
        } else {
            throw new RuntimeException("ServiceMode not found");
        }
    }

    @Override
    public void deleteServiceMode(Long serviceModeId) {
        serviceModeRepository.deleteById(serviceModeId);
    }
}