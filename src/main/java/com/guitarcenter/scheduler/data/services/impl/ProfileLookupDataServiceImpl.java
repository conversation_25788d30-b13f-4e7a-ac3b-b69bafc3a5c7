package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.ProfileLookupRepository;
import com.guitarcenter.scheduler.data.services.ProfileLookupDataService;
import com.guitarcenter.scheduler.model.ProfileLookup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the ProfileLookupDataService interface.
 * Provides methods to perform CRUD operations on ProfileLookup entities.
 */
@Service("profileLookupDataService")
public class ProfileLookupDataServiceImpl implements ProfileLookupDataService {

    private final ProfileLookupRepository profileLookupRepository;

    @Autowired
    public ProfileLookupDataServiceImpl(ProfileLookupRepository profileLookupRepository) {
        this.profileLookupRepository = profileLookupRepository;
    }

    @Override
    public ProfileLookup createProfileLookup(ProfileLookup profileLookup) {
      //  profileLookup.setUpdated(new Date());
        return profileLookupRepository.save(profileLookup);
    }

    @Override
    public Optional<ProfileLookup> getProfileLookupById(Long profileLookupId) {
        return profileLookupRepository.findById(profileLookupId);
    }

    @Override
    public List<ProfileLookup> getAllProfileLookups() {
        return profileLookupRepository.findAll();
    }

    @Override
    public ProfileLookup updateProfileLookup(Long profileLookupId, ProfileLookup profileLookup) {
        if (profileLookupRepository.existsById(profileLookupId)) {
            profileLookup.setActivityId(profileLookupId);
        //    profileLookup.setUpdated(new Date());
            return profileLookupRepository.save(profileLookup);
        } else {
            throw new RuntimeException("ProfileLookup not found");
        }
    }

    @Override
    public void deleteProfileLookup(Long profileLookupId) {
        profileLookupRepository.deleteById(profileLookupId);
    }
}