package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.CustomerStatus;

import java.util.List;
import java.util.Optional;

public interface CustomerStatusDataService {
    CustomerStatus createCustomerStatus(CustomerStatus customerStatus);

    Optional<CustomerStatus> getCustomerStatusById(Long customerStatusId);

    List<CustomerStatus> getAllCustomerStatuses();

    CustomerStatus updateCustomerStatus(Long customerStatusId, CustomerStatus customerStatus);

    void deleteCustomerStatus(Long customerStatusId);
}