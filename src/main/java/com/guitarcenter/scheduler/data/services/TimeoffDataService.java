package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.Timeoff;

import java.util.List;
import java.util.Optional;

public interface TimeoffDataService {
    Timeoff createTimeoff(Timeoff timeoff);

    Optional<Timeoff> getTimeoffById(Long timeoffId);

    List<Timeoff> getAllTimeoffs();

    Timeoff updateTimeoff(Long timeoffId, Timeoff timeoff);

    void deleteTimeoff(Long timeoffId);
}