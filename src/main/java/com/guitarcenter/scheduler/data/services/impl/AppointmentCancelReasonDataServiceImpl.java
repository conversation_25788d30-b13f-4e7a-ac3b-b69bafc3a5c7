package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.AppointmentCancelReasonRepository;
import com.guitarcenter.scheduler.data.services.AppointmentCancelReasonDataService;
import com.guitarcenter.scheduler.model.AppointmentCancelReason;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the AppointmentCancelReasonDataService interface.
 * Provides methods to perform CRUD operations on AppointmentCancelReason entities.
 */
@Service("appointmentCancelReasonDataService")
public class AppointmentCancelReasonDataServiceImpl implements AppointmentCancelReasonDataService {

    private final AppointmentCancelReasonRepository appointmentCancelReasonRepository;

    @Autowired
    public AppointmentCancelReasonDataServiceImpl(AppointmentCancelReasonRepository appointmentCancelReasonRepository) {
        this.appointmentCancelReasonRepository = appointmentCancelReasonRepository;
    }

    @Override
    public AppointmentCancelReason createAppointmentCancelReason(AppointmentCancelReason appointmentCancelReason) {
        appointmentCancelReason.setUpdated(new Date());
        return appointmentCancelReasonRepository.save(appointmentCancelReason);
    }

    @Override
    public Optional<AppointmentCancelReason> getAppointmentCancelReasonById(Long appointmentCancelReasonId) {
        return appointmentCancelReasonRepository.findById(appointmentCancelReasonId);
    }

    @Override
    public List<AppointmentCancelReason> getAllAppointmentCancelReasons() {
        return appointmentCancelReasonRepository.findAll();
    }

    @Override
    public AppointmentCancelReason updateAppointmentCancelReason(Long appointmentCancelReasonId, AppointmentCancelReason appointmentCancelReason) {
        if (appointmentCancelReasonRepository.existsById(appointmentCancelReasonId)) {
            appointmentCancelReason.setAppointmentcancelreasonID(appointmentCancelReasonId);
            appointmentCancelReason.setUpdated(new Date());
            return appointmentCancelReasonRepository.save(appointmentCancelReason);
        } else {
            throw new RuntimeException("AppointmentCancelReason not found");
        }
    }

    @Override
    public void deleteAppointmentCancelReason(Long appointmentCancelReasonId) {
        appointmentCancelReasonRepository.deleteById(appointmentCancelReasonId);
    }
}