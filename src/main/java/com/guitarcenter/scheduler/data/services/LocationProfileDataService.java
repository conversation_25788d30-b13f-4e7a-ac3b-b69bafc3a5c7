package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.LocationProfile;

import java.util.List;
import java.util.Optional;

public interface LocationProfileDataService {
    LocationProfile createLocationProfile(LocationProfile locationProfile);

    Optional<LocationProfile> getLocationProfileById(Long locationProfileId);

    List<LocationProfile> getAllLocationProfiles();

    LocationProfile updateLocationProfile(Long locationProfileId, LocationProfile locationProfile);

    void deleteLocationProfile(Long locationProfileId);
}