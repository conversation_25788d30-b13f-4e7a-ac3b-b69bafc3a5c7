package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.RoomSize;

import java.util.List;
import java.util.Optional;

public interface RoomSizeDataService {
    RoomSize createRoomSize(RoomSize roomSize);

    Optional<RoomSize> getRoomSizeById(Long roomSizeId);

    List<RoomSize> getAllRoomSizes();

    RoomSize updateRoomSize(Long roomSizeId, RoomSize roomSize);

    void deleteRoomSize(Long roomSizeId);
}