package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.RoomNumberRepository;
import com.guitarcenter.scheduler.data.services.RoomNumberDataService;
import com.guitarcenter.scheduler.model.RoomNumber;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the RoomNumberDataService interface.
 * Provides methods to perform CRUD operations on RoomNumber entities.
 */
@Service("roomNumberDataService")
public class RoomNumberDataServiceImpl implements RoomNumberDataService {

    private final RoomNumberRepository roomNumberRepository;

    @Autowired
    public RoomNumberDataServiceImpl(RoomNumberRepository roomNumberRepository) {
        this.roomNumberRepository = roomNumberRepository;
    }

    @Override
    public RoomNumber createRoomNumber(RoomNumber roomNumber) {
        roomNumber.setUpdated(new Date());
        return roomNumberRepository.save(roomNumber);
    }

    @Override
    public Optional<RoomNumber> getRoomNumberById(Long roomNumberId) {
        return roomNumberRepository.findById(roomNumberId);
    }

    @Override
    public List<RoomNumber> getAllRoomNumbers() {
        return roomNumberRepository.findAll();
    }

    @Override
    public RoomNumber updateRoomNumber(Long roomNumberId, RoomNumber roomNumber) {
        if (roomNumberRepository.existsById(roomNumberId)) {
            roomNumber.setRoomNumberId(roomNumberId);
            roomNumber.setUpdated(new Date());
            return roomNumberRepository.save(roomNumber);
        } else {
            throw new RuntimeException("RoomNumber not found");
        }
    }

    @Override
    public void deleteRoomNumber(Long roomNumberId) {
        roomNumberRepository.deleteById(roomNumberId);
    }
}