package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.ProfileLookup;

import java.util.List;
import java.util.Optional;

public interface ProfileLookupDataService {
    ProfileLookup createProfileLookup(ProfileLookup profileLookup);

    Optional<ProfileLookup> getProfileLookupById(Long profileLookupId);

    List<ProfileLookup> getAllProfileLookups();

    ProfileLookup updateProfileLookup(Long profileLookupId, ProfileLookup profileLookup);

    void deleteProfileLookup(Long profileLookupId);
}