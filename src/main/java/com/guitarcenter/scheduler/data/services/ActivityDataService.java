package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.Activity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

public interface ActivityDataService {

    // CRUD operations
    Activity createActivity(Activity activity);

    Optional<Activity> getActivityById(Long activityId);

    List<Activity> getAllActivities();

    List<Activity> getActivitiesByName(String activityName);

    Activity updateActivity(Long activityId, Activity activity);

    void deleteActivity(Long activityId);
}
