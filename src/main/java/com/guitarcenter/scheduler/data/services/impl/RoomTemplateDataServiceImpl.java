package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.RoomTemplateRepository;
import com.guitarcenter.scheduler.data.services.RoomTemplateDataService;
import com.guitarcenter.scheduler.model.RoomTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the RoomTemplateDataService interface.
 * Provides methods to perform CRUD operations on RoomTemplate entities.
 */
@Service("roomTemplateDataService")
public class RoomTemplateDataServiceImpl implements RoomTemplateDataService {

    private final RoomTemplateRepository roomTemplateRepository;

    @Autowired
    public RoomTemplateDataServiceImpl(RoomTemplateRepository roomTemplateRepository) {
        this.roomTemplateRepository = roomTemplateRepository;
    }

    @Override
    public RoomTemplate createRoomTemplate(RoomTemplate roomTemplate) {
        roomTemplate.setUpdated(new Date());
        return roomTemplateRepository.save(roomTemplate);
    }

    @Override
    public Optional<RoomTemplate> getRoomTemplateById(Long roomTemplateId) {
        return roomTemplateRepository.findById(roomTemplateId);
    }

    @Override
    public List<RoomTemplate> getAllRoomTemplates() {
        return roomTemplateRepository.findAll();
    }

    @Override
    public RoomTemplate updateRoomTemplate(Long roomTemplateId, RoomTemplate roomTemplate) {
        if (roomTemplateRepository.existsById(roomTemplateId)) {
            roomTemplate.setRoomTemplateId(roomTemplateId);
            roomTemplate.setUpdated(new Date());
            return roomTemplateRepository.save(roomTemplate);
        } else {
            throw new RuntimeException("RoomTemplate not found");
        }
    }

    @Override
    public void deleteRoomTemplate(Long roomTemplateId) {
        roomTemplateRepository.deleteById(roomTemplateId);
    }
}