package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.CustomerStatusRepository;
import com.guitarcenter.scheduler.data.services.CustomerStatusDataService;
import com.guitarcenter.scheduler.model.CustomerStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the CustomerStatusDataService interface.
 * Provides methods to perform CRUD operations on CustomerStatus entities.
 */
@Service("customerStatusDataService")
public class CustomerStatusDataServiceImpl implements CustomerStatusDataService {

    private final CustomerStatusRepository customerStatusRepository;

    @Autowired
    public CustomerStatusDataServiceImpl(CustomerStatusRepository customerStatusRepository) {
        this.customerStatusRepository = customerStatusRepository;
    }

    @Override
    public CustomerStatus createCustomerStatus(CustomerStatus customerStatus) {
        customerStatus.setUpdated(new Date());
        return customerStatusRepository.save(customerStatus);
    }

    @Override
    public Optional<CustomerStatus> getCustomerStatusById(Long customerStatusId) {
        return customerStatusRepository.findById(customerStatusId);
    }

    @Override
    public List<CustomerStatus> getAllCustomerStatuses() {
        return customerStatusRepository.findAll();
    }

    @Override
    public CustomerStatus updateCustomerStatus(Long customerStatusId, CustomerStatus customerStatus) {
        if (customerStatusRepository.existsById(customerStatusId)) {
            customerStatus.setCustomerStatusId(customerStatusId);
            customerStatus.setUpdated(new Date());
            return customerStatusRepository.save(customerStatus);
        } else {
            throw new RuntimeException("CustomerStatus not found");
        }
    }

    @Override
    public void deleteCustomerStatus(Long customerStatusId) {
        customerStatusRepository.deleteById(customerStatusId);
    }
}