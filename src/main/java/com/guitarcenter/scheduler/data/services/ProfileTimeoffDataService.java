package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.ProfileTimeoff;

import java.util.List;
import java.util.Optional;

public interface ProfileTimeoffDataService {
    ProfileTimeoff createProfileTimeoff(ProfileTimeoff profileTimeoff);

    Optional<ProfileTimeoff> getProfileTimeoffById(Long profileTimeoffId);

    List<ProfileTimeoff> getAllProfileTimeoffs();

    ProfileTimeoff updateProfileTimeoff(Long profileTimeoffId, ProfileTimeoff profileTimeoff);

    void deleteProfileTimeoff(Long profileTimeoffId);
}