package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.Customer;

import java.util.List;
import java.util.Optional;

public interface CustomerDataService {
    Customer createCustomer(Customer customer);

    Optional<Customer> getCustomerById(Long customerId);

    List<Customer> getAllCustomers();

    Customer updateCustomer(Long customerId, Customer customer);

    void deleteCustomer(Long customerId);
}