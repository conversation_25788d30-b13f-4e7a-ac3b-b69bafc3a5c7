package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.Instructor;

import java.util.List;
import java.util.Optional;

public interface InstructorDataService {
    Instructor createInstructor(Instructor instructor);

    Optional<Instructor> getInstructorById(Long instructorId);

    List<Instructor> getAllInstructors();

    Instructor updateInstructor(Long instructorId, Instructor instructor);

    void deleteInstructor(Long instructorId);
}