package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.ProfileActivity;

import java.util.List;
import java.util.Optional;

public interface ProfileActivityDataService {
    ProfileActivity createProfileActivity(ProfileActivity profileActivity);

    Optional<ProfileActivity> getProfileActivityById(Long profileActivityId);

    List<ProfileActivity> getAllProfileActivities();

    ProfileActivity updateProfileActivity(Long profileActivityId, ProfileActivity profileActivity);

    void deleteProfileActivity(Long profileActivityId);
}