package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.RoomType;

import java.util.List;
import java.util.Optional;

public interface RoomTypeDataService {
    RoomType createRoomType(RoomType roomType);

    Optional<RoomType> getRoomTypeById(Long roomTypeId);

    List<RoomType> getAllRoomTypes();

    RoomType updateRoomType(Long roomTypeId, RoomType roomType);

    void deleteRoomType(Long roomTypeId);
}