package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.RoleRepository;
import com.guitarcenter.scheduler.data.services.RoleDataService;
import com.guitarcenter.scheduler.model.Role;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the RoleDataService interface.
 * Provides methods to perform CRUD operations on Role entities.
 */
@Service("roleDataService")
public class RoleDataServiceImpl implements RoleDataService {

    private final RoleRepository roleRepository;

    @Autowired
    public RoleDataServiceImpl(RoleRepository roleRepository) {
        this.roleRepository = roleRepository;
    }

    @Override
    public Role createRole(Role role) {
        role.setUpdated(new Date());
        return roleRepository.save(role);
    }

    @Override
    public Optional<Role> getRoleById(Long roleId) {
        return roleRepository.findById(roleId);
    }

    @Override
    public List<Role> getAllRoles() {
        return roleRepository.findAll();
    }

    @Override
    public Role updateRole(Long roleId, Role role) {
        if (roleRepository.existsById(roleId)) {
            role.setRoleId(roleId);
            role.setUpdated(new Date());
            return roleRepository.save(role);
        } else {
            throw new RuntimeException("Role not found");
        }
    }

    @Override
    public void deleteRole(Long roleId) {
        roleRepository.deleteById(roleId);
    }
}