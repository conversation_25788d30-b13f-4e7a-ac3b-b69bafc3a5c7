package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.RoomTemplate;

import java.util.List;
import java.util.Optional;

public interface RoomTemplateDataService {
    RoomTemplate createRoomTemplate(RoomTemplate roomTemplate);

    Optional<RoomTemplate> getRoomTemplateById(Long roomTemplateId);

    List<RoomTemplate> getAllRoomTemplates();

    RoomTemplate updateRoomTemplate(Long roomTemplateId, RoomTemplate roomTemplate);

    void deleteRoomTemplate(Long roomTemplateId);
}