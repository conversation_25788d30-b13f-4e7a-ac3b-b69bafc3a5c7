package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.AppointmentCancelReason;

import java.util.List;
import java.util.Optional;

public interface AppointmentCancelReasonDataService {
    AppointmentCancelReason createAppointmentCancelReason(AppointmentCancelReason appointmentCancelReason);

    Optional<AppointmentCancelReason> getAppointmentCancelReasonById(Long appointmentCancelReasonId);

    List<AppointmentCancelReason> getAllAppointmentCancelReasons();

    AppointmentCancelReason updateAppointmentCancelReason(Long appointmentCancelReasonId, AppointmentCancelReason appointmentCancelReason);

    void deleteAppointmentCancelReason(Long appointmentCancelReasonId);
}