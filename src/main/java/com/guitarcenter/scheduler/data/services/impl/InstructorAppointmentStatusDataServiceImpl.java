package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.InstructorAppointmentStatusRepository;
import com.guitarcenter.scheduler.data.services.InstructorAppointmentStatusDataService;
import com.guitarcenter.scheduler.model.InstructorAppointmentStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the InstructorAppointmentStatusDataService interface.
 * Provides methods to perform CRUD operations on InstructorAppointmentStatus entities.
 */
@Service("instructorAppointmentStatusDataService")
public class InstructorAppointmentStatusDataServiceImpl implements InstructorAppointmentStatusDataService {

    private final InstructorAppointmentStatusRepository instructorAppointmentStatusRepository;

    @Autowired
    public InstructorAppointmentStatusDataServiceImpl(InstructorAppointmentStatusRepository instructorAppointmentStatusRepository) {
        this.instructorAppointmentStatusRepository = instructorAppointmentStatusRepository;
    }

    @Override
    public InstructorAppointmentStatus createInstructorAppointmentStatus(InstructorAppointmentStatus instructorAppointmentStatus) {
        instructorAppointmentStatus.setUpdated(new Date());
        return instructorAppointmentStatusRepository.save(instructorAppointmentStatus);
    }

    @Override
    public Optional<InstructorAppointmentStatus> getInstructorAppointmentStatusById(Long appointmentId) {
        return instructorAppointmentStatusRepository.findById(appointmentId);
    }

    @Override
    public List<InstructorAppointmentStatus> getAllInstructorAppointmentStatuses() {
        return instructorAppointmentStatusRepository.findAll();
    }

    @Override
    public InstructorAppointmentStatus updateInstructorAppointmentStatus(Long appointmentId, InstructorAppointmentStatus instructorAppointmentStatus) {
        if (instructorAppointmentStatusRepository.existsById(appointmentId)) {
            instructorAppointmentStatus.setAppointmentId(appointmentId);
            instructorAppointmentStatus.setUpdated(new Date());
            return instructorAppointmentStatusRepository.save(instructorAppointmentStatus);
        } else {
            throw new RuntimeException("InstructorAppointmentStatus not found");
        }
    }

    @Override
    public void deleteInstructorAppointmentStatus(Long appointmentId) {
        instructorAppointmentStatusRepository.deleteById(appointmentId);
    }
}