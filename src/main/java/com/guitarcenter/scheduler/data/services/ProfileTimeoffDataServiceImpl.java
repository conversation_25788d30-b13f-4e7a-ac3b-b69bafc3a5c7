package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.repository.ProfileTimeoffRepository;
import com.guitarcenter.scheduler.data.services.ProfileTimeoffDataService;
import com.guitarcenter.scheduler.model.ProfileTimeoff;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the ProfileTimeoffDataService interface.
 * Provides methods to perform CRUD operations on ProfileTimeoff entities.
 */
@Service("profileTimeoffDataService")
public class ProfileTimeoffDataServiceImpl implements ProfileTimeoffDataService {

    private final ProfileTimeoffRepository profileTimeoffRepository;

    @Autowired
    public ProfileTimeoffDataServiceImpl(ProfileTimeoffRepository profileTimeoffRepository) {
        this.profileTimeoffRepository = profileTimeoffRepository;
    }

    @Override
    public ProfileTimeoff createProfileTimeoff(ProfileTimeoff profileTimeoff) {
        profileTimeoff.setUpdated(new Date());
        return profileTimeoffRepository.save(profileTimeoff);
    }

    @Override
    public Optional<ProfileTimeoff> getProfileTimeoffById(Long profileTimeoffId) {
        return profileTimeoffRepository.findById(profileTimeoffId);
    }

    @Override
    public List<ProfileTimeoff> getAllProfileTimeoffs() {
        return profileTimeoffRepository.findAll();
    }

    @Override
    public ProfileTimeoff updateProfileTimeoff(Long profileTimeoffId, ProfileTimeoff profileTimeoff) {
        if (profileTimeoffRepository.existsById(profileTimeoffId)) {
            profileTimeoff.setProfiletimeoffId(profileTimeoffId);
            profileTimeoff.setUpdated(new Date());
            return profileTimeoffRepository.save(profileTimeoff);
        } else {
            throw new RuntimeException("ProfileTimeoff not found");
        }
    }

    @Override
    public void deleteProfileTimeoff(Long profileTimeoffId) {
        profileTimeoffRepository.deleteById(profileTimeoffId);
    }
}