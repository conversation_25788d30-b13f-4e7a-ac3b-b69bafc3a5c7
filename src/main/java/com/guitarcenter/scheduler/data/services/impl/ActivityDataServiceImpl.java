package com.guitarcenter.scheduler.data.services.impl;

import com.guitarcenter.scheduler.data.services.ActivityDataService;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.data.repository.ActivityRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service("activityDataService")
public class ActivityDataServiceImpl implements ActivityDataService {

    private final ActivityRepository activityRepository;

    @Autowired
    public ActivityDataServiceImpl(ActivityRepository activityRepository) {
        this.activityRepository = activityRepository;
    }

    @Override
    public Activity createActivity(Activity activity) {
        // Business logic can be added here
        return activityRepository.save(activity);
    }

    @Override
    public Optional<Activity> getActivityById(Long activityId) {
        return activityRepository.findById(activityId);
    }

    @Override
    public List<Activity> getAllActivities() {
        return activityRepository.findAll();
    }

    @Override
    public List<Activity> getActivitiesByName(String activityName) {
        return activityRepository.findByActivityName(activityName);
    }

    @Override
    public Activity updateActivity(Long activityId, Activity activity) {
        // You may want to check if the activity exists first before updating
        if (activityRepository.existsById(activityId)) {
            activity.setActivityId(activityId); // Ensure the ID is set before saving
            return activityRepository.save(activity);
        } else {
            throw new RuntimeException("Activity not found");
        }
    }

    @Override
    public void deleteActivity(Long activityId) {
        activityRepository.deleteById(activityId);
    }
}
