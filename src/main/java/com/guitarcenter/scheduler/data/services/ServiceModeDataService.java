package com.guitarcenter.scheduler.data.services;

import com.guitarcenter.scheduler.model.ServiceMode;

import java.util.List;
import java.util.Optional;

public interface ServiceModeDataService {
    ServiceMode createServiceMode(ServiceMode serviceMode);

    Optional<ServiceMode> getServiceModeById(Long serviceModeId);

    List<ServiceMode> getAllServiceModes();

    ServiceMode updateServiceMode(Long serviceModeId, ServiceMode serviceMode);

    void deleteServiceMode(Long serviceModeId);
}