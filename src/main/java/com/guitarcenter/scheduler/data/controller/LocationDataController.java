package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.LocationDataService;
import com.guitarcenter.scheduler.model.Location;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing Location entities.
 * Provides endpoints for CRUD operations.
 */
@RestController
@RequestMapping("/data/locations")
public class LocationDataController {

    private final LocationDataService locationDataService;

    @Autowired
    public LocationDataController(LocationDataService locationDataService) {
        this.locationDataService = locationDataService;
    }

    @PostMapping
    public ResponseEntity<Location> createLocation(@RequestBody Location location) {
        location.setUpdated(new Date());
        Location createdLocation = locationDataService.createLocation(location);
        return ResponseEntity.ok(createdLocation);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> getLocationById(@PathVariable Long id) {
        Optional<Location> location = locationDataService.getLocationById(id);
        if (location.isPresent()) {
            return ResponseEntity.ok(location.get());
        } else {
            return ResponseEntity.ok("No data found");
        }
    }

    @GetMapping
    public ResponseEntity<List<Location>> getAllLocations() {
        List<Location> locations = locationDataService.getAllLocations();
        return ResponseEntity.ok(locations);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Location> updateLocation(@PathVariable Long id, @RequestBody Location location) {
        try {
            Location updatedLocation = locationDataService.updateLocation(id, location);
            return ResponseEntity.ok(updatedLocation);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteLocation(@PathVariable Long id) {
        locationDataService.deleteLocation(id);
        return ResponseEntity.noContent().build();
    }
}