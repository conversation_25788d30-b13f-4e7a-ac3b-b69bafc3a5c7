package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.AppointmentCancelReasonDataService;
import com.guitarcenter.scheduler.model.AppointmentCancelReason;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing AppointmentCancelReason entities.
 * Provides endpoints for CRUD operations.
 */
@RestController
@RequestMapping("/data/appointment-cancel-reasons")
public class AppointmentCancelReasonDataController {

    private final AppointmentCancelReasonDataService appointmentCancelReasonDataService;

    @Autowired
    public AppointmentCancelReasonDataController(AppointmentCancelReasonDataService appointmentCancelReasonDataService) {
        this.appointmentCancelReasonDataService = appointmentCancelReasonDataService;
    }

    @PostMapping
    public ResponseEntity<AppointmentCancelReason> createAppointmentCancelReason(@RequestBody AppointmentCancelReason appointmentCancelReason) {
        appointmentCancelReason.setUpdated(new Date());
        AppointmentCancelReason createdAppointmentCancelReason = appointmentCancelReasonDataService.createAppointmentCancelReason(appointmentCancelReason);
        return ResponseEntity.ok(createdAppointmentCancelReason);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> getAppointmentCancelReasonById(@PathVariable Long id) {
        Optional<AppointmentCancelReason> appointmentCancelReason = appointmentCancelReasonDataService.getAppointmentCancelReasonById(id);
        if (appointmentCancelReason.isPresent()) {
            return ResponseEntity.ok(appointmentCancelReason.get());
        } else {
            return ResponseEntity.ok("No data found");
        }
    }

    @GetMapping
    public ResponseEntity<List<AppointmentCancelReason>> getAllAppointmentCancelReasons() {
        List<AppointmentCancelReason> appointmentCancelReasons = appointmentCancelReasonDataService.getAllAppointmentCancelReasons();
        return ResponseEntity.ok(appointmentCancelReasons);
    }

    @PutMapping("/{id}")
    public ResponseEntity<AppointmentCancelReason> updateAppointmentCancelReason(@PathVariable Long id, @RequestBody AppointmentCancelReason appointmentCancelReason) {
        try {
            AppointmentCancelReason updatedAppointmentCancelReason = appointmentCancelReasonDataService.updateAppointmentCancelReason(id, appointmentCancelReason);
            return ResponseEntity.ok(updatedAppointmentCancelReason);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteAppointmentCancelReason(@PathVariable Long id) {
        appointmentCancelReasonDataService.deleteAppointmentCancelReason(id);
        return ResponseEntity.noContent().build();
    }
}