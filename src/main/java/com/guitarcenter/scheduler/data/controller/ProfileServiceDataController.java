package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.ProfileServiceDataService;
import com.guitarcenter.scheduler.model.ProfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing ProfileService entities.
 * Provides endpoints for CRUD operations.
 */
@RestController
@RequestMapping("/data/profile-services")
public class ProfileServiceDataController {

    private final ProfileServiceDataService profileServiceDataService;

    @Autowired
    public ProfileServiceDataController(ProfileServiceDataService profileServiceDataService) {
        this.profileServiceDataService = profileServiceDataService;
    }

    @PostMapping
    public ResponseEntity<ProfileService> createProfileService(@RequestBody ProfileService profileService) {
        profileService.setUpdated(new Date());
        ProfileService createdProfileService = profileServiceDataService.createProfileService(profileService);
        return ResponseEntity.ok(createdProfileService);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> getProfileServiceById(@PathVariable Long id) {
        Optional<ProfileService> profileService = profileServiceDataService.getProfileServiceById(id);
        if (profileService.isPresent()) {
            return ResponseEntity.ok(profileService.get());
        } else {
            return ResponseEntity.ok("No data found");
        }
    }

    @GetMapping
    public ResponseEntity<List<ProfileService>> getAllProfileServices() {
        List<ProfileService> profileServices = profileServiceDataService.getAllProfileServices();
        return ResponseEntity.ok(profileServices);
    }

    @PutMapping("/{id}")
    public ResponseEntity<ProfileService> updateProfileService(@PathVariable Long id, @RequestBody ProfileService profileService) {
        try {
            ProfileService updatedProfileService = profileServiceDataService.updateProfileService(id, profileService);
            return ResponseEntity.ok(updatedProfileService);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteProfileService(@PathVariable Long id) {
        profileServiceDataService.deleteProfileService(id);
        return ResponseEntity.noContent().build();
    }
}