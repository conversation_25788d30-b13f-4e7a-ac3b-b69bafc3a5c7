package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.ServiceModeDataService;
import com.guitarcenter.scheduler.model.ServiceMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/data/service-modes")
public class ServiceModeDataController {

    private final ServiceModeDataService serviceModeDataService;

    @Autowired
    public ServiceModeDataController(ServiceModeDataService serviceModeDataService) {
        this.serviceModeDataService = serviceModeDataService;
    }

    @PostMapping
    public ResponseEntity<ServiceMode> createServiceMode(@RequestBody ServiceMode serviceMode) {
        ServiceMode createdServiceMode = serviceModeDataService.createServiceMode(serviceMode);
        return ResponseEntity.ok(createdServiceMode);
    }

    @GetMapping("/{id}")
    public ResponseEntity<ServiceMode> getServiceModeById(@PathVariable Long id) {
        Optional<ServiceMode> serviceMode = serviceModeDataService.getServiceModeById(id);
        return serviceMode.map(ResponseEntity::ok).orElseGet(() -> ResponseEntity.notFound().build());
    }

    @GetMapping
    public ResponseEntity<List<ServiceMode>> getAllServiceModes() {
        List<ServiceMode> serviceModes = serviceModeDataService.getAllServiceModes();
        return ResponseEntity.ok(serviceModes);
    }

    @PutMapping("/{id}")
    public ResponseEntity<ServiceMode> updateServiceMode(@PathVariable Long id, @RequestBody ServiceMode serviceMode) {
        try {
            ServiceMode updatedServiceMode = serviceModeDataService.updateServiceMode(id, serviceMode);
            return ResponseEntity.ok(updatedServiceMode);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteServiceMode(@PathVariable Long id) {
        serviceModeDataService.deleteServiceMode(id);
        return ResponseEntity.noContent().build();
    }
}