package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.data.services.ActivityDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/data/activities")
public class ActivityDataController {

    private final ActivityDataService activityDataService;

    @Autowired
    public ActivityDataController(ActivityDataService activityDataService) {
        this.activityDataService = activityDataService;
    }

    @PostMapping(produces = "application/json")
    public ResponseEntity<Activity> createActivity(@RequestBody Activity activity) {
        Activity savedActivity = activityDataService.createActivity(activity);
        return new ResponseEntity<>(savedActivity, HttpStatus.CREATED);
    }

    @GetMapping(value = "/{activityId}", produces = "application/json")
    public ResponseEntity<Activity> getActivityById(@PathVariable Long activityId) {
        Optional<Activity> activity = activityDataService.getActivityById(activityId);
        return activity.map(ResponseEntity::ok)
                .orElseGet(() -> ResponseEntity.status(HttpStatus.NOT_FOUND).build());
    }

    @GetMapping(produces = "application/json")
    public List<Activity> getAllActivities() {
        return activityDataService.getAllActivities();
    }

    @GetMapping(value = "/search", produces = "application/json")
    public List<Activity> getActivitiesByName(@RequestParam String activityName) {
        return activityDataService.getActivitiesByName(activityName);
    }

    @PutMapping(value = "/{activityId}", produces = "application/json")
    public ResponseEntity<Activity> updateActivity(@PathVariable Long activityId, @RequestBody Activity activity) {
        Activity updatedActivity = activityDataService.updateActivity(activityId, activity);
        return ResponseEntity.ok(updatedActivity);
    }

    @DeleteMapping(value = "/{activityId}", produces = "application/json")
    public ResponseEntity<Void> deleteActivity(@PathVariable Long activityId) {
        activityDataService.deleteActivity(activityId);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }
}