package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.AvailabilityDataService;
import com.guitarcenter.scheduler.model.Availability;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing Availability entities.
 * Provides endpoints for CRUD operations.
 */
@RestController
@RequestMapping("/data/availabilities")
public class AvailabilityDataController {

    private final AvailabilityDataService availabilityDataService;

    @Autowired
    public AvailabilityDataController(AvailabilityDataService availabilityDataService) {
        this.availabilityDataService = availabilityDataService;
    }

    @PostMapping
    public ResponseEntity<Availability> createAvailability(@RequestBody Availability availability) {
        availability.setUpdated(new Date());
        Availability createdAvailability = availabilityDataService.createAvailability(availability);
        return ResponseEntity.ok(createdAvailability);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> getAvailabilityById(@PathVariable Long id) {
        Optional<Availability> availability = availabilityDataService.getAvailabilityById(id);
        if (availability.isPresent()) {
            return ResponseEntity.ok(availability.get());
        } else {
            return ResponseEntity.ok("No data found");
        }
    }

    @GetMapping
    public ResponseEntity<List<Availability>> getAllAvailabilities() {
        List<Availability> availabilities = availabilityDataService.getAllAvailabilities();
        return ResponseEntity.ok(availabilities);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Availability> updateAvailability(@PathVariable Long id, @RequestBody Availability availability) {
        try {
            Availability updatedAvailability = availabilityDataService.updateAvailability(id, availability);
            return ResponseEntity.ok(updatedAvailability);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteAvailability(@PathVariable Long id) {
        availabilityDataService.deleteAvailability(id);
        return ResponseEntity.noContent().build();
    }
}