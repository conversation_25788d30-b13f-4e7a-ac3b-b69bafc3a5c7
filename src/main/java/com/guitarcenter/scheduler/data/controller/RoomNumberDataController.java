package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.RoomNumberDataService;
import com.guitarcenter.scheduler.model.RoomNumber;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing RoomNumber entities.
 * Provides endpoints for CRUD operations.
 */
@RestController
@RequestMapping("/data/room-numbers")
public class RoomNumberDataController {

    private final RoomNumberDataService roomNumberDataService;

    @Autowired
    public RoomNumberDataController(RoomNumberDataService roomNumberDataService) {
        this.roomNumberDataService = roomNumberDataService;
    }

    @PostMapping
    public ResponseEntity<RoomNumber> createRoomNumber(@RequestBody RoomNumber roomNumber) {
        roomNumber.setUpdated(new Date());
        RoomNumber createdRoomNumber = roomNumberDataService.createRoomNumber(roomNumber);
        return ResponseEntity.ok(createdRoomNumber);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> getRoomNumberById(@PathVariable Long id) {
        Optional<RoomNumber> roomNumber = roomNumberDataService.getRoomNumberById(id);
        if (roomNumber.isPresent()) {
            return ResponseEntity.ok(roomNumber.get());
        } else {
            return ResponseEntity.ok("No data found");
        }
    }

    @GetMapping
    public ResponseEntity<List<RoomNumber>> getAllRoomNumbers() {
        List<RoomNumber> roomNumbers = roomNumberDataService.getAllRoomNumbers();
        return ResponseEntity.ok(roomNumbers);
    }

    @PutMapping("/{id}")
    public ResponseEntity<RoomNumber> updateRoomNumber(@PathVariable Long id, @RequestBody RoomNumber roomNumber) {
        try {
            RoomNumber updatedRoomNumber = roomNumberDataService.updateRoomNumber(id, roomNumber);
            return ResponseEntity.ok(updatedRoomNumber);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteRoomNumber(@PathVariable Long id) {
        roomNumberDataService.deleteRoomNumber(id);
        return ResponseEntity.noContent().build();
    }
}