package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.RoleDataService;
import com.guitarcenter.scheduler.model.Role;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing Role entities.
 * Provides endpoints for CRUD operations.
 */
@RestController
@RequestMapping("/data/roles")
public class RoleDataController {

    private final RoleDataService roleDataService;

    @Autowired
    public RoleDataController(RoleDataService roleDataService) {
        this.roleDataService = roleDataService;
    }

    @PostMapping
    public ResponseEntity<Role> createRole(@RequestBody Role role) {
        role.setUpdated(new Date());
        Role createdRole = roleDataService.createRole(role);
        return ResponseEntity.ok(createdRole);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> getRoleById(@PathVariable Long id) {
        Optional<Role> role = roleDataService.getRoleById(id);
        if (role.isPresent()) {
            return ResponseEntity.ok(role.get());
        } else {
            return ResponseEntity.ok("No data found");
        }
    }

    @GetMapping
    public ResponseEntity<List<Role>> getAllRoles() {
        List<Role> roles = roleDataService.getAllRoles();
        return ResponseEntity.ok(roles);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Role> updateRole(@PathVariable Long id, @RequestBody Role role) {
        try {
            Role updatedRole = roleDataService.updateRole(id, role);
            return ResponseEntity.ok(updatedRole);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteRole(@PathVariable Long id) {
        roleDataService.deleteRole(id);
        return ResponseEntity.noContent().build();
    }
}