package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.ProfileActivityDataService;
import com.guitarcenter.scheduler.model.ProfileActivity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing ProfileActivity entities.
 * Provides endpoints for CRUD operations.
 */
@RestController
@RequestMapping("/data/profile-activities")
public class ProfileActivityDataController {

    private final ProfileActivityDataService profileActivityDataService;

    @Autowired
    public ProfileActivityDataController(ProfileActivityDataService profileActivityDataService) {
        this.profileActivityDataService = profileActivityDataService;
    }

    @PostMapping
    public ResponseEntity<ProfileActivity> createProfileActivity(@RequestBody ProfileActivity profileActivity) {
        profileActivity.setUpdated(new Date());
        ProfileActivity createdProfileActivity = profileActivityDataService.createProfileActivity(profileActivity);
        return ResponseEntity.ok(createdProfileActivity);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> getProfileActivityById(@PathVariable Long id) {
        Optional<ProfileActivity> profileActivity = profileActivityDataService.getProfileActivityById(id);
        if (profileActivity.isPresent()) {
            return ResponseEntity.ok(profileActivity.get());
        } else {
            return ResponseEntity.ok("No data found");
        }
    }

    @GetMapping
    public ResponseEntity<List<ProfileActivity>> getAllProfileActivities() {
        List<ProfileActivity> profileActivities = profileActivityDataService.getAllProfileActivities();
        return ResponseEntity.ok(profileActivities);
    }

    @PutMapping("/{id}")
    public ResponseEntity<ProfileActivity> updateProfileActivity(@PathVariable Long id, @RequestBody ProfileActivity profileActivity) {
        try {
            ProfileActivity updatedProfileActivity = profileActivityDataService.updateProfileActivity(id, profileActivity);
            return ResponseEntity.ok(updatedProfileActivity);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteProfileActivity(@PathVariable Long id) {
        profileActivityDataService.deleteProfileActivity(id);
        return ResponseEntity.noContent().build();
    }
}