package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.RoomTypeDataService;
import com.guitarcenter.scheduler.model.RoomType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing RoomType entities.
 * Provides endpoints for CRUD operations.
 */
@RestController
@RequestMapping("/data/room-types")
public class RoomTypeDataController {

    private final RoomTypeDataService roomTypeDataService;

    @Autowired
    public RoomTypeDataController(RoomTypeDataService roomTypeDataService) {
        this.roomTypeDataService = roomTypeDataService;
    }

    @PostMapping
    public ResponseEntity<RoomType> createRoomType(@RequestBody RoomType roomType) {
        roomType.setUpdated(new Date());
        RoomType createdRoomType = roomTypeDataService.createRoomType(roomType);
        return ResponseEntity.ok(createdRoomType);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> getRoomTypeById(@PathVariable Long id) {
        Optional<RoomType> roomType = roomTypeDataService.getRoomTypeById(id);
        if (roomType.isPresent()) {
            return ResponseEntity.ok(roomType.get());
        } else {
            return ResponseEntity.ok("No data found");
        }
    }

    @GetMapping
    public ResponseEntity<List<RoomType>> getAllRoomTypes() {
        List<RoomType> roomTypes = roomTypeDataService.getAllRoomTypes();
        return ResponseEntity.ok(roomTypes);
    }

    @PutMapping("/{id}")
    public ResponseEntity<RoomType> updateRoomType(@PathVariable Long id, @RequestBody RoomType roomType) {
        try {
            RoomType updatedRoomType = roomTypeDataService.updateRoomType(id, roomType);
            return ResponseEntity.ok(updatedRoomType);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteRoomType(@PathVariable Long id) {
        roomTypeDataService.deleteRoomType(id);
        return ResponseEntity.noContent().build();
    }
}