package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.RoomTemplateDataService;
import com.guitarcenter.scheduler.model.RoomTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing RoomTemplate entities.
 * Provides endpoints for CRUD operations.
 */
@RestController
@RequestMapping("/data/room-templates")
public class RoomTemplateDataController {

    private final RoomTemplateDataService roomTemplateDataService;

    @Autowired
    public RoomTemplateDataController(RoomTemplateDataService roomTemplateDataService) {
        this.roomTemplateDataService = roomTemplateDataService;
    }

    @PostMapping
    public ResponseEntity<RoomTemplate> createRoomTemplate(@RequestBody RoomTemplate roomTemplate) {
        roomTemplate.setUpdated(new Date());
        RoomTemplate createdRoomTemplate = roomTemplateDataService.createRoomTemplate(roomTemplate);
        return ResponseEntity.ok(createdRoomTemplate);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> getRoomTemplateById(@PathVariable Long id) {
        Optional<RoomTemplate> roomTemplate = roomTemplateDataService.getRoomTemplateById(id);
        if (roomTemplate.isPresent()) {
            return ResponseEntity.ok(roomTemplate.get());
        } else {
            return ResponseEntity.ok("No data found");
        }
    }

    @GetMapping
    public ResponseEntity<List<RoomTemplate>> getAllRoomTemplates() {
        List<RoomTemplate> roomTemplates = roomTemplateDataService.getAllRoomTemplates();
        return ResponseEntity.ok(roomTemplates);
    }

    @PutMapping("/{id}")
    public ResponseEntity<RoomTemplate> updateRoomTemplate(@PathVariable Long id, @RequestBody RoomTemplate roomTemplate) {
        try {
            RoomTemplate updatedRoomTemplate = roomTemplateDataService.updateRoomTemplate(id, roomTemplate);
            return ResponseEntity.ok(updatedRoomTemplate);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteRoomTemplate(@PathVariable Long id) {
        roomTemplateDataService.deleteRoomTemplate(id);
        return ResponseEntity.noContent().build();
    }
}