package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.SiteDataService;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Site;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/data/sites")
public class SiteDataController {

    private final SiteDataService siteDataService;

    @Autowired
    public SiteDataController(SiteDataService siteDataService) {
        this.siteDataService = siteDataService;
    }

  @PostMapping
public ResponseEntity<Site> createSite(@RequestBody Site site) {
    // Extract personId from the input data
    Long personId = site.getUpdatedBy().getPersonId();

    // Create a Person object and set the personId
    Person person = new Person();
    person.setPersonId(personId);

    // Set the Person object to the updatedBy field of the Site entity
    site.setUpdatedBy(person);

    // Set the updated field to the current date and time
    site.setUpdated(new Date());

    // Save the Site entity
    Site createdSite = siteDataService.createSite(site);
    return ResponseEntity.ok(createdSite);
}



    @GetMapping("/{id}")
    public ResponseEntity<Site> getSiteById(@PathVariable Long id) {
        Optional<Site> site = siteDataService.getSiteById(id);
        return site.map(ResponseEntity::ok).orElseGet(() -> ResponseEntity.notFound().build());
    }

    @GetMapping
    public ResponseEntity<List<Site>> getAllSites() {
        List<Site> sites = siteDataService.getAllSites();
        return ResponseEntity.ok(sites);
    }

  @PutMapping("/{id}")
public ResponseEntity<Site> updateSite(@PathVariable Long id, @RequestBody Site site) {
    Optional<Site> existingSite = siteDataService.getSiteById(id);
    if (existingSite.isPresent()) {
        Site updatedSite = siteDataService.updateSite(id, site);
        return ResponseEntity.ok(updatedSite);
    } else {
        return ResponseEntity.notFound().build();
    }
}

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteSite(@PathVariable Long id) {
        siteDataService.deleteSite(id);
        return ResponseEntity.noContent().build();
    }
}