package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.ProfileTimeoffDataService;
import com.guitarcenter.scheduler.model.ProfileTimeoff;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing ProfileTimeoff entities.
 * Provides endpoints for CRUD operations.
 */
@RestController
@RequestMapping("/data/profile-timeoffs")
public class ProfileTimeoffDataController {

    private final ProfileTimeoffDataService profileTimeoffDataService;

    @Autowired
    public ProfileTimeoffDataController(ProfileTimeoffDataService profileTimeoffDataService) {
        this.profileTimeoffDataService = profileTimeoffDataService;
    }

    @PostMapping
    public ResponseEntity<ProfileTimeoff> createProfileTimeoff(@RequestBody ProfileTimeoff profileTimeoff) {
        profileTimeoff.setUpdated(new Date());
        ProfileTimeoff createdProfileTimeoff = profileTimeoffDataService.createProfileTimeoff(profileTimeoff);
        return ResponseEntity.ok(createdProfileTimeoff);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> getProfileTimeoffById(@PathVariable Long id) {
        Optional<ProfileTimeoff> profileTimeoff = profileTimeoffDataService.getProfileTimeoffById(id);
        if (profileTimeoff.isPresent()) {
            return ResponseEntity.ok(profileTimeoff.get());
        } else {
            return ResponseEntity.ok("No data found");
        }
    }

    @GetMapping
    public ResponseEntity<List<ProfileTimeoff>> getAllProfileTimeoffs() {
        List<ProfileTimeoff> profileTimeoffs = profileTimeoffDataService.getAllProfileTimeoffs();
        return ResponseEntity.ok(profileTimeoffs);
    }

    @PutMapping("/{id}")
    public ResponseEntity<ProfileTimeoff> updateProfileTimeoff(@PathVariable Long id, @RequestBody ProfileTimeoff profileTimeoff) {
        try {
            ProfileTimeoff updatedProfileTimeoff = profileTimeoffDataService.updateProfileTimeoff(id, profileTimeoff);
            return ResponseEntity.ok(updatedProfileTimeoff);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteProfileTimeoff(@PathVariable Long id) {
        profileTimeoffDataService.deleteProfileTimeoff(id);
        return ResponseEntity.noContent().build();
    }
}