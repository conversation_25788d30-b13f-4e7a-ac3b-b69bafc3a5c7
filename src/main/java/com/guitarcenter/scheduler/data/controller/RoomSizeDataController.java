package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.RoomSizeDataService;
import com.guitarcenter.scheduler.model.RoomSize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing RoomSize entities.
 * Provides endpoints for CRUD operations.
 */
@RestController
@RequestMapping("/data/room-sizes")
public class RoomSizeDataController {

    private final RoomSizeDataService roomSizeDataService;

    @Autowired
    public RoomSizeDataController(RoomSizeDataService roomSizeDataService) {
        this.roomSizeDataService = roomSizeDataService;
    }

    @PostMapping
    public ResponseEntity<RoomSize> createRoomSize(@RequestBody RoomSize roomSize) {
        roomSize.setUpdated(new Date());
        RoomSize createdRoomSize = roomSizeDataService.createRoomSize(roomSize);
        return ResponseEntity.ok(createdRoomSize);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> getRoomSizeById(@PathVariable Long id) {
        Optional<RoomSize> roomSize = roomSizeDataService.getRoomSizeById(id);
        if (roomSize.isPresent()) {
            return ResponseEntity.ok(roomSize.get());
        } else {
            return ResponseEntity.ok("No data found");
        }
    }

    @GetMapping
    public ResponseEntity<List<RoomSize>> getAllRoomSizes() {
        List<RoomSize> roomSizes = roomSizeDataService.getAllRoomSizes();
        return ResponseEntity.ok(roomSizes);
    }

    @PutMapping("/{id}")
    public ResponseEntity<RoomSize> updateRoomSize(@PathVariable Long id, @RequestBody RoomSize roomSize) {
        try {
            RoomSize updatedRoomSize = roomSizeDataService.updateRoomSize(id, roomSize);
            return ResponseEntity.ok(updatedRoomSize);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteRoomSize(@PathVariable Long id) {
        roomSizeDataService.deleteRoomSize(id);
        return ResponseEntity.noContent().build();
    }
}