package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.ProfileLookupDataService;
import com.guitarcenter.scheduler.model.ProfileLookup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing ProfileLookup entities.
 * Provides endpoints for CRUD operations.
 */
@RestController
@RequestMapping("/data/profile-lookups")
public class ProfileLookupDataController {

    private final ProfileLookupDataService profileLookupDataService;

    @Autowired
    public ProfileLookupDataController(ProfileLookupDataService profileLookupDataService) {
        this.profileLookupDataService = profileLookupDataService;
    }

    @PostMapping
    public ResponseEntity<ProfileLookup> createProfileLookup(@RequestBody ProfileLookup profileLookup) {
       // profileLookup.setUpdated(new Date());
        ProfileLookup createdProfileLookup = profileLookupDataService.createProfileLookup(profileLookup);
        return ResponseEntity.ok(createdProfileLookup);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> getProfileLookupById(@PathVariable Long id) {
        Optional<ProfileLookup> profileLookup = profileLookupDataService.getProfileLookupById(id);
        if (profileLookup.isPresent()) {
            return ResponseEntity.ok(profileLookup.get());
        } else {
            return ResponseEntity.ok("No data found");
        }
    }

    @GetMapping
    public ResponseEntity<List<ProfileLookup>> getAllProfileLookups() {
        List<ProfileLookup> profileLookups = profileLookupDataService.getAllProfileLookups();
        return ResponseEntity.ok(profileLookups);
    }

    @PutMapping("/{id}")
    public ResponseEntity<ProfileLookup> updateProfileLookup(@PathVariable Long id, @RequestBody ProfileLookup profileLookup) {
        try {
            ProfileLookup updatedProfileLookup = profileLookupDataService.updateProfileLookup(id, profileLookup);
            return ResponseEntity.ok(updatedProfileLookup);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteProfileLookup(@PathVariable Long id) {
        profileLookupDataService.deleteProfileLookup(id);
        return ResponseEntity.noContent().build();
    }
}