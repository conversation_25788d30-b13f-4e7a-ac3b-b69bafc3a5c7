package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.TimeoffDataService;
import com.guitarcenter.scheduler.model.Timeoff;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing Timeoff entities.
 * Provides endpoints for CRUD operations.
 */
@RestController
@RequestMapping("/data/timeoffs")
public class TimeoffDataController {

    private final TimeoffDataService timeoffDataService;

    @Autowired
    public TimeoffDataController(TimeoffDataService timeoffDataService) {
        this.timeoffDataService = timeoffDataService;
    }

    @PostMapping
    public ResponseEntity<Timeoff> createTimeoff(@RequestBody Timeoff timeoff) {
        timeoff.setUpdated(new Date());
        Timeoff createdTimeoff = timeoffDataService.createTimeoff(timeoff);
        return ResponseEntity.ok(createdTimeoff);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> getTimeoffById(@PathVariable Long id) {
        Optional<Timeoff> timeoff = timeoffDataService.getTimeoffById(id);
        if (timeoff.isPresent()) {
            return ResponseEntity.ok(timeoff.get());
        } else {
            return ResponseEntity.ok("No data found");
        }
    }

    @GetMapping
    public ResponseEntity<List<Timeoff>> getAllTimeoffs() {
        List<Timeoff> timeoffs = timeoffDataService.getAllTimeoffs();
        return ResponseEntity.ok(timeoffs);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Timeoff> updateTimeoff(@PathVariable Long id, @RequestBody Timeoff timeoff) {
        try {
            Timeoff updatedTimeoff = timeoffDataService.updateTimeoff(id, timeoff);
            return ResponseEntity.ok(updatedTimeoff);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTimeoff(@PathVariable Long id) {
        timeoffDataService.deleteTimeoff(id);
        return ResponseEntity.noContent().build();
    }
}