package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.InstructorAppointmentStatusDataService;
import com.guitarcenter.scheduler.model.InstructorAppointmentStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing InstructorAppointmentStatus entities.
 * Provides endpoints for CRUD operations.
 */
@RestController
@RequestMapping("/data/instructor-appointment-statuses")
public class InstructorAppointmentStatusDataController {

    private final InstructorAppointmentStatusDataService instructorAppointmentStatusDataService;

    @Autowired
    public InstructorAppointmentStatusDataController(InstructorAppointmentStatusDataService instructorAppointmentStatusDataService) {
        this.instructorAppointmentStatusDataService = instructorAppointmentStatusDataService;
    }

    @PostMapping
    public ResponseEntity<InstructorAppointmentStatus> createInstructorAppointmentStatus(@RequestBody InstructorAppointmentStatus instructorAppointmentStatus) {
        instructorAppointmentStatus.setUpdated(new Date());
        InstructorAppointmentStatus createdInstructorAppointmentStatus = instructorAppointmentStatusDataService.createInstructorAppointmentStatus(instructorAppointmentStatus);
        return ResponseEntity.ok(createdInstructorAppointmentStatus);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> getInstructorAppointmentStatusById(@PathVariable Long id) {
        Optional<InstructorAppointmentStatus> instructorAppointmentStatus = instructorAppointmentStatusDataService.getInstructorAppointmentStatusById(id);
        if (instructorAppointmentStatus.isPresent()) {
            return ResponseEntity.ok(instructorAppointmentStatus.get());
        } else {
            return ResponseEntity.ok("No data found");
        }
    }

    @GetMapping
    public ResponseEntity<List<InstructorAppointmentStatus>> getAllInstructorAppointmentStatuses() {
        List<InstructorAppointmentStatus> instructorAppointmentStatuses = instructorAppointmentStatusDataService.getAllInstructorAppointmentStatuses();
        return ResponseEntity.ok(instructorAppointmentStatuses);
    }

    @PutMapping("/{id}")
    public ResponseEntity<InstructorAppointmentStatus> updateInstructorAppointmentStatus(@PathVariable Long id, @RequestBody InstructorAppointmentStatus instructorAppointmentStatus) {
        try {
            InstructorAppointmentStatus updatedInstructorAppointmentStatus = instructorAppointmentStatusDataService.updateInstructorAppointmentStatus(id, instructorAppointmentStatus);
            return ResponseEntity.ok(updatedInstructorAppointmentStatus);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteInstructorAppointmentStatus(@PathVariable Long id) {
        instructorAppointmentStatusDataService.deleteInstructorAppointmentStatus(id);
        return ResponseEntity.noContent().build();
    }
}