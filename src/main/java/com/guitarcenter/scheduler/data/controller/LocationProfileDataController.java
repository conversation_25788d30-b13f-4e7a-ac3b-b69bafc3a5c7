package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.LocationProfileDataService;
import com.guitarcenter.scheduler.model.LocationProfile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing LocationProfile entities.
 * Provides endpoints for CRUD operations.
 */
@RestController
@RequestMapping("/data/location-profiles")
public class LocationProfileDataController {

    private final LocationProfileDataService locationProfileDataService;

    @Autowired
    public LocationProfileDataController(LocationProfileDataService locationProfileDataService) {
        this.locationProfileDataService = locationProfileDataService;
    }

    @PostMapping
    public ResponseEntity<LocationProfile> createLocationProfile(@RequestBody LocationProfile locationProfile) {
        locationProfile.setUpdated(new Date());
        LocationProfile createdLocationProfile = locationProfileDataService.createLocationProfile(locationProfile);
        return ResponseEntity.ok(createdLocationProfile);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> getLocationProfileById(@PathVariable Long id) {
        Optional<LocationProfile> locationProfile = locationProfileDataService.getLocationProfileById(id);
        if (locationProfile.isPresent()) {
            return ResponseEntity.ok(locationProfile.get());
        } else {
            return ResponseEntity.ok("No data found");
        }
    }

    @GetMapping
    public ResponseEntity<List<LocationProfile>> getAllLocationProfiles() {
        List<LocationProfile> locationProfiles = locationProfileDataService.getAllLocationProfiles();
        return ResponseEntity.ok(locationProfiles);
    }

    @PutMapping("/{id}")
    public ResponseEntity<LocationProfile> updateLocationProfile(@PathVariable Long id, @RequestBody LocationProfile locationProfile) {
        try {
            LocationProfile updatedLocationProfile = locationProfileDataService.updateLocationProfile(id, locationProfile);
            return ResponseEntity.ok(updatedLocationProfile);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteLocationProfile(@PathVariable Long id) {
        locationProfileDataService.deleteLocationProfile(id);
        return ResponseEntity.noContent().build();
    }
}