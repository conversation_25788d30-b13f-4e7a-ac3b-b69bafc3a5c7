package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.InstructorDataService;
import com.guitarcenter.scheduler.model.Instructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing Instructor entities.
 * Provides endpoints for CRUD operations.
 */
@RestController
@RequestMapping("/data/instructors")
public class InstructorDataController {

    private final InstructorDataService instructorDataService;

    @Autowired
    public InstructorDataController(InstructorDataService instructorDataService) {
        this.instructorDataService = instructorDataService;
    }

    @PostMapping
    public ResponseEntity<Instructor> createInstructor(@RequestBody Instructor instructor) {
        instructor.setUpdated(new Date());
        Instructor createdInstructor = instructorDataService.createInstructor(instructor);
        return ResponseEntity.ok(createdInstructor);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> getInstructorById(@PathVariable Long id) {
        Optional<Instructor> instructor = instructorDataService.getInstructorById(id);
        if (instructor.isPresent()) {
            return ResponseEntity.ok(instructor.get());
        } else {
            return ResponseEntity.ok("No data found");
        }
    }

    @GetMapping
    public ResponseEntity<List<Instructor>> getAllInstructors() {
        List<Instructor> instructors = instructorDataService.getAllInstructors();
        return ResponseEntity.ok(instructors);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Instructor> updateInstructor(@PathVariable Long id, @RequestBody Instructor instructor) {
        try {
            Instructor updatedInstructor = instructorDataService.updateInstructor(id, instructor);
            return ResponseEntity.ok(updatedInstructor);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteInstructor(@PathVariable Long id) {
        instructorDataService.deleteInstructor(id);
        return ResponseEntity.noContent().build();
    }
}