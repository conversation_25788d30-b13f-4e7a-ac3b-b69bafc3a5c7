package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.RoomDataService;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * REST controller for managing Room entities.
 * Provides endpoints for CRUD operations and additional APIs based on relationships.
 */
@RestController
@RequestMapping("/data/rooms")
public class RoomDataController {

    private final RoomDataService roomDataService;

    @Autowired
    public RoomDataController(RoomDataService roomDataService) {
        this.roomDataService = roomDataService;
    }

    @PostMapping
    public ResponseEntity<Room> createRoom(@RequestBody Room room) {
        room.setUpdated(new Date());
        Room createdRoom = roomDataService.createRoom(room);
        return ResponseEntity.ok(createdRoom);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> getRoomById(@PathVariable Long id) {
        Optional<Room> room = roomDataService.getRoomById(id);
        if (room.isPresent()) {
            return ResponseEntity.ok(room.get());
        } else {
            return ResponseEntity.ok("No data found");
        }
    }

    @GetMapping
    public ResponseEntity<List<Room>> getAllRooms() {
        List<Room> rooms = roomDataService.getAllRooms();
        return ResponseEntity.ok(rooms);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Room> updateRoom(@PathVariable Long id, @RequestBody Room room) {
        try {
            Room updatedRoom = roomDataService.updateRoom(id, room);
            return ResponseEntity.ok(updatedRoom);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteRoom(@PathVariable Long id) {
        roomDataService.deleteRoom(id);
        return ResponseEntity.noContent().build();
    }

    // Additional APIs based on relationships

    @GetMapping("/{id}/activities")
    public ResponseEntity<Set<Activity>> getRoomActivities(@PathVariable Long id) {
        Optional<Room> room = roomDataService.getRoomById(id);
        if (room.isPresent()) {
            return ResponseEntity.ok(room.get().getActivities());
        } else {
            return ResponseEntity.ok(Collections.emptySet());
        }
    }

    @GetMapping("/{id}/services")
    public ResponseEntity<Set<Service>> getRoomServices(@PathVariable Long id) {
        Optional<Room> room = roomDataService.getRoomById(id);
        if (room.isPresent()) {
            return ResponseEntity.ok(room.get().getServices());
        } else {
            return ResponseEntity.ok(Collections.emptySet());
        }
    }
}