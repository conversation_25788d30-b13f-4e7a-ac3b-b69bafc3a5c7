package com.guitarcenter.scheduler.data.controller;

import com.guitarcenter.scheduler.data.services.CustomerStatusDataService;
import com.guitarcenter.scheduler.model.CustomerStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing CustomerStatus entities.
 * Provides endpoints for CRUD operations.
 */
@RestController
@RequestMapping("/data/customer-statuses")
public class CustomerStatusDataController {

    private final CustomerStatusDataService customerStatusDataService;

    @Autowired
    public CustomerStatusDataController(CustomerStatusDataService customerStatusDataService) {
        this.customerStatusDataService = customerStatusDataService;
    }

    @PostMapping
    public ResponseEntity<CustomerStatus> createCustomerStatus(@RequestBody CustomerStatus customerStatus) {
        customerStatus.setUpdated(new Date());
        CustomerStatus createdCustomerStatus = customerStatusDataService.createCustomerStatus(customerStatus);
        return ResponseEntity.ok(createdCustomerStatus);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> getCustomerStatusById(@PathVariable Long id) {
        Optional<CustomerStatus> customerStatus = customerStatusDataService.getCustomerStatusById(id);
        if (customerStatus.isPresent()) {
            return ResponseEntity.ok(customerStatus.get());
        } else {
            return ResponseEntity.ok("No data found");
        }
    }

    @GetMapping
    public ResponseEntity<List<CustomerStatus>> getAllCustomerStatuses() {
        List<CustomerStatus> customerStatuses = customerStatusDataService.getAllCustomerStatuses();
        return ResponseEntity.ok(customerStatuses);
    }

    @PutMapping("/{id}")
    public ResponseEntity<CustomerStatus> updateCustomerStatus(@PathVariable Long id, @RequestBody CustomerStatus customerStatus) {
        try {
            CustomerStatus updatedCustomerStatus = customerStatusDataService.updateCustomerStatus(id, customerStatus);
            return ResponseEntity.ok(updatedCustomerStatus);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCustomerStatus(@PathVariable Long id) {
        customerStatusDataService.deleteCustomerStatus(id);
        return ResponseEntity.noContent().build();
    }
}