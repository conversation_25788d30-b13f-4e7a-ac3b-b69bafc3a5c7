package com.guitarcenter.scheduler.dto;

import java.util.Date;

public class ProfileTimeOffDTO {
private Long				profiletimeoffId;
private Long     			profileId;
private Date				startTime;
private Date				endTime;
private Long				version;
private Date				updated;
private Long				updatedBy;

private String fromTime;
private String fromDate;
private String toTime;
private String toDate;
private String timeOffStartToEnd;


public Long getProfiletimeoffId() {
	return profiletimeoffId;
}
public void setProfiletimeoffId(Long profiletimeoffId) {
	this.profiletimeoffId = profiletimeoffId;
}
public Long getProfileId() {
	return profileId;
}
public void setProfileId(Long profileId) {
	this.profileId = profileId;
}
public Date getStartTime() {
	return startTime;
}
public void setStartTime(Date startTime) {
	this.startTime = startTime;
}
public Date getEndTime() {
	return endTime;
}
public void setEndTime(Date endTime) {
	this.endTime = endTime;
}
public Long getVersion() {
	return version;
}
public void setVersion(Long version) {
	this.version = version;
}
public Date getUpdated() {
	return updated;
}
public void setUpdated(Date updated) {
	this.updated = updated;
}
public Long getUpdatedBy() {
	return updatedBy;
}
public void setUpdatedBy(Long updatedBy) {
	this.updatedBy = updatedBy;
}
public String getFromTime() {
	return fromTime;
}
public void setFromTime(String fromTime) {
	this.fromTime = fromTime;
}
public String getFromDate() {
	return fromDate;
}
public void setFromDate(String fromDate) {
	this.fromDate = fromDate;
}
public String getToTime() {
	return toTime;
}
public void setToTime(String toTime) {
	this.toTime = toTime;
}
public String getToDate() {
	return toDate;
}
public void setToDate(String toDate) {
	this.toDate = toDate;
}
public String getTimeOffStartToEnd() {
	return timeOffStartToEnd;
}
public void setTimeOffStartToEnd(String timeOffStartToEnd) {
	this.timeOffStartToEnd = timeOffStartToEnd;
}

}
