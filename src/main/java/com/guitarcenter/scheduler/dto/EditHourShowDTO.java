package com.guitarcenter.scheduler.dto;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.model.Availability;

public class EditHourShowDTO implements AppConstants{

	private String startTimeString;
	private String endTimeString;
	private Boolean unavaliable = false;
	private String weekDay;
 	
	
	public EditHourShowDTO(){}
	
	
	
	public final String getWeekDay() {
		return weekDay;
	}



	public final void setWeekDay(String weekDay) {
		this.weekDay = weekDay;
	}


	public EditHourShowDTO(String startTimeString,String endTimeString,String weekDay){
		this.startTimeString = startTimeString;
		this.endTimeString = endTimeString;
		this.weekDay = weekDay;
	}

	public EditHourShowDTO(String startTimeString,String endTimeString){
		this.startTimeString = startTimeString;
		this.endTimeString = endTimeString;
	}
	public String getStartTimeString() {
		return startTimeString;
	}
	public void setStartTimeString(String startTimeString) {
		this.startTimeString = startTimeString;
	}
	public String getEndTimeString() {
		return endTimeString;
	}
	public void setEndTimeString(String endTimeString) {
		this.endTimeString = endTimeString;
	}

	public final Boolean getUnavaliable() {
		return unavaliable;
	}

	public final void setUnavaliable(Boolean unavaliable) {
		this.unavaliable = unavaliable;
	}


    /**
     * parse startDateString to date,if timeString is null or "",return null.
     * @return
     */
	private Date getStartDate(){
	    Date date;
	    if(getUnavaliable() || StringUtils.isBlank(startTimeString)){
	        date = null;
	    }else{
	        DateTime startTime =  DateTime.parse(startTimeString, DateTimeFormat.forPattern("hh:mm aa"));
	        date = startTime.toDate();
	    }
	    return date;
	}
	
	/**
	 * parse endDateString to date,if timeString is null or "",return null.
	 * @return
	 */
	private Date getEndDate(){
	    Date date;
	    if(getUnavaliable() || StringUtils.isBlank(endTimeString)){
	        date = null;
	    }else if("12:00 AM".equals(endTimeString)){
	    	 DateTime endTime = DateTime.parse("11:59:59 PM", DateTimeFormat.forPattern("hh:mm:ss aa"));
		     date = endTime.toDate();
	    }else {
	        DateTime endTime = DateTime.parse(endTimeString, DateTimeFormat.forPattern("hh:mm aa"));
	        date = endTime.toDate();
	    }
	    return date;
	}

	/**
	 * return if endDate is larger than start-date ,if large return true,else return false.
	 * @return boolean 
	 */
	private boolean checkDate(final Date startDate, final Date endDate){
	    Boolean result;
	    if(startDate != null && endDate != null && startDate.after(endDate)){
	        result = false;
	    }else{
	        result = true;
	    }
	    return result;
	}
	
    public Availability buildDayTime(final Availability instance, int dayOfweek) throws InvalidDateTimeException {
        
        if(!checkDate(getStartDate(),getEndDate())){
            throw new InvalidDateTimeException(weekDay + "'s endTime cannot be earlier than startTime!");
        }
        
        final Availability availability = instance;
        switch (dayOfweek) {
        case 0:
            availability.setSundayStartTime(getStartDate());
            availability.setSundayEndTime(getEndDate());
           break;
        case 1:
            availability.setMondayStartTime(getStartDate());
            availability.setMondayEndTime(getEndDate());
            break;
        case 2:
            availability.setTuesdayStartTime(getStartDate());
            availability.setTuesdayEndTime(getEndDate());
            break;
        case 3:
            availability.setWednesdayStartTime(getStartDate());
            availability.setWednesdayEndTime(getEndDate());
            break;
        case 4:
            availability.setThursdayStartTime(getStartDate());
            availability.setThursdayEndTime(getEndDate());
            break;
        case 5:
            availability.setFridayStartTime(getStartDate());
            availability.setFridayEndTime(getEndDate());
            break;
        case 6:
            availability.setSaturdayStartTime(getStartDate());
            availability.setSaturdayEndTime(getEndDate());
            break;
        default:
            break;
        }
        return availability;
    }


	
	
}
