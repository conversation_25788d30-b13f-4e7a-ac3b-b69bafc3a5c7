package com.guitarcenter.scheduler.dto;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.solr.client.solrj.beans.Field;

/**
 * Encapsulates the information present in a search record.
 * 
 * This class is used when creating/updating a search engine record, and is
 * returned as a part of a results set when executing a search for matching
 * records.
 * 
 * Implemented as an annotated POJO for simplicity; note that even though the
 * field names currently match with schema.xml definitions, the fields are
 * annotated with a constant to keep things in sync should fields change.
 * 
 * <AUTHOR> <a
 *         href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

public class SearchDTO {

	/*
	 * The field identifiers must match up with those in Solr schema.xml. To
	 * help enforce that the code will use these constants.
	 */
	public static final String SOLR_ID_FIELD = "id";
	public static final String SOLR_SITE_ID_FIELD = "siteId";
	public static final String SOLR_TYPE_FIELD = "type";
	public static final String SOLR_RECORD_ID_FIELD = "recordId";
	public static final String SOLR_EXTERNAL_ID_FIELD = "externalId";
	
	//GSSP-220 Changes
	public static final String SOLR_LOCATION_EXTERNAL_ID_FIELD = "locationExternalId";

	public static final String SOLR_FIRST_NAME_FIELD = "firstName";
	public static final String SOLR_LAST_NAME_FIELD = "lastName";
	public static final String SOLR_EMAIL_FIELD = "email";
	public static final String SOLR_PHONE_FIELD = "phone";
	public static final String SOLR_STATUS_FIELD = "status";
	public static final String SOLR_SEARCH_FIRST_NAME_FIELD = "searchFirstName";
	public static final String SOLR_SEARCH_LAST_NAME_FIELD = "searchLastName";
	public static final String SOLR_SEARCH_EMAIL_FIELD = "searchEmail";
	public static final String SOLR_LOCATION_NAME_FIELD = "locationName";
	public static final String SOLR_ADDRESS_ONE_FIELD = "address1";
	public static final String SOLR_ADDRESS_TWO_FIELD = "address2";
	public static final String SOLR_CITY_FIELD = "city";
	public static final String SOLR_STATE_FIELD = "state";
	public static final String SOLR_ZIP_FIELD = "zip";
	public static final String SOLR_COUNTRY_FIELD = "country";
	public static final String SOLR_FAX_FIELD = "fax";
	public static final String SOLR_ENABLED_FIELD = "enabled";
	public static final String SOLR_SEARCH_LOCATION_NAME_FIELD = "searchLocationName";
	public static final String SOLR_SEARCH_STUDIO_NUMBER = "searchStudioNumber";
	public static final String SOLR_INSTRUMENT_TYPE_FIELD = "instrumentType";
	public static final String SOLR_SEARCH_PHONE_FIELD = "searchPhone";
	public static final String SOLR_SEARCH_EXTERNAL_ID_FIELD = "searchExternalId";

	//GGP-220 Changes
	public static final String SOLR_SEARCH_LOCATION_EXTERNAL_ID_FIELD = "searchLocationExternalId";
	
	//POS-808 Changes
	public static final String SOLR_LESSON_COUNT = "lessonCount";
	
	
	public static final String SOLR_SECONDARY_EMAIL = "secondaryEmail";
	
	/**
	 * The internal id of the solr record; must be unique
	 */
	private String id;

	/**
	 * Identifies the site for the record
	 */
	private Long siteId;

	/**
	 * Identifies the type of record
	 */
	private String type;

	/**
	 * The scheduler model identifier
	 */
	private Long recordId;

	/**
	 * An external identifier; may be null
	 */
	private String externalId;

	/**
	 * First name of person, if this record is related to a person
	 */
	private String firstName;

	/**
	 * Last name of person, if this record is related to a person
	 */
	private String lastName;

	/**
	 * Email address for person, if this record is related to a person
	 */
	private String email;

	/**
	 * Phone number for person, if this record is related to a person
	 */
	private String phone;

	/**
	 * Status of the record; the meaning of this field will vary by record type
	 */
	private String status;

	/**
	 * this field contains firstName and lastName of customer
	 */
	private String fullName;

	/**
	 * The name of the location
	 */
	private String locationName;

	/**
	 * The address1 of location
	 */
	private String address1;

	/**
	 * The address2 of location
	 */
	private String address2;

	/**
	 * The city of location
	 */
	private String city;

	/**
	 * The state of location
	 */
	private String state;

	/**
	 * The zip of location
	 */
	private String zip;

	/**
	 * The country of location
	 */
	private String country;

	/**
	 * The fax of location
	 */
	private String fax;
	
	/**
	 * The instrument name of a customer used
	 */
	private String instrumentType;

	/**
	 * If locationprofile of location is enabled, this field set to "Y" else set
	 * to "N" if a locationprofile is related to location
	 */
	private String enabled;
	//GSSP-343 Changes to fix solr issue for Online customers(Customer# Start with 903)
	private String locationExternalId;
	
	//POS-808 changes.
	private String lessonCount;
	
	
	private String secondaryEmail;

	public String getId() {
		return id;
	}

	@Field(SOLR_ID_FIELD)
	public void setId(String solrId) {
		this.id = solrId;
	}

	public Long getSiteId() {
		return siteId;
	}

	@Field(SOLR_SITE_ID_FIELD)
	public void setSiteId(Long siteId) {
		this.siteId = siteId;
	}

	public String getType() {
		return type;
	}

	@Field(SOLR_TYPE_FIELD)
	public void setType(String type) {
		this.type = type;
	}

	public Long getRecordId() {
		return recordId;
	}

	@Field(SOLR_RECORD_ID_FIELD)
	public void setRecordId(Long recordId) {
		this.recordId = recordId;
	}

	public String getExternalId() {
		return externalId;
	}

	@Field(SOLR_EXTERNAL_ID_FIELD)
	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}

	public String getFirstName() {
		return firstName;
	}

	@Field(SOLR_FIRST_NAME_FIELD)
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	@Field(SOLR_LAST_NAME_FIELD)
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getEmail() {
		return email;
	}

	@Field(SOLR_EMAIL_FIELD)
	public void setEmail(String email) {
		this.email = email;
	}

	public String getStatus() {
		return status;
	}

	@Field(SOLR_STATUS_FIELD)
	public void setStatus(String status) {
		this.status = status;
	}
	
	/* XXX: MEmes: why is there a need for a full name when the data
	 * returned has first and last?
	 */

	public String getFullName() {
		return fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}
	
	public String getLocationName() {
		return locationName;
	}

	@Field(SOLR_LOCATION_NAME_FIELD)
	public void setLocationName(String locationName) {
		this.locationName = locationName;
	}

	public String getAddress1() {
		return address1;
	}

	@Field(SOLR_ADDRESS_ONE_FIELD)
	public void setAddress1(String address1) {
		this.address1 = address1;
	}

	public String getAddress2() {
		return address2;
	}

	@Field(SOLR_ADDRESS_TWO_FIELD)
	public void setAddress2(String address2) {
		this.address2 = address2;
	}

	public String getCity() {
		return city;
	}

	@Field(SOLR_CITY_FIELD)
	public void setCity(String city) {
		this.city = city;
	}

	public String getState() {
		return state;
	}

	@Field(SOLR_STATE_FIELD)
	public void setState(String state) {
		this.state = state;
	}

	public String getZip() {
		return zip;
	}

	@Field(SOLR_ZIP_FIELD)
	public void setZip(String zip) {
		this.zip = zip;
	}

	public String getCountry() {
		return country;
	}

	@Field(SOLR_COUNTRY_FIELD)
	public void setCountry(String country) {
		this.country = country;
	}

	public String getPhone() {
		return phone;
	}

	@Field(SOLR_PHONE_FIELD)
	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getFax() {
		return fax;
	}

	@Field(SOLR_FAX_FIELD)
	public void setFax(String fax) {
		this.fax = fax;
	}

	public String getEnabled() {
		return enabled;
	}

	@Field(SOLR_ENABLED_FIELD)
	public void setEnabled(String enabled) {
		this.enabled = enabled;
	}
	
	public String getInstrumentType() {
		return instrumentType;
	}

	@Field(SOLR_INSTRUMENT_TYPE_FIELD)
	public void setInstrumentType(String instrumentType) {
		this.instrumentType = instrumentType;
	}
	
	
	
	
	public String getSecondaryEmail() {
		return secondaryEmail;
	}
	@Field(SOLR_SECONDARY_EMAIL)
	public void setSecondaryEmail(String secondaryEmail) {
		this.secondaryEmail = secondaryEmail;
	}

		//GSSP-343 Changes to fix solr issue for Online customers(Customer# Start with 903)
		public String getLocationExternalId() {
			return locationExternalId;
		}


		@Field(SOLR_LOCATION_EXTERNAL_ID_FIELD)
		public void setLocationExternalId(String locationExternalId) {
			this.locationExternalId = locationExternalId;
		}
		
		//POS-808 Changes
		public String getLessonCount() {
			return lessonCount;
		}
		@Field(SOLR_LESSON_COUNT)
		public void setLessonCount(String lessonCount) {
			this.lessonCount = lessonCount;
		}

	/**
	 * Stupid helper to set 'full name' based on two supplied name parts.
	 * 
	 * @param firstName
	 * @param lastName
	 */
	public void setFullName(String firstName, String lastName) {
	    if (StringUtils.isNotBlank(firstName) ||
            StringUtils.isNotBlank(lastName)) {
            StringBuilder buf = new StringBuilder();
            if (StringUtils.isNotBlank(firstName)) {
                 buf.append(firstName);
            }
            if (StringUtils.isNotBlank(firstName) &&
                StringUtils.isNotBlank(lastName)) {
                buf.append(" ");
            }
            if (StringUtils.isNotBlank(lastName)) {
                buf.append(lastName);
            }
            setFullName(buf.toString());
	    }
	}

	public String toString() {
		return new ToStringBuilder(this).append(SOLR_ID_FIELD, id)
            .append(SOLR_SITE_ID_FIELD, siteId)
            .append(SOLR_TYPE_FIELD, type)
            .append(SOLR_RECORD_ID_FIELD, recordId)
            .append(SOLR_EXTERNAL_ID_FIELD, externalId)
            .append(SOLR_FIRST_NAME_FIELD, firstName)
            .append(SOLR_LAST_NAME_FIELD, lastName)
            .append(SOLR_EMAIL_FIELD, email)
            .append(SOLR_PHONE_FIELD, phone)
            .append(SOLR_STATUS_FIELD, status)
			.append(SOLR_LOCATION_NAME_FIELD, locationName)
			.append(SOLR_ADDRESS_ONE_FIELD, address1)
			.append(SOLR_ADDRESS_TWO_FIELD, address2)
			.append(SOLR_STATE_FIELD, state)
			.append(SOLR_ZIP_FIELD, zip)
			.append(SOLR_COUNTRY_FIELD, country)
			.append(SOLR_FAX_FIELD, fax)
			.append(SOLR_ENABLED_FIELD, enabled).toString();
	}
}
