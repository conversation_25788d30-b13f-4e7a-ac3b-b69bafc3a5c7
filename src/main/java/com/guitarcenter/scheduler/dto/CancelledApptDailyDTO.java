package com.guitarcenter.scheduler.dto;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.enums.Canceled;

public class CancelledApptDailyDTO {
	private Long appointmentId;
	private Date startTime;
	private Date endTime;
	private Canceled canceled;
	private String roomName;
	private String activityName;
	private Instructor instructor;
	private List<Customer> customers = new ArrayList<Customer>();
	private Map<Long, Customer> customerMap = new HashMap<Long, Customer>();
	private String notes;
	//GSSP-214 changes
	private Instructor cancelledUser;
	private String cancelledTime;
	//GSSP-269 changes
	private String cancelledReason;
	public CancelledApptDailyDTO(Long pAppointmentId, Date pStartTime,
			Date pEndTime, String pCanceled, String pRoomName,
			String pActivityName) {
		this.appointmentId = pAppointmentId;
		this.startTime = pStartTime;
		this.endTime = pEndTime;
		this.canceled = Canceled.valueOf(pCanceled == null ? "N" : pCanceled);		
		this.roomName = pRoomName;
		this.activityName = pActivityName;
	}

	public Long getAppointmentId() {
		return appointmentId;
	}

	public Instructor getCancelledUser() {
		return cancelledUser;
	}

	public void setCancelledUser(Instructor instructor1) {
		this.cancelledUser = instructor1;
	}

	public Date getStartTime() {
		return startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public Canceled getCanceled() {
		return canceled;
	}

	

	public String getRoomName() {
		return roomName;
	}

	public String getActivityName() {
		return activityName;
	}

	public Instructor getInstructor() {
		return instructor;
	}

	public void setInstructor(Instructor instructor) {
		this.instructor = instructor;
	}

	public List<Customer> getCustomers() {
		return customers;
	}

	public void addCustomer(Customer customer) {
		Long customerId = customer.getCustomerId();
		Customer customer1 = customerMap.get(customerId);
		if (customer1 == null) {
			customers.add(customer);
			customerMap.put(customerId, customer);
		}
	}

	/**
	 * @return the notes
	 */
	public String getNotes() {
		return notes;
	}

	/**
	 * @param notes
	 *            the notes to set
	 */
	public void setNotes(String notes) {
		this.notes = notes;
	}


	public String getCancelledTime() {
		return cancelledTime;
	}

	public void setCancelledTime(String string) {
		this.cancelledTime = string;
	}
	public String getCancelledReason(){
		return cancelledReason;
	}
	public void setCancelledReason(String cancelledReason){
		this.cancelledReason = cancelledReason;
	}
	
}
