/**
 * @Title: OnetimeDTO.java
 * @Package com.guitarcenter.scheduler.dto
 * @Description: 
 * Copyright: Copyright (c) 2014 
 * Company:
 * 
 * <AUTHOR>
 * @date Jun 9, 2014 2:12:49 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.dto;

/**
 * @ClassName: OnetimeDTO
 * @Description: 
 * <AUTHOR>
 * @date Jun 9, 2014 2:12:49 PM
 *
 */
public class OnLineAvailableDTO {
	private Long onlineAvailabilityId;
	private String startDate;
	private String fromTime;
	private String toTime;
	private Long instructorId;
	private String onetimeStartToEnd;
	private String weekDay;
	/**
	 * getter method
	 * @return the onetimeId
	 */
 
	public Long getOnlineAvailabilityId() {
		return onlineAvailabilityId;
	}
	public void setOnlineAvailabilityId(Long onlineAvailabilityId) {
		this.onlineAvailabilityId = onlineAvailabilityId;
	}
	/**
	 * getter method
	 * @return the startDate
	 */
	public String getStartDate() {
		return startDate;
	}
	/**
	 * setter method
	 * @param startDate the startDate to set
	 */
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}
	/**
	 * getter method
	 * @return the fromTime
	 */
	public String getFromTime() {
		return fromTime;
	}
	/**
	 * setter method
	 * @param fromTime the fromTime to set
	 */
	public void setFromTime(String fromTime) {
		this.fromTime = fromTime;
	}
	/**
	 * getter method
	 * @return the toTime
	 */
	public String getToTime() {
		return toTime;
	}
	/**
	 * setter method
	 * @param toTime the toTime to set
	 */
	public void setToTime(String toTime) {
		this.toTime = toTime;
	}
	/**
	 * getter method
	 * @return the instructorId
	 */
	public Long getInstructorId() {
		return instructorId;
	}
	/**
	 * setter method
	 * @param instructorId the instructorId to set
	 */
	public void setInstructorId(Long instructorId) {
		this.instructorId = instructorId;
	}
	/**
	 * getter method
	 * @return the onetimeStartToEnd
	 */
	public String getOnetimeStartToEnd() {
		return onetimeStartToEnd;
	}
	/**
	 * setter method
	 * @param onetimeStartToEnd the onetimeStartToEnd to set
	 */
	public void setOnetimeStartToEnd(String onetimeStartToEnd) {
		this.onetimeStartToEnd = onetimeStartToEnd;
	}
 
	public String getWeekDay() {
		return weekDay;
	}
	public void setWeekDay(String weekDay) {
		this.weekDay = weekDay;
	}
	@Override
	public String toString() {
		return "OnLineAvailableDTO [onlineAvailabilityId=" + onlineAvailabilityId + ", startDate=" + startDate + ", fromTime=" + fromTime
				+ ", toTime=" + toTime + ", instructorId=" + instructorId + ", onetimeStartToEnd=" + onetimeStartToEnd
				+ ", weekDay=" + weekDay + "]";
	}
	 
	
}
