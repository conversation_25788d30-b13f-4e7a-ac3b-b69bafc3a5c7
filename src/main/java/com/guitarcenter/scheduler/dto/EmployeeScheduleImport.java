package com.guitarcenter.scheduler.dto;

import java.util.List;

import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElementWrapper;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;

@XmlRootElement(name = "EmployeeScheduleImport")
@XmlType(propOrder = {
	    "startTime",
	    "endTime",
	    "deleteLevel",
	    "validationLevel",
	    "employeeSchedule"
	})
public class EmployeeScheduleImport {

    private String startTime;
    private String endTime;
    private String deleteLevel;
    private String validationLevel;
    private List<EmployeeScheduleDTO> employeeSchedule;
	
    @XmlElement(name = "StartTime")
    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    @XmlElement(name = "EndTime")
    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    @XmlElement(name = "DeleteLevel")
    public String getDeleteLevel() {
        return deleteLevel;
    }

    public void setDeleteLevel(String deleteLevel) {
        this.deleteLevel = deleteLevel;
    }

    @XmlElement(name = "ValidationLevel")
    public String getValidationLevel() {
        return validationLevel;
    }

    public void setValidationLevel(String validationLevel) {
        this.validationLevel = validationLevel;
    }

    @XmlElement(name = "EmployeeSchedule")
    public List<EmployeeScheduleDTO> getEmployeeSchedule() {
        return employeeSchedule;
    }

    public void setEmployeeSchedule(List<EmployeeScheduleDTO> employeeSchedule) {
        this.employeeSchedule = employeeSchedule;
    }
}
