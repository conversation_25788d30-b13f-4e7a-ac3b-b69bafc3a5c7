package com.guitarcenter.scheduler.dto;

import java.util.Date;

public class TimeoffDateDTO {

	private Long instructorId;
	private Date startTime;
	private Date endTime;

	public TimeoffDateDTO() {
		super();
	}

	public TimeoffDateDTO(Long instructorId, Date startTime, Date endTime) {
		super();
		this.instructorId = instructorId;
		this.startTime = startTime;
		this.endTime = endTime;
	}

	public TimeoffDateDTO(Date startTime, Date endTime) {
		super();
		this.startTime = startTime;
		this.endTime = endTime;
	}

	/**
	 * @return the instructorId
	 */
	public Long getInstructorId() {
		return instructorId;
	}

	/**
	 * @param instructorId
	 *            the instructorId to set
	 */
	public void setInstructorId(Long instructorId) {
		this.instructorId = instructorId;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

}
