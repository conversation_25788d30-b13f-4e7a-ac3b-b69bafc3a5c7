package com.guitarcenter.scheduler.dto;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.joda.time.DateTime;

import com.google.common.base.Objects;

/**
 * Created by josedeng on 6/13/2014.
 */
public class TimeLineDTO {

    private DateTime mStartDateTime;
    private DateTime mEndDateTime;

    public TimeLineDTO() {
    }

    public TimeLineDTO(DateTime pStartDateTime, DateTime pEndDateTime) {
        this.mStartDateTime = pStartDateTime;
        this.mEndDateTime = pEndDateTime;
    }

    public DateTime getStartDateTime() {
        return mStartDateTime;
    }

    public DateTime getEndDateTime() {
        return mEndDateTime;
    }

    public void setStartDateTime(DateTime pStartDateTime) {
        this.mStartDateTime = pStartDateTime;
    }

    public void setEndDateTime(DateTime pEndDateTime) {
        this.mEndDateTime = pEndDateTime;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) { return false; }
        if (obj == this) { return true; }
        if (obj.getClass() != getClass()) {
            return false;
        }
        TimeLineDTO rhs = (TimeLineDTO) obj;
        return new EqualsBuilder()
                .append(mStartDateTime, rhs.mStartDateTime)
                .append(mEndDateTime, rhs.mEndDateTime)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(mStartDateTime, mEndDateTime);
    }
}
