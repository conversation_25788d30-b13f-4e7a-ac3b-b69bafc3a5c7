package com.guitarcenter.scheduler.dto;

import java.util.Date;
import java.util.List;

import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

@XmlRootElement(name = "EmployeeSchedule")
@XmlType(propOrder = {
		"instructorId",
		"startTime",
	    "endTime",
	    "store",
	    "breakActvity"
	})
public class EmployeeScheduleDTO {

	private String instructorId;
	private String startTime;
	private String endTime;	
	private List<Break> breakActvity;
	private String actvity;
	private Date startDateTime;
	private Date endDateTime;
	private String store;
	
	@XmlElement(name = "EmployeeXrefCode")
	public String getInstructorId() {
		return instructorId;
	}
	public void setInstructorId(String instructorId) {
		this.instructorId = instructorId;
	}
	
	@XmlElement(name = "StartTime")
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	
	@XmlElement(name = "EndTime")
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	
	@XmlElement(name = "Break")
	public List<Break> getBreakActvity() {
		return breakActvity;
	}
	public void setBreakActvity(List<Break> breakActvity) {
		this.breakActvity = breakActvity;
	}
	
	@XmlTransient
	public String getActvity() {
		return actvity;
	}
	public void setActvity(String actvity) {
		this.actvity = actvity;
	}
	
	@XmlTransient
	public Date getStartDateTime() {
		return startDateTime;
	}
	public void setStartDateTime(Date startDateTime) {
		this.startDateTime = startDateTime;
	}
	
	@XmlTransient
	public Date getEndDateTime() {
		return endDateTime;
	}
	public void setEndDateTime(Date endDateTime) {
		this.endDateTime = endDateTime;
	}
	
	@XmlElement(name = "OrgUnitXrefCode")
	public String getStore() {
		return store;
	}
	public void setStore(String store) {
		this.store = store;
	}
}
