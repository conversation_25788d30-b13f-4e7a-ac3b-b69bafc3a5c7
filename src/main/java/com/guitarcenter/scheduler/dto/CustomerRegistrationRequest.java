package com.guitarcenter.scheduler.dto;

public class CustomerRegistrationRequest {
    private String firstName;
    private String lastName;
    private String email;
    private String password;
    private String googleRecaptcha;
    private String confirmPassword;
    private String gRecaptchaResponse;
    private boolean requireCaptcha;
    private String pageLocation;
    private String _dynSessConf;
    private boolean SPA;
	public String getFirstName() {
		return firstName;
	}
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}
	public String getLastName() {
		return lastName;
	}
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
	public String getGoogleRecaptcha() {
		return googleRecaptcha;
	}
	public void setGoogleRecaptcha(String googleRecaptcha) {
		this.googleRecaptcha = googleRecaptcha;
	}
	public String getConfirmPassword() {
		return confirmPassword;
	}
	public void setConfirmPassword(String confirmPassword) {
		this.confirmPassword = confirmPassword;
	}
	public String getgRecaptchaResponse() {
		return gRecaptchaResponse;
	}
	public void setgRecaptchaResponse(String gRecaptchaResponse) {
		this.gRecaptchaResponse = gRecaptchaResponse;
	}
	public boolean isRequireCaptcha() {
		return requireCaptcha;
	}
	public void setRequireCaptcha(boolean requireCaptcha) {
		this.requireCaptcha = requireCaptcha;
	}
	public String getPageLocation() {
		return pageLocation;
	}
	public void setPageLocation(String pageLocation) {
		this.pageLocation = pageLocation;
	}
	public String get_dynSessConf() {
		return _dynSessConf;
	}
	public void set_dynSessConf(String _dynSessConf) {
		this._dynSessConf = _dynSessConf;
	}
	public boolean isSPA() {
		return SPA;
	}
	public void setSPA(boolean sPA) {
		SPA = sPA;
	}
	
	@Override
	public String toString() {
		return "CustomerRegistrationRequest [firstName=" + firstName + ", lastName=" + lastName + ", email=" + email
				+ ", password=" + password + ", googleRecaptcha=" + googleRecaptcha + ", confirmPassword="
				+ confirmPassword + ", gRecaptchaResponse=" + gRecaptchaResponse + ", requireCaptcha=" + requireCaptcha
				+ ", pageLocation=" + pageLocation + ", _dynSessConf=" + _dynSessConf + ", SPA=" + SPA + "]";
	}

   
    
}
