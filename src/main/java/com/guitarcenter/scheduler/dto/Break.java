package com.guitarcenter.scheduler.dto;

import java.util.List;

import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;

@XmlRootElement(name = "break")
@XmlType(propOrder = {
	    "startTime",
	    "endTime",
	    "breakType"
	})
public class Break {

	private String startTime;
	private String endTime;
	private String breakType;
	
	@XmlElement(name = "StartTime")
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	
	@XmlElement(name = "EndTime")
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	
	@XmlElement(name = "BreakType")
	public String getBreakType() {
		return breakType;
	}
	public void setBreakType(String breakType) {
		this.breakType = breakType;
	}
}
