package com.guitarcenter.scheduler.dto;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.guitarcenter.scheduler.dao.criterion.dto.ActiveStudentsDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.AppointmentHistoryDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.ExportDetailsDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InActiveStudentsDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorAppointmentStatusDTO;
import com.guitarcenter.scheduler.model.Customer;

public class InstructorReportPagingDTO {

	private String currentDate;
	private List<InstructorInfoDTO> instructorInfo;
	/**
	 * For paginating by instructor
	 */
	private InstructorInfoDTO instructorInfoDTO;
	private List<InstructorScheduleReportDTO> instructorReports;
	private List<InstructorAppointmentStatusDTO> instructorAppointmentStatusReports;
	//added for Active Student Report
	private List<ActiveStudentsDTO> activeStudentsReports;
	private List<InstructorScheduleReportDTO> inActiveStudentsReports;
	private InActiveStudentsDTO inActiveStudentsDTO;

	private ActiveStudentsDTO activeStudentsDTO;
	private ExportDetailsDTO exportDetailsDTO;
	//added for GSSP-213
		private Long profile1;
		private Long activeAppointments;
		private Long activeStudents;
		private Long canceledAppointmentsCount;
		private String location;
		private Long storeNumber;
		private Long serviceCount;
		private Long roomCount;
		private Long activityCount;
		private String queryStartDate;
		private String queryEndDate;
		
		private String studioHours;
		
		private Long totalInstructors;
	//added for GSSP-210
		private AppointmentHistoryDTO appointmentHistoryDTO;
		private Long appointmentId;
		private String updatedBy;
		private Date updatedTimestamp;
		private String customerName;
		public String getCustomerName() {
			return customerName;
		}
		public void setCustomerName(String customerName) {
			this.customerName = customerName;
		}
		private String serviceType;
		private String lessonType;
		private Date startDate;
		private Date endDate;
		private String isRecurring;
		private String Time;
		private Long duration;
		private String instructor;
		private String room;
		private String note;
		private String isCancelled;
		private Map<Long, Customer> customerMap = new HashMap<Long, Customer>();
		public Long getAppointmentId() {
			return appointmentId;
		}
		public void setAppointmentId(Long appointmentId) {
			this.appointmentId = appointmentId;
		}
		public String getUpdatedBy() {
			return updatedBy;
		}
		public void setUpdatedBy(String updatedBy) {
			this.updatedBy = updatedBy;
		}
		public Date getUpdatedTimestamp() {
			return updatedTimestamp;
		}
		public void setUpdatedTimestamp(Date updatedTimestamp) {
			this.updatedTimestamp = updatedTimestamp;
		}
		
		public String getServiceType() {
			return serviceType;
		}
		public void setServiceType(String serviceType) {
			this.serviceType = serviceType;
		}
		public String getLessonType() {
			return lessonType;
		}
		public void setLessonType(String lessonType) {
			this.lessonType = lessonType;
		}
		public Date getStartDate() {
			return startDate;
		}
		public void setStartDate(Date startDate) {
			this.startDate = startDate;
		}
		public Date getEndDate() {
			return endDate;
		}
		public void setEndDate(Date endDate) {
			this.endDate = endDate;
		}
		public String getIsRecurring() {
			return isRecurring;
		}
		public void setIsRecurring(String isRecurring) {
			this.isRecurring = isRecurring;
		}
		public String getTime() {
			return Time;
		}
		public void setTime(String time) {
			Time = time;
		}
		public Long getDuration() {
			return duration;
		}
		public void setDuration(Long duration) {
			this.duration = duration;
		}
		public String getInstructor() {
			return instructor;
		}
		public void setInstructor(String instructor) {
			this.instructor = instructor;
		}
		public String getRoom() {
			return room;
		}
		public void setRoom(String room) {
			this.room = room;
		}
		public String getNote() {
			return note;
		}
		public void setNote(String note) {
			this.note = note;
		}
		public String getIsCancelled() {
			return isCancelled;
		}
		public void setIsCancelled(String isCancelled) {
			this.isCancelled = isCancelled;
		}
		public Map<Long, Customer> getCustomerMap() {
			return customerMap;
		}
		public void setCustomerMap(Map<Long, Customer> customerMap) {
			this.customerMap = customerMap;
		}
		public AppointmentHistoryDTO getAppointmentHistoryDTO() {
			return appointmentHistoryDTO;
		}
		public void setAppointmentHistoryDTO(AppointmentHistoryDTO appointmentHistoryDTO) {
			this.appointmentHistoryDTO = appointmentHistoryDTO;
		}
		public Long getTotalInstructors() {
			return totalInstructors;
		}
		public void setTotalInstructors(Long totalInstructors) {
			this.totalInstructors = totalInstructors;
		}
		private Long totalConflictingAppointments;

	
	public String getStudioHours() {
			return studioHours;
		}
		public void setStudioHours(String studioHours) {
			this.studioHours = studioHours;
		}
		public Long getTotalConflictingAppointments() {
			return totalConflictingAppointments;
		}
		public void setTotalConflictingAppointments(Long totalConflictingAppointments) {
			this.totalConflictingAppointments = totalConflictingAppointments;
		}
	public String getQueryStartDate() {
			return queryStartDate;
		}
		public void setQueryStartDate(String queryStartDate) {
			this.queryStartDate = queryStartDate;
		}
		public String getQueryEndDate() {
			return queryEndDate;
		}
		public void setQueryEndDate(String queryEndDate) {
			this.queryEndDate = queryEndDate;
		}
	public Long getProfile1() {
			return profile1;
		}
		public void setProfile1(Long profile1) {
			this.profile1 = profile1;
		}
		public Long getActiveAppointments() {
			return activeAppointments;
		}
		public void setActiveAppointments(Long activeAppointments) {
			this.activeAppointments = activeAppointments;
		}
		public Long getActiveStudents() {
			return activeStudents;
		}
		public void setActiveStudents(Long activeStudents) {
			this.activeStudents = activeStudents;
		}
		public Long getCanceledAppointmentsCount() {
			return canceledAppointmentsCount;
		}
		public void setCanceledAppointmentsCount(Long canceledAppointmentsCount) {
			this.canceledAppointmentsCount = canceledAppointmentsCount;
		}
		public String getLocation() {
			return location;
		}
		public void setLocation(String location) {
			this.location = location;
		}
		public Long getStoreNumber() {
			return storeNumber;
		}
		public void setStoreNumber(Long storeNumber) {
			this.storeNumber = storeNumber;
		}
		public Long getServiceCount() {
			return serviceCount;
		}
		public void setServiceCount(Long serviceCount) {
			this.serviceCount = serviceCount;
		}
		public Long getRoomCount() {
			return roomCount;
		}
		public void setRoomCount(Long roomCount) {
			this.roomCount = roomCount;
		}
		public Long getActivityCount() {
			return activityCount;
		}
		public void setActivityCount(Long activityCount) {
			this.activityCount = activityCount;
		}
	public InstructorReportPagingDTO() {
		super();
		// TODO Auto-generated constructor stub
	}
	public InstructorReportPagingDTO(List<ActiveStudentsDTO> activeStudentsReports,ActiveStudentsDTO activeStudentsDTO) {
		this.activeStudentsReports =activeStudentsReports;
		this.activeStudentsDTO=activeStudentsDTO;
	}
	
	public ExportDetailsDTO getExportDetailsDTO() {
		return exportDetailsDTO;
	}
	public void setExportDetailsDTO(ExportDetailsDTO exportDetailsDTO) {
		this.exportDetailsDTO = exportDetailsDTO;
	}
	public List<ActiveStudentsDTO> getActiveStudentsReports() {
		return activeStudentsReports;
	}
	public void setActiveStudentsReports(
			List<ActiveStudentsDTO> activeStudentsReports) {
		this.activeStudentsReports = activeStudentsReports;
	}
	public ActiveStudentsDTO getActiveStudentsDTO() {
		return activeStudentsDTO;
	}
	public void setActiveStudentsDTO(ActiveStudentsDTO activeStudentsDTO) {
		this.activeStudentsDTO = activeStudentsDTO;
	}
	/**
	 * @return the instructorInfoDTO
	 */
	public InstructorInfoDTO getInstructorInfoDTO() {
		return instructorInfoDTO;
	}

	/**
	 * @param instructorInfoDTO
	 *            the instructorInfoDTO to set
	 */
	public void setInstructorInfoDTO(InstructorInfoDTO instructorInfoDTO) {
		this.instructorInfoDTO = instructorInfoDTO;
	}

	/**
	 * @return the currentDate
	 */
	public String getCurrentDate() {
		return currentDate;
	}

	/**
	 * @param currentDate
	 *            the currentDate to set
	 */
	public void setCurrentDate(String currentDate) {
		this.currentDate = currentDate;
	}

	/**
	 * @return the instructorInfo
	 */
	public List<InstructorInfoDTO> getInstructorInfo() {
		return instructorInfo;
	}

	/**
	 * @param instructorInfo
	 *            the instructorInfo to set
	 */
	public void setInstructorInfo(List<InstructorInfoDTO> instructorInfo) {
		this.instructorInfo = instructorInfo;
	}

	/**
	 * @return the instructorReports
	 */
	public List<InstructorScheduleReportDTO> getInstructorReports() {
		return instructorReports;
	}

	/**
	 * @param instructorReports
	 *            the instructorReports to set
	 */
	public void setInstructorReports(
			List<InstructorScheduleReportDTO> instructorReports) {
		this.instructorReports = instructorReports;
	}
	public List<InstructorScheduleReportDTO> getInActiveStudentsReports() {
		return inActiveStudentsReports;
	}
	public void setInActiveStudentsReports(
			List<InstructorScheduleReportDTO> list) {
		this.inActiveStudentsReports = list;
	}
	public InActiveStudentsDTO getInActiveStudentsDTO() {
		return inActiveStudentsDTO;
	}
	public void setInActiveStudentsDTO(InActiveStudentsDTO inActiveStudentsDTO) {
		this.inActiveStudentsDTO = inActiveStudentsDTO;
	}
	public List<InstructorAppointmentStatusDTO> getInstructorAppointmentStatusReports() {
		return instructorAppointmentStatusReports;
	}
	public void setInstructorAppointmentStatusReports(
			List<InstructorAppointmentStatusDTO> instructorAppointmentStatusReports) {
		this.instructorAppointmentStatusReports = instructorAppointmentStatusReports;
	}
	
	

}
