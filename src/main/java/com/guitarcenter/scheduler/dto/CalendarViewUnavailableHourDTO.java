package com.guitarcenter.scheduler.dto;

public class CalendarViewUnavailableHourDTO {

	private String startTime;
	private String duration;

	public CalendarViewUnavailableHourDTO() {

	}

	public CalendarViewUnavailableHourDTO(String startTime, String duration) {
		this.startTime = startTime;
		this.duration = duration;
	}

	/**
	 * @return the startTime
	 */
	public String getStartTime() {
		return startTime;
	}

	/**
	 * @param startTime
	 *            the startTime to set
	 */
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	/**
	 * @return the duration
	 */
	public String getDuration() {
		return duration;
	}

	/**
	 * @param duration
	 *            the duration to set
	 */
	public void setDuration(String duration) {
		this.duration = duration;
	}

}
