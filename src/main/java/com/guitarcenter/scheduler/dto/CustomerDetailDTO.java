package com.guitarcenter.scheduler.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * CustomerDetail DTO
 *
 * @Date 4/23/2020 6:52 PM
 * <AUTHOR>
 **/
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public class CustomerDetailDTO {
    private Long gcId;

    private String customerExternalId;

    private String customerFullName;

    @JsonFormat(pattern="MMM dd, yyyy")
    private Date birthday;

    private String phoneNumber;

    private String email;
    
    private String secondaryEmail;
    
    private String customerStatus;

    @JsonFormat(pattern="MM-dd-yyyy")
    private Date lastBooked;

    private Integer lessonsCount;

    private String futureAppointments;

    private String headSculpturePath;

    public String getHeadSculpturePath() {
        return headSculpturePath;
    }

    public void setHeadSculpturePath(String headSculpturePath) {
        this.headSculpturePath = headSculpturePath;
    }

    public Long getGcId() {
        return gcId;
    }

    public void setGcId(Long gcId) {
        this.gcId = gcId;
    }

    public String getCustomerExternalId() {
        return customerExternalId;
    }

    public void setCustomerExternalId(String customerExternalId) {
        this.customerExternalId = customerExternalId;
    }

    public String getCustomerFullName() {
        return customerFullName;
    }

    public void setCustomerFullName(String customerFullName) {
        this.customerFullName = customerFullName;
    }

    public void generateAndSetFullName(String firstName,String lastName){
        if (StringUtils.isNotBlank(firstName)
                || StringUtils.isNotBlank(lastName)) {
            StringBuilder buf = new StringBuilder();
            if (StringUtils.isNotBlank(firstName)) {
                buf.append(firstName);
            }
            if (StringUtils.isNotBlank(firstName)
                    && StringUtils.isNotBlank(lastName)) {
                buf.append(" ");
            }
            if (StringUtils.isNotBlank(lastName)) {
                buf.append(lastName);
            }
            setCustomerFullName(buf.toString());
        }
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Date getLastBooked() {
        return lastBooked;
    }

    public void setLastBooked(Date lastBooked) {
        this.lastBooked = lastBooked;
    }

    public Integer getLessonsCount() {
        return lessonsCount;
    }

    public void setLessonsCount(Integer lessonsCount) {
        this.lessonsCount = lessonsCount;
    }

    public String getFutureAppointments() {
        return futureAppointments;
    }

    public void setFutureAppointments(String futureAppointments) {
        this.futureAppointments = futureAppointments;
    }

	public String getSecondaryEmail() {
		return secondaryEmail;
	}

	public void setSecondaryEmail(String secondaryEmail) {
		this.secondaryEmail = secondaryEmail;
	}

	public String getCustomerStatus() {
		return customerStatus;
	}

	public void setCustomerStatus(String customerStatus) {
		this.customerStatus = customerStatus;
	}

	@Override
	public String toString() {
		return "CustomerDetailDTO [gcId=" + gcId + ", customerExternalId=" + customerExternalId + ", customerFullName="
				+ customerFullName + ", birthday=" + birthday + ", phoneNumber=" + phoneNumber + ", email=" + email
				+ ", secondaryEmail=" + secondaryEmail + ", lastBooked=" + lastBooked + ", lessonsCount=" + lessonsCount
				+ ", futureAppointments=" + futureAppointments + ", headSculpturePath=" + headSculpturePath + "]";
	}
}
