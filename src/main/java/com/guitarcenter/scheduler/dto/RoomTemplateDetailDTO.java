package com.guitarcenter.scheduler.dto;

import java.util.List;

import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.RoomSize;
import com.guitarcenter.scheduler.model.RoomType;
import com.guitarcenter.scheduler.model.Service;

public class RoomTemplateDetailDTO {
	
	private Long roomTemplateId;
	private Long version;
	private String enabled;
	private String isSplitRoom;
	private String activities;
	private String roomTemplateName;
	private Boolean globalChange = false;
	private List<RoomType> roomTypeList;
	private String roomType;
	private Long roomTypeId;
	private List<RoomSize> roomSizeList;
	private String roomSize;
	private Long roomSizeId;
	private List<Activity> selectedActivities;
	private List<Activity> unSelectedActivities;
	private List<Service> selectedServices;
	private String services;
	private List<Service> unSelectedServices;
	public RoomTemplateDetailDTO(Long id) {
		this.roomTemplateId = id;
	}
	public RoomTemplateDetailDTO(){}
	public Long getRoomTemplateId() {
		return roomTemplateId;
	}
	public void setRoomTemplateId(Long roomTemplateId) {
		this.roomTemplateId = roomTemplateId;
	}
	public Long getVersion() {
		return version;
	}
	public void setVersion(Long version) {
		this.version = version;
	}
	public String getEnabled() {
		return enabled;
	}
	public void setEnabled(String enabled) {
		this.enabled = enabled;
	}
	public String getIsSplitRoom() {
		return isSplitRoom;
	}
	public void setIsSplitRoom(String isSplitRoom) {
		this.isSplitRoom = isSplitRoom;
	}
	public String getActivities() {
		return activities;
	}
	public void setActivities(String activities) {
		this.activities = activities;
	}
	public String getRoomTemplateName() {
		return roomTemplateName;
	}
	public void setRoomTemplateName(String roomTemplateName) {
		this.roomTemplateName = roomTemplateName;
	}
	public List<RoomType> getRoomTypeList() {
		return roomTypeList;
	}
	public void setRoomTypeList(List<RoomType> roomTypeList) {
		this.roomTypeList = roomTypeList;
	}
	public String getRoomType() {
		return roomType;
	}
	public void setRoomType(String roomType) {
		this.roomType = roomType;
	}
	public Long getRoomTypeId() {
		return roomTypeId;
	}
	public void setRoomTypeId(Long roomTypeId) {
		this.roomTypeId = roomTypeId;
	}
	public List<RoomSize> getRoomSizeList() {
		return roomSizeList;
	}
	public void setRoomSizeList(List<RoomSize> roomSizeList) {
		this.roomSizeList = roomSizeList;
	}
	public String getRoomSize() {
		return roomSize;
	}
	public void setRoomSize(String roomSize) {
		this.roomSize = roomSize;
	}
	public Long getRoomSizeId() {
		return roomSizeId;
	}
	public void setRoomSizeId(Long roomSizeId) {
		this.roomSizeId = roomSizeId;
	}
	public List<Activity> getSelectedActivities() {
		return selectedActivities;
	}
	public void setSelectedActivities(List<Activity> selectedActivities) {
		this.selectedActivities = selectedActivities;
	}
	public List<Activity> getUnSelectedActivities() {
		return unSelectedActivities;
	}
	public void setUnSelectedActivities(List<Activity> unSelectedActivities) {
		this.unSelectedActivities = unSelectedActivities;
	}
	public List<Service> getSelectedServices() {
		return selectedServices;
	}
	public void setSelectedServices(List<Service> selectedServices) {
		this.selectedServices = selectedServices;
	}
	public String getServices() {
		return services;
	}
	public void setServices(String services) {
		this.services = services;
	}
	public List<Service> getUnSelectedServices() {
		return unSelectedServices;
	}
	public void setUnSelectedServices(List<Service> unSelectedServices) {
		this.unSelectedServices = unSelectedServices;
	}
	public Boolean getGlobalChange() {
		return globalChange;
	}
	public void setGlobalChange(Boolean globalChange) {
		this.globalChange = globalChange;
	}
	
}
