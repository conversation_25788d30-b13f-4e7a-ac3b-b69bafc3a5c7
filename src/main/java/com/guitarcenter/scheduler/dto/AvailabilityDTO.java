package com.guitarcenter.scheduler.dto;

import java.util.List;
import java.util.Set;

public class AvailabilityDTO{

	public AvailabilityDTO(Set<StudioHourDTO> set,String message,Boolean status){
		this.set = set;
		this.message = message;
		this.status = status;
	}
	
	public AvailabilityDTO(Set<StudioHourDTO> set,String message,Boolean status,List<EditHourShowDTO> editHourShowDTOs){
		this.set = set;
		this.message = message;
		this.status = status;
		this.editHourShowDTOs = editHourShowDTOs;
	}
	
	public AvailabilityDTO(){}
	private List<EditHourShowDTO> editHourShowDTOs;
	private Set<StudioHourDTO> set;
	private String message;
	private Boolean status;
	private String availabilityString;
	private Long availabilityversion;
	private Long idString;
	
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public Boolean getStatus() {
		return status;
	}
	public void setStatus(Boolean status) {
		this.status = status;
	}
	public Set<StudioHourDTO> getSet() {
		return set;
	}
	public void setSet(Set<StudioHourDTO> set) {
		this.set = set;
	}

	public final List<EditHourShowDTO> getEditHourShowDTOs() {
		return editHourShowDTOs;
	}

	public final void setEditHourShowDTOs(List<EditHourShowDTO> editHourShowDTOs) {
		this.editHourShowDTOs = editHourShowDTOs;
	}

	public final String getAvailabilityString() {
		return availabilityString;
	}

	public final void setAvailabilityString(String availabilityString) {
		this.availabilityString = availabilityString;
	}

	public final Long getAvailabilityversion() {
		return availabilityversion;
	}

	public final void setAvailabilityversion(Long availabilityversion) {
		this.availabilityversion = availabilityversion;
	}

	public Long getIdString() {
		return idString;
	}

	public void setIdString(Long idString) {
		this.idString = idString;
	}

	
	
}
