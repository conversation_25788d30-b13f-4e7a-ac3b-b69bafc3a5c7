package com.guitarcenter.scheduler.dto;

public class ProfileServiceDTO implements Comparable<ProfileServiceDTO>{

	private Long serviceId;
	private String serviceName;
	private String avaibleAct;
	private boolean enable;

	public String getAvaibleAct() {
		return avaibleAct;
	}

	public void setAvaibleAct(String avaibleAct) {
		this.avaibleAct = avaibleAct;
	}


	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public String getServiceName() {
		return serviceName;
	}

	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}

	public Long getServiceId() {
		return serviceId;
	}

	public void setServiceId(Long serviceId) {
		this.serviceId = serviceId;
	}

    @Override
    public int compareTo(ProfileServiceDTO o) {
        return (int) (serviceId - o.getServiceId());
    }

}
