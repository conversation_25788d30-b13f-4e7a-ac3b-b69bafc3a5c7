package com.guitarcenter.scheduler.dto;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Id;

/**
 * Represents appointments in the scheduler
 */

public class UserLogDTO implements java.io.Serializable {

	private static final long	serialVersionUID	= -3273105690730365834L;
	private Long				personID;
	private Integer				version;
	private Date			    Updated;
	private Date				loginTimeStamp;
	private Date				logOutTimeStamp;
	private Integer				roleId;
	private String		        locationExternalId;
	private String				UpdatedBy;
	 

	public UserLogDTO() {
	}

	@Id
	@Column(name = "APPOINTMENT_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getPersonID() {
		return personID;
	}


	public void setPersonID(Long personID) {
		this.personID = personID;
	}


	public Integer getVersion() {
		return version;
	}


	public void setVersion(Integer version) {
		this.version = version;
	}


	public Date getUpdated() {
		return Updated;
	}


	public void setUpdated(Date updated) {
		Updated = updated;
	}


	public Date getLoginTimeStamp() {
		return loginTimeStamp;
	}


	public void setLoginTimeStamp(Date loginTimeStamp) {
		this.loginTimeStamp = loginTimeStamp;
	}


	public Date getLogOutTimeStamp() {
		return logOutTimeStamp;
	}


	public void setLogOutTimeStamp(Date logOutTimeStamp) {
		this.logOutTimeStamp = logOutTimeStamp;
	}


	public Integer getRoleId() {
		return roleId;
	}


	public void setRoleId(Integer roleId) {
		this.roleId = roleId;
	}


	public String getUpdatedBy() {
		return UpdatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		UpdatedBy = updatedBy;
	}

	public String getLocationExternalId() {
		return locationExternalId;
	}

	public void setLocationExternalId(String locationExternalId) {
		this.locationExternalId = locationExternalId;
	}


	
	
	
}
