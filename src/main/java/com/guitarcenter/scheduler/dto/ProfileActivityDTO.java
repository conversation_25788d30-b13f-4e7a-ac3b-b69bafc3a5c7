package com.guitarcenter.scheduler.dto;

import java.util.HashSet;
import java.util.Set;

public class ProfileActivityDTO {
	private Long activityId;
	private Set<String> service = new HashSet<String>();
	private String minAttender;
	private String maxAttender;
	private String duration;
	private String instrunctor;
	private boolean enable;

	public Long getActivityId() {
		return activityId;
	}

	public void setActivityId(Long activityId) {
		this.activityId = activityId;
	}

	public Set<String> getService() {
		return service;
	}

	public void setService(Set<String> service) {
		this.service = service;
	}

	public String getMinAttender() {
		return minAttender;
	}

	public void setMinAttender(String minAttender) {
		this.minAttender = minAttender;
	}

	public String getMaxAttender() {
		return maxAttender;
	}

	public void setMaxAttender(String maxAttender) {
		this.maxAttender = maxAttender;
	}

	public String getDuration() {
		return duration;
	}

	public void setDuration(String duration) {
		this.duration = duration;
	}

	public String getInstrunctor() {
		return instrunctor;
	}

	public void setInstrunctor(String instrunctor) {
		this.instrunctor = instrunctor;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

}
