package com.guitarcenter.scheduler.dto;

import java.util.List;

import com.guitarcenter.scheduler.dao.criterion.dto.ExportDetailsDTO;

/**
 * <AUTHOR>
 *
 */
public class InstructorScheduleReportDTO {

	private long instructorId;
	//For GSSP-170
	private long appointmentId;
	private String recurringStatus;
	private Boolean showCancelButton;
	
	private int attendees;
	private String duration;
	private String instructorName;
	private String startDate;
	private String endDate;
	private String startTime;
	private String endTime;
	private String activityType;
	private String customerName;
	private String signitureBlock;
	private String cancelled;
	private String show;
	private String noShow;
	private String in;
	private String out;
	private String timeFrame;
	//added for GSSP-213
	private Long profile1;
	private Long activeAppointments;
	private Long activeStudents;
	private Long canceledAppointmentsCount;
	private String location;
	private Long storeNumber;
	private Long serviceCount;
	private Long roomCount;
	private Long activityCount;
	private Long instructorCount;
	private List<InstructorReportPagingDTO> instructorReports;
	private ExportDetailsDTO exportDetailsDTO;
	/* Added for NewInsAptReport _ June 2015 Enhancement */
	private String roomName;

	private String queryStartDate;
	private String queryEndDate;
	//added for GSSP-185
	private String customerEmail;
	//added for GSSP-203
    private String checkIn;
    private String checkOut;
  //added for GSSP-252
    private String  external_id;
	public String getExternal_id() {
		return external_id;
	}

	public void setExternal_id(String external_id) {
		this.external_id = external_id;
	}

	//added for GSSP-205
    private String customerPhone;

	/**
	 * @return the timeFrame
	 */
	public String getTimeFrame() {
		return timeFrame;
	}

	/**
	 * @param timeFrame
	 *            the timeFrame to set
	 */
	public void setTimeFrame(String timeFrame) {
		this.timeFrame = timeFrame;
	}

	/**
	 * @return the in
	 */
	public String getIn() {
		return in;
	}

	/**
	 * @param in
	 *            the in to set
	 */
	public void setIn(String in) {
		this.in = in;
	}

	/**
	 * @return the out
	 */
	public String getOut() {
		return out;
	}

	/**
	 * @param out
	 *            the out to set
	 */
	public void setOut(String out) {
		this.out = out;
	}

	public String getQueryStartDate() {
		return queryStartDate;
	}

	public void setQueryStartDate(String queryStartDate) {
		this.queryStartDate = queryStartDate;
	}

	public String getQueryEndDate() {
		return queryEndDate;
	}

	public void setQueryEndDate(String queryEndDate) {
		this.queryEndDate = queryEndDate;
	}

	public InstructorScheduleReportDTO() {
		super();
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getShow() {
		return show;
	}

	public void setShow(String show) {
		this.show = show;
	}

	public String getNoShow() {
		return noShow;
	}

	public void setNoShow(String noShow) {
		this.noShow = noShow;
	}

	public String getDuration() {
		return duration;
	}

	public void setDuration(String duration) {
		this.duration = duration;
	}

	public String getCancelled() {
		return cancelled;
	}

	public void setCancelled(String cancelled) {
		this.cancelled = cancelled;
	}

	public long getInstructorId() {
		return instructorId;
	}

	public void setInstructorId(long instructorId) {
		this.instructorId = instructorId;
	}

	public String getInstructorName() {
		return instructorName;
	}

	public void setInstructorName(String instructorName) {
		this.instructorName = instructorName;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getActivityType() {
		return activityType;
	}

	public void setActivityType(String activityType) {
		this.activityType = activityType;
	}

	public int getAttendees() {
		return attendees;
	}

	public void setAttendees(int attendees) {
		this.attendees = attendees;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getSignitureBlock() {
		return signitureBlock;
	}

	public void setSignitureBlock(String signitureBlock) {
		this.signitureBlock = signitureBlock;
	}
	
	/* Added for NewInsAptReport _ June 2015 Enhancement */
	public String getRoomName() {
		return roomName;
	}
	
	/* Added for NewInsAptReport _ June 2015 Enhancement */
	public void setRoomName(String roomName) {
		this.roomName = roomName;
	}
	
	//For GSSP-170
	public long getAppointmentId() {
		return appointmentId;
	}

	//For GSSP-170
	public void setAppointmentId(long appointmentId) {
		this.appointmentId = appointmentId;
	}

	//For GSSP-170
	public String getRecurringStatus() {
		return recurringStatus;
	}

	//For GSSP-170
	public void setRecurringStatus(String recurringStatus) {
		this.recurringStatus = recurringStatus;
	}

	//For GSSP-170
	public Boolean getShowCancelButton() {
		return showCancelButton;
	}

	//For GSSP-170
	public void setShowCancelButton(Boolean showCancelButton) {
		this.showCancelButton = showCancelButton;
	}

	public String getCustomerEmail() {
		return customerEmail;
	}

	public void setCustomerEmail(String customerEmail) {
		this.customerEmail = customerEmail;
	}

	public String getCheckIn() {
		return checkIn;
	}

	public void setCheckIn(String checkIn) {
		this.checkIn = checkIn;
	}

	public String getCheckOut() {
		return checkOut;
	}

	public void setCheckOut(String checkOut) {
		this.checkOut = checkOut;
	}

	public String getCustomerPhone() {
		return customerPhone;
	}

	public void setCustomerPhone(String customerPhone) {
		this.customerPhone = customerPhone;
	}
//added for GSSP-213

	public Long getProfile1() {
		return profile1;
	}

	public void setProfile1(Long profile1) {
		this.profile1 = profile1;
	}

	public Long getActiveAppointments() {
		return activeAppointments;
	}

	public void setActiveAppointments(Long activeAppointments) {
		this.activeAppointments = activeAppointments;
	}

	public Long getActiveStudents() {
		return activeStudents;
	}

	public void setActiveStudents(Long activeStudents) {
		this.activeStudents = activeStudents;
	}

	public Long getCanceledAppointmentsCount() {
		return canceledAppointmentsCount;
	}

	public void setCanceledAppointmentsCount(Long canceledAppointmentsCount) {
		this.canceledAppointmentsCount = canceledAppointmentsCount;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public Long getStoreNumber() {
		return storeNumber;
	}

	public void setStoreNumber(Long storeNumber) {
		this.storeNumber = storeNumber;
	}

	public Long getServiceCount() {
		return serviceCount;
	}

	public void setServiceCount(Long serviceCount) {
		this.serviceCount = serviceCount;
	}

	public Long getRoomCount() {
		return roomCount;
	}

	public void setRoomCount(Long roomCount) {
		this.roomCount = roomCount;
	}

	public Long getActivityCount() {
		return activityCount;
	}

	public void setActivityCount(Long activityCount) {
		this.activityCount = activityCount;
	}

	public Long getInstructorCount() {
		return instructorCount;
	}

	public void setInstructorCount(Long instructorCount) {
		this.instructorCount = instructorCount;
	}

	public List<InstructorReportPagingDTO> getInstructorReports() {
		return instructorReports;
	}

	public void setInstructorReports(List<InstructorReportPagingDTO> instructorReports) {
		this.instructorReports = instructorReports;
	}

	public ExportDetailsDTO getExportDetailsDTO() {
		return exportDetailsDTO;
	}

	public void setExportDetailsDTO(ExportDetailsDTO exportDetailsDTO) {
		this.exportDetailsDTO = exportDetailsDTO;
	}

	
}
