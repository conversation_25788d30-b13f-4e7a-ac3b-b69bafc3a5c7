package com.guitarcenter.scheduler.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * @Date 4/21/2020 5:26 PM
 * <AUTHOR>
 **/
public class BookedAppointmentQueryDTO {

//    @JsonFormat(pattern="MM/dd/yyyy HH:mm")
    private String startDatetime;

//    @JsonFormat(pattern="MM/dd/yyyy HH:mm")
    private String endDatetime;

    private Long locationId;

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getStartDatetime() {
        return startDatetime;
    }

    public void setStartDatetime(String startDatetime) {
        this.startDatetime = startDatetime;
    }

    public String getEndDatetime() {
        return endDatetime;
    }

    public void setEndDatetime(String endDatetime) {
        this.endDatetime = endDatetime;
    }
}
