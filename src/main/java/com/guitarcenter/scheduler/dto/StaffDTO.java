package com.guitarcenter.scheduler.dto;

import java.util.List;

public class StaffDTO implements Comparable<StaffDTO>{

	private Long id;
	private String instructorName;
	private String email;
	private List<String> roleLocation;
	private String roleLocationName;
	private Boolean active;
	
	public StaffDTO(long id, String instructorName,String email,  String roleLocationName, Boolean status )
	{
		this.id = id;
		this.instructorName = instructorName;
		this.email= email;
		this.roleLocationName= roleLocationName;
		this.active = status;
		
	}
	
	public StaffDTO() {
	}

	public String getRoleLocationName() {
		return roleLocationName;
	}
	public void setRoleLocationName(String roleLocationName) {
		this.roleLocationName = roleLocationName;
	}
	public final String getInstructorName() {
		return instructorName;
	}
	public final void setInstructorName(String instructorName) {
		this.instructorName = instructorName;
	}
	public final String getEmail() {
		return email;
	}
	public final void setEmail(String email) {
		this.email = email;
	}
	public final Boolean getActive() {
		return active;
	}
	public final void setActive(Boolean active) {
		this.active = active;
	}
	public final List<String> getRoleLocation() {
		return roleLocation;
	}
	public final void setRoleLocation(List<String> roleLocation) {
		this.roleLocation = roleLocation;
	}
	public final Long getId() {
		return id;
	}
	public final void setId(Long id) {
		this.id = id;
	}
    @Override
    public int compareTo(StaffDTO o) {
        return (int) (id - o.getId());
    }
	
}
