package com.guitarcenter.scheduler.dto;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.AppointmentSeries;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.Service;
import com.guitarcenter.scheduler.model.enums.IsRecurring;

public class CreateAppointmentDTO {

	private static final Logger LOG = LoggerFactory
			.getLogger(CreateAppointmentDTO.class);

	private static final String SPACE = " ";
	private static final String SPLITOR_COMMA = ",";
	
	private String appointmentSeriesId;
	private String appointmentId;
	private Long serviceId;
	//private long activityId;
	private String activityId;
	private String activityName;
	private Long instructorId;
	private String instructorName;
	private Long roomId;
	private String startTime;
	private String isRecurring;
	private String customerId;
	private String startDate;
	private String endDate;
	private String note;
	private String bandName;
	private String duration;
	private String recurringStatus;
	
	private Long profileId;
	private String appStatus;
	private String orderId;
	private String email;
	private String locationId;
	private String phone;
	private CustomerDTO customerDetails;
	private String parentFullName;
	
	public String getAppointmentSeriesId() {
		return appointmentSeriesId;
	}

	public void setAppointmentSeriesId(String appointmentSeriesId) {
		this.appointmentSeriesId = appointmentSeriesId;
	}

	public String getAppointmentId() {
		return appointmentId;
	}

	public void setAppointmentId(String appointmentId) {
		this.appointmentId = appointmentId;
	}

	public String getActivityName() {
		return activityName;
	}

	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}

	public String getInstructorName() {
		return instructorName;
	}

	public void setInstructorName(String instructorName) {
		this.instructorName = instructorName;
	}

	public String getDuration() {
		return duration;
	}

	public void setDuration(String duration) {
		this.duration = duration;
	}

	public String getCustomerId() {
		return customerId;
	}

	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	/**
	 * getter method
	 * 
	 * @return the startDate
	 */
	public String getStartDate() {
		return startDate;
	}

	/**
	 * setter method
	 * 
	 * @param startDate
	 *            the startDate to set
	 */
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getIsRecurring() {
		return isRecurring;
	}

	public void setIsRecurring(String isRecurring) {
		this.isRecurring = isRecurring;
	}

	public Long getServiceId() {
		return serviceId;
	}

	public void setServiceId(Long serviceId) {
		this.serviceId = serviceId;
	}
	
	public String getActivityId() {
		return activityId;
	}

	public void setActivityId(String activityId) {
		this.activityId = activityId;
	}

	public Long getInstructorId() {
		return instructorId;
	}

	public void setInstructorId(Long instructorId) {
		this.instructorId = instructorId;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public Long getRoomId() {
		return roomId;
	}

	public void setRoomId(Long roomId) {
		this.roomId = roomId;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public String getBandName() {
		return bandName;
	}

	public void setBandName(String bandName) {
		this.bandName = bandName;
	}
	
	public String getRecurringStatus() {
		return recurringStatus;
	}

	public void setRecurringStatus(String recurringStatus) {
		this.recurringStatus = recurringStatus;
	}

	public Activity getActivity() {
		if(null != activityId && !"".equals(activityId)) {
			Activity a = new Activity();
				a.setActivityId(Long.parseLong(activityId));
				a.setActivityName(activityName);
				a.setService(getService());
			return a;
		} else {
			return null;
		}
	}

	public Service getService() {
		Service s = new Service();
		s.setServiceId(serviceId);
		return s;
	}

	public Instructor getInstructor() {
		if(null != instructorId) {
			Instructor i = new Instructor();
			i.setInstructorId(instructorId);
			return i;
		} else {
			return null;
		}
	}

	public Room getRoom() {
		Room r = new Room();
		r.setRoomId(roomId);
		return r;
	}

	public AppointmentSeries getAppointmentSeries() {
		AppointmentSeries series = new AppointmentSeries();
		if(null != getCustomers()) {
			series.setCustomers(getCustomers());
		}
		if(null != appointmentSeriesId && 0 != appointmentSeriesId.length()) {
			series.setAppointmentSeriesId(Long.parseLong(appointmentSeriesId));
		}
		series.setUpdated(new Date());
		
		String r = getIsRecurring();
		boolean isRecurring = false;
		if (null != r && 0 != r.length()) {
			isRecurring = Boolean.parseBoolean(r);
		}
		series.setIsRecurring((isRecurring == false) ? IsRecurring.N : IsRecurring.Y);
		
		Date seriStartTime = null;
		try {
			seriStartTime = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).parse(startDate);
		} catch (ParseException e) {
			LOG.error("CreateAppointmentDto.getAppointmentSeries: parse date error");
		}
		series.setSeriesStartTime(seriStartTime);
		
		Date seriEndTime = null;
		if(!isRecurring) {
			seriEndTime = seriStartTime;
		} else {
			try {
				seriEndTime = (null == endDate || 0 == endDate.length()) ? null : new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).parse(endDate);
			} catch (ParseException e) {
				LOG.error("CreateAppointmentDto.getAppointmentSeries: parse date error");
			}
		}
		series.setSeriesEndTime(seriEndTime);
		
		series.setActivity(getActivity());
		series.setBandName((null == bandName || 0 == bandName.length()) ? "" : bandName);
		
		return series;
	}

	public Appointment getAppointment() {
		Appointment appointment = new Appointment();
		if(null != appointmentId && 0 != appointmentId.length()) {
			appointment.setAppointmentId(Long.parseLong(appointmentId));
		}
		if(null != getInstructor()) {
			appointment.setInstructor(getInstructor());
		}
		appointment.setRoom(getRoom());
		appointment.setActivity(getActivity());
		appointment.setUpdated(new Date());
		
		Date st = null;
		try {
			String startDateString = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN).format(new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).parse(startDate));
			st = new SimpleDateFormat(DateTimeUtil.DATETIME_PATTERN).parse(startDateString + SPACE + startTime + ":00");
		} catch (ParseException e) {
			LOG.error("CreateAppointmentDto.getAppointment: parse date error");
		}
		appointment.setStartTime(st);
		//GSsp-279 changes
		Date et =null;
		try {
		 et = new DateTime(st).plusMinutes(Integer.parseInt(duration)+0).toDate();
		} catch (NumberFormatException e) {
			LOG.error("CreateAppointmentDto.getAppointment: number format error");
		}
		appointment.setEndTime(et);
		
		appointment.setCustomers(getCustomers());
		appointment.setBandName(getBandName());
		appointment.setNote((null == note || 0 == note.length()) ? "" : note);
		
		return appointment;
	}
	
	public Set<Customer> getCustomers() {
		if(null != customerId && 0 != customerId.length()) {
			String[] idstr = customerId.split(SPLITOR_COMMA);
			Set<Customer> customers = new HashSet<Customer>();
			for(int i=0; i<idstr.length; i++) {
				Customer c = new Customer();
				c.setCustomerId(Long.parseLong(idstr[i]));
				customers.add(c);
			}
			return customers;
		} else {
			return null;
		}
	}

	public Long getProfileId() {
		return profileId;
	}

	public void setProfileId(Long profileId) {
		this.profileId = profileId;
	}

	public String getAppStatus() {
		return appStatus;
	}

	public void setAppStatus(String appStatus) {
		this.appStatus = appStatus;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public String getLocationId() {
		return locationId;
	}

	public void setLocationId(String locationId) {
		this.locationId = locationId;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public CustomerDTO getCustomerDetails() {
		return customerDetails;
	}

	public void setCustomerDetails(CustomerDTO customerDetails) {
		this.customerDetails = customerDetails;
	}

	public String getParentFullName() {
		return parentFullName;
	}

	public void setParentFullName(String parentFullName) {
		this.parentFullName = parentFullName;
	}

	@Override
	public String toString() {
		return "CreateAppointmentDTO [appointmentSeriesId=" + appointmentSeriesId + ", appointmentId=" + appointmentId
				+ ", serviceId=" + serviceId + ", activityId=" + activityId + ", activityName=" + activityName
				+ ", instructorId=" + instructorId + ", instructorName=" + instructorName + ", roomId=" + roomId
				+ ", startTime=" + startTime + ", isRecurring=" + isRecurring + ", customerId=" + customerId
				+ ", startDate=" + startDate + ", endDate=" + endDate + ", note=" + note + ", bandName=" + bandName
				+ ", duration=" + duration + ", recurringStatus=" + recurringStatus + ", profileId=" + profileId
				+ ", appStatus=" + appStatus + ", orderId=" + orderId + ", email=" + email + ", locationId="
				+ locationId + ", phone=" + phone + ", customerDetails=" + customerDetails + ", parentFullName="
				+ parentFullName + "]";
	}

	 
	
	
	

 

}
