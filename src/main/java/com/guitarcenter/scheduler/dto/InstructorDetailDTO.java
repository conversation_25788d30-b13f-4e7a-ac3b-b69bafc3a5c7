package com.guitarcenter.scheduler.dto;

import java.util.List;
import java.util.Set;

import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.ServiceMode;

public class InstructorDetailDTO implements Comparable<InstructorDetailDTO>{

	
	private EditHourShowListDTO editHourShowList;
	
	private Set<Activity> activitys;
	
	private Set<Activity> notSelectedActivities;
	
	private Boolean active;
	
	private String activityTypes;
	
	private String availability;
	
	private String instructorName;
	
	private Long id;
	
	private String email;
	
	private long version;
	//GSSP-211 CHANGES
	private long externalId;
	
	private List<LocationDTO> location;
	
	private List<TimeOffDTO> timeOffs;
	
	private List<OnetimeDTO> onetimes;
	
	//---GSSP Instructor Mode update changes
	private Set<ServiceMode> notSelectedServiceMode;
	
	private String instructorMode;
	
	private String serviceModeId;
	
	private String serviceModeName;
	
 	
	public long getExternalId() {
		return externalId;
	}

	public void setExternalId(long externalId) {
		this.externalId = externalId;
	}

	public List<LocationDTO> getLocation() {
		return location;
	}

	public void setLocation(List<LocationDTO> location) {
		this.location = location;
	}

	/**
	 * getter method
	 * @return the onetimes
	 */
	public List<OnetimeDTO> getOnetimes() {
		return onetimes;
	}

	/**
	 * setter method
	 * @param onetimes the onetimes to set
	 */
	public void setOnetimes(List<OnetimeDTO> onetimes) {
		this.onetimes = onetimes;
	}

	public List<TimeOffDTO> getTimeOffs() {
		return timeOffs;
	}

	public void setTimeOffs(List<TimeOffDTO> timeOffs) {
		this.timeOffs = timeOffs;
	}

	public final EditHourShowListDTO getEditHourShowList() {
		return editHourShowList;
	}

	public InstructorDetailDTO(){}
	public InstructorDetailDTO(Long id , long version){
		this.id = id;
		this.version = version;
	}
	
	public final String getEmail() {
		return email;
	}


	public final void setEmail(String email) {
		this.email = email;
	}


	public final void setEditHourShowList(EditHourShowListDTO editHourShowList) {
		this.editHourShowList = editHourShowList;
	}


	public final Set<Activity> getActivitys() {
		return activitys;
	}

	public final void setActivitys(Set<Activity> activitys) {
		this.activitys = activitys;
	}

	public final Set<Activity> getNotSelectedActivities() {
		return notSelectedActivities;
	}

	public final void setNotSelectedActivities(Set<Activity> notSelectedActivities) {
		this.notSelectedActivities = notSelectedActivities;
	}

	public final long getVersion() {
		return version;
	}

	public final void setVersion(long version) {
		this.version = version;
	}

	public final Boolean getActive() {
		return active;
	}

	public final void setActive(Boolean active) {
		this.active = active;
	}

	public final String getActivityTypes() {
		return activityTypes;
	}

	public final void setActivityTypes(String activityTypes) {
		this.activityTypes = activityTypes;
	}

	public final String getAvailability() {
		return availability;
	}

	public final void setAvailability(String availability) {
		this.availability = availability;
	}

	public final String getInstructorName() {
		return instructorName;
	}

	public final void setInstructorName(String instructorName) {
		this.instructorName = instructorName;
	}

	public final Long getId() {
		return id;
	}

	public final void setId(Long id) {
		this.id = id;
	}

    @Override
    public int compareTo(InstructorDetailDTO o) {
        return (int) (id - o.getId());
    }

    //---GSSP Instructor Mode update changes
	public Set<ServiceMode> getNotSelectedServiceMode() {
		return notSelectedServiceMode;
	}

	public void setNotSelectedServiceMode(Set<ServiceMode> notSelectedServiceMode) {
		this.notSelectedServiceMode = notSelectedServiceMode;
	}

	public String getInstructorMode() {
		return instructorMode;
	}

	public void setInstructorMode(String instructorMode) {
		this.instructorMode = instructorMode;
	}

	public String getServiceModeId() {
		return serviceModeId;
	}

	public void setServiceModeId(String serviceModeId) {
		this.serviceModeId = serviceModeId;
	}

	public String getServiceModeName() {
		return serviceModeName;
	}

	public void setServiceModeName(String serviceModeName) {
		this.serviceModeName = serviceModeName;
	}

 	
}
