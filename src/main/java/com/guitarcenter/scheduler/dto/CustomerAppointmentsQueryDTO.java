package com.guitarcenter.scheduler.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * @Date 5/6/2020 4:14 PM
 * <AUTHOR>
 **/
public class CustomerAppointmentsQueryDTO {

    //@JsonFormat(pattern="MM/dd/yyyy")
    private String startDatetime;

    //@JsonFormat(pattern="MM/dd/yyyy")
    private String endDatetime;

    private Long customerId;

    public String getStartDatetime() {
        return startDatetime;
    }

    public void setStartDatetime(String startDatetime) {
        this.startDatetime = startDatetime;
    }

    public String getEndDatetime() {
        return endDatetime;
    }

    public void setEndDatetime(String endDatetime) {
        this.endDatetime = endDatetime;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }
}
