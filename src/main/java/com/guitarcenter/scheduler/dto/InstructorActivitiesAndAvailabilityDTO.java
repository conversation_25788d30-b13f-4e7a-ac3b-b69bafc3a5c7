package com.guitarcenter.scheduler.dto;
//created for GSSP-298
public class InstructorActivitiesAndAvailabilityDTO {
	private String store;
	private String instructorName;
	private String activityName;
	private String recipientId;
	private String availability;
	private String mondayStartTime;
	private String mondayEndTime;
	private String tuesdayStartTime;
	private String tuesdayendTime;
	private String wednesdayStartTime;
	private String wednesdayEndTime;
	private String thursdayStartTime;
	private String thursdayEndTime;
	private String fridayStartTime;
	private String fridayEndTime;
	private String saturdayStarTime;
	private String saturdayEndTime;
	private String sundayStartTime;
	private String sundayEndTime;
	
	public InstructorActivitiesAndAvailabilityDTO(String recipId){
		this.recipientId = recipId;
	}
	public InstructorActivitiesAndAvailabilityDTO(){
		
	}
	public InstructorActivitiesAndAvailabilityDTO(String store, String instructorName, 
			 String activityName, String monStartTime, String monEndTime,
			 String tueStartTime, String tueEndTime, String wedStartTime, String wedEndTime, 
			 String thuStartTime, String thuEndtime, String friStartTime, String friEndTime,
			 String satStartTime, String satEndTime, String sunStartTime, String sunEndTime){
		
		this.instructorName = instructorName;
		this.activityName = activityName;
		this.store = store;
		this.mondayStartTime = monStartTime;
		this.mondayEndTime = monEndTime;
		this.tuesdayStartTime = tueStartTime;
		this.tuesdayStartTime = tueEndTime;
		this.wednesdayStartTime = wedStartTime;
		this.wednesdayEndTime = wedEndTime;
		this.thursdayStartTime = thuStartTime;
		this.thursdayEndTime = thuEndtime;
		this.fridayStartTime = friStartTime;
		this.fridayEndTime = friEndTime;
		this.saturdayStarTime = satStartTime;
		this.saturdayEndTime = satEndTime;
		this.sundayStartTime = sunStartTime;
		this.sundayEndTime = sunEndTime;		
		}
	
	public String getStore() {
		return store;
	}
	public String getInstructorName() {
		return instructorName;
	}
	public String getActivityName() {
		return activityName;
	}

	public void setStore(String store) {
		this.store = store;
	}

	public void setInstructorName(String instructorName) {
		this.instructorName = instructorName;
	}

	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}

	public void setRecipientId(String recipientId) {
		this.recipientId = recipientId;
	}

	public void setAvailability(String availability) {
		this.availability = availability;
	}

	public void setMondayStartTime(String mondayStartTime) {
		this.mondayStartTime = mondayStartTime;
	}

	public void setMondayEndTime(String mondayEndTime) {
		this.mondayEndTime = mondayEndTime;
	}

	public void setTuesdayStartTime(String tuesdayStartTime) {
		this.tuesdayStartTime = tuesdayStartTime;
	}

	public void setTuesdayendTime(String tuesdayendTime) {
		this.tuesdayendTime = tuesdayendTime;
	}

	public void setWednesdayStartTime(String wednesdayStartTime) {
		this.wednesdayStartTime = wednesdayStartTime;
	}

	public void setWednesdayEndTime(String wednesdayEndTime) {
		this.wednesdayEndTime = wednesdayEndTime;
	}

	public void setThursdayStartTime(String thursdayStartTime) {
		this.thursdayStartTime = thursdayStartTime;
	}

	public void setThursdayEndTime(String thursdayEndTime) {
		this.thursdayEndTime = thursdayEndTime;
	}

	public void setFridayStartTime(String fridayStartTime) {
		this.fridayStartTime = fridayStartTime;
	}

	public void setFridayEndTime(String fridayEndTime) {
		this.fridayEndTime = fridayEndTime;
	}

	public void setSaturdayStarTime(String saturdayStarTime) {
		this.saturdayStarTime = saturdayStarTime;
	}

	public void setSaturdayEndTime(String saturdayEndTime) {
		this.saturdayEndTime = saturdayEndTime;
	}

	public void setSundayStartTime(String sundayStartTime) {
		this.sundayStartTime = sundayStartTime;
	}

	public void setSundayEndTime(String sundayEndTime) {
		this.sundayEndTime = sundayEndTime;
	}

	public String getRecipientId() {
		return recipientId;
	}

	public String getAvailability() {
		return availability;
	}

	public String getMondayStartTime() {
		return mondayStartTime;
	}

	public String getMondayEndTime() {
		return mondayEndTime;
	}

	public String getTuesdayStartTime() {
		return tuesdayStartTime;
	}

	public String getTuesdayendTime() {
		return tuesdayendTime;
	}

	public String getWednesdayStartTime() {
		return wednesdayStartTime;
	}

	public String getWednesdayEndTime() {
		return wednesdayEndTime;
	}

	public String getThursdayStartTime() {
		return thursdayStartTime;
	}

	public String getThursdayEndTime() {
		return thursdayEndTime;
	}

	public String getFridayStartTime() {
		return fridayStartTime;
	}

	public String getFridayEndTime() {
		return fridayEndTime;
	}

	public String getSaturdayStarTime() {
		return saturdayStarTime;
	}

	public String getSaturdayEndTime() {
		return saturdayEndTime;
	}

	public String getSundayStartTime() {
		return sundayStartTime;
	}
	public String getSundayEndTime() {
		return sundayEndTime;
	}
}
