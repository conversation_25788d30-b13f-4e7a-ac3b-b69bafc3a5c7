package com.guitarcenter.scheduler.dto;

import java.util.LinkedList;
import java.util.List;

import com.guitarcenter.scheduler.common.util.ActivityAndServiceUtil;

public class DurationBuilder {
	
	/**
	 * For rehearsal,the duration from 2 hours to 12 hours
	 * 
	 */
	private static final int REHEALSAL_MIN_DURATION = 2;
	private static final int REHEALSAL_MAX_DURATION = 12;
	
	public static List<DurationDTO> buildRehearsalDurations() {
		List<DurationDTO> dtos = new LinkedList<DurationDTO>();
		for(int i=REHEALSAL_MIN_DURATION; i<=REHEALSAL_MAX_DURATION; i++) {
			DurationDTO dto = new DurationDTO(String.valueOf(i*60), i + " Hours");
			dtos.add(dto);
		}
		return dtos;
	}
	
	public static List<DurationDTO> buildLessonDurations() {
		List<DurationDTO> dtos = new LinkedList<DurationDTO>();
		dtos.add(new DurationDTO("30", "30 Min"));
		dtos.add(new DurationDTO("60", "1 Hour"));
		dtos.addAll(buildRehearsalDurations());
		return dtos;
	}
	
	public List<DurationDTO> buildDurationListByMinAndMaxDuration(Long minDuration, Long maxDuration) {
		//build minDuration string
		minDuration = (minDuration == null) ? Long.valueOf(30) : minDuration;
		List<DurationDTO> dtos = new LinkedList<DurationDTO>();
		//GSSP-340 :: Changed for Paid break 10 min adding, so now paid break support to min and max 10 min.
		//Need to make sure no other service sare effecting.
		if(new Long(10) == minDuration && minDuration == maxDuration ){
			dtos.add(new DurationDTO(String.valueOf(minDuration),  minDuration + " Min"));
			return dtos;
		}
		double max = ((null == maxDuration) ? REHEALSAL_MAX_DURATION : (maxDuration*1.0 / 60));
		double from = (minDuration*1.0 / 60);
		if(minDuration < 60) {
			dtos.add(new DurationDTO(String.valueOf(minDuration), (String.valueOf(minDuration) + " Min")));
//			from = from + 1;
		} 
		
		String reg = "\\d+[.]{1}[5]{1}";
		
		double to = max;
		//Change for GCSS-520
		if(minDuration > 15){
		for(double i=from; i<=to; i+=0.5) {
			if(1 == i) {
				dtos.add(new DurationDTO(String.valueOf(60), "1 Hour"));
			} else {
				if(i > 1) {
					String  suffix = ((int)i) + " Hours";
					if(String.valueOf(i).matches(reg)) {
						suffix = ((int)i) + ".5 Hours";
					}
					dtos.add(new DurationDTO(String.valueOf((int)(i*60)), suffix));
				}			
				
				}
			}//GSSP- 302 Room Reset option changes which will allow any activity between min duration as 15 and max duration as 30 only 
		}else if(minDuration==15 && maxDuration >15){
			double i1 = from;
			if(i1 ==0.25){
			for( i1=from; i1<=to; i1+=0.25){
				 if(i1 == 0.25){
			String suffix1 = String.valueOf(Math.round((((int)i1)+0.5) * 60))+" Min";
			dtos.add(new DurationDTO(String.valueOf((int)((i1+0.25)*60)), suffix1));
						}
				 else{
						if(i1 > 0.25) {
							String  suffix = null;
							for( i1=from+0.25; i1<=to; i1+=0.5){
								if(i1 != 0.5){
									if(1 == i1) {
										dtos.add(new DurationDTO(String.valueOf(60), "1 Hour"));
									}else{
							  suffix = ((int)i1) + " Hours";
							  if(String.valueOf(i1).matches(reg)) {
									suffix = ((int)i1) + ".5 Hours";
								}
							  dtos.add(new DurationDTO(String.valueOf((int)(i1*60)), suffix));
									}
									}
							}
							
						}
					}
			}
			}
		}
		
		return dtos;
	}
	
	/**
	 * 
	 * create a duration list , 
	 * 
	 * @param fromTime Desired start time,default value is 30
	 * @param toTime  Desired end time,default value is 720
	 * @param minDuration  the minimum Duration,default value is 30
	 * @param maxDuration  the minimum Duration,default value is 720
	 * @param interval  time interval,default value is 60
	 * @return List<DurationDTO>
	 */
	public static  List<DurationDTO> buildDurationListByMinAndMaxDuration(Integer fromTime,Integer toTime,Integer minDuration,Integer maxDuration,Integer interval){
		
		LinkedList<DurationDTO> durationDTOs = new LinkedList<DurationDTO>();
		
		if(minDuration == null || minDuration < 0){
			minDuration = 30;
		}
		
		if(maxDuration == null || maxDuration > 720){
			maxDuration = 720;
		}
		
		if(interval == null){
			interval = 60;
		}
		
		 if(fromTime == null || fromTime < interval){
			 
			
			 if(fromTime == null || (fromTime <30 && minDuration < 30 && interval > 30)){
				 	//fromTime = minDuration;
				durationDTOs.addFirst(new DurationDTO(String.valueOf(30), "30 min"));
			 }
			fromTime = interval;
		}else{
			fromTime = (fromTime/interval + 1) * interval;
		}
		
		if(toTime == null || toTime > maxDuration){
			toTime = maxDuration;
		}
		
		for(Integer duration = fromTime ; duration <= toTime; duration += interval){
			String durationHr = ActivityAndServiceUtil.getDuration(duration.longValue());
			durationDTOs.add(new DurationDTO(String.valueOf(duration),durationHr));
			
		}
		return durationDTOs;
	}
}
