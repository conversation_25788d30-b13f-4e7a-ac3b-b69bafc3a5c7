package com.guitarcenter.scheduler.dto;

public class ProfileActivityCreateDTO {
	private Long activityId;
	private Long serviceType;
	private Long minAttenders;
	private Long maxAttenders;
	private String instructor;
	private String minimumDuration;
	private String maxmumDuration;
	private String enable;

	public Long getActivityId() {
		return activityId;
	}

	
	public String getMaxmumDuration() {
		return maxmumDuration;
	}


	public void setMaxmumDuration(String maxmumDuration) {
		this.maxmumDuration = maxmumDuration;
	}


	public void setActivityId(Long activityId) {
		this.activityId = activityId;
	}

	public Long getServiceType() {
		return serviceType;
	}

	public void setServiceType(Long serviceType) {
		this.serviceType = serviceType;
	}

	public Long getMinAttenders() {
		return minAttenders;
	}

	public void setMinAttenders(Long minAttenders) {
		this.minAttenders = minAttenders;
	}

	public Long getMaxAttenders() {
		return maxAttenders;
	}

	public void setMaxAttenders(Long maxAttenders) {
		this.maxAttenders = maxAttenders;
	}

	public String getInstructor() {
		return instructor;
	}

	public void setInstructor(String instructor) {
		this.instructor = instructor;
	}

	public String getMinimumDuration() {
		return minimumDuration;
	}

	public void setMinimumDuration(String minimumDuration) {
		this.minimumDuration = minimumDuration;
	}

	public String getEnable() {
		return enable;
	}

	public void setEnable(String enable) {
		this.enable = enable;
	}

	
}
