package com.guitarcenter.scheduler.dto;

import java.util.List;

import com.guitarcenter.scheduler.model.Role;

public class StaffDetailDTO {

	private long				id;
	private List<LocationDTO>	notSelectedLocation;
	private List<Role>			roles;
	private List<LocationDTO>	selectedLocationsList;
	private Boolean				active;
	private String				instructorName;
	private String				email;
	private List<String>		roleLocation;
	private String				roleLocationName;

	public String getRoleLocationName() {
		return roleLocationName;
	}

	public void setRoleLocationName(String roleLocationName) {
		this.roleLocationName = roleLocationName;
	}

	public final List<String> getRoleLocation() {
		return roleLocation;
	}

	public final void setRoleLocation(List<String> roleLocation) {
		this.roleLocation = roleLocation;
	}

	public final String getInstructorName() {
		return instructorName;
	}

	public final void setInstructorName(String instructorName) {
		this.instructorName = instructorName;
	}

	public final String getEmail() {
		return email;
	}

	public final void setEmail(String email) {
		this.email = email;
	}

	public final Boolean getActive() {
		return active;
	}

	public final void setActive(Boolean active) {
		this.active = active;
	}

	public final List<Role> getRoles() {
		return roles;
	}

	public final void setRoles(List<Role> roles) {
		this.roles = roles;
	}

	public final long getId() {
		return id;
	}

	public final void setId(long id) {
		this.id = id;
	}

	public final List<LocationDTO> getNotSelectedLocation() {
		return notSelectedLocation;
	}

	public final void setNotSelectedLocation(List<LocationDTO> notSelectedLocation) {
		this.notSelectedLocation = notSelectedLocation;
	}

	public final List<LocationDTO> getSelectedLocationsList() {
		return selectedLocationsList;
	}

	public final void setSelectedLocationsList(List<LocationDTO> selectedLocationsList) {
		this.selectedLocationsList = selectedLocationsList;
	}

}
