package com.guitarcenter.scheduler.dto;

import java.util.List;

public class WeekViewUnavailableHourDTOWrapper {

	private List<CalendarViewUnavailableHourDTO> list;

	public WeekViewUnavailableHourDTOWrapper() {
		super();
	}

	public WeekViewUnavailableHourDTOWrapper(List<CalendarViewUnavailableHourDTO> list) {
		super();
		this.list = list;
	}

	/**
	 * @return the list
	 */
	public List<CalendarViewUnavailableHourDTO> getList() {
		return list;
	}

	/**
	 * @param list
	 *            the list to set
	 */
	public void setList(List<CalendarViewUnavailableHourDTO> list) {
		this.list = list;
	}

}
