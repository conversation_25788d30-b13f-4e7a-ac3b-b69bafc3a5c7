package com.guitarcenter.scheduler.dto;

import java.util.Date;

public class CustomerAppointmentDetailsDTO {
	private String customerId;
	private String startDate;
	private String endDate;
	
	
	public String getCustomerId() {
		return customerId;
	}
	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}
	public String getStartDate() {
		return startDate;
	}
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}
	
	
	
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	@Override
	public String toString() {
		return "CustomerAppointmentDetailsDTO [customerId=" + customerId + ", startDate=" + startDate + ", endDate="
				+ endDate + "]";
	}
	
	
}
