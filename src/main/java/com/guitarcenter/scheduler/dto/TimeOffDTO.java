package com.guitarcenter.scheduler.dto;

/**
 * 
 * <AUTHOR>
 * @date
 */
public class TimeOffDTO {
	
	private String fromTime;
	private String fromDate;
	private Long timeoffId;
	private String timeOffStartToEnd;
	private String toTime;
	private String toDate;
	private Long instructorId;
	
	/**
	 * getter method
	 * @return the fromTime
	 */
	public String getFromTime() {
		return fromTime;
	}
	/**
	 * setter method
	 * @param fromTime the fromTime to set
	 */
	public void setFromTime(String fromTime) {
		this.fromTime = fromTime;
	}
	/**
	 * getter method
	 * @return the fromDate
	 */
	public String getFromDate() {
		return fromDate;
	}
	/**
	 * setter method
	 * @param fromDate the fromDate to set
	 */
	public void setFromDate(String fromDate) {
		this.fromDate = fromDate;
	}
	/**
	 * getter method
	 * @return the timeoffId
	 */
	public Long getTimeoffId() {
		return timeoffId;
	}
	/**
	 * setter method
	 * @param timeoffId the timeoffId to set
	 */
	public void setTimeoffId(Long timeoffId) {
		this.timeoffId = timeoffId;
	}
	/**
	 * getter method
	 * @return the timeOffStartToEnd
	 */
	public String getTimeOffStartToEnd() {
		return timeOffStartToEnd;
	}
	/**
	 * setter method
	 * @param timeOffStartToEnd the timeOffStartToEnd to set
	 */
	public void setTimeOffStartToEnd(String timeOffStartToEnd) {
		this.timeOffStartToEnd = timeOffStartToEnd;
	}
	/**
	 * getter method
	 * @return the toTime
	 */
	public String getToTime() {
		return toTime;
	}
	/**
	 * setter method
	 * @param toTime the toTime to set
	 */
	public void setToTime(String toTime) {
		this.toTime = toTime;
	}
	/**
	 * getter method
	 * @return the toDate
	 */
	public String getToDate() {
		return toDate;
	}
	/**
	 * setter method
	 * @param toDate the toDate to set
	 */
	public void setToDate(String toDate) {
		this.toDate = toDate;
	}
	/**
	 * getter method
	 * @return the instructorId
	 */
	public Long getInstructorId() {
		return instructorId;
	}
	/**
	 * setter method
	 * @param instructorId the instructorId to set
	 */
	public void setInstructorId(Long instructorId) {
		this.instructorId = instructorId;
	}
	
}
