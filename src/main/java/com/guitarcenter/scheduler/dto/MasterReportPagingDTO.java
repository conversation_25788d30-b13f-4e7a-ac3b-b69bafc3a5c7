package com.guitarcenter.scheduler.dto;

import java.util.List;

import com.guitarcenter.scheduler.model.dto.InstructorAppointmentBusinessHours;

public class MasterReportPagingDTO {

	private String currentDate;
	private List<InstructorAppointmentBusinessHours> employees;
	private List<MasterScheduleReportDTO> masters;

	/**
	 * @return the currentDate
	 */
	public String getCurrentDate() {
		return currentDate;
	}

	/**
	 * @param currentDate
	 *            the currentDate to set
	 */
	public void setCurrentDate(String currentDate) {
		this.currentDate = currentDate;
	}

	/**
	 * @return the employees
	 */
	public List<InstructorAppointmentBusinessHours> getEmployees() {
		return employees;
	}

	/**
	 * @param employees
	 *            the employees to set
	 */
	public void setEmployees(List<InstructorAppointmentBusinessHours> employees) {
		this.employees = employees;
	}

	/**
	 * @return the masters
	 */
	public List<MasterScheduleReportDTO> getMasters() {
		return masters;
	}

	/**
	 * @param masters
	 *            the masters to set
	 */
	public void setMasters(List<MasterScheduleReportDTO> masters) {
		this.masters = masters;
	}

}
