package com.guitarcenter.scheduler.dto;

public class ActivityCreateDTO {

	private String activityName;
	private Long serviceId;

	private String maxAttenders;
	private String minAttenders;
	
	private String instructor;
	private String enable;

	private String minimumDuration;
	private String maxmumDuration;

	public String getActivityName() {
		return activityName;
	}

	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}

	public String getEnable() {
		return enable;
	}

	public void setEnable(String enable) {
		this.enable = enable;
	}

	public Long getServiceId() {
		return serviceId;
	}

	public void setServiceId(Long serviceId) {
		this.serviceId = serviceId;
	}

	public String getMaxAttenders() {
		return maxAttenders;
	}

	public void setMaxAttenders(String maxAttenders) {
		this.maxAttenders = maxAttenders;
	}

	public String getMinAttenders() {
		return minAttenders;
	}

	public void setMinAttenders(String minAttenders) {
		this.minAttenders = minAttenders;
	}

	public String getInstructor() {
		return instructor;
	}

	public void setInstructor(String instructor) {
		this.instructor = instructor;
	}

	public String getMinimumDuration() {
		return minimumDuration;
	}

	public void setMinimumDuration(String minimumDuration) {
		this.minimumDuration = minimumDuration;
	}

	public String getMaxmumDuration() {
		return maxmumDuration;
	}

	public void setMaxmumDuration(String maxmumDuration) {
		this.maxmumDuration = maxmumDuration;
	}

}
