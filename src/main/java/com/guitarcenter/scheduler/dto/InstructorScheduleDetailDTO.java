package com.guitarcenter.scheduler.dto;

public class InstructorScheduleDetailDTO {

 
	private String zoomMeetingID;

	private String appointmentId;
	 
	private String comments;
	
	private String showStatus;
	
	private String studentNote;
	
	//private String				lessonStatus;
	//private String				nextLessonStatus;
	private String				assignment;
	private String 				practiceNotes;
	private String				remarks;
	//private String				rate;
	

	
	public String getZoomMeetingID() {
		return zoomMeetingID;
	}

	public void setZoomMeetingID(String zoomMeetingID) {
		this.zoomMeetingID = zoomMeetingID;
	}

	public String getAppointmentId() {
		return appointmentId;
	}

	public void setAppointmentId(String appointmentId) {
		this.appointmentId = appointmentId;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getShowStatus() {
		return showStatus;
	}

	public void setShowStatus(String showStatus) {
		this.showStatus = showStatus;
	}

	public String getStudentNote() {
		return studentNote;
	}

	public void setStudentNote(String studentNote) {
		this.studentNote = studentNote;
	}

	/*public String getLessonStatus() {
		return lessonStatus;
	}

	public void setLessonStatus(String lessonStatus) {
		this.lessonStatus = lessonStatus;
	}

	public String getNextLessonStatus() {
		return nextLessonStatus;
	}

	public void setNextLessonStatus(String nextLessonStatus) {
		this.nextLessonStatus = nextLessonStatus;
	}*/

	public String getAssignment() {
		return assignment;
	}

	public void setAssignment(String assignment) {
		this.assignment = assignment;
	}

	public String getPracticeNotes() {
		return practiceNotes;
	}

	public void setPracticeNotes(String practiceNotes) {
		this.practiceNotes = practiceNotes;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	/*public String getAnyRemarks() {
		return anyRemarks;
	}

	public void setAnyRemarks(String anyRemarks) {
		this.anyRemarks = anyRemarks;
	}

	public String getRate() {
		return rate;
	}

	public void setRate(String rate) {
		this.rate = rate;
	}*/

	
 
}
