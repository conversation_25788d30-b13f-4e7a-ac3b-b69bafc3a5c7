package com.guitarcenter.scheduler.dto;

import java.util.List;

public class EditHourShowListDTO {

	private List<EditHourShowDTO> list;
	private List<EditHourShowDTO> insAvl24HrsList;
	private String idString;
	private String externalId;
	private String siteIdString;
	private String versionString;
	private Boolean isAvailability;
	private String availabilityString;
	private Long instructorId;
	
	public List<EditHourShowDTO> getList() {
		return list;
	}

	public void setList(List<EditHourShowDTO> list) {
		this.list = list;
	}

	public List<EditHourShowDTO> getInsAvl24HrsList() {
		return insAvl24HrsList;
	}

	public void setInsAvl24HrsList(List<EditHourShowDTO> insAvl24HrsList) {
		this.insAvl24HrsList = insAvl24HrsList;
	}

	public String getIdString() {
		return idString;
	}

	public void setIdString(String idString) {
		this.idString = idString;
	}

	public String getExternalId() {
		return externalId;
	}

	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}

	public String getSiteIdString() {
		return siteIdString;
	}

	public void setSiteIdString(String siteIdString) {
		this.siteIdString = siteIdString;
	}

	public String getVersionString() {
		return versionString;
	}

	public void setVersionString(String versionString) {
		this.versionString = versionString;
	}

	public final Boolean getIsAvailability() {
		return isAvailability;
	}

	public final void setIsAvailability(Boolean isAvailability) {
		this.isAvailability = isAvailability;
	}

	public final String getAvailabilityString() {
		return availabilityString;
	}

	public final void setAvailabilityString(String availabilityString) {
		this.availabilityString = availabilityString;
	}

    public Long getInstructorId() {
        return instructorId;
    }

    public void setInstructorId(Long instructorId) {
        this.instructorId = instructorId;
    }

	
}
