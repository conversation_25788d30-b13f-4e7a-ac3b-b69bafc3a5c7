package com.guitarcenter.scheduler.dto;

public class ActivityDTO implements Comparable<ActivityDTO>{
	private Long activityId;
	private String activityName;
	private String serviceName;
	private Long serviceId;
	private Boolean globalChange = false;
	private Boolean enable;
	private String maximunAttendees;
	private String minimunAttendees;
	private String minimumDuration;
	private String minimumDurationText;
	private String maxmumDuration;
	private String maxmumDurationText;
	private ServiceDTO serviceDTO;
	private String requiresInstructor;
	private Long version;
	private String attenders;
	
	/**
	 * Field used to identify if the activity is selected in filter list
	 */
	private String isSelectedActivity;

	/**
	
	  * ActivityDTO. 
	  * <p>Title: </p>
	  * <p>Description: </p>
	  */
	public ActivityDTO() {
		super();
	}

	/**
	
	  * ActivityDTO. 
	  * <p>Title: </p>
	  * <p>Description: </p>
	  * @param activityId
	  * @param activityName
	  */
	public ActivityDTO(Long activityId, String activityName) {
		super();
		this.activityId = activityId;
		this.activityName = activityName;
	}

	public Long getActivityId() {
		return activityId;
	}

	public void setActivityId(Long activityId) {
		this.activityId = activityId;
	}

	public String getActivityName() {
		return activityName;
	}

	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}

	public String getServiceName() {
		return serviceName;
	}

	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}

	public Long getServiceId() {
		return serviceId;
	}

	public void setServiceId(Long serviceId) {
		this.serviceId = serviceId;
	}

	public Boolean getEnable() {
		return enable;
	}

	public void setEnable(Boolean enable) {
		this.enable = enable;
	}

	public String getMaximunAttendees() {
		return maximunAttendees;
	}

	public void setMaximunAttendees(String maximunAttendees) {
		this.maximunAttendees = maximunAttendees;
	}

	public String getMinimunAttendees() {
		return minimunAttendees;
	}

	public void setMinimunAttendees(String minimunAttendees) {
		this.minimunAttendees = minimunAttendees;
	}

	public String getMinimumDuration() {
		return minimumDuration;
	}

	public void setMinimumDuration(String minimumDuration) {
		this.minimumDuration = minimumDuration;
	}

	public ServiceDTO getServiceDTO() {
		return serviceDTO;
	}

	public void setServiceDTO(ServiceDTO serviceDTO) {
		this.serviceDTO = serviceDTO;
	}

	public String getRequiresInstructor() {
		return requiresInstructor;
	}

	public void setRequiresInstructor(String requiresInstructor) {
		this.requiresInstructor = requiresInstructor;
	}

	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getAttenders() {
		return attenders;
	}

	public void setAttenders(String attenders) {
		this.attenders = attenders;
	}

	public Boolean getGlobalChange() {
		return globalChange;
	}

	public void setGlobalChange(Boolean globalChange) {
		this.globalChange = globalChange;
	}

	public String getMaxmumDuration() {
		return maxmumDuration;
	}

	public void setMaxmumDuration(String maxmumDuration) {
		this.maxmumDuration = maxmumDuration;
	}

	public String getMinimumDurationText() {
		return minimumDurationText;
	}

	public void setMinimumDurationText(String minimumDurationText) {
		this.minimumDurationText = minimumDurationText;
	}

	public String getMaxmumDurationText() {
		return maxmumDurationText;
	}

	public void setMaxmumDurationText(String maxmumDurationText) {
		this.maxmumDurationText = maxmumDurationText;
	}

    @Override
    public int compareTo(ActivityDTO o) {
        return (int) (activityId - o.getActivityId());
    }

	/**
	 * @return the isSelectedActivity
	 */
	public String getIsSelectedActivity() {
		return isSelectedActivity;
	}

	/**
	 * @param isSelectedActivity the isSelectedActivity to set
	 */
	public void setIsSelectedActivity(String isSelectedActivity) {
		this.isSelectedActivity = isSelectedActivity;
	}
    
}
