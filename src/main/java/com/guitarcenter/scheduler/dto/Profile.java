package com.guitarcenter.scheduler.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
 
@JsonIgnoreProperties(ignoreUnknown = true)
public class Profile {
	
	 private String lastName;
	    private String gCProTier;
	    private boolean isGCProUser;
	    private String gCProUserShipId;
	    private String login;
	    private String gCProApplicationStatus;
	    private String firstName;
	    private String phoneNumber;
	    private String gcProAgentUser;
	    private boolean isGCProExperience;
	    private String middleName;
	    private String id;
	    private String gCProUserBillId;
	    private boolean gcProUser;
	    
		public String getLastName() {
			return lastName;
		}
		public void setLastName(String lastName) {
			this.lastName = lastName;
		}
		public String getgCProTier() {
			return gCProTier;
		}
		public void setgCProTier(String gCProTier) {
			this.gCProTier = gCProTier;
		}
		public boolean isGCProUser() {
			return isGCProUser;
		}
		public void setGCProUser(boolean isGCProUser) {
			this.isGCProUser = isGCProUser;
		}
		public String getgCProUserShipId() {
			return gCProUserShipId;
		}
		public void setgCProUserShipId(String gCProUserShipId) {
			this.gCProUserShipId = gCProUserShipId;
		}
		public String getLogin() {
			return login;
		}
		public void setLogin(String login) {
			this.login = login;
		}
		public String getgCProApplicationStatus() {
			return gCProApplicationStatus;
		}
		public void setgCProApplicationStatus(String gCProApplicationStatus) {
			this.gCProApplicationStatus = gCProApplicationStatus;
		}
		public String getFirstName() {
			return firstName;
		}
		public void setFirstName(String firstName) {
			this.firstName = firstName;
		}
		public String getPhoneNumber() {
			return phoneNumber;
		}
		public void setPhoneNumber(String phoneNumber) {
			this.phoneNumber = phoneNumber;
		}
		public String getGcProAgentUser() {
			return gcProAgentUser;
		}
		public void setGcProAgentUser(String gcProAgentUser) {
			this.gcProAgentUser = gcProAgentUser;
		}
		public boolean isGCProExperience() {
			return isGCProExperience;
		}
		public void setGCProExperience(boolean isGCProExperience) {
			this.isGCProExperience = isGCProExperience;
		}
		public String getMiddleName() {
			return middleName;
		}
		public void setMiddleName(String middleName) {
			this.middleName = middleName;
		}
		public String getId() {
			return id;
		}
		public void setId(String id) {
			this.id = id;
		}
		public String getgCProUserBillId() {
			return gCProUserBillId;
		}
		public void setgCProUserBillId(String gCProUserBillId) {
			this.gCProUserBillId = gCProUserBillId;
		}
		public boolean isGcProUser() {
			return gcProUser;
		}
		public void setGcProUser(boolean gcProUser) {
			this.gcProUser = gcProUser;
		}
		
		@Override
		public String toString() {
			return "Profile [lastName=" + lastName + ", gCProTier=" + gCProTier + ", isGCProUser=" + isGCProUser
					+ ", gCProUserShipId=" + gCProUserShipId + ", login=" + login + ", gCProApplicationStatus="
					+ gCProApplicationStatus + ", firstName=" + firstName + ", phoneNumber=" + phoneNumber
					+ ", gcProAgentUser=" + gcProAgentUser + ", isGCProExperience=" + isGCProExperience
					+ ", middleName=" + middleName + ", id=" + id + ", gCProUserBillId=" + gCProUserBillId
					+ ", gcProUser=" + gcProUser + "]";
		}
		
	    
	    

}
