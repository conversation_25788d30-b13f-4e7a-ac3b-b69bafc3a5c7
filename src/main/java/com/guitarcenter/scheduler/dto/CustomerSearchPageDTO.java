package com.guitarcenter.scheduler.dto;

import java.util.Date;
import java.util.List;


public class CustomerSearchPageDTO {
 
	
	
	private String customerId;
	private String instrument_type;
	private String instructor;
	private String customerName;
	private String ActivityName;
	private String lessonCount;
	private String phone;
	private String email;
	private Date lastBooked;
	private String customerStatus;
	private String customerExtId;
	
	List<SearchDTO> dtos;
	
	

	public String getCustomerId() {
		return customerId;
	}
	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}
	public String getInstrument_type() {
		return instrument_type;
	}
	public void setInstrument_type(String instrument_type) {
		this.instrument_type = instrument_type;
	}
	public String getInstructor() {
		return instructor;
	}
	public void setInstructor(String instructor) {
		this.instructor = instructor;
	}
	public String getCustomerName() {
		return customerName;
	}
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
	public String getActivityName() {
		return ActivityName;
	}
	public void setActivityName(String activityName) {
		ActivityName = activityName;
	}
	public String getLessonCount() {
		return lessonCount;
	}
	public void setLessonCount(String lessonCount) {
		this.lessonCount = lessonCount;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public Date getLastBooked() {
		return lastBooked;
	}
	public void setLastBooked(Date lastBooked) {
		this.lastBooked = lastBooked;
	}
	public String getCustomerStatus() {
		return customerStatus;
	}
	public void setCustomerStatus(String customerStatus) {
		this.customerStatus = customerStatus;
	}
	public String getCustomerExtId() {
		return customerExtId;
	}
	public void setCustomerExtId(String customerExtId) {
		this.customerExtId = customerExtId;
	}
	public List<SearchDTO> getDtos() {
		return dtos;
	}
	public void setDtos(List<SearchDTO> dtos) {
		this.dtos = dtos;
	}
	
	
}
