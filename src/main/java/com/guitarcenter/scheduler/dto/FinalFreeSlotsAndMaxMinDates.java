package com.guitarcenter.scheduler.dto;

import java.util.List;

import com.guitarcenter.scheduler.webservice.dto.InstructorAvailableHoursDTO;

public class FinalFreeSlotsAndMaxMinDates {
	
	private InstructorAvailableHoursDTO instructorAvailableHoursminMaxDTO;
	List<InstructorAvailableHoursDTO> freeSlotsList;
	
	
	
	
	public FinalFreeSlotsAndMaxMinDates() {
		super();
	}
	public FinalFreeSlotsAndMaxMinDates(InstructorAvailableHoursDTO instructorAvailableHoursminMaxDTO,
			List<InstructorAvailableHoursDTO> freeSlotsList) {
		super();
		this.instructorAvailableHoursminMaxDTO = instructorAvailableHoursminMaxDTO;
		this.freeSlotsList = freeSlotsList;
	}
	public InstructorAvailableHoursDTO getInstructorAvailableHoursminMaxDTO() {
		return instructorAvailableHoursminMaxDTO;
	}
	public void setInstructorAvailableHoursminMaxDTO(InstructorAvailableHoursDTO instructorAvailableHoursminMaxDTO) {
		this.instructorAvailableHoursminMaxDTO = instructorAvailableHoursminMaxDTO;
	}
	public List<InstructorAvailableHoursDTO> getFreeSlotsList() {
		return freeSlotsList;
	}
	public void setFreeSlotsList(List<InstructorAvailableHoursDTO> freeSlotsList) {
		this.freeSlotsList = freeSlotsList;
	} 
	
	

}
