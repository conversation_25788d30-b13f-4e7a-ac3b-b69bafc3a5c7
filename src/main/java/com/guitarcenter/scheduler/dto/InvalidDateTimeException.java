package com.guitarcenter.scheduler.dto;

public class InvalidDateTimeException extends RuntimeException {

    private static final long serialVersionUID = -5003197963620413698L;

    public InvalidDateTimeException() {
        super();
    }

    public InvalidDateTimeException(String message, Throwable cause) {
        super(message, cause);
    }

    public InvalidDateTimeException(String message) {
        super(message);
    }

    public InvalidDateTimeException(Throwable cause) {
        super(cause);
    }

    
}
