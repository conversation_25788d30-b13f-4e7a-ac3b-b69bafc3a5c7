/**
 * @Title: RoomTemplateDTO.java
 * @Package com.guitarcenter.scheduler.dto
 * @Description: TODO
 * Copyright: Copyright (c) 2013 
 * Company:
 * 
 * <AUTHOR>
 * @date Sep 11, 2013 10:21:26 AM
 * @version V1.0
 */
package com.guitarcenter.scheduler.dto;


/**
 * @ClassName: RoomTemplateDTO
 * @Description: TODO
 * <AUTHOR>
 * @date Sep 11, 2013 10:21:26 AM
 * 
 */
public class RoomTemplateDTO implements Comparable<RoomTemplateDTO>{
	private Long roomTemplateId;
	private String roomSize;
	private String roomType;
	private String roomTemplateName;
	private String isSplitRoom;
	private String enabled;
	private String activities;
	private String services;

	/*
	 * <p>Title: toString</p> <p>Description: </p>
	 * 
	 * @return
	 * 
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "RoomTemplateDTO [roomTemplateId=" + roomTemplateId
				+ ", roomSize=" + roomSize + ", roomType=" + roomType
				+ ", roomTemplateName=" + roomTemplateName + ", isSplitRoom="
				+ isSplitRoom + ", enabled=" + enabled + ", activities="
				+ activities + ", services=" + services + "]";
	}

	/**
	 * 
	 * 
	 * RoomTemplateDTO.
	 * <p>
	 * Title:
	 * </p>
	 * <p>
	 * Description:
	 * </p>
	 * 
	 * @param roomTemplateId
	 * @param roomSize
	 * @param roomType
	 * @param roomTemplateName
	 * @param isSplitRoom
	 * @param enabled
	 * @param activities
	 * @param services
	 */
	public RoomTemplateDTO(Long roomTemplateId, String roomSize,
			String roomType, String roomTemplateName, String isSplitRoom,
			String enabled, String activities, String services) {
		super();
		this.roomTemplateId = roomTemplateId;
		this.roomSize = roomSize;
		this.roomType = roomType;
		this.roomTemplateName = roomTemplateName;
		this.isSplitRoom = isSplitRoom;
		this.enabled = enabled;
		this.activities = activities;
		this.services = services;
	}

	/**
	
	  * RoomTemplateDTO. 
	  * <p>Title: </p>
	  * <p>Description: </p>
	  */
	public RoomTemplateDTO() {
		super();
	}

	/**
	 * getter method
	 * 
	 * @return the roomSize
	 */
	public String getRoomSize() {
		return roomSize;
	}

	/**
	 * setter method
	 * 
	 * @param roomSize
	 *            the roomSize to set
	 */
	public void setRoomSize(String roomSize) {
		this.roomSize = roomSize;
	}

	/**
	 * getter method
	 * 
	 * @return the roomType
	 */
	public String getRoomType() {
		return roomType;
	}

	/**
	 * setter method
	 * 
	 * @param roomType
	 *            the roomType to set
	 */
	public void setRoomType(String roomType) {
		this.roomType = roomType;
	}

	/**
	 * getter method
	 * 
	 * @return the roomTemplateId
	 */
	public Long getRoomTemplateId() {
		return roomTemplateId;
	}

	/**
	 * setter method
	 * 
	 * @param roomTemplateId
	 *            the roomTemplateId to set
	 */
	public void setRoomTemplateId(Long roomTemplateId) {
		this.roomTemplateId = roomTemplateId;
	}

	/**
	 * getter method
	 * 
	 * @return the roomTemplateName
	 */
	public String getRoomTemplateName() {
		return roomTemplateName;
	}

	/**
	 * setter method
	 * 
	 * @param roomTemplateName
	 *            the roomTemplateName to set
	 */
	public void setRoomTemplateName(String roomTemplateName) {
		this.roomTemplateName = roomTemplateName;
	}

	/**
	 * getter method
	 * 
	 * @return the isSplitRoom
	 */
	public String getIsSplitRoom() {
		return isSplitRoom;
	}

	/**
	 * setter method
	 * 
	 * @param isSplitRoom
	 *            the isSplitRoom to set
	 */
	public void setIsSplitRoom(String isSplitRoom) {
		this.isSplitRoom = isSplitRoom;
	}

	/**
	 * getter method
	 * 
	 * @return the enabled
	 */
	public String getEnabled() {
		return enabled;
	}

	/**
	 * setter method
	 * 
	 * @param enabled
	 *            the enabled to set
	 */
	public void setEnabled(String enabled) {
		this.enabled = enabled;
	}

	/**
	 * getter method
	 * 
	 * @return the activities
	 */
	public String getActivities() {
		return activities;
	}

	/**
	 * setter method
	 * 
	 * @param activities
	 *            the activities to set
	 */
	public void setActivities(String activities) {
		this.activities = activities;
	}

	/**
	 * getter method
	 * 
	 * @return the services
	 */
	public String getServices() {
		return services;
	}

	/**
	 * setter method
	 * 
	 * @param services
	 *            the services to set
	 */
	public void setServices(String services) {
		this.services = services;
	}

    @Override
    public int compareTo(RoomTemplateDTO o) {
        return (int) (roomTemplateId - o.getRoomTemplateId());
    }
}
