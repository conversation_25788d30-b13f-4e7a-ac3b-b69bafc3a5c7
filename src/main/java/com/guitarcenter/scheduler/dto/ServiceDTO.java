package com.guitarcenter.scheduler.dto;

import java.util.List;

public class ServiceDTO implements Comparable<ServiceDTO>{

	private Long serviceId;
	private String serviceName;
	private String instructor;
	private Boolean enable;
	private Boolean allowBandName;
	private Boolean globalChange = false;
	private List<ActivityDTO> activityDTOs;
	private List<ActivityDTO> notSelecteDtos;
	private Long version;
	private String avaibleAct;
	
	/**
	 * The field is used to identify if the service is selected in filter list
	 */
	private String isSelectedService;
	
	/**
	 * 
	 * ServiceDTO.
	 * <p>
	 * Title:
	 * </p>
	 * <p>
	 * Description:
	 * </p>
	 */
	
	public ServiceDTO() {
		super();
	}

	/**
	 * 
	 * ServiceDTO.
	 * <p>
	 * Title:
	 * </p>
	 * <p>
	 * Description:
	 * </p>
	 * 
	 * @param serviceId
	 * @param serviceName
	 */
	public ServiceDTO(Long serviceId, String serviceName) {
		super();
		this.serviceId = serviceId;
		this.serviceName = serviceName;
	}

	public Long getServiceId() {
		return serviceId;
	}

	public void setServiceId(Long serviceId) {
		this.serviceId = serviceId;
	}

	public String getServiceName() {
		return serviceName;
	}

	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}

	public String getInstructor() {
		return instructor;
	}

	public void setInstructor(String instructor) {
		this.instructor = instructor;
	}

	public Boolean getEnable() {
		return enable;
	}

	public void setEnable(Boolean enable) {
		this.enable = enable;
	}

	public Boolean getAllowBandName() {
		return allowBandName;
	}

	public void setAllowBandName(Boolean allowBandName) {
		this.allowBandName = allowBandName;
	}

	public Boolean getGlobalChange() {
		return globalChange;
	}

	public void setGlobalChange(Boolean globalChange) {
		this.globalChange = globalChange;
	}

	public List<ActivityDTO> getActivityDTOs() {
		return activityDTOs;
	}

	public void setActivityDTOs(List<ActivityDTO> activityDTOs) {
		this.activityDTOs = activityDTOs;
	}

	public List<ActivityDTO> getNotSelecteDtos() {
		return notSelecteDtos;
	}

	public void setNotSelecteDtos(List<ActivityDTO> notSelecteDtos) {
		this.notSelecteDtos = notSelecteDtos;
	}

	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getAvaibleAct() {
		return avaibleAct;
	}

	public void setAvaibleAct(String avaibleAct) {
		this.avaibleAct = avaibleAct;
	}

    @Override
    public int compareTo(ServiceDTO o) {
        return (int) (serviceId - o.serviceId);
    }

	/**
	 * @return the isSelectedService
	 */
	public String getIsSelectedService() {
		return isSelectedService;
	}

	/**
	 * @param isSelectedService the isSelectedService to set
	 */
	public void setIsSelectedService(String isSelectedService) {
		this.isSelectedService = isSelectedService;
	}

}
