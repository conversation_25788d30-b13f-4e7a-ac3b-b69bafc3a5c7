package com.guitarcenter.scheduler.dto;

import java.util.List;

import com.guitarcenter.scheduler.dao.criterion.dto.InstructorOpenAppointmentsDTO;

public class ConflictingAppointmentListDTO {
	
	private Integer conflictingappointmentcount;
	private List<InstructorOpenAppointmentsDTO> conflictingappointmentlist;
	
	private boolean roomConflict=true;
	
	private boolean status=false;
	
	


	public boolean isStatus() {
		return status;
	}

	public void setStatus(boolean status) {
		this.status = status;
	}

	/**
	 * Field used to identify if the activity is selected in filter list
	 */
	

	public boolean isRoomConflict() {
		return roomConflict;
	}

	public void setRoomConflict(boolean isRoomConflict) {
		this.roomConflict = isRoomConflict;
	}

	/**
	
	  * ConflictingAppointmentListDTO. 
	  * <p>Title: </p>
	  * <p>Description: </p>
	  */
	public ConflictingAppointmentListDTO() {
		super();
	}

	/**
	
	  * ConflictingAppointmentListDTO. 
	  * <p>Title: </p>
	  * <p>Description: </p>
	  * @param conflictingappointmentcount
	  * @param conflictingappointmentlist
	  */
	public ConflictingAppointmentListDTO(Integer conflictingappointmentcount, List<InstructorOpenAppointmentsDTO> conflictingappointmentlist) {
		super();
		
		this.conflictingappointmentcount = conflictingappointmentcount;
		this.conflictingappointmentlist = conflictingappointmentlist;
	}

	public Integer getConflictingappointmentcount() {
		return conflictingappointmentcount;
	}

	public void setConflictingappointmentcount(Integer conflictingappointmentcount) {
		this.conflictingappointmentcount = conflictingappointmentcount;
	}

	public List<InstructorOpenAppointmentsDTO> getConflictingappointmentlist() {
		return conflictingappointmentlist;
	}

	public void setConflictingappointmentlist(List<InstructorOpenAppointmentsDTO>l) {
		this.conflictingappointmentlist = l;
	}

	

}