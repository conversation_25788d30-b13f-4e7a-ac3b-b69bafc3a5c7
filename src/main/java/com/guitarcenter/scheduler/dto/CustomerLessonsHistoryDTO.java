package com.guitarcenter.scheduler.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
import java.util.Locale;

/**
 * LessonHistoryDTO related to customer detail page
 * @Date 4/27/2020 2:25 PM
 * <AUTHOR>
 **/
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public class CustomerLessonsHistoryDTO {
    private String lessonName;

    private String lessonDuration;

    @JsonFormat(pattern = "MMMMM d, yyyy '@' h:mm a")
    private Date date1;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss'@'z")
    private Date date2;

    private String instructorFullName;

    private String lessonStatusName;

    private String lessonStatusCode;

    public String getLessonStatusCode() {
        return lessonStatusCode;
    }

    public void setLessonStatusCode(String lessonStatusCode) {
        this.lessonStatusCode = lessonStatusCode;
    }

    public String getLessonName() {
        return lessonName;
    }

    public void setLessonName(String lessonName) {
        this.lessonName = lessonName;
    }

    public String getLessonDuration() {
        return lessonDuration;
    }

    public void setLessonDuration(String lessonDuration) {
        this.lessonDuration = lessonDuration;
    }

    public Date getDate1() {
        return date1;
    }

    public void setDate1(Date date1) {
        this.date1 = date1;
    }

    public Date getDate2() {
        return date2;
    }

    public void setDate2(Date date2) {
        this.date2 = date2;
    }

    public String getInstructorFullName() {
        return instructorFullName;
    }

    public void setInstructorFullName(String instructorFullName) {
        this.instructorFullName = instructorFullName;
    }

    public String getLessonStatusName() {
        return lessonStatusName;
    }

    public void setLessonStatusName(String lessonStatusName) {
        this.lessonStatusName = lessonStatusName;
    }
}
