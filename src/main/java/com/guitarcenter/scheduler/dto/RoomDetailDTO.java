/**
 * @Title: UpdateRoomDTO.java
 * @Package com.guitarcenter.scheduler.dto
 * @Description: TODO
 * Copyright: Copyright (c) 2013 
 * Company:
 * 
 * <AUTHOR>
 * @date Sep 18, 2013 11:33:53 AM
 * @version V1.0
 */
package com.guitarcenter.scheduler.dto;

import java.util.List;

import com.guitarcenter.scheduler.model.RoomNumber;
import com.guitarcenter.scheduler.model.RoomSize;
import com.guitarcenter.scheduler.model.RoomTemplate;

/**
 * 
  * @ClassName: RoomDetailDTO
  * @Description: TODO
  * <AUTHOR>
  * @date Sep 18, 2013 11:44:18 AM
  *
 */
public class RoomDetailDTO {
	private Long roomId;
	private String isSplitRoom;
	private String enabled;
	private List<RoomSize> roomSize;
	private List<RoomNumber> roomNumber;
	private List<RoomTemplate> roomTemplate;
	private List<ActivityDTO> selectedActivities;
	private List<ActivityDTO> unSelectedActivities;
	private List<ServiceDTO> selectedServices;
	private List<ServiceDTO> unSelectedServices;
	/**
	
	  * RoomDetailDTO. 
	  * <p>Title: </p>
	  * <p>Description: </p>
	  */
	public RoomDetailDTO() {
		super();
	}
	/**
	
	  * RoomDetailDTO. 
	  * <p>Title: </p>
	  * <p>Description: </p>
	  * @param roomId
	  * @param isSplitRoom
	  * @param enabled
	  * @param roomSize
	  * @param roomNumber
	  * @param roomTemplate
	  * @param selectedActivities
	  * @param unSelectedActivities
	  * @param selectedServices
	  * @param unSelectedServices
	  */
	public RoomDetailDTO(Long roomId, String isSplitRoom, String enabled,
			List<RoomSize> roomSize, List<RoomNumber> roomNumber,
			List<RoomTemplate> roomTemplate,
			List<ActivityDTO> selectedActivities,
			List<ActivityDTO> unSelectedActivities,
			List<ServiceDTO> selectedServices,
			List<ServiceDTO> unSelectedServices) {
		super();
		this.roomId = roomId;
		this.isSplitRoom = isSplitRoom;
		this.enabled = enabled;
		this.roomSize = roomSize;
		this.roomNumber = roomNumber;
		this.roomTemplate = roomTemplate;
		this.selectedActivities = selectedActivities;
		this.unSelectedActivities = unSelectedActivities;
		this.selectedServices = selectedServices;
		this.unSelectedServices = unSelectedServices;
	}
	/**
	 * getter method
	 * @return the roomId
	 */
	public Long getRoomId() {
		return roomId;
	}
	/**
	 * setter method
	 * @param roomId the roomId to set
	 */
	public void setRoomId(Long roomId) {
		this.roomId = roomId;
	}
	/**
	 * getter method
	 * @return the roomSize
	 */
	public List<RoomSize> getRoomSize() {
		return roomSize;
	}
	/**
	 * setter method
	 * @param roomSize the roomSize to set
	 */
	public void setRoomSize(List<RoomSize> roomSize) {
		this.roomSize = roomSize;
	}
	/**
	 * getter method
	 * @return the roomNumber
	 */
	public List<RoomNumber> getRoomNumber() {
		return roomNumber;
	}
	/**
	 * setter method
	 * @param roomNumber the roomNumber to set
	 */
	public void setRoomNumber(List<RoomNumber> roomNumber) {
		this.roomNumber = roomNumber;
	}
	/**
	 * getter method
	 * @return the roomTemplate
	 */
	public List<RoomTemplate> getRoomTemplate() {
		return roomTemplate;
	}
	/**
	 * setter method
	 * @param roomTemplate the roomTemplate to set
	 */
	public void setRoomTemplate(List<RoomTemplate> roomTemplate) {
		this.roomTemplate = roomTemplate;
	}
	/**
	 * getter method
	 * @return the isSplitRoom
	 */
	public String getIsSplitRoom() {
		return isSplitRoom;
	}
	/**
	 * setter method
	 * @param isSplitRoom the isSplitRoom to set
	 */
	public void setIsSplitRoom(String isSplitRoom) {
		this.isSplitRoom = isSplitRoom;
	}
	/**
	 * getter method
	 * @return the enabled
	 */
	public String getEnabled() {
		return enabled;
	}
	/**
	 * setter method
	 * @param enabled the enabled to set
	 */
	public void setEnabled(String enabled) {
		this.enabled = enabled;
	}
	/**
	 * getter method
	 * @return the selectedActivities
	 */
	public List<ActivityDTO> getSelectedActivities() {
		return selectedActivities;
	}
	/**
	 * setter method
	 * @param selectedActivities the selectedActivities to set
	 */
	public void setSelectedActivities(List<ActivityDTO> selectedActivities) {
		this.selectedActivities = selectedActivities;
	}
	/**
	 * getter method
	 * @return the unSelectedActivities
	 */
	public List<ActivityDTO> getUnSelectedActivities() {
		return unSelectedActivities;
	}
	/**
	 * setter method
	 * @param unSelectedActivities the unSelectedActivities to set
	 */
	public void setUnSelectedActivities(List<ActivityDTO> unSelectedActivities) {
		this.unSelectedActivities = unSelectedActivities;
	}
	/**
	 * getter method
	 * @return the selectedServices
	 */
	public List<ServiceDTO> getSelectedServices() {
		return selectedServices;
	}
	/**
	 * setter method
	 * @param selectedServices the selectedServices to set
	 */
	public void setSelectedServices(List<ServiceDTO> selectedServices) {
		this.selectedServices = selectedServices;
	}
	/**
	 * getter method
	 * @return the unSelectedServices
	 */
	public List<ServiceDTO> getUnSelectedServices() {
		return unSelectedServices;
	}
	/**
	 * setter method
	 * @param unSelectedServices the unSelectedServices to set
	 */
	public void setUnSelectedServices(List<ServiceDTO> unSelectedServices) {
		this.unSelectedServices = unSelectedServices;
	}
}
