package com.guitarcenter.scheduler.dto;
//New class created for GSSP-272
public class InstructorFloorTimeDTO {
	private String locationID;
	private String locationName;
	private int instructorID;
	private String firstName;
	private String lastName;
	private int duration;
	private String recipientId;
	
	public InstructorFloorTimeDTO(String recipientId)
	{
		this.recipientId = recipientId;
	}
	
	public InstructorFloorTimeDTO(String locationID, String locationName, int instructorID, 
			String firstName, String lastName, int duration)
	{
		this.locationID = locationID;
		this.locationName = locationName;
		this.instructorID = instructorID;
		this.firstName = firstName;
		this.lastName = lastName;
		this.duration = duration;
		
	}
	public String getFirstName() {
		return firstName;
	}
	public String getLastName() {
		return lastName;
	}
	public String getLocationID() {
		return locationID;
	}
	public String getLocationName() {
		return locationName;
	}
	public int getInstructorID() {
		return instructorID;
	}
	public int getDuration() {
		return duration;
	}

	public String getRecipientId() {
		return recipientId;
	}
	

}
