package com.guitarcenter.scheduler.dto;

public class CancelledAppointmentReportDTO {

	private String startTime;
	private String endTime;
	private String activityType;
	private String instructorName;
	private String customerName;
	private String startDateStr;
	private String endDateStr;
	private String notes;
	private String time;
	//GSSP-214 changes
	private String cancelledTime;
	private String cancelledUser;
	private String cancelledTimestamp;
	private String queryStartDate;
	private String queryEndDate;
	//GSSP-269 changes
	private String cancelledReason;

	public String getCancelledReason() {
		return cancelledReason;
	}

	public void setCancelledReason(String cancelledReason) {
		this.cancelledReason = cancelledReason;
	}

	public CancelledAppointmentReportDTO() {
		
	}
	
	
	public String getCancelledTimestamp() {
		return cancelledTimestamp;
	}

	public void setCancelledTimestamp(String cancelledTimestamp) {
		this.cancelledTimestamp = cancelledTimestamp;
	}

	public String getCancelledTime() {
		return cancelledTime;
	}

	public void setCancelledTime(String cancelledTime) {
		this.cancelledTime = cancelledTime;
	}

	public String getCancelledUser() {
		return cancelledUser;
	}

	public void setCancelledUser(String cancelledUser) {
		this.cancelledUser = cancelledUser;
	}

	/**
	 * @return the startTime
	 */
	public String getStartTime() {
		return startTime;
	}

	/**
	 * @param startTime
	 *            the startTime to set
	 */
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	/**
	 * @return the endTime
	 */
	public String getEndTime() {
		return endTime;
	}

	/**
	 * @param endTime
	 *            the endTime to set
	 */
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	/**
	 * @return the activityType
	 */
	public String getActivityType() {
		return activityType;
	}

	/**
	 * @param activityType
	 *            the activityType to set
	 */
	public void setActivityType(String activityType) {
		this.activityType = activityType;
	}

	/**
	 * @return the instructorName
	 */
	public String getInstructorName() {
		return instructorName;
	}

	/**
	 * @param instructorName
	 *            the instructorName to set
	 */
	public void setInstructorName(String instructorName) {
		this.instructorName = instructorName;
	}

	/**
	 * @return the customerName
	 */
	public String getCustomerName() {
		return customerName;
	}

	/**
	 * @param customerName
	 *            the customerName to set
	 */
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	/**
	 * @return the startDateStr
	 */
	public String getStartDateStr() {
		return startDateStr;
	}

	/**
	 * @param startDateStr
	 *            the startDateStr to set
	 */
	public void setStartDateStr(String startDateStr) {
		this.startDateStr = startDateStr;
	}

	/**
	 * @return the endDateStr
	 */
	public String getEndDateStr() {
		return endDateStr;
	}

	/**
	 * @param endDateStr
	 *            the endDateStr to set
	 */
	public void setEndDateStr(String endDateStr) {
		this.endDateStr = endDateStr;
	}

	/**
	 * @return the notes
	 */
	public String getNotes() {
		return notes;
	}

	/**
	 * @param notes
	 *            the notes to set
	 */
	public void setNotes(String notes) {
		this.notes = notes;
	}

	/**
	 * @return the time
	 */
	public String getTime() {
		return time;
	}

	/**
	 * @param time
	 *            the time to set
	 */
	public void setTime(String time) {
		this.time = time;
	}

	/**
	 * @return the queryStartDate
	 */
	public String getQueryStartDate() {
		return queryStartDate;
	}

	/**
	 * @param queryStartDate
	 *            the queryStartDate to set
	 */
	public void setQueryStartDate(String queryStartDate) {
		this.queryStartDate = queryStartDate;
	}

	/**
	 * @return the queryEndDate
	 */
	public String getQueryEndDate() {
		return queryEndDate;
	}

	/**
	 * @param queryEndDate
	 *            the queryEndDate to set
	 */
	public void setQueryEndDate(String queryEndDate) {
		this.queryEndDate = queryEndDate;
	}

}
