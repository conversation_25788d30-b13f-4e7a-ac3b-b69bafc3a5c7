package com.guitarcenter.scheduler.dto;

import com.guitarcenter.scheduler.model.Availability;

public class InstructorInfoDTO {
	
	private String instructorId;
	private String instructorName;
	private String isClosestScheduled;
	private Availability availability;
	private String instructorAvailibilityTime;
	
	private String instructorNameWithAvaiTime;
	
	/**
	 * Field used to identify if the instructor is selected in filter list
	 */
	private String isSelectedInstructor;

	public InstructorInfoDTO() {

	}

	public InstructorInfoDTO(String instructorId, String instructorName) {
		super();
		this.instructorId = instructorId;
		this.instructorName = instructorName;
	}

	public String getInstructorAvailibilityTime() {
		return instructorAvailibilityTime;
	}

	public void setInstructorAvailibilityTime(String instructorAvailibilityTime) {
		this.instructorAvailibilityTime = instructorAvailibilityTime;
	}

	public Availability getAvailability() {
		return availability;
	}

	public void setAvailability(Availability availability) {
		this.availability = availability;
	}

	public String getIsClosestScheduled() {
		return isClosestScheduled;
	}

	public void setIsClosestScheduled(String isClosestScheduled) {
		this.isClosestScheduled = isClosestScheduled;
	}

	public String getInstructorId() {
		return instructorId;
	}

	public void setInstructorId(String instructorId) {
		this.instructorId = instructorId;
	}

	public String getInstructorName() {
		return instructorName;
	}

	public void setInstructorName(String instructorName) {
		this.instructorName = instructorName;
	}

	/**
	 * @return the isSelectedInstructor
	 */
	public String getIsSelectedInstructor() {
		return isSelectedInstructor;
	}

	/**
	 * @param isSelectedInstructor the isSelectedInstructor to set
	 */
	public void setIsSelectedInstructor(String isSelectedInstructor) {
		this.isSelectedInstructor = isSelectedInstructor;
	}

	/**
	 * @return the instructorNameWithAvaiTime
	 */
	public String getInstructorNameWithAvaiTime() {
		return instructorNameWithAvaiTime;
	}

	/**
	 * @param instructorNameWithAvaiTime the instructorNameWithAvaiTime to set
	 */
	public void setInstructorNameWithAvaiTime(String instructorNameWithAvaiTime) {
		this.instructorNameWithAvaiTime = instructorNameWithAvaiTime;
	}
	
}
