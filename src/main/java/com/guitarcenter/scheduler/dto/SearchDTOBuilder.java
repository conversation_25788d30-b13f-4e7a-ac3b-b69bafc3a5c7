package com.guitarcenter.scheduler.dto;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.enums.Enabled;

/**
 * Handles the conversion of a model record to an instance of SearchDTO.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

public class SearchDTOBuilder {
    private static final Logger log = LoggerFactory.getLogger(SearchDTOBuilder.class);
    
    private static final String LOCATION_ENABLED = "Yes";
    private static final String LOCATION_NOT_ENABLED = "No";

    /**
     * Factory method to build a populated SearchDTO instance from the supplied
     * record object.
     * 
     * @param record An Object to transform into SearchDTO
     * @return SearchDTO with values provided from record
     */
    public static SearchDTO buildSearchDTO(Object record) {
        if (log.isDebugEnabled()) {
            log.debug("attempting to build a DTO for {}", record);
        }
        SearchDTO dto = null;
        if (record instanceof Customer) {
            dto = buildCustomerSearchDTO((Customer) record);
        } else if (record instanceof Instructor) {
            dto = buildInstructorSearchDTO((Instructor) record);
        } else if (record instanceof Location) {
            dto = buildLocationSearchDTO(record);
        }
        if (log.isDebugEnabled()) {
            log.debug("returning DTO {}", dto);
        }
        return dto;
    }
    
    public static SearchDTO buildSearchSecDTO(Object record) {
        if (log.isDebugEnabled()) {
            log.debug("attempting to build a secondary email DTO for {}", record);
        }
        SearchDTO dto = null;
        if (record instanceof Customer) {
            dto = buildCustomerSearchSecDTO((Customer) record);
        } else if (record instanceof Instructor) {
            dto = buildInstructorSearchDTO((Instructor) record);
        } else if (record instanceof Location) {
            dto = buildLocationSearchDTO(record);
        }
        if (log.isDebugEnabled()) {
            log.debug("returning secondary email DTO {}", dto);
        }
        return dto;
    }
    
    /**
     * Factory method to return a specially typed SearchDTO indicating there
     * numMoreResults records available but not shown.
     * 
     * @param numMoreResults long integer with the count of other records not
     *                       included in the results
     * @return an instance of SearchDTO
     */

    public static SearchDTO buildMoreResultsSearchDTO(long numMoreResults) {
        if (log.isDebugEnabled()) {
            log.debug("attempting to build a DTO for {} 'more results'",
                      numMoreResults);
        }
        SearchDTO dto = new SearchDTO();
        StringBuilder buf = new StringBuilder()
            .append(numMoreResults)
            .append(" more result");
        if (numMoreResults > 1) {
            buf.append("s");
        }
        dto.setStatus(buf.toString());
        dto.setType(AppConstants.SEARCH_MORE_RESULTS_TYPE_STRING);
        if (log.isDebugEnabled()) {
            log.debug("returning DTO {}", dto);
        }
        return dto;
    }
    
    /**
     * Builds a SearchDTO instance from the supplied Customer record.
     * 
     * @param customer a Customer instance
     * @return SearchDTO populated with customer data
     */
    private static SearchDTO buildCustomerSearchDTO(Customer customer) {
        SearchDTO dto = new SearchDTO();
        dto.setId(AppConstants.SEARCH_CUSTOMER_TYPE_STRING + "_" +
                  customer.getCustomerId());
        dto.setSiteId(customer.getSite().getSiteId());
        dto.setType(AppConstants.SEARCH_CUSTOMER_TYPE_STRING);
        dto.setRecordId(customer.getCustomerId());
        dto.setExternalId(customer.getExternalId());
        if(customer.getParentId() != null) {
		  if(customer.getParentId().getSecondaryEmail()!= null || !"".equals(customer.getParentId().getSecondaryEmail())) {
			 dto.setSecondaryEmail(customer.getParentId().getSecondaryEmail());
			}
        }
        if (customer.getPerson() != null) {
            dto.setFirstName(customer.getPerson().getFirstName());
            dto.setLastName(customer.getPerson().getLastName());
            dto.setEmail(customer.getPerson().getEmail());
            dto.setPhone(customer.getPerson().getPhone());
        }
	    if (customer.getCustomerStatus() != null) {
            dto.setStatus(customer.getCustomerStatus().getExternalId());
        }
	    
	  //GSSP-343 Changes to fix solr issue for Online customers(Customer# Start with 903)
        if (customer.getLocation_external_id() != null) {
            dto.setLocationExternalId(customer.getLocation_external_id());
        }
        
        if(null != customer.getLessonCounts()){
        	dto.setLessonCount(customer.getLessonCounts());
        }
        
        return dto;
    }
    
    private static SearchDTO buildCustomerSearchSecDTO(Customer customer) {
        SearchDTO dto = new SearchDTO();
        dto.setId(AppConstants.SEARCH_CUSTOMER_TYPE_STRING + "_" +
                  customer.getCustomerId());
        dto.setSiteId(customer.getSite().getSiteId());
        dto.setType(AppConstants.SEARCH_CUSTOMER_TYPE_STRING);
        dto.setRecordId(customer.getCustomerId());
        dto.setExternalId(customer.getExternalId());
 
        dto.setSecondaryEmail(customer.getParentId().getSecondaryEmail());
        if (customer.getPerson() != null) {
            dto.setFirstName(customer.getPerson().getFirstName());
            dto.setLastName(customer.getPerson().getLastName());
            dto.setEmail(customer.getPerson().getEmail());
            dto.setPhone(customer.getPerson().getPhone());
        }
	    if (customer.getCustomerStatus() != null) {
            dto.setStatus(customer.getCustomerStatus().getExternalId());
        }
	    
	  //GSSP-343 Changes to fix solr issue for Online customers(Customer# Start with 903)
        if (customer.getLocation_external_id() != null) {
            dto.setLocationExternalId(customer.getLocation_external_id());
        }
        
        if(null != customer.getLessonCounts()){
        	dto.setLessonCount(customer.getLessonCounts());
        }
        
        return dto;
    }
    
    /**
     * Builds a SearchDTO instance from the supplied Instructor record.
     * 
     * @param instructor a Instructor instance
     * @return SearchDTO populated with customer data
     */
    private static SearchDTO buildInstructorSearchDTO(Instructor instructor) {
        SearchDTO dto = new SearchDTO();
        dto.setId(AppConstants.SEARCH_INSTRUCTOR_TYPE_STRING + "_" +
                  instructor.getInstructorId());
        dto.setSiteId(instructor.getSite().getSiteId());
        dto.setType(AppConstants.SEARCH_INSTRUCTOR_TYPE_STRING);
        dto.setRecordId(instructor.getInstructorId());
        dto.setExternalId(instructor.getExternalId());
        dto.setFirstName(instructor.getPerson().getFirstName());
        dto.setLastName(instructor.getPerson().getLastName());
        dto.setEmail(instructor.getPerson().getEmail());
        dto.setStatus(instructor.getStatus());
        return dto;
    }
    
    /**
     * Builds a SearchDTO by given parameter
     * 
     * @param record 
     * @return
     */
    public static SearchDTO buildLocationSearchDTO(Object record) {
    	SearchDTO dto = new SearchDTO();
    	if(record instanceof Location) {
    		Location l = (Location)record;
    		dto.setId(AppConstants.SEARCH_LOCATION_TYPE_STRING + "_" + l.getLocationId());
    		dto.setSiteId(l.getSite().getSiteId());
    		dto.setType(AppConstants.SEARCH_LOCATION_TYPE_STRING);
    		dto.setRecordId(l.getLocationId());
    		dto.setExternalId(l.getExternalId());
    		dto.setLocationName(l.getLocationName());
    		dto.setAddress1(l.getAddress1());
    		dto.setAddress2(l.getAddress2());
    		dto.setCity(l.getCity());
    		dto.setCountry(l.getCountry());
    		Enabled isEnabled = l.getLocationProfile() == null ? null :l.getLocationProfile().getEnabled();
    		String enabled = "";
    		if(Enabled.Y.equals(isEnabled)) {
    			enabled = LOCATION_ENABLED;
    		}
    		if(Enabled.N.equals(isEnabled)) {
    			enabled = LOCATION_NOT_ENABLED;
    		}
    		dto.setEnabled(enabled);
    		dto.setFax(l.getFax());
    		dto.setPhone(l.getPhone());
    		dto.setState(l.getState());
    		dto.setZip(l.getZip());
    	}
    	return dto;
    }
    
}
