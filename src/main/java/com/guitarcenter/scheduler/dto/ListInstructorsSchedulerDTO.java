package com.guitarcenter.scheduler.dto;

import java.util.List;

import com.guitarcenter.scheduler.dao.criterion.dto.InstructorLessonLinkDTO;

public class ListInstructorsSchedulerDTO {

	private String instructorFirstName;

	private String instructorLastName;

	private String instructorAuthId;

	private String queryDate;
	
	private String timeZone;

	public String getQueryDate() {
		return queryDate;
	}

	public void setQueryDate(String queryDate) {
		this.queryDate = queryDate;
	}

	public String getInstructorAuthId() {
		return instructorAuthId;
	}

	public void setInstructorAuthId(String instructorAuthId) {
		this.instructorAuthId = instructorAuthId;
	}

	public String getInstructorFirstName() {
		return instructorFirstName;
	}

	public void setInstructorFirstName(String instructorFirstName) {
		this.instructorFirstName = instructorFirstName;
	}

	public String getInstructorLastName() {
		return instructorLastName;
	}

	public void setInstructorLastName(String instructorLastName) {
		this.instructorLastName = instructorLastName;
	}

	private List<InstructorLessonLinkDTO> instructorLessonLinkDTO;
	
	private String  dayStartEndTime;

	public List<InstructorLessonLinkDTO> getInstructorLessonLinkDTO() {
		return instructorLessonLinkDTO;
	}

	public void setInstructorLessonLinkDTO(List<InstructorLessonLinkDTO> instructorLessonLinkDTO) {
		this.instructorLessonLinkDTO = instructorLessonLinkDTO;
	}

	public String getDayStartEndTime() {
		return dayStartEndTime;
	}

	public void setDayStartEndTime(String dayStartEndTime) {
		this.dayStartEndTime = dayStartEndTime;
	}

	public String getTimeZone() {
		return timeZone;
	}

	public void setTimeZone(String timeZone) {
		this.timeZone = timeZone;
	}
	
	

	 
}
