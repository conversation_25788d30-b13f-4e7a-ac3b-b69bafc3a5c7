package com.guitarcenter.scheduler.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * @Date 4/27/2020 4:34 PM
 * <AUTHOR>
 **/
public class InternalRemarkDTO {

    private String remark;

    private String instructorFullName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss'@'z")
    private Date remarkDate;


    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getInstructorFullName() {
        return instructorFullName;
    }

    public void setInstructorFullName(String instructorFullName) {
        this.instructorFullName = instructorFullName;
    }

    public Date getRemarkDate() {
        return remarkDate;
    }

    public void setRemarkDate(Date remarkDate) {
        this.remarkDate = remarkDate;
    }
}
