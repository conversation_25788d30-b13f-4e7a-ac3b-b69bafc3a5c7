package com.guitarcenter.scheduler.dto;

import java.util.List;

public class InstructorViewUnavailableHourDTOWrapper {

	private String instructorId;
	private List<CalendarViewUnavailableHourDTO> list;

	public InstructorViewUnavailableHourDTOWrapper() {
		super();
	}

	public InstructorViewUnavailableHourDTOWrapper(String instructorId,
			List<CalendarViewUnavailableHourDTO> list) {
		super();
		this.instructorId = instructorId;
		this.list = list;
	}

	/**
	 * @return the instructorId
	 */
	public String getInstructorId() {
		return instructorId;
	}

	/**
	 * @param instructorId
	 *            the instructorId to set
	 */
	public void setInstructorId(String instructorId) {
		this.instructorId = instructorId;
	}

	/**
	 * @return the list
	 */
	public List<CalendarViewUnavailableHourDTO> getList() {
		return list;
	}

	/**
	 * @param list
	 *            the list to set
	 */
	public void setList(List<CalendarViewUnavailableHourDTO> list) {
		this.list = list;
	}

}
