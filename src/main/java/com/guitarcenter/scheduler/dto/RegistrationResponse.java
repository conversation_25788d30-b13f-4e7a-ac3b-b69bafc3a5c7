package com.guitarcenter.scheduler.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RegistrationResponse {
	
	  private String statusMessage;
	    private Profile profile;
	    private String statusCode;
	    
		public String getStatusMessage() {
			return statusMessage;
		}
		public void setStatusMessage(String statusMessage) {
			this.statusMessage = statusMessage;
		}
		public Profile getProfile() {
			return profile;
		}
		public void setProfile(Profile profile) {
			this.profile = profile;
		}
		public String getStatusCode() {
			return statusCode;
		}
		public void setStatusCode(String statusCode) {
			this.statusCode = statusCode;
		}
		
		@Override
		public String toString() {
			return "RegistrationResponse [statusMessage=" + statusMessage + ", profile=" + profile + ", statusCode="
					+ statusCode + "]";
		}

	    
	    

}
