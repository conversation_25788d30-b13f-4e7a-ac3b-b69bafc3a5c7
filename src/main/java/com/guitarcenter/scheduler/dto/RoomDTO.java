package com.guitarcenter.scheduler.dto;

import java.util.List;


public class RoomDTO {

	private Long roomId;
	private String roomSize;
	private String roomNumber;
	private String roomTemplate;
	private String isSplitRoom;
	private String profileRoomName;
	private String enabled;
	private String activities;
	private String services;
	private Long parentRoom;
	private List<ActivityDTO> activityList;
	private List<ServiceDTO> serviceList;
	
	private String isSelectedRoom;

	public RoomDTO() {
	}
	
	public RoomDTO(Long roomId, String profileRoomName) {
		super();
		this.roomId = roomId;
		this.profileRoomName = profileRoomName;
	}

	/**
	 * getter method
	 * @return the roomId
	 */
	public Long getRoomId() {
		return roomId;
	}

	/**
	 * setter method
	 * @param roomId the roomId to set
	 */
	public void setRoomId(Long roomId) {
		this.roomId = roomId;
	}

	/**
	 * getter method
	 * @return the roomSize
	 */
	public String getRoomSize() {
		return roomSize;
	}

	/**
	 * setter method
	 * @param roomSize the roomSize to set
	 */
	public void setRoomSize(String roomSize) {
		this.roomSize = roomSize;
	}

	/**
	 * getter method
	 * @return the roomNumber
	 */
	public String getRoomNumber() {
		return roomNumber;
	}

	/**
	 * setter method
	 * @param roomNumber the roomNumber to set
	 */
	public void setRoomNumber(String roomNumber) {
		this.roomNumber = roomNumber;
	}

	/**
	 * getter method
	 * @return the roomTemplate
	 */
	public String getRoomTemplate() {
		return roomTemplate;
	}

	/**
	 * setter method
	 * @param roomTemplate the roomTemplate to set
	 */
	public void setRoomTemplate(String roomTemplate) {
		this.roomTemplate = roomTemplate;
	}

	/**
	 * getter method
	 * @return the isSplitRoom
	 */
	public String getIsSplitRoom() {
		return isSplitRoom;
	}

	/**
	 * setter method
	 * @param isSplitRoom the isSplitRoom to set
	 */
	public void setIsSplitRoom(String isSplitRoom) {
		this.isSplitRoom = isSplitRoom;
	}

	/**
	 * getter method
	 * @return the profileRoomName
	 */
	public String getProfileRoomName() {
		return profileRoomName;
	}

	/**
	 * setter method
	 * @param profileRoomName the profileRoomName to set
	 */
	public void setProfileRoomName(String profileRoomName) {
		this.profileRoomName = profileRoomName;
	}

	/**
	 * getter method
	 * @return the enabled
	 */
	public String getEnabled() {
		return enabled;
	}

	/**
	 * setter method
	 * @param enabled the enabled to set
	 */
	public void setEnabled(String enabled) {
		this.enabled = enabled;
	}

	/**
	 * getter method
	 * @return the activities
	 */
	public String getActivities() {
		return activities;
	}

	/**
	 * setter method
	 * @param activities the activities to set
	 */
	public void setActivities(String activities) {
		this.activities = activities;
	}

	/**
	 * getter method
	 * @return the services
	 */
	public String getServices() {
		return services;
	}

	/**
	 * setter method
	 * @param services the services to set
	 */
	public void setServices(String services) {
		this.services = services;
	}

	/**
	
	  * RoomDTO. 
	  * <p>Title: </p>
	  * <p>Description: </p>
	  * @param roomId
	  * @param roomSize
	  * @param roomNumber
	  * @param roomTemplate
	  * @param isSplitRoom
	  * @param profileRoomName
	  * @param enabled
	  * @param activities
	  * @param services
	  */
	public RoomDTO(Long roomId, String roomSize,
			String roomNumber, String roomTemplate, String isSplitRoom,
			String profileRoomName, String enabled, String activities,
			String services) {
		super();
		this.roomId = roomId;
		this.roomSize = roomSize;
		this.roomNumber = roomNumber;
		this.roomTemplate = roomTemplate;
		this.isSplitRoom = isSplitRoom;
		this.profileRoomName = profileRoomName;
		this.enabled = enabled;
		this.activities = activities;
		this.services = services;
	}

	/**
	 * getter method
	 * @return the parentRoom
	 */
	public Long getParentRoom() {
		return parentRoom;
	}

	/**
	 * setter method
	 * @param parentRoom the parentRoom to set
	 */
	public void setParentRoom(Long parentRoom) {
		this.parentRoom = parentRoom;
	}


	/**
	 * getter method
	 * @return the activityList
	 */
	public List<ActivityDTO> getActivityList() {
		return activityList;
	}

	/**
	 * setter method
	 * @param activityList the activityList to set
	 */
	public void setActivityList(List<ActivityDTO> activityList) {
		this.activityList = activityList;
	}

	/**
	 * getter method
	 * @return the serviceList
	 */
	public List<ServiceDTO> getServiceList() {
		return serviceList;
	}

	/**
	 * setter method
	 * @param serviceList the serviceList to set
	 */
	public void setServiceList(List<ServiceDTO> serviceList) {
		this.serviceList = serviceList;
	}

	/**
	  * <p>Title: toString</p>
	  * <p>Description: </p>
	  * @return
	  * @see java.lang.Object#toString()
	  */
	@Override
	public String toString() {
		return "RoomDTO [roomId=" + roomId + ", roomSize=" + roomSize
				+ ", roomNumber=" + roomNumber
				+ ", roomTemplate=" + roomTemplate + ", isSplitRoom="
				+ isSplitRoom + ", profileRoomName=" + profileRoomName
				+ ", enabled=" + enabled + ", activities=" + activities
				+ ", services=" + services + ", parentRoom=" + parentRoom + "]";
	}

	/**
	 * @return the isSelectedRoom
	 */
	public String getIsSelectedRoom() {
		return isSelectedRoom;
	}

	/**
	 * @param isSelectedRoom the isSelectedRoom to set
	 */
	public void setIsSelectedRoom(String isSelectedRoom) {
		this.isSelectedRoom = isSelectedRoom;
	}
	
}
