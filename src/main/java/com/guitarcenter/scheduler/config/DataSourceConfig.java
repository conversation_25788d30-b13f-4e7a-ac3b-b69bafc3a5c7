package com.guitarcenter.scheduler.config;

import jakarta.mail.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Properties;

@Configuration
public class DataSourceConfig {

    private static final Logger logger = LoggerFactory.getLogger(DataSourceConfig.class);

    @Value("${jdbc.schedulerDB.driverClassName}")
    private String driverClassName;

    @Value("${jdbc.schedulerDB.url}")
    private String dbUrl;

    @Value("${jdbc.schedulerDB.username}")
    private String dbUsername;

    @Value("${jdbc.schedulerDB.password}")
    private String dbPassword;

    @Value("${mail.smtp.host}")
    private String smtpHost;

    @Value("${mail.smtp.port}")
    private String smtpPort;

    @Value("${mail.debug}")
    private String mailDebug;

    @Bean
    @Primary
    public DataSource primaryDataSource() {
        logger.info("Initializing primary DataSource...");
        try {
            DataSource dataSource = DataSourceBuilder.create()
                    .driverClassName(driverClassName)
                    .url(dbUrl)
                    .username(dbUsername)
                    .password(dbPassword)
                    .type(org.apache.commons.dbcp2.BasicDataSource.class)
                    .build();
            logger.info("Primary DataSource initialized successfully.");
            return dataSource;
        } catch (Exception e) {
            logger.error("Error initializing primary DataSource.", e);
            throw e;
        }
    }

    @Bean
    public DataSource schemaAwareDataSource() throws SQLException {
        logger.info("Configuring schema-aware DataSource...");
        DataSource ds = primaryDataSource();
        try (Connection connection = ds.getConnection()) {
            logger.info("Testing database connection...");
            if (connection.isValid(2)) {
                logger.info("Database connection is valid.");
            } else {
                logger.warn("Database connection is not valid.");
            }
            logger.info("Setting schema to 'gcstudio'...");
            connection.prepareStatement("ALTER SESSION SET CURRENT_SCHEMA = gcstudio").execute();
            logger.info("Schema set successfully.");
        } catch (SQLException e) {
            logger.error("Error testing database connection or setting schema.", e);
            throw e;
        }
        return ds;
    }

    @Bean
    public Session mailSession() {
        logger.info("Configuring JavaMail Session...");
        Properties mailProperties = new Properties();
        mailProperties.put("mail.smtp.host", smtpHost);
        mailProperties.put("mail.smtp.port", smtpPort);
        mailProperties.put("mail.debug", mailDebug);

        Session session = Session.getInstance(mailProperties);

        // Validate if the session is created
        if (session != null) {
            logger.info("JavaMail Session created successfully with properties: {}", mailProperties);
        } else {
            logger.error("Failed to create JavaMail Session.");
            throw new IllegalStateException("JavaMail Session creation failed.");
        }

        return session;
    }
}