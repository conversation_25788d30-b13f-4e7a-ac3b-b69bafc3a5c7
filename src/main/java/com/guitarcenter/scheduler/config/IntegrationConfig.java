package com.guitarcenter.scheduler.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.jms.annotation.EnableJms;
import org.springframework.jms.config.DefaultJmsListenerContainerFactory;
import org.springframework.jms.listener.adapter.MessageListenerAdapter;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.jms.support.converter.MarshallingMessageConverter;

import com.guitarcenter.scheduler.integration.service.impl.CustomerIntegrationServiceImpl;


@Configuration
@EnableJms
@ComponentScan(basePackages = "com.guitarcenter.scheduler.integration")
public class IntegrationConfig {

	/*
	 * @Bean public Jaxb2Marshaller posCustomerMarshaller() { Jaxb2Marshaller
	 * marshaller = new Jaxb2Marshaller();
	 * marshaller.setClassesToBeBound(com.guitarcenter.scheduler.integration.
	 * generated.SyncCustomer.class); return marshaller; }
	 *
	 * @Bean public MarshallingMessageConverter posMessageConverter(Jaxb2Marshaller
	 * posCustomerMarshaller) { return new
	 * MarshallingMessageConverter(posCustomerMarshaller); }
	 *
	 * @Bean public MessageListenerAdapter
	 * posCustomerListener(CustomerIntegrationServiceImpl
	 * customerIntegrationServiceImpl, MarshallingMessageConverter
	 * posMessageConverter) { MessageListenerAdapter adapter = new
	 * MessageListenerAdapter(customerIntegrationServiceImpl, "process");
	 * adapter.setMessageConverter(posMessageConverter); return adapter; }
	 *
	 * @Bean public DefaultJmsListenerContainerFactory
	 * jmsListenerContainerFactory(ConnectionFactory connectionFactory) {
	 * DefaultJmsListenerContainerFactory factory = new
	 * DefaultJmsListenerContainerFactory();
	 * factory.setConnectionFactory(connectionFactory);
	 * factory.setSessionTransacted(true);
	 * factory.setCacheLevelName("CACHE_SESSION"); return factory; }
	 *
	 * @Bean public String posQueue() { // Replace with the appropriate queue name
	 * if needed return "GCI.GC.LESSONS.PRV.CUSTOMER.INBOUND"; }
	 */
}
