/*
package com.guitarcenter.scheduler.config;

import com.guitarcenter.scheduler.security.GCSSAuthenticationProviderDecorator;
import com.guitarcenter.scheduler.security.LoginAuthenticationSuccessHandler;
import com.guitarcenter.scheduler.service.EmployeeService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.service.PersonRoleService;
import com.guitarcenter.scheduler.service.UserLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.ldap.authentication.LdapAuthenticationProvider;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;

import java.io.IOException;
import java.util.Properties;

*/
/**
 * @Date 11/25/2020 3:47 PM
 * <AUTHOR>
 **//*

@Configuration
@Profile("production")
public class ProdWebSecurityBeans {
    private static Logger logger = LoggerFactory.getLogger(ProdWebSecurityBeans.class);




    @Autowired
    private LdapAuthenticationProvider domesticProvider;

    @Autowired
    private LdapAuthenticationProvider retailProvider;

    @Autowired
    private LdapAuthenticationProvider mfProvider;


    private static final String DOMAIN_DOMESTIC = "DOMESTIC";
    private static final String DOMAIN_RETAIL = "RETAIL";
    private static final String DOMAIN_MF = "MF";


    @Bean
    public LoginAuthenticationSuccessHandler loginAuthenticationSuccessHandler(UserLogService userLogService){
        LoginAuthenticationSuccessHandler loginAuthenticationSuccessHandler = new LoginAuthenticationSuccessHandler();
        loginAuthenticationSuccessHandler.setUserLogService(userLogService);
        return loginAuthenticationSuccessHandler;
    }


    @Bean
    @Primary
    public ProviderManager gcssAuthenticationManager(@Qualifier("domesticProviderDecorator") GCSSAuthenticationProviderDecorator domesticProviderDecorator,
                                                     @Qualifier("retailProviderDecorator") GCSSAuthenticationProviderDecorator retailProviderDecorator,
                                                     @Qualifier("mfProviderDecorator") GCSSAuthenticationProviderDecorator mfProviderDecorator) {


        return new ProviderManager(domesticProviderDecorator, retailProviderDecorator, mfProviderDecorator);
    }


    @Bean("domesticProviderDecorator")
    public GCSSAuthenticationProviderDecorator domesticProviderDecorator(PersonRoleService personRoleService,
                                                                         EmployeeService employeeService,
                                                                         InstructorService instructorService) {

        GCSSAuthenticationProviderDecorator domesticProviderDecorator = new GCSSAuthenticationProviderDecorator();
        domesticProviderDecorator.setPersonRoleService(personRoleService);
        domesticProviderDecorator.setEmployeeService(employeeService);
        domesticProviderDecorator.setInstructorService(instructorService);
        domesticProviderDecorator.setDomain(DOMAIN_DOMESTIC);
        domesticProviderDecorator.setAuthenticationProvider(domesticProvider);

        return domesticProviderDecorator;
    }


    @Bean("retailProviderDecorator")
    public GCSSAuthenticationProviderDecorator retailProviderDecorator(PersonRoleService personRoleService,
                                                                       EmployeeService employeeService,
                                                                       InstructorService instructorService) {



        GCSSAuthenticationProviderDecorator retailProviderDecorator = new GCSSAuthenticationProviderDecorator();
        retailProviderDecorator.setPersonRoleService(personRoleService);
        retailProviderDecorator.setEmployeeService(employeeService);
        retailProviderDecorator.setInstructorService(instructorService);
        retailProviderDecorator.setDomain(DOMAIN_RETAIL);
        retailProviderDecorator.setAuthenticationProvider(retailProvider);

        return retailProviderDecorator;
    }

    @Bean("mfProviderDecorator")
    public GCSSAuthenticationProviderDecorator mfProviderDecorator(PersonRoleService personRoleService,
                                                                   EmployeeService employeeService,
                                                                   InstructorService instructorService) {


        GCSSAuthenticationProviderDecorator mfProviderDecorator = new GCSSAuthenticationProviderDecorator();
        mfProviderDecorator.setDomain(DOMAIN_MF);
        mfProviderDecorator.setAuthenticationProvider(mfProvider);
        mfProviderDecorator.setInstructorService(instructorService);
        mfProviderDecorator.setEmployeeService(employeeService);
        mfProviderDecorator.setPersonRoleService(personRoleService);
        return mfProviderDecorator;
    }



}
*/
