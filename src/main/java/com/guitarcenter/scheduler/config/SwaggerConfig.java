/*
package com.guitarcenter.scheduler.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.Contact;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("API Documentation")
                        .version("3.1.0")
                        .description("API documentation for the application")
                        .contact(new Contact()
                                .name("Support Team")
                                .email("<EMAIL>")
                                .url("https://example.com")));
    }
}*/
