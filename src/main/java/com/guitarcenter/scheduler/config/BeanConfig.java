package com.guitarcenter.scheduler.config;

import jakarta.mail.Session;
import org.apache.solr.client.solrj.impl.CloudSolrClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.PropertiesFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

import javax.naming.InitialContext;
import javax.naming.NamingException;
import java.util.Properties;
import java.util.TimeZone;

@Configuration
public class BeanConfig {

    private static final Logger logger = LoggerFactory.getLogger(BeanConfig.class);

    @Bean
    public PropertiesFactoryBean hibernateProperties() {
        PropertiesFactoryBean propertiesFactoryBean = new PropertiesFactoryBean();
        Properties properties = new Properties();
        properties.setProperty("hibernate.dialect", "org.hibernate.dialect.Oracle12cDialect");
        properties.setProperty("hibernate.show_sql", "true");
        properties.setProperty("hibernate.connection.autocommit", "true");
        properties.setProperty("hibernate.current_session_context_class", "org.hibernate.context.internal.ThreadLocalSessionContext");
        properties.setProperty("hibernate.cache.use_second_level_cache", "false");
        properties.setProperty("hibernate.cache.use_query_cache", "false");
        properties.setProperty("hibernate.javax.cache.provider", "org.ehcache.jsr107.EhcacheCachingProvider");
        properties.setProperty("hibernate.cache.region.factory_class", "org.hibernate.cache.jcache.JCacheRegionFactory");
        propertiesFactoryBean.setProperties(properties);
        return propertiesFactoryBean;
    }

    @Bean(destroyMethod = "close")
    public CloudSolrClient solrServer() throws NamingException {
        InitialContext ctx = new InitialContext();
        String solrUrl = null;
        CloudSolrClient solrClient = new CloudSolrClient.Builder()
                .withZkHost(solrUrl)
                .build();
        solrClient.setDefaultCollection("collection1");
        return solrClient;
    }

    @Bean
    public FreeMarkerConfigurer freeMarker() {
        FreeMarkerConfigurer configurer = new FreeMarkerConfigurer();
        configurer.setTemplateLoaderPath("classpath:mailtemplates");
        Properties settings = new Properties();
        settings.setProperty("template_update_delay", "0");
        settings.setProperty("default_encoding", "UTF-8");
        settings.setProperty("locale", "en_US");
        configurer.setFreemarkerSettings(settings);
        return configurer;
    }

    @Bean
    public JavaMailSenderImpl mailSender() {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        try {
            logger.info("Initializing mail session...");
            Properties mailProperties = new Properties();
            mailProperties.put("mail.smtp.host", "phxcas.guitarcenter.com");
            mailProperties.put("mail.smtp.port", "25");
            mailProperties.put("mail.smtp.auth", "false");
            Session dummySession = Session.getInstance(mailProperties);
            mailSender.setSession(dummySession);
            logger.info("Mail session initialized successfully.");
        } catch (Exception e) {
            logger.error("Failed to initialize mail session.", e);
        }
        return mailSender;
    }

    @Bean
    public TimeZone easternTimeZone() {
        return TimeZone.getTimeZone("America/New_York");
    }

    @Bean
    public TimeZone centralTimeZone() {
        return TimeZone.getTimeZone("America/Chicago");
    }

    @Bean
    public TimeZone mountainTimeZone() {
        return TimeZone.getTimeZone("America/Phoenix");
    }

    @Bean
    public TimeZone pacificTimeZone() {
        return TimeZone.getTimeZone("America/Los_Angeles");
    }

    @Bean
    public TimeZone hawaiiTimeZone() {
        return TimeZone.getTimeZone("Pacific/Honolulu");
    }

    @Bean
    public TimeZone alaskaTimeZone() {
        return TimeZone.getTimeZone("America/Anchorage");
    }

    @Bean
    public TimeZone arizonaTimeZone() {
        return TimeZone.getTimeZone("America/Denver");
    }

    @Bean
    public PropertySourcesPlaceholderConfigurer propertyConfigurer() {
        PropertySourcesPlaceholderConfigurer configurer = new PropertySourcesPlaceholderConfigurer();
        configurer.setLocations(new org.springframework.core.io.ClassPathResource("config/adp.properties"),
                new org.springframework.core.io.ClassPathResource("config/crm.properties"),
                new org.springframework.core.io.ClassPathResource("config/edw.properties"));
        return configurer;
    }

    private Session jndiLookup(String jndiName) {
        try {
            InitialContext ctx = new InitialContext();
            return (Session) ctx.lookup(jndiName);
        } catch (NamingException e) {
            throw new RuntimeException("JNDI lookup failed for: " + jndiName, e);
        }
    }
}