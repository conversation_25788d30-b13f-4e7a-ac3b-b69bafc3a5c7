package com.guitarcenter.scheduler.config;

import jakarta.annotation.PostConstruct;
import jakarta.servlet.ServletContext;
import jakarta.servlet.ServletException;
import org.springframework.boot.web.server.ErrorPage;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletContextInitializer;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.http.HttpStatus;
import org.springframework.jndi.JndiObjectFactoryBean;
import org.springframework.web.filter.CharacterEncodingFilter;
import org.springframework.web.filter.DelegatingFilterProxy;
import org.springframework.web.servlet.DispatcherServlet;

import javax.naming.NamingException;
import javax.sql.DataSource;

@Configuration
public class CombinedWebConfig {

    private final ConfigurableEnvironment environment;

    public CombinedWebConfig(ConfigurableEnvironment environment) {
        this.environment = environment;
    }

    /**
     * Error pages configuration
     */
    @Bean
    public WebServerFactoryCustomizer<ConfigurableServletWebServerFactory> webServerFactoryCustomizer() {
        return factory -> {
            factory.addErrorPages(new ErrorPage(HttpStatus.BAD_REQUEST, "/WEB-INF/views/error/400.jsp"));
            factory.addErrorPages(new ErrorPage(HttpStatus.FORBIDDEN, "/WEB-INF/views/error/403.jsp"));
            factory.addErrorPages(new ErrorPage(HttpStatus.NOT_FOUND, "/WEB-INF/views/error/404.jsp"));
            factory.addErrorPages(new ErrorPage(HttpStatus.INTERNAL_SERVER_ERROR, "/WEB-INF/views/error/500.jsp"));
        };
    }

    /**
     * ServletContextInitializer to set up session timeout and other context-specific settings
     */
    @Bean
    public ServletContextInitializer initializer() {
        return new ServletContextInitializer() {
            @Override
            public void onStartup(ServletContext servletContext) throws ServletException {
                // Set session timeout
                servletContext.getSessionCookieConfig().setMaxAge(40 * 60);

                // Set the default spring profile
                servletContext.setInitParameter("spring.profiles.default", "production");
            }
        };
    }

    /**
     * JNDI DataSource configuration
     */
  /*  @Bean
    public DataSource dataSource() throws NamingException {
        JndiObjectFactoryBean bean = new JndiObjectFactoryBean();
        bean.setJndiName("java:comp/env/jdbc/schedulerDB");
        bean.setProxyInterface(DataSource.class);
        bean.setLookupOnStartup(false);
        bean.afterPropertiesSet();
        return (DataSource) bean.getObject();
    }*/

    /**
     * JNDI Mail Session configuration
     */
   /* @Bean
    public Session mailSession() throws NamingException {
        JndiObjectFactoryBean bean = new JndiObjectFactoryBean();
        bean.setJndiName("java:comp/env/mail/session");
        bean.setProxyInterface(Session.class);
        bean.setLookupOnStartup(false);
        bean.afterPropertiesSet();
        return (Session) bean.getObject();
    }*/

    /**
     * Character Encoding Filter configuration
     */
    @Bean
    public FilterRegistrationBean<CharacterEncodingFilter> characterEncodingFilter() {
        CharacterEncodingFilter filter = new CharacterEncodingFilter();
        filter.setEncoding("UTF-8");
        filter.setForceEncoding(true);
        FilterRegistrationBean<CharacterEncodingFilter> filterBean = new FilterRegistrationBean<>(filter);
        filterBean.addUrlPatterns("/*");
        return filterBean;
    }

    /**
     * Spring Security Filter Chain configuration
     */
 /*   @Bean
    public FilterRegistrationBean<DelegatingFilterProxy> securityFilterChain() {
        DelegatingFilterProxy securityFilter = new DelegatingFilterProxy("springSecurityFilterChain");
        FilterRegistrationBean<DelegatingFilterProxy> filterBean = new FilterRegistrationBean<>(securityFilter);
        filterBean.addUrlPatterns("/*");
        return filterBean;
    }*/

    /**
     * SimpleCORSFilter configuration (if you have a custom implementation)
     */
    @Bean
    public FilterRegistrationBean<com.guitarcenter.scheduler.security.CORSFilter> simpleCorsFilter() {
        FilterRegistrationBean<com.guitarcenter.scheduler.security.CORSFilter> filterBean = new FilterRegistrationBean<>();
        filterBean.setFilter(new com.guitarcenter.scheduler.security.CORSFilter());
        filterBean.addUrlPatterns("/*");
        return filterBean;
    }

    /**
     * Log4j configuration
     */
    @PostConstruct
    public void initLog4jConfig() {
        System.setProperty("log4j.configurationFile", "/log/log4j2.properties");
        System.setProperty("log4j2.disable.jmx", "true");
    }
}
