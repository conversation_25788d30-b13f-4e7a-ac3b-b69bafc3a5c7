/*
package com.guitarcenter.scheduler.config.beans;

import com.guitarcenter.scheduler.security.GCSSAuthenticationProviderDecorator;
import com.guitarcenter.scheduler.security.LoginAuthenticationSuccessHandler;
import com.guitarcenter.scheduler.service.EmployeeService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.service.PersonRoleService;
import com.guitarcenter.scheduler.service.UserLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;

import java.io.IOException;
import java.util.Properties;

*/
/**
 * @Date 11/25/2020 3:47 PM
 * <AUTHOR>
 **//*

@Configuration
@Profile("local")
public class LocalWebSecurityBeans {
  private static Logger logger = LoggerFactory.getLogger(LocalWebSecurityBeans.class);


  private static ResourceLoader resourceLoader = new DefaultResourceLoader();


  private static final String DOMAIN_DOMESTIC = "DOMESTIC";
  private static final String DOMAIN_RETAIL = "RETAIL";
  private static final String DOMAIN_MF = "MF";

  @Bean
  public LoginAuthenticationSuccessHandler loginAuthenticationSuccessHandler(UserLogService userLogService){
    LoginAuthenticationSuccessHandler loginAuthenticationSuccessHandler = new LoginAuthenticationSuccessHandler();
    loginAuthenticationSuccessHandler.setUserLogService(userLogService);
    return loginAuthenticationSuccessHandler;
  }


  @Bean
  @Primary
  public ProviderManager gcssAuthenticationManager(@Qualifier("domesticProviderDecorator") GCSSAuthenticationProviderDecorator domesticProviderDecorator,
                                                   @Qualifier("retailProviderDecorator") GCSSAuthenticationProviderDecorator retailProviderDecorator,
                                                   @Qualifier("mfProviderDecorator") GCSSAuthenticationProviderDecorator mfProviderDecorator) {


    return new ProviderManager(domesticProviderDecorator, retailProviderDecorator, mfProviderDecorator);
  }


  @Bean("domesticProviderDecorator")
  public GCSSAuthenticationProviderDecorator domesticProviderDecorator(PersonRoleService personRoleService,
                                                                       EmployeeService employeeService,
                                                                       InstructorService instructorService) {

    Properties domesticAuthDevProperties = new Properties();
    Resource domesticAuthResource = resourceLoader.getResource("classpath:domestic.auth.dev");
    try {
      domesticAuthDevProperties.load(domesticAuthResource.getInputStream());
    } catch (IOException e) {
      logger.error("load auth property files error", e);
      throw new RuntimeException("load auth property files error");
    }
    UserDetailsService domesticUserDetailsService = new InMemoryUserDetailsManager(domesticAuthDevProperties);
    DaoAuthenticationProvider domesticProvider = new DaoAuthenticationProvider();
    domesticProvider.setUserDetailsService(domesticUserDetailsService);

    GCSSAuthenticationProviderDecorator domesticProviderDecorator = new GCSSAuthenticationProviderDecorator();
    domesticProviderDecorator.setPersonRoleService(personRoleService);
    domesticProviderDecorator.setEmployeeService(employeeService);
    domesticProviderDecorator.setInstructorService(instructorService);
    domesticProviderDecorator.setDomain(DOMAIN_DOMESTIC);
    domesticProviderDecorator.setAuthenticationProvider(domesticProvider);

    return domesticProviderDecorator;
  }


  @Bean("retailProviderDecorator")
  public GCSSAuthenticationProviderDecorator retailProviderDecorator(PersonRoleService personRoleService,
                                                                     EmployeeService employeeService,
                                                                     InstructorService instructorService) {

    Properties retailAuthDevProperties = new Properties();
    Resource retailAuthResource = resourceLoader.getResource("classpath:retail.auth.dev");

    try {
      retailAuthDevProperties.load(retailAuthResource.getInputStream());
    } catch (IOException e) {
      logger.error("load auth property files error", e);
      throw new RuntimeException("load auth property files error");
    }
    UserDetailsService retailDetailsService = new InMemoryUserDetailsManager(retailAuthDevProperties);
    DaoAuthenticationProvider retailProvider = new DaoAuthenticationProvider();
    retailProvider.setUserDetailsService(retailDetailsService);

    GCSSAuthenticationProviderDecorator retailProviderDecorator = new GCSSAuthenticationProviderDecorator();
    retailProviderDecorator.setPersonRoleService(personRoleService);
    retailProviderDecorator.setEmployeeService(employeeService);
    retailProviderDecorator.setInstructorService(instructorService);
    retailProviderDecorator.setDomain(DOMAIN_RETAIL);
    retailProviderDecorator.setAuthenticationProvider(retailProvider);

    return retailProviderDecorator;
  }

  @Bean("mfProviderDecorator")
  public GCSSAuthenticationProviderDecorator mfProviderDecorator(PersonRoleService personRoleService,
                                                                 EmployeeService employeeService,
                                                                 InstructorService instructorService) {
    Properties mfAuthDevProperties = new Properties();
    Resource mfAuthResource = resourceLoader.getResource("classpath:mf.auth.dev");
    try {
      mfAuthDevProperties.load(mfAuthResource.getInputStream());
    } catch (IOException e) {
      logger.error("load auth property files error", e);
      throw new RuntimeException("load auth property files error");
    }
    UserDetailsService mfUserDetailService = new InMemoryUserDetailsManager(mfAuthDevProperties);


    DaoAuthenticationProvider mfUserProvider = new DaoAuthenticationProvider();
    mfUserProvider.setUserDetailsService(mfUserDetailService);

    GCSSAuthenticationProviderDecorator mfProviderDecorator = new GCSSAuthenticationProviderDecorator();
    mfProviderDecorator.setDomain(DOMAIN_MF);
    mfProviderDecorator.setAuthenticationProvider(mfUserProvider);
    mfProviderDecorator.setInstructorService(instructorService);
    mfProviderDecorator.setEmployeeService(employeeService);
    mfProviderDecorator.setPersonRoleService(personRoleService);
    return mfProviderDecorator;
  }



}
*/
