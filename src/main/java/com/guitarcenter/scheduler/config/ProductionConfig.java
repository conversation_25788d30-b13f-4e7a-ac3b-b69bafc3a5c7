package com.guitarcenter.scheduler.config;

        import com.guitarcenter.scheduler.integration.service.EmployeeIntegrationService;
        import com.guitarcenter.scheduler.integration.service.InstructorPersonalDetailsIntegrationService;
        import com.guitarcenter.scheduler.integration.service.LocationIntegrationService;
        import com.guitarcenter.scheduler.integration.service.impl.EmployeeIntegrationServiceImpl;
        import com.guitarcenter.scheduler.integration.service.impl.InstructorPersonalDetailsIntegrationServiceImpl;
        import com.guitarcenter.scheduler.integration.service.impl.LocationIntegrationServiceImpl;
        import com.guitarcenter.scheduler.service.*;
        import com.guitarcenter.scheduler.service.impl.*;
        import org.springframework.beans.factory.annotation.Value;
        import org.springframework.context.annotation.*;
        import org.springframework.orm.jpa.JpaTransactionManager;
        import org.springframework.orm.jpa.JpaVendorAdapter;
        import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
        import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;

        import javax.sql.DataSource;
        import java.util.Properties;
        import java.util.TimeZone;

        @Configuration
        @Import(CacheConfig.class)
        @Profile("prod")
        @PropertySource({
                "classpath:/config/edw.properties",
                "classpath:/config/adp.properties",
                "classpath:/config/pos.properties",
                "classpath:/config/auth.properties",
                "classpath:/config/crm.properties"
        })
        public class ProductionConfig {

            @Value("${auth.domestic.url}")
            private String domesticUrl;

            @Value("${auth.domestic.bindUser}")
            private String domesticBindUser;

            @Value("${auth.domestic.bindPassword}")
            private String domesticBindPassword;

            @Value("${auth.retail.url}")
            private String retailUrl;

            @Value("${auth.retail.bindUser}")
            private String retailBindUser;

            @Value("${auth.retail.bindPassword}")
            private String retailBindPassword;

            @Value("${auth.mf.url}")
            private String mfUrl;

            @Value("${auth.mf.bindUser}")
            private String mfBindUser;

            @Value("${auth.mf.bindPassword}")
            private String mfBindPassword;

            @Value("${pos.serverUrl}")
            private String serverUrl;

            @Bean
            public Properties hibernateProperties() {
                Properties properties = new Properties();
                properties.setProperty("hibernate.dialect", "org.hibernate.dialect.Oracle12cDialect");
                properties.setProperty("hibernate.show_sql", "false");
                properties.setProperty("hibernate.connection.autocommit", "true");
                properties.setProperty("hibernate.cache.use_second_level_cache", "false");
                properties.setProperty("hibernate.cache.use_query_cache", "false");
                properties.setProperty("hibernate.javax.cache.provider", "org.ehcache.jsr107.EhcacheCachingProvider");
                properties.setProperty("hibernate.cache.region.factory_class", "org.hibernate.cache.jcache.JCacheRegionFactory");
                return properties;
            }

            @Bean
            public LocalContainerEntityManagerFactoryBean entityManagerFactory(DataSource schedulerDB, Properties hibernateProperties) {
                LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
                em.setDataSource(schedulerDB);
                em.setPackagesToScan("com.guitarcenter.scheduler.model");
                JpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
                em.setJpaVendorAdapter(vendorAdapter);
                em.setJpaProperties(hibernateProperties);
                return em;
            }

            @Bean
            public JpaTransactionManager transactionManager(DataSource schedulerDB, LocalContainerEntityManagerFactoryBean entityManagerFactory) {
                JpaTransactionManager transactionManager = new JpaTransactionManager();
                transactionManager.setDataSource(schedulerDB);
                transactionManager.setEntityManagerFactory(entityManagerFactory.getObject());
                return transactionManager;
            }

            @Bean
            public TimeZone easternTimeZone() {
                return TimeZone.getTimeZone("America/New_York");
            }

            @Bean
            public TimeZone centralTimeZone() {
                return TimeZone.getTimeZone("America/Chicago");
            }

            @Bean
            public TimeZone mountainTimeZone() {
                return TimeZone.getTimeZone("America/Phoenix");
            }

            @Bean
            public TimeZone pacificTimeZone() {
                return TimeZone.getTimeZone("America/Los_Angeles");
            }

            @Bean
            public TimeZone hawaiiTimeZone() {
                return TimeZone.getTimeZone("Pacific/Honolulu");
            }

            @Bean
            public TimeZone alaskaTimeZone() {
                return TimeZone.getTimeZone("America/Anchorage");
            }

            @Bean
            public TimeZone arizonaTimeZone() {
                return TimeZone.getTimeZone("America/Denver");
            }

            @Bean
            public GetInstructorAvailabiltyService getInstructorAvailabiltyService() {
                return new GetInstructorAvailabiltyServiceImpl();
            }

            @Bean
            public ReportService reportService() {
                return new ReportServiceImpl();
            }

            @Bean
            public InstructorActivitiesService instructorActivitiesService() {
                return new InstructorActivitiesServiceImpl();
            }

            @Bean
            public InstructorFloorTimeService instructorFloorTimeService() {
                return new InstructorFloorTimeServiceImpl();
            }

            @Bean
            public InstructorActivityAndAvailabitityService instructorActivityAndAvailabitityService() {
                return new InstructorActivityAndAvailabilityServiceImpl();
            }

            @Bean
            public DailySubscriptionReportService dailySubscriptionReportService() {
                return new DailySubscriptionReportServiceImpl();
            }

            @Bean
            public MasterScheduleReportService masterScheduleReportService() {
                return new MasterScheduleReportServiceImpl();
            }

            @Bean
            public EmployeeIntegrationService employeeIntegrationService() {
                return new EmployeeIntegrationServiceImpl();
            }

            @Bean
            public CRMAppointmentDataFileService cRMAppointmentDataFileService() {
                return new CRMAppointmentDataFileServiceImpl();
            }

            @Bean
            public InstructorPersonalDetailsIntegrationService instructorPersonalDetailsIntegrationService() {
                return new InstructorPersonalDetailsIntegrationServiceImpl();
            }

            @Bean
            public LackingAppointmentService lackingAppointmentService() {
                return new LackingAppointmentServiceImpl();
            }

            @Bean
            public CancelHoldAppointmentsService cancelHoldAppointmentsService() {
                return new CancelHoldAppointmentsServiceImpl();
            }

            @Bean
            public AppointmentEmailService appointmentEmailService() {
                return new AppointmentEmailServiceImpl();
            }

            @Bean
            public GetInstructorFullAvailabiltyService getInstructorFullAvailabiltyService() {
                return new GetInstructorFullAvailabiltyServiceImpl();
            }

            @Bean
            public LocationIntegrationService locationIntegrationService() {
                return new LocationIntegrationServiceImpl();
            }

            @Bean
            public DeleteDayforceInstructorApptRecordsService deleteDayforceInstructorApptRecordsService() {
                return new DeleteDayforceInstructorApptRecordsServiceImpl();
            }
        }