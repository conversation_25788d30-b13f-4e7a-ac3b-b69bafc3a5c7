package  com.guitarcenter.scheduler.config;

import com.guitarcenter.scheduler.model.*;
import org.ehcache.CacheManager;
import org.ehcache.config.builders.CacheConfigurationBuilder;
import org.ehcache.config.builders.CacheManagerBuilder;
import org.ehcache.config.builders.ExpiryPolicyBuilder;
import org.ehcache.config.builders.ResourcePoolsBuilder;
import org.ehcache.config.units.MemoryUnit;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.jcache.JCacheCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.cache.Caching;
import javax.cache.spi.CachingProvider;
import java.net.URI;
import java.time.Duration;

@Configuration
@EnableCaching
public class CacheConfig {

    @Bean
    public JCacheCacheManager cacheManager() throws Exception {
        javax.cache.CacheManager cacheManager = buildEhcacheCacheManager();
        return new JCacheCacheManager(cacheManager);
    }

    private javax.cache.CacheManager buildEhcacheCacheManager() throws Exception {
        // Create the Ehcache CacheManager using Java configuration
        CacheManager ehcacheManager = CacheManagerBuilder.newCacheManagerBuilder()
                // Default Cache Configuration
                // Define types for keys and values (e.g., String for key, YourClass for value)
             /*   .withCache("defaultCache",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        String.class, YourClass.class, // Replace with your actual types
                                        ResourcePoolsBuilder.heap(100).offheap(100, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.timeToLiveExpiration(Duration.ofSeconds(120)))
                )*/
                // Cache for Activity
                .withCache("defaultCache",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, String.class, // Replace with actual key-value types
                                        ResourcePoolsBuilder.heap(100) // Max 100 entries in the heap
                                )
                                .withExpiry(ExpiryPolicyBuilder.timeToLiveExpiration(Duration.ofSeconds(120))) // TTL 120 seconds
                                .withExpiry(ExpiryPolicyBuilder.timeToIdleExpiration(Duration.ofSeconds(120))) // TTI 120 seconds
                            //    .withStatisticsEnabled(true) // Enable statistics
                )
                .withCache("com.guitarcenter.scheduler.model.Activity",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, Activity.class, // Assuming the key is a String (like an ID) and the value is Activity
                                        ResourcePoolsBuilder.heap(20).offheap(50, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.noExpiration())
                )

                // Cache for CustomerStatus
                .withCache("com.guitarcenter.scheduler.model.CustomerStatus",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, CustomerStatus.class,
                                        ResourcePoolsBuilder.heap(10).offheap(50, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.noExpiration())
                )
                // Cache for Instrument
                .withCache("com.guitarcenter.scheduler.model.Instrument",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, Instrument.class,
                                        ResourcePoolsBuilder.heap(5).offheap(50, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.noExpiration())
                )
                // Cache for Role
                .withCache("com.guitarcenter.scheduler.model.Role",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, Role.class,
                                        ResourcePoolsBuilder.heap(5).offheap(50, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.noExpiration())
                )
                // Cache for RoomNumber
                .withCache("com.guitarcenter.scheduler.model.RoomNumber",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, RoomNumber.class,
                                        ResourcePoolsBuilder.heap(20).offheap(50, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.noExpiration())
                )
                // Cache for RoomSize
                .withCache("com.guitarcenter.scheduler.model.RoomSize",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, RoomSize.class,
                                        ResourcePoolsBuilder.heap(5).offheap(50, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.noExpiration())
                )
                // Cache for RoomTemplate
                .withCache("com.guitarcenter.scheduler.model.RoomTemplate",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, RoomTemplate.class,
                                        ResourcePoolsBuilder.heap(20).offheap(50, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.noExpiration())
                )
                // Cache for RoomType
                .withCache("com.guitarcenter.scheduler.model.RoomType",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, RoomType.class,
                                        ResourcePoolsBuilder.heap(5).offheap(50, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.noExpiration())
                )
                // Cache for Service
                .withCache("com.guitarcenter.scheduler.model.Service",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, Service.class,
                                        ResourcePoolsBuilder.heap(30).offheap(50, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.noExpiration())
                )
                // Cache for Appointment with timeToIdleSeconds and memoryStoreEvictionPolicy
                .withCache("com.guitarcenter.scheduler.model.Appointment",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, Appointment.class,
                                        ResourcePoolsBuilder.heap(1000).offheap(100, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.timeToIdleExpiration(Duration.ofMinutes(20)))
                )
                // Cache for AppointmentSeries
                .withCache("com.guitarcenter.scheduler.model.AppointmentSeries",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, AppointmentSeries.class,
                                        ResourcePoolsBuilder.heap(500).offheap(100, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.timeToIdleExpiration(Duration.ofMinutes(20)))
                )
                // Cache for Availability
                .withCache("com.guitarcenter.scheduler.model.Availability",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, Availability.class,
                                        ResourcePoolsBuilder.heap(100).offheap(100, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.timeToIdleExpiration(Duration.ofMinutes(20)))
                )
                // Cache for Customer
                .withCache("com.guitarcenter.scheduler.model.Customer",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, Customer.class,
                                        ResourcePoolsBuilder.heap(1000).offheap(100, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.timeToIdleExpiration(Duration.ofMinutes(20)))
                )
                // Cache for Employee
                .withCache("com.guitarcenter.scheduler.model.Employee",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, Employee.class,
                                        ResourcePoolsBuilder.heap(1000).offheap(100, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.timeToIdleExpiration(Duration.ofMinutes(20)))
                )
                // Cache for Instructor
                .withCache("com.guitarcenter.scheduler.model.Instructor",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, Instructor.class,
                                        ResourcePoolsBuilder.heap(200).offheap(100, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.timeToIdleExpiration(Duration.ofMinutes(20)))
                )
                // Cache for Location
                .withCache("com.guitarcenter.scheduler.model.Location",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, Location.class,
                                        ResourcePoolsBuilder.heap(200).offheap(100, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.timeToIdleExpiration(Duration.ofMinutes(20)))
                )
                .withCache("com.guitarcenter.scheduler.model.LocationProfile",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, LocationProfile.class,
                                        ResourcePoolsBuilder.heap(1200).offheap(100, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.timeToIdleExpiration(Duration.ofMinutes(20)))
                )
                .withCache("com.guitarcenter.scheduler.model.Person",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, Person.class,
                                        ResourcePoolsBuilder.heap(1000).offheap(100, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.timeToIdleExpiration(Duration.ofMinutes(20)))
                )
                .withCache("com.guitarcenter.scheduler.model.PersonRole",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, PersonRole.class,
                                        ResourcePoolsBuilder.heap(1000).offheap(100, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.timeToIdleExpiration(Duration.ofMinutes(20)))
                )

                .withCache("com.guitarcenter.scheduler.model.ProfileActivity",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, ProfileActivity.class,
                                        ResourcePoolsBuilder.heap(1000).offheap(100, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.timeToIdleExpiration(Duration.ofMinutes(20)))
                )
                .withCache("com.guitarcenter.scheduler.model.ProfileService",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, ProfileService.class,
                                        ResourcePoolsBuilder.heap(1000).offheap(100, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.timeToIdleExpiration(Duration.ofMinutes(20)))
                )
                .withCache("com.guitarcenter.scheduler.model.Room",
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(
                                        Long.class, ProfileService.class,
                                        ResourcePoolsBuilder.heap(1200).offheap(100, MemoryUnit.MB))
                                .withExpiry(ExpiryPolicyBuilder.timeToIdleExpiration(Duration.ofMinutes(20)))
                )
                // Add more caches as needed...

                .build(true);

        // Create JCache CacheManager from Ehcache CacheManager using JSR-107
        CachingProvider cachingProvider = Caching.getCachingProvider();
        URI defaultUri = cachingProvider.getDefaultURI();
        ClassLoader classLoader = cachingProvider.getDefaultClassLoader();

        return cachingProvider.getCacheManager(defaultUri, classLoader);
    }
}
