/*
package com.guitarcenter.scheduler.config.env;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.ConfigurableEnvironment;

*/
/**
 * @Date 12/2/2020 12:44 PM
 * <AUTHOR>
 **//*

@Order
public class EnvironmentConfig implements EnvironmentPostProcessor {

  @Override
  public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
    application.setAllowBeanDefinitionOverriding(true);
  }
}
*/
