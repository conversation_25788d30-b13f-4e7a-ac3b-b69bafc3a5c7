/*
package com.guitarcenter.scheduler.config;


import com.guitarcenter.scheduler.service.LocationManagerService;
import com.guitarcenter.scheduler.service.SiteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Configuration
public class WebSecurityConfig {

    private static final Logger log = LoggerFactory.getLogger(WebSecurityConfig.class);


    @Bean
    @Order(1)
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        log.debug("Configuring HttpSecurity to allow all requests without authentication and no session requirement.");
        System.out.println("Configuring HttpSecurity to allow all requests without authentication and no session requirement.");

        http.csrf(csrf -> csrf.disable()) // Disable CSRF if not needed
                .authorizeHttpRequests(authorize -> authorize
                        .anyRequest().permitAll() // Allow all requests without authentication
                )
                .sessionManagement(sessionManagement ->
                        sessionManagement.sessionCreationPolicy(SessionCreationPolicy.STATELESS) // Do not create sessions
                );

        log.debug("HttpSecurity configuration complete with all requests allowed, no login/logout, and stateless sessions.");
            return http.build();
    }


    // Custom firewall to allow double slashes
    @Bean
    public HttpFirewall httpFirewall() {
        log.debug("Configuring custom HttpFirewall to allow URL encoded double slashes.");
        StrictHttpFirewall firewall = new StrictHttpFirewall();
        firewall.setAllowUrlEncodedDoubleSlash(true);
        firewall.setAllowSemicolon(true);
        firewall.setAllowBackSlash(true);
        return firewall;
    }
}
*/
