package com.guitarcenter.scheduler.config;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@SpringBootApplication(scanBasePackages = {"com.guitarcenter.scheduler", "com.guitarcenter.scheduler.security"}, exclude = {DataSourceAutoConfiguration.class})
@EnableScheduling
@EnableTransactionManagement
@EnableJpaRepositories(basePackages = {"com.guitarcenter.scheduler.dao", "com.guitarcenter.scheduler.data.repository"})
public class Application {

  public static void main(String[] args) {
    SpringApplication.run(Application.class, args);
  }
}