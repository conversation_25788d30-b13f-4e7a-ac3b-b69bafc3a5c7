package com.guitarcenter.scheduler.config;

//import org.apache.activemq.ActiveMQConnectionFactory;
import org.quartz.JobDetail;
import org.springframework.beans.factory.config.PropertiesFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.quartz.CronTriggerFactoryBean;

import java.util.Properties;

@Configuration
@Profile("local")
public class LocalProfileConfig {

    @Bean
    public PropertiesFactoryBean hibernateProperties() {
        PropertiesFactoryBean propertiesFactoryBean = new PropertiesFactoryBean();
        Properties properties = new Properties();

        // Use the Oracle 12c dialect
       // properties.setProperty("hibernate.dialect", "org.hibernate.dialect.OracleDialect");
        properties.setProperty("hibernate.dialect", "org.hibernate.dialect.Oracle12cDialect");


        properties.setProperty("hibernate.show_sql", "true");
        properties.setProperty("hibernate.connection.autocommit", "true");
        properties.setProperty("hibernate.current_session_context_class", "org.hibernate.context.internal.ThreadLocalSessionContext");

        // Use the Ehcache 3.x region factory with CacheConfig.java
        properties.setProperty("hibernate.cache.use_second_level_cache", "true");
        properties.setProperty("hibernate.cache.use_query_cache", "true");
        properties.setProperty("hibernate.javax.cache.provider", "org.ehcache.jsr107.EhcacheCachingProvider");
        properties.setProperty("hibernate.cache.region.factory_class", "org.hibernate.cache.jcache.JCacheRegionFactory");

        propertiesFactoryBean.setProperties(properties);
        return propertiesFactoryBean;
    }

	/*
	 * @Bean public ActiveMQConnectionFactory connectionFactory() {
	 * ActiveMQConnectionFactory factory = new ActiveMQConnectionFactory();
	 * factory.setBrokerURL("vm://pos?broker.persistent=false&broker.useJmx=false");
	 * return factory; }
	 */

    @Bean
    public CronTriggerFactoryBean lackingAppointmentJobScheduler(JobDetail lackingAppointmentJob) {
        CronTriggerFactoryBean factory = new CronTriggerFactoryBean();
        factory.setJobDetail(lackingAppointmentJob);
        factory.setCronExpression("0 0 3 * * ?");
        return factory;
    }

    @Bean
    public CronTriggerFactoryBean conflictingAppointmentJobScheduler(JobDetail conflictingAppointmentJob) {
        CronTriggerFactoryBean factory = new CronTriggerFactoryBean();
        factory.setJobDetail(conflictingAppointmentJob);
        factory.setCronExpression("0 0 3 * * ?");
        return factory;
    }
}
