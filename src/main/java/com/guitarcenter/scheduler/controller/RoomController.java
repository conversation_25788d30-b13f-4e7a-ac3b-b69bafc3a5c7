/**
 * @Title: RoomController.java
 * @Package com.guitarcenter.scheduler.controller
 * @Description: TODO
 * Copyright: Copyright (c) 2013
 * Company:
 * <AUTHOR>
 * @date Sep 10, 2013 2:37:09 PM
 * @version V1.0
 */

package com.guitarcenter.scheduler.controller;

import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.dto.RoomDTO;
import com.guitarcenter.scheduler.dto.RoomDetailDTO;
import com.guitarcenter.scheduler.dto.ServiceDTO;
import com.guitarcenter.scheduler.model.*;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.SplitRoom;
import com.guitarcenter.scheduler.service.*;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.*;

import static com.guitarcenter.scheduler.common.util.MessageConstants.*;

/**
 * <AUTHOR>
 * @ClassName: RoomController
 * @Description: TODO
 * @date Sep 10, 2013 2:37:09 PM
 */
@RestController
@RequestMapping("/rooms")
public class RoomController {
    private static final Logger LOG = LoggerFactory.getLogger(RoomController.class);

    private static final String MESSAGE_KEY = "message";
    private static final String STATUS_KEY = "status";
    private static final String RETURN_DTO = "dto";
    private static final long DEFAULT_SPLIT_ROOM_TYPE = 1;

    @Resource(name = "roomService")
    private RoomService roomService;

    @Resource(name = "roomSizeService")
    private RoomSizeService roomSizeService;

    @Resource(name = "roomTemplateService")
    private RoomTemplateService roomTemplateService;

    @Resource(name = "roomNumberService")
    private RoomNumberService roomNumberService;

    @Resource
    private ActivityService activityService;

    @Autowired
    private LocationProfileService locationProfileService;

    @Autowired
    private ValidationService validationService;

    @GetMapping(value = "/manage", produces = "application/json")
    public ResponseEntity<Map<String, Object>> forwardRoomManagePage(@RequestParam Long siteId) {
        LOG.debug("RoomController.forwardRoomManagePage: start");
        Map<String, Object> response = new HashMap<>();
        try {
            List<RoomTemplate> rtList = roomTemplateService.getRoomTemplateListBySiteId(siteId);
            response.put("roomTemplateList", rtList);
            response.put("status", "success");
            response.put("message", "Room templates retrieved successfully");
            LOG.debug("RoomController.forwardRoomManagePage: end");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            LOG.error("Error retrieving room templates", e);
            response.put("status", "error");
            response.put("message", "Failed to retrieve room templates");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping(value = "/loadRoom", produces = "application/json")
    public ResponseEntity<List<RoomDTO>> loadRoomDTO(@RequestParam Long profileId) {
        LOG.debug("RoomController.loadRoomDTO: start");

        List<RoomDTO> roomDTOlist = roomService.getRoomList(profileId);

        LOG.debug("RoomController.loadRoomDTO: end");
        return ResponseEntity.ok(roomDTOlist);
    }

    @GetMapping(value = "/room/getEditRoom/{roomId}", produces = "application/json")
    public ResponseEntity<Map<String, Object>> getEditRoom(@PathVariable long roomId, @RequestParam long profileId, @RequestParam long siteId) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("RoomController.getEditRoom: start");
        }
        Map<String, Object> map = new HashMap<>();
        RoomDTO roomDTO = roomService.getRoomDTOByRoomId(roomId);
        if (roomDTO == null) {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, "Room not found!");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(map);
        }
        RoomDetailDTO roomDetailDTO = this.getRoomDetailDTO(roomId,profileId,siteId);

        map.put("room", roomDTO);
        map.put("roomDetail", roomDetailDTO);
        RoomTemplate roomTemplate = roomTemplateService.getRoomTemplate(Long.parseLong(roomDTO.getRoomTemplate()));
        SplitRoom canSplit = roomTemplate.getRoomType().getCanSplitRoom();
        map.put("canSplit", null == roomDTO.getParentRoom() && SplitRoom.Y.equals(canSplit));
        List<RoomSize> roomSize = roomSizeService.getRoomSizeListByRoomTypeSiteId(roomTemplate.getSite().getSiteId(), roomTemplate.getRoomType().getRoomTypeId());
        map.put("isShowRoomSize", null == roomDTO.getParentRoom() && roomSize.size() != 0);
        if (LOG.isDebugEnabled()) {
            LOG.debug("RoomController.getEditRoom: end");
        }
        return ResponseEntity.ok(map);
    }

    private RoomDetailDTO getRoomDetailDTO(long roomId, long profileId, long siteId) {
        Room room = roomService.getRoom(roomId);
       // long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute("locationProfileId", RequestAttributes.SCOPE_SESSION);
       // long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute("siteId", RequestAttributes.SCOPE_SESSION);
        List<RoomTemplate> roomTemplateList = roomTemplateService.getRoomTemplateListBySiteId(siteId);
        List<RoomSize> roomSize = roomSizeService.getRoomSizeListByRoomTypeSiteId(siteId, room.getRoomType().getRoomTypeId());
        List<RoomNumber> roomNumber = roomNumberService.getRoomNumberBySiteId(siteId);
        RoomDTO roomDTO = roomService.getRoomDTOByRoomId(roomId);
        RoomTemplate roomTemplate = roomTemplateService.getRoomTemplate(room.getRoomTemplate().getRoomTemplateId());
        if (Enabled.N.equals(roomTemplate.getEnabled())) {
            roomTemplateList.add(roomTemplate);
        }
        Set<Service> tService = roomTemplate.getServices();
        Set<Activity> tActivity = roomTemplate.getActivities();
        List<ServiceDTO> selectedServices = roomDTO.getServiceList();
        List<Service> pService = locationProfileService.getProfileServiceList(profileId);
        List<Long> serviceIds = SystemUtil.getIdList(roomDTO.getServices());
        List<Service> fsList = this.getServiceList(pService, tService);
        List<ServiceDTO> unSelectedServices = this.getServiceDTOList(fsList, serviceIds);
        List<ActivityDTO> selectedActivities = filterServiceActivitiesDTO(roomDTO.getActivityList(), serviceIds);
        List<Activity> pActivity = locationProfileService.getProfileActivityList(profileId);
        List<Long> activityId = SystemUtil.getIdList(roomDTO.getActivities());
        List<Activity> faList = this.getActivityList(pActivity, tActivity);
        List<ActivityDTO> unSelectedActivities = filterServiceActivitiesDTO(this.getActivityDTOList(faList, activityId), SystemUtil.getIdListByServiceDTO(selectedServices));
        RoomDetailDTO roomDetailDTO = new RoomDetailDTO(roomId, room.getIsSplitRoom().toString(), room.getEnabled().toString(), roomSize, roomNumber, roomTemplateList, selectedActivities, unSelectedActivities, selectedServices, unSelectedServices);
        return roomDetailDTO;
    }


    @GetMapping(value = "/room/loadRoomServices", produces = "application/json")
    public ResponseEntity<Map<String, Object>> loadRoomServices(@RequestParam long roomTemplateId, @RequestParam String serviceString, @RequestParam long profileId) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("RoomController.loadRoomServices: start");
        }
        RoomTemplate roomTemplate = roomTemplateService.getRoomTemplate(roomTemplateId);
        Set<Service> tServiceList = roomTemplate.getServices();
        List<Service> pServiceList = locationProfileService.getProfileServiceList(profileId);
        List<Long> serviceIds = SystemUtil.getIdList(serviceString);
        List<Service> fList = this.getServiceList(pServiceList, tServiceList);
        List<ServiceDTO> sList = this.getServiceDTOList(fList, serviceIds);

        Map<String, Object> map = new HashMap<>();
        map.put("serviceList", sList);

        if (LOG.isDebugEnabled()) {
            LOG.debug("RoomController.loadRoomServices: end");
        }
        return ResponseEntity.ok(map);
    }

    private List<ServiceDTO> getServiceDTOList(List<Service> pServiceList, Set<Service> tServiceList) {
        List<ServiceDTO> sList = new ArrayList<ServiceDTO>();
        for (Service s : tServiceList) {
            if (Enabled.Y.equals(s.getEnabled()) && pServiceList.contains(s)) {
                ServiceDTO dto = new ServiceDTO(s.getServiceId(), s.getServiceName());
                sList.add(dto);
            }
        }
        return sList;
    }

    private List<Service> getServiceList(List<Service> pServiceList, Set<Service> tServiceList) {
        List<Service> sList = new ArrayList<Service>();
        for (Service s : pServiceList) {
            if (Enabled.Y.equals(s.getEnabled()) && !sList.contains(s)) {
                sList.add(s);
            }
        }
        for (Service s : tServiceList) {
            if (Enabled.Y.equals(s.getEnabled()) && !sList.contains(s)) {
                sList.add(s);
            }
        }
        return sList;
    }

    private List<ServiceDTO> getServiceDTOList(List<Service> serviceList, List<Long> serviceId) {
        List<ServiceDTO> sList = new ArrayList<ServiceDTO>();
        for (Service s : serviceList) {
            if (Enabled.Y.equals(s.getEnabled()) && !serviceId.contains(s.getServiceId())) {
                ServiceDTO dto = new ServiceDTO(s.getServiceId(), s.getServiceName());
                sList.add(dto);
            }
        }
        return sList;
    }

    @GetMapping(value = "/room/loadRoomActivities", produces = "application/json")
    public ResponseEntity<Map<String, Object>> loadRoomActivities(@RequestParam long roomTemplateId, @RequestParam String activityString, @RequestParam String serviceString,@RequestParam long profileId) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("RoomController.loadRoomActivities: start");
        }
    //    long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute("locationProfileId", RequestAttributes.SCOPE_SESSION);
        RoomTemplate roomTemplate = roomTemplateService.getRoomTemplate(roomTemplateId);
        Set<Activity> tActivityList = roomTemplate.getActivities();
        List<Activity> pActivityList = locationProfileService.getProfileActivityList(profileId);
        List<Long> activityId = SystemUtil.getIdList(activityString);
        List<Activity> faList = this.getActivityList(pActivityList, tActivityList);
        List<Long> serviceIds = SystemUtil.getIdList(serviceString);
        Set<Activity> fActivityList = filterServiceActivities(faList, serviceIds);
        List<ActivityDTO> aList = this.getActivityDTOList(fActivityList, activityId);
        List<ActivityDTO> activityList = getIncludeActivityDTOList(fActivityList, activityString);

        Map<String, Object> map = new HashMap<>();
        map.put("activityList", aList);
        map.put("selectedActivityList", activityList);

        if (LOG.isDebugEnabled()) {
            LOG.debug("RoomController.loadRoomActivities: end");
        }
        return ResponseEntity.ok(map);
    }

    private Set<Activity> filterServiceActivities(Collection<Activity> tActivityList, List<Long> serviceIds) {
        Set<Activity> activities = new HashSet<Activity>(0);
        for (Long serviceId : serviceIds) {
            List<Activity> activityList = activityService.loadActivitiesByService(serviceId);
            for (Activity activity : tActivityList) {
                if (activityList.contains(activity)) {
                    activities.add(activity);
                }
            }
        }
        return activities;
    }

    private List<ActivityDTO> filterServiceActivitiesDTO(Collection<ActivityDTO> tActivityList, List<Long> serviceIds) {
        List<ActivityDTO> activities = new ArrayList<ActivityDTO>(0);
        for (Long serviceId : serviceIds) {
            List<Activity> activityList = activityService.loadActivitiesByService(serviceId);
            for (ActivityDTO activityDTO : tActivityList) {
                if (contains(activityList, activityDTO)) {
                    ActivityDTO dto = new ActivityDTO(activityDTO.getActivityId(), activityDTO.getActivityName());
                    activities.add(dto);
                }
            }
        }
        return activities;
    }

    private boolean contains(List<Activity> activityList, ActivityDTO activityDTO) {
        for (Activity activity : activityList) {
            if (activityDTO.getActivityId().equals(activity.getActivityId())) {
                return true;
            }
        }
        return false;
    }

    private List<ActivityDTO> getIncludeActivityDTOList(Set<Activity> activityList, String activityString) {
        List<Long> activityId = SystemUtil.getIdList(activityString);
        List<ActivityDTO> asList = new ArrayList<ActivityDTO>();
        for (Activity a : activityList) {
            if (activityId.contains(a.getActivityId())) {
                ActivityDTO dto = new ActivityDTO(a.getActivityId(), a.getActivityName());
                asList.add(dto);
            }
        }
        return asList;
    }

    @GetMapping(value = "/room/preDelService", produces = "application/json")
    public ResponseEntity<Map<String, Object>> preDelService(@RequestParam long roomTemplateId, @RequestParam String activityString, @RequestParam String serviceString) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("RoomController.preDelService: start");
        }
        Set<Activity> tActivityList = roomTemplateService.getRoomTemplate(roomTemplateId).getActivities();
        List<Long> serviceIds = SystemUtil.getIdList(serviceString);
        Set<Activity> fActivityList = filterServiceActivities(tActivityList, serviceIds);
        List<ActivityDTO> aList = getIncludeActivityDTOList(fActivityList, activityString);
        Map<String, Object> map = new HashMap<>();
        map.put("activityList", aList);
        if (LOG.isDebugEnabled()) {
            LOG.debug("RoomController.preDelService: end");
        }
        return ResponseEntity.ok(map);
    }


    private List<ActivityDTO> getActivityDTOList(Collection<Activity> pActivityList, Set<Activity> activityList) {
        List<ActivityDTO> asList = new ArrayList<ActivityDTO>();
        for (Activity a : activityList) {
            if (Enabled.Y.equals(a.getEnabled()) && pActivityList.contains(a)) {
                ActivityDTO dto = new ActivityDTO(a.getActivityId(), a.getActivityName());
                dto.setServiceId(a.getService().getServiceId());
                asList.add(dto);
            }
        }
        return asList;
    }

    private List<Activity> getActivityList(List<Activity> pActivityList, Set<Activity> activityList) {
        List<Activity> aList = new ArrayList<Activity>();
        for (Activity a : pActivityList) {
            if (Enabled.Y.equals(a.getEnabled()) && !aList.contains(a)) {
                aList.add(a);
            }
        }
        for (Activity a : activityList) {
            if (Enabled.Y.equals(a.getEnabled()) && !aList.contains(a)) {
                aList.add(a);
            }
        }
        return aList;
    }

    private List<ActivityDTO> getActivityDTOList(Collection<Activity> activityList, List<Long> activityId) {
        List<ActivityDTO> asList = new ArrayList<ActivityDTO>();
        for (Activity a : activityList) {
            if (Enabled.Y.equals(a.getEnabled()) && !activityId.contains(a.getActivityId())) {
                ActivityDTO dto = new ActivityDTO(a.getActivityId(), a.getActivityName());
                dto.setServiceId(a.getService().getServiceId());
                asList.add(dto);
            }
        }
        return asList;
    }


    @GetMapping(value = "/room/selectRoomTemplate", produces = "application/json")
    public ResponseEntity<Map<String, Object>> selectRoomTemplate(@RequestParam long roomTemplateId, @RequestParam long profileId) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("RoomController.selectRoomTemplate: start");
        }
      //  long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute("locationProfileId", RequestAttributes.SCOPE_SESSION);
        RoomTemplate roomTemplate = roomTemplateService.getRoomTemplate(roomTemplateId);
        Set<Service> tServiceList = roomTemplate.getServices();
        List<Service> pServiceList = locationProfileService.getProfileServiceList(profileId);
        //fix the bug of GCSS-477, filter the enable status of service
        List<Long> serviceIds = getEnableServiceIdListByService(tServiceList);
        List<Service> fsList = this.getServiceList(pServiceList, tServiceList);
        List<ServiceDTO> sList = this.getServiceDTOList(fsList, serviceIds);
        List<ServiceDTO> selectedServiceList = this.getServiceDTOList(fsList, tServiceList);
        Set<Activity> tActivityList = roomTemplate.getActivities();
        List<Activity> pActivityList = locationProfileService.getProfileActivityList(profileId);
        List<Long> activityId = SystemUtil.getIdListByActivity(roomTemplate.getActivities());
        List<Activity> faList = this.getActivityList(pActivityList, tActivityList);
        Set<Activity> fActivityList = filterServiceActivities(faList, serviceIds);
        List<ActivityDTO> aList = this.getActivityDTOList(fActivityList, activityId);
        List<ActivityDTO> selectedActivityList = this.getActivityDTOList(fActivityList, tActivityList);

        long siteId = roomTemplate.getSite().getSiteId();
        List<RoomSize> rsList = roomSizeService.getRoomSizeListByRoomTypeSiteId(siteId, roomTemplate.getRoomType().getRoomTypeId());
        List<RoomNumber> rnList = roomNumberService.getRoomNumberBySiteId(siteId);
        SplitRoom canSplit = roomTemplate.getRoomType().getCanSplitRoom();

        Map<String, Object> map = new HashMap<>();
        map.put("roomTemplate", roomTemplate);
        map.put("roomSizeList", rsList);
        map.put("serviceList", sList);
        map.put("activityList", aList);
        map.put("roomNumberList", rnList);
        map.put("canSplit", SplitRoom.Y.equals(canSplit));
        map.put("isShowRoomSize", rsList.size() != 0);
        map.put("selectedServiceList", selectedServiceList);
        map.put("selectedActivityList", selectedActivityList);
        if (LOG.isDebugEnabled()) {
            LOG.debug("RoomController.selectRoomTemplate: end");
        }
        return ResponseEntity.ok(map);
    }

    private List<Long> getEnableServiceIdListByService(Collection<Service> sSet) {
        List<Long> ids = new ArrayList<Long>();
        for (Service s : sSet) {
            if (Enabled.Y.equals(s.getEnabled())) {
                ids.add(s.getServiceId());
            }
        }
        return ids;
    }

    @PostMapping(value = "/room/createRoom", produces = "application/json")
    public ResponseEntity<Map<String, Object>> createRoom(@RequestBody RoomDTO dto, @RequestParam long profileId, @RequestParam long siteId) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("RoomController.createRoom: start");
        }
        Map<String, Object> map = new HashMap<>();

      //  long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute("locationProfileId", RequestAttributes.SCOPE_SESSION);
     //   long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute("siteId", RequestAttributes.SCOPE_SESSION);
      //  Person person = (Person) RequestContextHolder.currentRequestAttributes().getAttribute("person", RequestAttributes.SCOPE_SESSION);
        Person person = new Person();
        person.setPersonId(1L);
        try {
            dto.setProfileRoomName(SystemUtil.createName(dto.getProfileRoomName()));
            RoomTemplate roomTemplate = roomTemplateService.getRoomTemplate(Long.parseLong(dto.getRoomTemplate()));
            if (SplitRoom.N.equals(roomTemplate.getRoomType().getCanSplitRoom())) {
                dto.setIsSplitRoom(null);
            }
            map = this.checkRoom(dto, roomTemplate);
            if ((Boolean) map.get(STATUS_KEY)) {
                Room room = this.getRoomByRoomDTO(new Room(), dto,profileId,siteId);
                long newRoomId = roomService.createRoom(room);
                //do split room
                if (SystemUtil.DIAPLAY_SELECT_ON.equals(dto.getIsSplitRoom())) {
                    this.createSplitRoom(newRoomId, dto, profileId, siteId);
                }
                map.put(STATUS_KEY, true);
                map.put(MESSAGE_KEY, "Create Room Success!");
            }
        } catch (Exception e) {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, "Create Room Failed");
            LOG.error("Caught an exception from RoomController.createRoom: {}", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(map);
        }
        //Any service and activity that are specified in the form, but not associated with the profile, will automatically be added to the studio profile
        if ((Boolean) map.get(STATUS_KEY)) {
            try {
                List<Service> pServiceList = locationProfileService.getProfileServiceList(profileId);
                List<Activity> pActivityList = locationProfileService.getProfileActivityList(profileId);
                RoomTemplate roomTemplate = roomTemplateService.getRoomTemplate(Long.parseLong(dto.getRoomTemplate()));
                List<Long> serviceIds = getServiceIdList(pServiceList, roomTemplate.getServices());
                Set<Activity> activityList = roomTemplate.getActivities();
                List<Long> activityIds = getActivityIdList(pActivityList, activityList);
                locationProfileService.batchSaveProServiceAndProActivity(profileId, serviceIds, activityIds, person, siteId);

            } catch (Exception e) {
                map.put(STATUS_KEY, false);
                map.put(MESSAGE_KEY, "Add Service And Activity To Studio Profile Failed");
                LOG.error("Caught an exception from RoomController.createRoom: {}", e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(map);
            }
        }
        if (LOG.isDebugEnabled()) {
            LOG.debug("RoomController.createRoom: end");
        }
        return ResponseEntity.ok(map);
    }

    private List<Long> getServiceIdList(Collection<Service> pServiceList, Collection<Service> tService) {
        List<Long> sList = new ArrayList<Long>();
        List<Long> serviceIds = SystemUtil.getIdListByService(tService);
        List<Long> pServiceIds = SystemUtil.getIdListByService(pServiceList);
        for (Long s : serviceIds) {
            if (!pServiceIds.contains(s)) {
                sList.add(s);
            }
        }
        return sList;
    }

    private List<Long> getActivityIdList(Collection<Activity> pActivityList, Collection<Activity> activityList) {
        List<Long> aList = new ArrayList<Long>();
        List<Long> activityId = SystemUtil.getIdListByActivity(activityList);
        List<Long> pActivityId = SystemUtil.getIdListByActivity(pActivityList);
        for (Long a : activityId) {
            if (!pActivityId.contains(a)) {
                aList.add(a);
            }
        }
        return aList;
    }

    @SuppressWarnings("unused")
    private List<Activity> getActivitiesByServiceString(String serviceString) {
        List<Activity> activities = new ArrayList<Activity>();
        List<Long> serviceIds = SystemUtil.getIdList(serviceString);
        for (Long serviceId : serviceIds) {
            List<Activity> activityList = activityService.loadActivitiesByService(serviceId);
            activities.addAll(activityList);
        }
        return activities;
    }

    private void createSplitRoom(long newRoomId, RoomDTO dto, long profileId, long siteId) {
        String name = dto.getProfileRoomName().trim();
        for (int i = 1; i < 3; i++) {
            dto.setParentRoom(newRoomId);
            dto.setIsSplitRoom(SplitRoom.N.toString());
            dto.setProfileRoomName(name + (i == 1 ? "a" : "b"));
            dto.setServices("");
            dto.setActivities("");
            Room splitRoom = this.getRoomByRoomDTO(new Room(), dto,profileId,siteId);
            roomService.createRoom(splitRoom);
        }
    }

    private Room getRoomByRoomDTO(Room room, RoomDTO dto, long profileId, long siteId) {
       // Person person = (Person) RequestContextHolder.currentRequestAttributes().getAttribute("person", RequestAttributes.SCOPE_SESSION);
        //long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute("locationProfileId", RequestAttributes.SCOPE_SESSION);
      //  long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute("siteId", RequestAttributes.SCOPE_SESSION);
        //Room room = new Room();
        Person person = new Person();
        person.setPersonId(1L);
        RoomTemplate roomTemplate = roomTemplateService.getRoomTemplate(Long.parseLong(dto.getRoomTemplate()));
        room.setRoomType(roomTemplate.getRoomType());
        room.setRoomTemplate(roomTemplate);
        LocationProfile locationProfile = new LocationProfile();
        locationProfile.setProfileId(profileId);
        room.setLocationProfile(locationProfile);
        room.setProfileRoomName(dto.getProfileRoomName().trim());
        RoomNumber roomNumber = new RoomNumber();
        roomNumber.setRoomNumberId(Long.parseLong(dto.getRoomNumber()));
        room.setRoomNumber(roomNumber);
        if (null != dto.getRoomSize() && !"".equals(dto.getRoomSize())) {
            RoomSize roomSize = new RoomSize();
            roomSize.setRoomSizeId(Long.parseLong(dto.getRoomSize()));
            room.setRoomSize(roomSize);
        }
        room.setIsSplitRoom(SystemUtil.DIAPLAY_SELECT_ON.equals(dto.getIsSplitRoom()) ? SplitRoom.Y : SplitRoom.N);
        room.setEnabled(SystemUtil.DIAPLAY_SELECT_ON.equals(dto.getEnabled()) ? Enabled.Y : Enabled.N);
        List<Long> serviceIds = SystemUtil.getIdList(dto.getServices());
        List<Long> activityIds = SystemUtil.getIdList(dto.getActivities());
        Set<Service> services = new HashSet<Service>(0);
        Set<Activity> activities = new HashSet<Activity>(0);
        for (Long serviceId : serviceIds) {
            Service service = new Service();
            service.setServiceId(serviceId);
            services.add(service);
        }
        for (Long activityId : activityIds) {
            Activity activity = new Activity();
            activity.setActivityId(activityId);
            activities.add(activity);
        }
        room.setServices(services);
        room.setActivities(activities);
        Site site = new Site();
        site.setSiteId(siteId);
        room.setSite(site);
        room.setUpdatedBy(person);
        room.setUpdated(new Date());

        //split room
        if (null != dto.getParentRoom()) {
            Room parentRoom = new Room();
            parentRoom.setRoomId(dto.getParentRoom());
            room.setParentRoom(parentRoom);
            room.setRoomSize(null);
            RoomType roomType = new RoomType();
            roomType.setRoomTypeId(DEFAULT_SPLIT_ROOM_TYPE);
            room.setRoomType(roomType);
        }
        return room;
    }

    private Map<String, Object> checkRoom(RoomDTO dto, RoomTemplate roomTemplate) throws RuntimeException {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put(STATUS_KEY, true);
        long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute("locationProfileId", RequestAttributes.SCOPE_SESSION);
        if (null == dto.getRoomTemplate() || "".equals(dto.getRoomTemplate().trim())) {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, VALIDATION_ROOM_ROOMTEMPLATE_NULL);
            return map;
        }
        if (null == dto.getRoomNumber() || "".equals(dto.getRoomNumber().trim())) {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, VALIDATION_ROOM_NUMBER_NULL);
            return map;
        }
        //Fix the bug of GCSS-492, validate the room name
        if (null == dto.getProfileRoomName() || "".equals(dto.getProfileRoomName().trim())) {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, VALIDATION_ROOM_NAME_NULL);
            return map;
        }
        boolean checkRoomName = validationService.checkRoomName(profileId, dto.getProfileRoomName().trim());
        if (!checkRoomName) {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, VALIDATION_ROOM_NAME_EXISTS);
            return map;
        }
        List<RoomSize> rsList = roomSizeService.getRoomSizeListByRoomTypeSiteId(roomTemplate.getSite().getSiteId(), roomTemplate.getRoomType().getRoomTypeId());
        if (rsList.size() != 0 && (null == dto.getRoomSize() || "".equals(dto.getRoomSize().trim()))) {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, VALIDATION_ROOM_SIZE_NULL);
            return map;
        }
        //fix the bug GCSS-396
        SplitRoom canSplit = roomTemplate.getRoomType().getCanSplitRoom();
        if (SystemUtil.DIAPLAY_SELECT_ON.equals(dto.getIsSplitRoom()) && SplitRoom.N.equals(canSplit)) {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, VALIDATION_ROOM_ROOMTEMPLATE_CAN_SPLIT);
            return map;
        }
        if (SystemUtil.DIAPLAY_SELECT_ON.equals(dto.getEnabled()) && Enabled.N.equals(roomTemplate.getEnabled())) {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, VALIDATION_ROOM_ENABLED);
            return map;
        }
        return map;
    }

    @PutMapping(value = "/editRoom", produces = "application/json")
    public ResponseEntity<Map<String, Object>> editRoom(@RequestBody RoomDTO dto, @RequestParam long profileId, @RequestParam long siteId) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("RoomController.editRoom: start");
        }
        Map<String, Object> map = new HashMap<>();

     //   long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute("locationProfileId", RequestAttributes.SCOPE_SESSION);
      //  long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute("siteId", RequestAttributes.SCOPE_SESSION);
     //   Person person = (Person) RequestContextHolder.currentRequestAttributes().getAttribute("person", RequestAttributes.SCOPE_SESSION);
        Person person = new Person();
        person.setPersonId(1L);
        try {
            long roomId = dto.getRoomId();
            Room room = roomService.getRoom(roomId);
            if (room == null) {
                room = new Room();
            }
            if (null != room.getParentRoom()) {
                dto.setParentRoom(room.getParentRoom().getRoomId());
            }
            RoomTemplate roomTemplate = roomTemplateService.getRoomTemplate(Long.parseLong(dto.getRoomTemplate()));
            if (SplitRoom.N.equals(roomTemplate.getRoomType().getCanSplitRoom())) {
                dto.setIsSplitRoom(null);
            }
            SplitRoom splitRoom = room.getIsSplitRoom();
            dto.setProfileRoomName(SystemUtil.createName(dto.getProfileRoomName()));
            map = this.checkEditRoom(dto, room,profileId);
            if ((Boolean) map.get(STATUS_KEY)) {
                Room newRoom = this.getRoomByRoomDTO(room, dto,profileId,siteId);
                newRoom.setRoomId(roomId);
                //do split room
                if (SplitRoom.N.equals(splitRoom) && SystemUtil.DIAPLAY_SELECT_ON.equals(dto.getIsSplitRoom())) {
                    this.createSplitRoom(roomId, dto,profileId,siteId);
                } else if (SplitRoom.Y.equals(splitRoom) && !SystemUtil.DIAPLAY_SELECT_ON.equals(dto.getIsSplitRoom())) {
                    //do cancel split room
                    roomService.deleteSplitRooms(roomId);
                }
                RoomDTO newRoomDTO = roomService.updateRoom(newRoom);
                map.put(RETURN_DTO, newRoomDTO);
                map.put(STATUS_KEY, true);
                map.put(MESSAGE_KEY, "Edit Room Success!");
            }
        } catch (Exception e) {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, "Edit Room Failed");
            LOG.error("Caught an exception from RoomController.editRoom: {}", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(map);
        }
        //Any service and activity that are specified in the form, but not associated with the profile, will automatically be added to the studio profile
        if ((Boolean) map.get(STATUS_KEY)) {
            try {
                List<Service> pServiceList = locationProfileService.getProfileServiceList(profileId);
                List<Activity> pActivityList = locationProfileService.getProfileActivityList(profileId);
                RoomTemplate roomTemplate = roomTemplateService.getRoomTemplate(Long.parseLong(dto.getRoomTemplate()));
                List<Long> serviceIds = getServiceIdList(pServiceList, roomTemplate.getServices());
                Set<Activity> activityList = roomTemplate.getActivities();
                List<Long> activityIds = getActivityIdList(pActivityList, activityList);
                locationProfileService.batchSaveProServiceAndProActivity(profileId, serviceIds, activityIds, person, siteId);
            } catch (Exception e) {
                map.put(STATUS_KEY, false);
                map.put(MESSAGE_KEY, "Add Service And Activity To Studio Profile Failed");
                LOG.error("Caught an exception from RoomController.editRoom: {}", e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(map);
            }
        }
        if (LOG.isDebugEnabled()) {
            LOG.debug("RoomController.editRoom: end");
        }
        return ResponseEntity.ok(map);
    }

    private Map<String, Object> checkEditRoom(RoomDTO dto, Room room, long profileId) throws RuntimeException {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put(STATUS_KEY, true);
     //   long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute("locationProfileId", RequestAttributes.SCOPE_SESSION);
        //Fix the bug of GCSS-492, validate the room name
        if (null == dto.getProfileRoomName() || "".equals(dto.getProfileRoomName().trim())) {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, VALIDATION_ROOM_NAME_NULL);
            return map;
        }
        RoomTemplate roomTemplate = roomTemplateService.getRoomTemplate(Long.parseLong(dto.getRoomTemplate()));
        if (!dto.getProfileRoomName().trim().equals(room.getProfileRoomName())) {
            boolean checkRoomName = validationService.checkRoomName(profileId, dto.getProfileRoomName().trim());
            if (!checkRoomName) {
                map.put(STATUS_KEY, false);
                map.put(MESSAGE_KEY, VALIDATION_ROOM_NAME_EXISTS);
                return map;
            }
        }
        List<RoomSize> rsList = roomSizeService.getRoomSizeListByRoomTypeSiteId(room.getSite().getSiteId(), roomTemplate.getRoomType().getRoomTypeId());
        if (room.getParentRoom() == null && rsList.size() != 0 && (null == dto.getRoomSize() || "".equals(dto.getRoomSize().trim()))) {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, VALIDATION_ROOM_SIZE_NULL);
            return map;
        }
        if (SystemUtil.DIAPLAY_SELECT_ON.equals(dto.getIsSplitRoom()) && SplitRoom.N.equals(room.getIsSplitRoom())) {
            boolean checkSplitByRoomId = validationService.checkSplitByRoomId(dto.getRoomId());
            if (!checkSplitByRoomId) {
                map.put(STATUS_KEY, false);
                map.put(MESSAGE_KEY, VALIDATION_ROOM_CAN_SPLIT);
                return map;
            }
        }
        if (!SystemUtil.DIAPLAY_SELECT_ON.equals(dto.getEnabled()) && Enabled.Y.equals(room.getEnabled())) {
            //check appointment while disable a room
            boolean checkRoomScheduleByRoomId = validationService.checkRoomScheduleByRoomId(dto.getRoomId(), profileId);
            if (!checkRoomScheduleByRoomId) {
                map.put(STATUS_KEY, false);
                map.put(MESSAGE_KEY, VALIDATION_ROOM_APPOINTMENT_ENABLED);
                return map;
            }
        }
        if (SplitRoom.Y.equals(room.getIsSplitRoom()) && !SystemUtil.DIAPLAY_SELECT_ON.equals(dto.getIsSplitRoom())) {
            boolean checkRoomScheduleByRoomId = validationService.checkSplitRoomScheduleByParentRoomId(dto.getRoomId(), profileId);
            if (!checkRoomScheduleByRoomId) {
                map.put(STATUS_KEY, false);
                map.put(MESSAGE_KEY, VALIDATION_ROOM_APPOINTMENT_DELETE);
                return map;
            }
        }
        List<Long> deleteActivities = getDeleteActivities(dto, room);
        if (deleteActivities.size() > 0) {
            boolean checkRoomActivities = validationService.checkRoomActivities(dto.getRoomId(), deleteActivities, profileId);
            if (!checkRoomActivities) {
                map.put(STATUS_KEY, false);
                map.put(MESSAGE_KEY, VALIDATION_ROOM_ACTIVITY_APPOINTMENT);
                return map;
            }
        }
        return map;
    }

    private List<Long> getDeleteActivities(RoomDTO dto, Room room) {
        long roomId = room.getRoomId();
        RoomDTO roomDTO = roomService.getRoomDTOByRoomId(roomId);
        List<ActivityDTO> activities = roomDTO.getActivityList();
        List<Long> activityIds = SystemUtil.getIdList(dto.getActivities());
        List<Long> list = new ArrayList<Long>();
        for (ActivityDTO activityDTO : activities) {
            if (!activityIds.contains(activityDTO.getActivityId())) {
                list.add(activityDTO.getActivityId());
            }
        }
        return list;
    }

    @DeleteMapping(value = "/deleteRoom", produces = "application/json")
    public ResponseEntity<Map<String, Object>> deleteRoom(@RequestParam long roomId) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("RoomController.deleteRoom: start");
        }
        boolean status = true;
        Map<String, Object> map = this.checkDeleteRoom(roomId);
        if ((Boolean) map.get(STATUS_KEY)) {
            try {
                Room room = roomService.getRoom(roomId);
                if (SplitRoom.Y.equals(room.getIsSplitRoom())) {
                    roomService.deleteSplitRooms(roomId);
                }
                roomService.deleteRoom(room);
                map.put(MESSAGE_KEY, "Delete Room Success!");
            } catch (Exception e) {
                LOG.error("Caught an exception from RoomController.deleteRoom: {}", e);
                status = false;
                map.put(MESSAGE_KEY, "Delete Room Failed");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(map);
            }
            map.put(STATUS_KEY, status);
        }
        if (LOG.isDebugEnabled()) {
            LOG.debug("RoomController.deleteRoom: end");
        }
        return ResponseEntity.ok(map);
    }

    private Map<String, Object> checkDeleteRoom(long roomId) throws RuntimeException {
        Map<String, Object> map = new HashMap<String, Object>();
        boolean checkRoomScheduleByRoomId = validationService.checkRoomScheduleByRoomId(roomId);
        if (!checkRoomScheduleByRoomId) {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, VALIDATION_ROOM_APPOINTMENT_DELETE);
            return map;
        }
        map.put(STATUS_KEY, true);
        return map;
    }
}
