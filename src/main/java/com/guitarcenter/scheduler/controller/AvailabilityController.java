package com.guitarcenter.scheduler.controller;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import jakarta.annotation.Resource;

import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.AvailabilityUtil;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.AvailabilityDAO;
import com.guitarcenter.scheduler.dao.InstructorDAO;
import com.guitarcenter.scheduler.dao.criterion.AvailabilityCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.InstructorCriterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.dto.AvailabilityDTO;
import com.guitarcenter.scheduler.dto.EditHourShowDTO;
import com.guitarcenter.scheduler.dto.EditHourShowListDTO;
import com.guitarcenter.scheduler.dto.StudioHourDTO;
import com.guitarcenter.scheduler.dto.UpdateMessageDTO;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Onetime;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.service.AvailabilityService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.service.OnetimeService;
import com.guitarcenter.scheduler.service.SiteService;
import com.guitarcenter.scheduler.service.impl.ServiceAndActivityValidate;



@RestController
@RequestMapping("/")
public class AvailabilityController implements AppConstants {

	private static final Logger LOGGER = LoggerFactory.getLogger(AvailabilityController.class);
	@Resource
	private AvailabilityService availabilityService;
	@Resource
	private SiteService siteService;
	/*GSSP-144 */
	@Resource
	InstructorService instructorService;
	
	@Autowired
    @Qualifier("serviceAndActivityValidate")
    private ServiceAndActivityValidate serviceAndActivityValidate;
	//GSSP-211 CHANGES
	@Autowired
	private OnetimeService onetimeService;
	
	
	@Autowired
	@Qualifier("availabilityDAO")
	private AvailabilityDAO availabilityDAO;
	
		
	@Autowired
	private InstructorDAO instructorDAO;
	
	//@SuppressWarnings("unchecked")
	@PutMapping( value = "/studio-hours", produces = "application/json")
	public AvailabilityDTO updateStudioHour( @RequestBody EditHourShowListDTO setOfEditHourDto,
											 @RequestAttribute("person") Person person,
											 @RequestAttribute("siteId") Long siteId,
											 @RequestAttribute("locationProfileId") Long profileId){
		
			Availability availability = availabilityService.getAvailability(Long.valueOf(setOfEditHourDto.getIdString().trim()));
			
		    List<EditHourShowDTO> editHourShowDTOList = setOfEditHourDto.getList();
		    for(int i = 0; i <= 6; i++){
		        EditHourShowDTO weekDayShowDto = editHourShowDTOList.get(i);
		       try {
		           availability =  weekDayShowDto.buildDayTime(availability, i);
		         
                } catch (Exception e) {
                    AvailabilityDTO availabilityDto = new AvailabilityDTO(null,e.getMessage(),false,null);
                    return availabilityDto;
                }
		    }
		    //GSSP-211 CHANGES
		    UpdateMessageDTO updateMessageDTO = null;
		    
		    //Changes made for GSSP-186 
		    
		    Availability studioAvailability = availabilityService.findByProfileId(profileId);	
		    
		    Long studioAvailabilityID  = studioAvailability.getAvailabilityId();
		    //GSSP-211 CHANGES
		    String extId  = "";
		    //GSSP-223 CHANGES
		    long secondaryInstructorId = -1;
		    
		    List<Availability> secondaryAvailability = null;
		    
		    Criterion<Availability, Availability> avalabilityCriterion = null;
		    
		    if(! setOfEditHourDto.getIdString().equalsIgnoreCase(studioAvailabilityID.toString()))
		    { 	
		    
			   UpdateMessageDTO updateStudioMessageDTO = compareInstructorHourWithStudioAvailability(availability,studioAvailability);
			    if(!updateStudioMessageDTO.getStatus()){
			        return new AvailabilityDTO(null,updateStudioMessageDTO.getMessage(),false);
			    }
			    //End of Changes made for GSSP-186
			    
			    //GSSP-211 CHANGES 
			    
			    if(null != setOfEditHourDto.getInstructorId())
			    { 	
			    	Criterion<Instructor, Instructor> instructorCriterion =   InstructorCriterion.findByInstructorIds(setOfEditHourDto.getInstructorId());
			    	List<Instructor> instructorss = instructorDAO.search(instructorCriterion, DAOHelper.FETCH_PERSON);
			    	
			    	if(null != instructorss && instructorss.size() > 0)
			    	{	
			    	
				    	 extId  =  instructorss.get(0).getExternalId();				    	
				    					    	
					    Criterion<Instructor, Instructor> criterion =
				                InstructorCriterion.findByExternalId(siteId,extId);				    
					    					    
				        List<Instructor> instructors = instructorDAO.search(criterion, DAOHelper.FETCH_PERSON);
				       
					     for(Instructor instructor: instructors)
					     {
					    	 //GSSP-223 CHANGES
					    	   // 292 –GSSP Multi location instructor comparison
					        	if(instructor.getStatus().equalsIgnoreCase("A") && instructor.getEnabled().equals(Enabled.Y)
					        			&& instructor.getAvailability().getAvailabilityId().longValue() != availability.getAvailabilityId().longValue() && instructor.getExternalId() != null)
					        	{
					        		
					        			secondaryInstructorId = Long.parseLong(instructor.getExternalId());
					        					        		
					        		 avalabilityCriterion =
					        				AvailabilityCriterion.getByInstructorId(availability.getAvailabilityId(),secondaryInstructorId);
					        		
					        		secondaryAvailability = availabilityDAO.search(avalabilityCriterion, DAOHelper.FETCH_AVAILABILITY);
					        		break;
								 
					        	}
					        	else{

						        		secondaryInstructorId = Long.parseLong(instructor.getExternalId());
						        					        		
						        		 avalabilityCriterion =
						        				 AvailabilityCriterion.getByInstructorId(availability.getAvailabilityId(),secondaryInstructorId);
						        		
						        		secondaryAvailability = availabilityDAO.search(avalabilityCriterion, DAOHelper.FETCH_AVAILABILITY);
						        		break;
					        		
					        	}
					        		
					        }
					     // 292 –GSSP Multi location instructor comparison
			      
			    	   if(null !=  availability && 0 != secondaryAvailability.size()){
			    		   
			    			   updateMessageDTO = compareInstructorHourWithSecondaryAvailability(availability,secondaryAvailability);
						         
						        if(!updateMessageDTO.getStatus()){
							        return new AvailabilityDTO(null,updateMessageDTO.getMessage(),false);
							   
			    			   
			    	   }
			       
			       }       //Compare the  Instructor Availability with One Time of  other studio - New Jira
			        
					        List<Onetime> oneTimeList = onetimeService.getDisplayOnetimeByInstructorId(secondaryInstructorId);
			        
			                updateMessageDTO = compareWithOneTimeAvailability(availability,oneTimeList);
						        
						        if(!updateMessageDTO.getStatus()){
							        return new AvailabilityDTO(null,updateMessageDTO.getMessage(),false);
							    }
						        
			    	}    
			    	
			    }	
		        
		        //End of comparing Instructor Availability with One Time of  other studio - New Jira
			 
		    }   
		   
		    updateMessageDTO = checkAvaililability(profileId,setOfEditHourDto.getInstructorId(),availability,setOfEditHourDto.getIsAvailability());
		    if(!updateMessageDTO.getStatus()){
		        return new AvailabilityDTO(null,updateMessageDTO.getMessage(),false);
		    }
		    //optimistic lock
			//availability.setVersion(Long.valueOf(setOfEditHourDto.getVersionString()));
			Site site = siteService.getSiteById(siteId);
			availability.setSite(site);
			//GSSP-211 CHANGES
			Set<StudioHourDTO> set = null;
			List<EditHourShowDTO> editHourShowDTOs = new ArrayList<EditHourShowDTO>();
			String availabilityString = "";
			try{
				try {
					availabilityService.update(availability, person.getPersonId());
				} catch (Exception e) {
					LOGGER.error("Caught an exception from Availability service {}", e);
					 
				}
				if(setOfEditHourDto.getIsAvailability()){
					editHourShowDTOs = AvailabilityUtil.getAvailabilityEditHourShowList(null,availability, siteId,true).getList();
					availabilityString = AvailabilityUtil.getAvailabilityEditHourShowList(null,availability, siteId,true).getAvailabilityString();
				}else{
					set = availabilityService.mergeMap(availability);
				}
			
		}catch (Exception e) {
			LOGGER.error(e.getMessage());
			return new AvailabilityDTO(set,e.getMessage(),false);
		}
		
		AvailabilityDTO availabilityDto = new AvailabilityDTO(set,UPDATE_SUCCESS,true,editHourShowDTOs);
		availabilityDto.setAvailabilityString(availabilityString);
		availabilityDto.setAvailabilityversion(availability.getVersion());
		availabilityDto.setIdString(Long.valueOf(setOfEditHourDto.getIdString().trim()));
		return availabilityDto;
	}
	
	private UpdateMessageDTO checkAvaililability(Long profileId,Long instructorId,Availability availability,Boolean isAvailability) {
	    UpdateMessageDTO updateMessageDTO =  new UpdateMessageDTO(null, true);
	    Boolean result;
	    /*GSSP-144 : All-day availability check block removed */
		
	    Availability db_availability;
	    List<Integer> days;
	    
	    if(isAvailability){
	    	if(instructorId != null){
	    		Instructor instructor = instructorService.getInstructor(instructorId);
	    		if(instructor != null){
	    			db_availability = instructor.getAvailability();
	    			days = compareAvailability(availability, db_availability);
	    			
	    			for(int i : days){
	    				result = serviceAndActivityValidate.checkInstructorAppoint(instructorId,availability,i);
	    				if(!result){
	    					updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[i-1]+" has been assigned to appointment,it cannot be updated!", false);
	    					break;
	    				}
	    			}
	    			
	    			/* Check only the days for which Instructor availability is edited */
	    			for(int i : days){
	    				result = serviceAndActivityValidate.checkInstructorAppoint(instructorId,availability,i);
	    				if(!result){
	    					updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[i-1]+" has been assigned to appointment,it cannot be updated!", false);
	    					break;
	    				}
	    			}
	    		}
	    	}
	    }
	    else{
	    	if(profileId != null){
	    		db_availability = availabilityService.findByProfileId(profileId);
	    		days = compareAvailability(availability, db_availability);
	    		/* Check only the days for which Studio availability is edited */
	    		for(int i : days) {
	    			result = serviceAndActivityValidate.checkProfileAppoint(profileId, availability, i);
	    			if(!result){
	    				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[i-1]+" has been assigned to appointment,it cannot be updated!", false);
	    				break;
	    			}
	    		}	
	    	}
	    }
        return updateMessageDTO;
    }

	@GetMapping(value = "/studio-update", produces = "application/json")
	public EditHourShowListDTO loadStudioUpdatePage(
			@RequestParam("locationProfileId") long profileId,
			@RequestParam("siteId") Long siteId) {
		
		Availability availability = availabilityService.findByProfileId(profileId);
		
		EditHourShowListDTO editHourShowList = AvailabilityUtil.getAvailabilityEditHourShowList(null,availability, siteId,false);
		return editHourShowList;
	}

    /**
	 * Compare Availability from DB with the edited availability to get the days which user is edited
	 * For GSSP-144
	 * 
	 * @param availability 
	 * @param db_availability
	 */
    private List<Integer> compareAvailability(Availability availability, Availability db_availability){	

    	List<Integer> daysList = new ArrayList<Integer>();
		
		if(availability.getMondayStartTime() != null && availability.getMondayEndTime() != null && 
				db_availability.getMondayStartTime() != null && db_availability.getMondayEndTime() != null){
			
			if(!(availability.getMondayStartTime().compareTo(db_availability.getMondayStartTime()) == 0) || 
					!(availability.getMondayEndTime().compareTo(db_availability.getMondayEndTime()) == 0)){
				daysList.add(1);
			}
		}
		else
		{
			if(!(null == availability.getMondayStartTime() && null == db_availability.getMondayStartTime()))
				{
					daysList.add(1);
				}
		}
		if(availability.getTuesdayStartTime() != null && availability.getTuesdayEndTime() != null &&
				db_availability.getTuesdayStartTime() != null && db_availability.getTuesdayEndTime() != null){
			
			if(!(availability.getTuesdayStartTime().compareTo(db_availability.getTuesdayStartTime()) == 0) || 
					!(availability.getTuesdayEndTime().compareTo(db_availability.getTuesdayEndTime()) == 0)){
				daysList.add(2);
			}
		}
		else
		{
			if(!(null == availability.getTuesdayStartTime() && null == db_availability.getTuesdayStartTime()))
			{
				daysList.add(2);
			}
		}
		if(availability.getWednesdayStartTime() != null && availability.getWednesdayEndTime() != null &&
				db_availability.getWednesdayStartTime() != null && db_availability.getWednesdayEndTime() != null){
			
			if(!(availability.getWednesdayStartTime().compareTo(db_availability.getWednesdayStartTime()) == 0) || 
					!(availability.getWednesdayEndTime().compareTo(db_availability.getWednesdayEndTime()) == 0)){
				daysList.add(3);
			}
    	}
		else
		{
			if(!(null == availability.getWednesdayStartTime() && null == db_availability.getWednesdayStartTime()))
			{
				daysList.add(3);
			}
		}
		if(availability.getThursdayStartTime() != null && availability.getThursdayEndTime() != null &&
				db_availability.getThursdayStartTime() != null && db_availability.getThursdayEndTime() != null){
			
			if(!(availability.getThursdayStartTime().compareTo(db_availability.getThursdayStartTime()) == 0) || 
					!(availability.getThursdayEndTime().compareTo(db_availability.getThursdayEndTime()) == 0)){
				daysList.add(4);
			}
		}
		else
		{
			if(!(null == availability.getThursdayStartTime() && null == db_availability.getThursdayStartTime()))
			{
				daysList.add(4);
			}
		}
		if(availability.getFridayStartTime() != null && availability.getFridayEndTime() != null &&
				db_availability.getFridayStartTime() != null && db_availability.getFridayEndTime() != null){
			
			if(!(availability.getFridayStartTime().compareTo(db_availability.getFridayStartTime()) == 0) || 
					!(availability.getFridayEndTime().compareTo(db_availability.getFridayEndTime()) == 0)){
				daysList.add(5);
			}
		}
		else
		{
			if(!(null == availability.getFridayStartTime() && null == db_availability.getFridayStartTime()))
			{
				daysList.add(5);
			}
		}
		if(availability.getSaturdayStartTime() != null && availability.getSaturdayEndTime() != null &&
				db_availability.getSaturdayStartTime() != null && db_availability.getSaturdayEndTime() != null){
			
			if(!(availability.getSaturdayStartTime().compareTo(db_availability.getSaturdayStartTime()) == 0) || 
					!(availability.getSaturdayEndTime().compareTo(db_availability.getSaturdayEndTime()) == 0)){
				daysList.add(6);
			}
		}
		else
		{
			if(!(null == availability.getSaturdayStartTime() && null == db_availability.getSaturdayStartTime()))
			{
				daysList.add(6);
			}
		}
		if(availability.getSundayStartTime() != null && availability.getSundayEndTime() != null &&
				db_availability.getSundayStartTime() != null && db_availability.getSundayEndTime() != null){
			
			if(!(availability.getSundayStartTime().compareTo(db_availability.getSundayStartTime()) == 0) || 
					!(availability.getSundayEndTime().compareTo(db_availability.getSundayEndTime()) == 0)){
				daysList.add(7);
			}
		}
		else
		{
			if(!(null == availability.getSundayStartTime() && null == db_availability.getSundayStartTime()))
			{
				daysList.add(7);
			}
		}
		
		if(daysList.size() == 0){
			daysList.add(8);
			return daysList;
		}
		return daysList;
        }
    
    
    
    /**
   	 * Check Instructor Availability with Studio Hour
   	 * For GSSP-186
   	 * 
   	 * @param availability 
   	 * @param studioAvailability
   	 */
       private UpdateMessageDTO compareInstructorHourWithStudioAvailability(Availability availability, Availability studioAvailability){	

       UpdateMessageDTO updateMessageDTO =  new UpdateMessageDTO(null, true);
    	
   		if(availability.getMondayStartTime() != null && availability.getMondayEndTime() != null){
   		
   			
   			if(studioAvailability.getMondayStartTime() != null && studioAvailability.getMondayEndTime() != null)
   			{
   				if((studioAvailability.getMondayStartTime().compareTo(availability.getMondayStartTime()) > 0) || 
   	   					(studioAvailability.getMondayEndTime().compareTo(availability.getMondayEndTime()) < 0)){
   	   				
   	   				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[0]+" instructor availability cannot be outside studio hours, it cannot be updated"  , false);
   	   				
   	   				return updateMessageDTO;
   	   			}
   			}
   			else
   			{
   				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[0]+" instructor availability cannot be outside studio hours, it cannot be updated"  , false);
	   				
	   				return updateMessageDTO;
   			}
   			
   			
   		}
   		
   		if(availability.getTuesdayStartTime() != null && availability.getTuesdayEndTime() != null){
   			
   			
   			if(studioAvailability.getTuesdayStartTime() != null && studioAvailability.getTuesdayEndTime() != null)
   			{	
	   			if((studioAvailability.getTuesdayStartTime().compareTo(availability.getTuesdayStartTime()) > 0) || 
	   					(studioAvailability.getTuesdayEndTime().compareTo(availability.getTuesdayEndTime()) < 0)){
	   				
	   				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[1]+" instructor availability cannot be outside studio hours, it cannot be updated"  , false);
	   				
	   				return updateMessageDTO;
	   			}
   			}
   			
   			else
   			{
   				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[1]+" instructor availability cannot be outside studio hours, it cannot be updated"  , false);
	   				
	   				return updateMessageDTO;
   			}
   			
   			
   		}
   		
   		if(availability.getWednesdayStartTime() != null && availability.getWednesdayEndTime() != null) 
   				{
   			if(studioAvailability.getWednesdayStartTime() != null && studioAvailability.getWednesdayEndTime() != null)
   			{
	   				if((studioAvailability.getWednesdayStartTime().compareTo(availability.getWednesdayStartTime()) > 0) || 
	   					(studioAvailability.getWednesdayEndTime().compareTo(availability.getWednesdayEndTime()) < 0)){
	   				
	   				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[2]+" instructor availability cannot be outside studio hours, it cannot be updated"  , false);
	   				
	   				return updateMessageDTO;
	   			}
   				}
   			else
   			{
   				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[2]+" instructor availability cannot be outside studio hours, it cannot be updated"  , false);
	   				
	   				return updateMessageDTO;
   			}
       	}
   	
   		if(availability.getThursdayStartTime() != null && availability.getThursdayEndTime() != null){ 
   			if(studioAvailability.getThursdayStartTime() != null && studioAvailability.getThursdayEndTime() != null){
   			
	   			if((studioAvailability.getThursdayStartTime().compareTo(availability.getThursdayStartTime()) > 0) || 
	   					(studioAvailability.getThursdayEndTime().compareTo(availability.getThursdayEndTime()) < 0)){
	   				
	   				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[3]+" instructor availability cannot be outside studio hours, it cannot be updated"  , false);
	   				
	   				return updateMessageDTO;
	   			}
   			}
   			else
   			{
   				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[3]+" instructor availability cannot be outside studio hours, it cannot be updated"  , false);
	   				
	   				return updateMessageDTO;
   			}
   		}
   		
   		if(availability.getFridayStartTime() != null && availability.getFridayEndTime() != null){
   				if(studioAvailability.getFridayStartTime() != null && studioAvailability.getFridayEndTime() != null){
   			
   			if((studioAvailability.getFridayStartTime().compareTo(availability.getFridayStartTime()) > 0) || 
   					(studioAvailability.getFridayEndTime().compareTo(availability.getFridayEndTime()) < 0)){
   				
   				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[4]+" instructor availability cannot be outside studio hours, it cannot be updated"  , false);
   				
   				return updateMessageDTO;
   			
   			}
   				}
   			else
   	   			{
   	   				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[4]+" instructor availability cannot be outside studio hours, it cannot be updated"  , false);
   		   				
   		   				return updateMessageDTO;
   	   			}
   		}
   		
   		if(availability.getSaturdayStartTime() != null && availability.getSaturdayEndTime() != null){
   				if(studioAvailability.getSaturdayStartTime() != null && studioAvailability.getSaturdayEndTime() != null){
   			
   			if((studioAvailability.getSaturdayStartTime().compareTo(availability.getSaturdayStartTime()) > 0) || 
   					(studioAvailability.getSaturdayEndTime().compareTo(availability.getSaturdayEndTime()) < 0)){
   				
   				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[5]+" instructor availability cannot be outside studio hours, it cannot be updated"  , false);
   				
   				return updateMessageDTO;
   				
   			}
   				}
   				else
   	   			{
   	   				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[5]+" instructor availability cannot be outside studio hours, it cannot be updated"  , false);
   		   				
   		   				return updateMessageDTO;
   	   			}
   		}
   		
   		
   		if(availability.getSundayStartTime() != null && availability.getSundayEndTime() != null){
   				if(studioAvailability.getSundayStartTime() != null && studioAvailability.getSundayEndTime() != null){
   			
   			if((studioAvailability.getSundayStartTime().compareTo(availability.getSundayStartTime()) > 0) || 
   					(studioAvailability.getSundayEndTime().compareTo(availability.getSundayEndTime()) < 0)){
   				
   				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[6]+" instructor availability cannot be outside studio hours, it cannot be updated"  , false);
   				
   				return updateMessageDTO;
   			}
   			}
   		
			else
  			{
  				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[6]+" instructor availability cannot be outside studio hours, it cannot be updated"  , false);
	   				
	   				return updateMessageDTO;
  			}
   		}
   		
   		return updateMessageDTO;
     }
       

       
       
       
       
       
       
       /**
      	 * Check Instructor Availability with his other Studio Availability
      	 * GSSP-211 CHANGES
      	 * 
      	 * @param availability 
      	 * @param studioAvailability
      	 */
       // 292 –GSSP Multi location instructor comparison
          private UpdateMessageDTO compareInstructorHourWithSecondaryAvailability(Availability availability, List<Availability> secondaryAvailability){	

          UpdateMessageDTO updateMessageDTO =  new UpdateMessageDTO(null, true);
       	
          DateFormat timef = new SimpleDateFormat("HH:mm:ss");
          //GCSS-313 Changes:: Added DateTimeUtil.addMinute method in all the cases.
          for(int i=0;i<secondaryAvailability.size();i++){
        	  //if(!availability.getAvailabilityId().equals(secondaryAvailability.get(i).getAvailabilityId())){
      		if(availability.getMondayStartTime() != null && availability.getMondayEndTime() != null && 
      				secondaryAvailability.get(i).getMondayStartTime() != null && secondaryAvailability.get(i).getMondayEndTime() != null){
      			
      			if(DateTimeUtil.isInBetweenTime(timef.format(availability.getMondayStartTime()),timef.format(DateTimeUtil.addMinute(secondaryAvailability.get(i).getMondayStartTime(),1)),
			    		timef.format(availability.getMondayEndTime()),	timef.format(DateTimeUtil.addMinute(secondaryAvailability.get(i).getMondayEndTime(),-1))))
      			
      			    {
      				
      				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[0]+" instructor availability overlaps with his availability in other studio"  , false);
      				
      				return updateMessageDTO;
      			}
      		}
      		
      		if(availability.getTuesdayStartTime() != null && availability.getTuesdayEndTime() != null &&
      				secondaryAvailability.get(i).getTuesdayStartTime() != null && secondaryAvailability.get(i).getTuesdayEndTime() != null){
      			
      			if(DateTimeUtil.isInBetweenTime(timef.format(availability.getTuesdayStartTime()),timef.format(DateTimeUtil.addMinute(secondaryAvailability.get(i).getTuesdayStartTime(),1)),
			    		timef.format(availability.getTuesdayEndTime()),	timef.format(DateTimeUtil.addMinute(secondaryAvailability.get(i).getTuesdayEndTime(),-1))))
          			    {
          				
          				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[1]+" instructor availability overlaps with his availability in other studio"  , false);
          				
          				return updateMessageDTO;
          			}
      		}
      		
      		if(availability.getWednesdayStartTime() != null && availability.getWednesdayEndTime() != null &&
      				secondaryAvailability.get(i).getWednesdayStartTime() != null && secondaryAvailability.get(i).getWednesdayEndTime() != null){
      			
      			if(DateTimeUtil.isInBetweenTime(timef.format(availability.getWednesdayStartTime()),timef.format(DateTimeUtil.addMinute(secondaryAvailability.get(i).getWednesdayStartTime(),1)),
			    		timef.format(availability.getWednesdayEndTime()),	timef.format(DateTimeUtil.addMinute(secondaryAvailability.get(i).getWednesdayEndTime(),-1))))
          		
          			    {
          				
          				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[2]+" instructor availability overlaps with his availability in other studio"  , false);
          				
          				return updateMessageDTO;
          			}
          	}
      	
      		if(availability.getThursdayStartTime() != null && availability.getThursdayEndTime() != null &&
      				secondaryAvailability.get(i).getThursdayStartTime() != null && secondaryAvailability.get(i).getThursdayEndTime() != null){
      			
      			if(DateTimeUtil.isInBetweenTime(timef.format(availability.getThursdayStartTime()),timef.format(DateTimeUtil.addMinute(secondaryAvailability.get(i).getThursdayStartTime(),1)),
			    		timef.format(availability.getThursdayEndTime()),	timef.format(DateTimeUtil.addMinute(secondaryAvailability.get(i).getThursdayEndTime(),-1))))
          			
          			    {
          				
          				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[3]+" instructor availability overlaps with his availability in other studio"  , false);
          				
          				return updateMessageDTO;
          			}
      		}
      		
      		if(availability.getFridayStartTime() != null && availability.getFridayEndTime() != null &&
      				secondaryAvailability.get(i).getFridayStartTime() != null && secondaryAvailability.get(i).getFridayEndTime() != null){
      			
      			if(DateTimeUtil.isInBetweenTime(timef.format(availability.getFridayStartTime()),timef.format(DateTimeUtil.addMinute(secondaryAvailability.get(i).getFridayStartTime(),1)),
			    		timef.format(availability.getFridayEndTime()),	timef.format(DateTimeUtil.addMinute(secondaryAvailability.get(i).getFridayEndTime(),-1))))
          			    {
          				
          				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[4]+" instructor availability overlaps with his availability in other studio"  , false);
          				
          				return updateMessageDTO;
          			}
      		}
      		
      		if(availability.getSaturdayStartTime() != null && availability.getSaturdayEndTime() != null &&
      				secondaryAvailability.get(i).getSaturdayStartTime() != null && secondaryAvailability.get(i).getSaturdayEndTime() != null){
      			
      			if(DateTimeUtil.isInBetweenTime(timef.format(availability.getSaturdayStartTime()),timef.format(DateTimeUtil.addMinute(secondaryAvailability.get(i).getSaturdayStartTime(),1)),
			    		timef.format(availability.getSaturdayEndTime()),	timef.format(DateTimeUtil.addMinute(secondaryAvailability.get(i).getSaturdayEndTime(),-1))))
          			
          			    {
          				
          				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[5]+" instructor availability overlaps with his availability in other studio"  , false);
          				
          				return updateMessageDTO;
          			}
      		}
      		
      		
      		if(availability.getSundayStartTime() != null && availability.getSundayEndTime() != null &&
      				secondaryAvailability.get(i).getSundayStartTime() != null && secondaryAvailability.get(i).getSundayEndTime() != null){
      			
      			if(DateTimeUtil.isInBetweenTime(timef.format(availability.getSundayStartTime()),timef.format(DateTimeUtil.addMinute(secondaryAvailability.get(i).getSundayStartTime(),1)),
			    		timef.format(availability.getSundayEndTime()),	timef.format(DateTimeUtil.addMinute(secondaryAvailability.get(i).getSundayEndTime(),-1))))
          			
          			    {
          				
          				updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[6]+" instructor availability overlaps with his availability in other studio"  , false);
          				
          				return updateMessageDTO;
          			}
      		}
          }
        //  }
      		return updateMessageDTO;
        }
       
          //GSSP-211 CHANGES
          private UpdateMessageDTO compareWithOneTimeAvailability(Availability availability,List<Onetime> list)
          {
        	  UpdateMessageDTO updateMessageDTO =  new UpdateMessageDTO(null, true);
        	  
        	  DateFormat timef = new SimpleDateFormat("HH:mm:ss");
		        
		        for(Onetime oneTime : list)
		        {
		        			        	
		        	DateTime today = new DateTime(oneTime.getStartTime());
				     int dayOfWeek = today.getDayOfWeek();
				     
				     String oneTimeStart = timef.format(oneTime.getStartTime());
				     String oneTimeEnd = timef.format(oneTime.getEndTime());
		        	
				     switch(dayOfWeek)
				     {
				     //GSSP-290 Issue list no 2
					     case 7 :
					    	
					    	if(availability.getSundayStartTime() != null && 
					    		availability.getSundayEndTime() != null )
					    	{
					    	 			    		
					    		if( DateTimeUtil.isInBetweenTime(timef.format(availability.getSundayStartTime()),oneTimeStart,
					    		timef.format(availability.getSundayEndTime()),	oneTimeEnd))
					    		{
					    			updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[6]+" instructor availability overlaps with his OneTime in other studio"  , false);
			          				
			          				return updateMessageDTO;
					    		}
					    		
					    		break;
					    	
					    	}		
					    		
					    	 
					    	 
					     case 1 :
					    	 
					    	 if(availability.getMondayStartTime() != null && 
					    		availability.getMondayEndTime() != null )
					    	{
					    	 			    		
					    		if( DateTimeUtil.isInBetweenTime(timef.format(availability.getMondayStartTime()),oneTimeStart,
					    		timef.format(availability.getMondayEndTime()),	oneTimeEnd))
					    		{
					    			updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[0]+" instructor availability overlaps with his OneTime in other studio"  , false);
			          				
			          				return updateMessageDTO;
					    		}
					    		
					    		break;
					    	
					    	}	
					    	 
					    	 break;
					    	 
					    	 
					     case 2 :
					    	 
					    	 if(availability.getTuesdayStartTime() != null && 
					    		availability.getTuesdayEndTime() != null )
					    	{
					    	 			    		
					    		if( DateTimeUtil.isInBetweenTime(timef.format(availability.getTuesdayStartTime()),oneTimeStart,
					    		timef.format(availability.getTuesdayEndTime()),	oneTimeEnd))
					    		{
					    			updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[1]+" instructor availability overlaps with his OneTime in other studio"  , false);
			          				
			          				return updateMessageDTO;
					    		}
					    		
					    		break;
					    	
					    	}	
					    	 
					    	 break;
					    	 
					     case 3 :
					    	 
					    	 if(availability.getWednesdayStartTime() != null && 
					    		availability.getWednesdayEndTime() != null )
					    	{
					    	 			    		
					    		if( DateTimeUtil.isInBetweenTime(timef.format(availability.getWednesdayStartTime()),oneTimeStart,
					    		timef.format(availability.getWednesdayEndTime()),	oneTimeEnd))
					    		{
					    			updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[2]+" instructor availability overlaps with his OneTime in other studio"  , false);
			          				
			          				return updateMessageDTO;
					    		}
					    		
					    		break;
					    	
					    	}	
					    	 
					    	 break;
					    	 
					     case 4 :
					    	 
					    	 if(availability.getThursdayStartTime() != null && 
					    		availability.getThursdayEndTime() != null )
					    	{
					    	 			    		
					    		if( DateTimeUtil.isInBetweenTime(timef.format(availability.getThursdayStartTime()),oneTimeStart,
					    		timef.format(availability.getThursdayEndTime()),	oneTimeEnd))
					    		{
					    			updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[3]+" instructor availability overlaps with his OneTime in other studio"  , false);
			          				
			          				return updateMessageDTO;
					    		}
					    		
					    		break;
					    	
					    	}	
					    	 
					    	 break;
					    	 
					     case 5 :
					    	 
					    	 if(availability.getFridayStartTime() != null && 
					    		availability.getFridayEndTime() != null )
					    	{
					    	 			    		
					    		if( DateTimeUtil.isInBetweenTime(timef.format(availability.getFridayStartTime()),oneTimeStart,
					    		timef.format(availability.getFridayEndTime()),	oneTimeEnd))
					    		{
					    			updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[4]+" instructor availability overlaps with his OneTime in other studio"  , false);
			          				
			          				return updateMessageDTO;
					    		}
					    		
					    		break;
					    	
					    	}	
					    	 
					    	 break;
					    	 
					    	 
					     case 6 :
					    	 
					    	 if(availability.getSaturdayStartTime() != null && 
					    		availability.getSaturdayEndTime() != null )
					    	{
					    	 			    		
					    		if( DateTimeUtil.isInBetweenTime(timef.format(availability.getSaturdayStartTime()),oneTimeStart,
					    		timef.format(availability.getSaturdayEndTime()),	oneTimeEnd))
					    		{
					    			updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[5]+" instructor availability overlaps with his OneTime in other studio"  , false);
			          				
			          				return updateMessageDTO;
					    		}
					    		
					    		break;
					    	
					    	}	
					    	 
					    	 break;
				    
				     }
		        	
		        }
		        
		        
		        return updateMessageDTO;
          }
          

          }
