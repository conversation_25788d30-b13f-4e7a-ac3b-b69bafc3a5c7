package com.guitarcenter.scheduler.controller;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.service.LocationProfileService;
import com.guitarcenter.scheduler.service.ValidationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
public class LocationProfileController {

    private static final Logger LOG = LoggerFactory.getLogger(LocationProfileController.class);
    private static final String DISABLE_PROFILE_MAPPING = "locationProfile/disableProfile";
    private static final String ENABLE_PROFILE_MAPPING = "locationProfile/enableProfile";
    private static final String MESSAGE_KEY = "msg";
    private static final String STATUS_KEY = "status";
    //message constants
    private static final String DISABLE_PROFILE_SUCCESS_MESSAGE_VALUE = "Disable Profile Successfully!";
    private static final String DISABLE_PROFILE_ERROR_MESSAGE_VALUE = "Disable Profile Error!";
    private static final String VALIDATION_PROFILE_MESSAGE_VALUE = "LocationProfile can't be disabled!";
    private static final String ENABLE_PROFILE_SUCCESS_MESSAGE_VALUE = "Enable Profile Successfully!";
    private static final String ENABLE_PROFILE_ERROR_MESSAGE_VALUE = "Enable Profile Error!";
    @Autowired
    private LocationProfileService locationProfileService;
    @Autowired
    private ValidationService validationService;

    @PostMapping(value = DISABLE_PROFILE_MAPPING, produces = "application/json")
    public ResponseEntity<Map<String, Object>> disableProfile(@RequestParam Long profileId, @RequestParam Person person) {
        long personId = person.getPersonId();
        Map<String, Object> map = new HashMap<>();

        // validate if there was future active appointment created in current profile
        if (validationService.checkAppointmentByProfileId(profileId)) {
            try {
                locationProfileService.disableProfile(profileId, personId);
            } catch (Exception e) { // disable profile error
                LOG.error("Disabling profile error", e);
                map.put(STATUS_KEY, false);
                map.put(MESSAGE_KEY, DISABLE_PROFILE_ERROR_MESSAGE_VALUE);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(map);
            }
            // disable profile successfully
            map.put(STATUS_KEY, true);
            map.put(MESSAGE_KEY, DISABLE_PROFILE_SUCCESS_MESSAGE_VALUE);
        } else {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, VALIDATION_PROFILE_MESSAGE_VALUE);
        }

        return ResponseEntity.ok(map);
    }

    @PutMapping(value = ENABLE_PROFILE_MAPPING, produces = "application/json")
    public ResponseEntity<Map<String, Object>> enableProfile(@RequestParam Long profileId, @RequestParam Person person) {
        long personId = person.getPersonId();
        Map<String, Object> map = new HashMap<>();
        try {
            locationProfileService.enableProfile(profileId, personId);
        } catch (Exception e) {
            LOG.error("Enabling profile error", e);
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, ENABLE_PROFILE_ERROR_MESSAGE_VALUE);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(map);
        }

        // enable profile successfully
        map.put(STATUS_KEY, true);
        map.put(MESSAGE_KEY, ENABLE_PROFILE_SUCCESS_MESSAGE_VALUE);

        return ResponseEntity.ok(map);
    }

}
