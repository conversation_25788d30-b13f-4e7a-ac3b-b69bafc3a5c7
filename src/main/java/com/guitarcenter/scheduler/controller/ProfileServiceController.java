package com.guitarcenter.scheduler.controller;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.MessageConstants;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dto.*;
import com.guitarcenter.scheduler.model.*;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.RequiresInstructor;
import com.guitarcenter.scheduler.service.ActivityService;
import com.guitarcenter.scheduler.service.LocationProfileService;
import com.guitarcenter.scheduler.service.ServiceService;
import com.guitarcenter.scheduler.service.SiteService;
import com.guitarcenter.scheduler.service.impl.ServiceAndActivityValidate;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
public class ProfileServiceController implements AppConstants {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProfileServiceController.class);
    private static final String COMMA_SPACE = ", ";
    private static final String STATUS_KEY = "status";
    private static final String MESSAGE_KEY = "message";
    private static final String VALIDATE_FLAG = "flag";
    private static final String VALIDATE_MESSAGE = "message";
    private static final String VALIDATE_OBJECT = "object";

    @Autowired
    private LocationProfileService locationProfileService;

    @Autowired
    @Qualifier("serviceService")
    private ServiceService serviceService;

    @Autowired
    private ActivityService activityService;

    @Autowired
    @Qualifier("siteService")
    private SiteService siteService;

    @Autowired
    @Qualifier("serviceAndActivityValidate")
    private ServiceAndActivityValidate serviceAndActivityValidate;

    // profile service redirction
    @RequestMapping("proServicePage")
    public String proServiceActivity() {
        return "service_activity";
    }

    @GetMapping(value = "/profileService/loadProfileServiceList", produces = "application/json")
    public ResponseEntity<List<ProfileServiceDTO>> loadProfileServiceList(@RequestParam Long profileId) {
        List<ProfileServiceDTO> list = new ArrayList<>();
        try {
            Set<ProfileService> serviceSet = new HashSet<>(locationProfileService.getRealProfileServiceList(profileId));

            for (ProfileService service : serviceSet) {
                ProfileServiceDTO dto = new ProfileServiceDTO();
                dto.setServiceId(service.getProfileServiceId());
                dto.setServiceName(service.getService().getServiceName());
                String avaibleAct = getActivityName(service.getService().getServiceId(), activityService.loadProfileActivityByProfileId(profileId));
                dto.setAvaibleAct(avaibleAct);
                dto.setEnable(!Enabled.N.equals(service.getEnabled()));
                list.add(dto);
            }
            Collections.sort(list);
        } catch (Exception e) {
            LOGGER.error("Get profileServiceDTO list Failed!", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
        return ResponseEntity.ok(list);
    }

    @GetMapping(value = "/profileService/addProfileService/loadAddedServiceList", produces = "application/json")
    public ResponseEntity<Set<ServiceDTO>> loadAddedServiceList(@RequestParam Long profileId, @RequestParam long siteId) {
        //long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
        Set<ServiceDTO> dtoList = new TreeSet<>();

        List<Service> sitelist = serviceService.findServiceBySite(siteId);
        Set<Service> profileSet = new HashSet<>(locationProfileService.getProfileServiceList(profileId));
        boolean tempFlag = false;

        for (Service service : sitelist) {
            if (profileSet != null && !profileSet.isEmpty()) {
                for (Service proService : profileSet) {
                    if (service.getServiceId().equals(proService.getServiceId())) {
                        tempFlag = true;
                        break;
                    }
                }
            }
            if (!tempFlag && Enabled.Y.equals(service.getEnabled())) {
                ServiceDTO dto = new ServiceDTO();
                dto.setServiceId(service.getServiceId());
                dto.setServiceName(service.getServiceName());
                dto.setEnable(Enabled.Y.equals(service.getEnabled()));
                dto.setInstructor(SystemUtil.getRequiresInscructor(service.getRequiresInstructor()));
                dtoList.add(dto);
            }
            tempFlag = false;
        }

        return ResponseEntity.ok(dtoList);
    }


    @GetMapping(value = "/profileService/addProfileService/loadActivitiesInServiceList", produces = "application/json")
    public ResponseEntity<List<ActivityDTO>> loadActivitiesInServiceList(@RequestParam Long serviceId) {
        List<Activity> list = activityService.loadActivitiesByService(serviceId);
        List<ActivityDTO> alist = new LinkedList<>();

        for (Activity activity : list) {
            if (!Enabled.Y.equals(activity.getEnabled())) {
                continue;
            }
            ActivityDTO act = new ActivityDTO();
            act.setActivityId(activity.getActivityId());
            act.setActivityName(activity.getActivityName());
            alist.add(act);
        }
        return ResponseEntity.ok(alist);
    }

    //when create service chose activity by serviceId
    @PostMapping(value = "/profileService/addProfileService/addSelectedActivity", produces = "application/json")
    public ResponseEntity<Map<String, Object>> addSelectedActivity(@RequestParam String activityString, @RequestParam Long serviceId) {
        Map<String, Object> map = new HashMap<>();
        if (serviceId != null) {
            List<ActivityDTO> sList = new ArrayList<>();
            List<Activity> activityList = activityService.loadActivitiesByService(serviceId);
            List<Long> activityId = SystemUtil.getIdList(activityString);
            for (Activity s : activityList) {
                if (!Enabled.Y.equals(s.getEnabled())) {
                    continue;
                }
                if (!activityId.contains(s.getActivityId())) {
                    ActivityDTO dto = new ActivityDTO();
                    dto.setActivityId(s.getActivityId());
                    dto.setActivityName(s.getActivityName());
                    sList.add(dto);
                    map.put("activityList", sList);
                    map.put(STATUS_KEY, true);
                    map.put(MESSAGE_KEY, "Load activityList successfully!!");
                }
            }
        } else {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, "Please select service first!!");
            map.put("activityList", null);
        }

        return ResponseEntity.ok(map);
    }


    @PostMapping(value = "/profileService/addProfileService/saveProService", produces = "application/json")
    public ResponseEntity<Map<String, Object>> createProServiceActivity(@RequestBody ProfileServiceCreateDTO pscDto, @RequestParam(PERSON_SESSION_KEY) Person person, @RequestParam(LOCATION_PROFILE_ID_SESSION_KEY) Long profileId, @RequestParam(SITE_ID_SESSION_KEY) Long siteId) {
        Site site = new Site();
        site.setSiteId(siteId);
        Map<String, Object> map = new HashMap<>();

        try {
            map = checkServiceActivity(pscDto);
            map = locationProfileService.saveServiceAndActivity(pscDto, profileId, site, person, map);
        } catch (Exception e) {
            LOGGER.info(e.getMessage());
            map.put("status", false);
            map.put(MESSAGE_KEY, "Save Profile Info failed!");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(map);
        }
        return ResponseEntity.ok(map);
    }

    //GCSS-657
    @PostMapping(value = "/profileService/deleteProfileService", produces = "application/json")
    public ResponseEntity<UpdateMessageDTO> deleteProfileService(@RequestParam Long serviceId, @RequestParam(PERSON_SESSION_KEY) Person person) {
        UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO("Delete success!", true);

        Map<String, Object> map = serviceAndActivityValidate.profileServiceCanDelete(serviceId);
        Boolean result = (Boolean) map.get(VALIDATE_FLAG);
        if (!result) {
            updateMessageDTO.setMessage((String) map.get(VALIDATE_MESSAGE));
            updateMessageDTO.setStatus(false);
            return ResponseEntity.ok(updateMessageDTO);
        }

        ProfileService profileService = (ProfileService) map.get(VALIDATE_OBJECT);
        try {
            serviceAndActivityValidate.deleteProfileService(profileService.getLocationProfile().getProfileId(), profileService.getService().getServiceId());
        } catch (Exception e) {
            LOGGER.info(e.getMessage());
            updateMessageDTO.setMessage(e.getMessage());
            updateMessageDTO.setStatus(false);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(updateMessageDTO);
        }
        return ResponseEntity.ok(updateMessageDTO);
    }


    @GetMapping(value = "/profileService/loadProfileServiceDetail", produces = "application/json")
    public ResponseEntity<ServiceDTO> loadProfileServiceDetail(@RequestParam Long id, @RequestParam(SITE_ID_SESSION_KEY) Long siteId, @RequestParam(LOCATION_PROFILE_ID_SESSION_KEY) Long profileId) {
        ServiceDTO serviceDTO = new ServiceDTO();
        ProfileService profileService = serviceService.getProfileService(id);

        List<ActivityDTO> activityDTOs = new ArrayList<>();
        List<ActivityDTO> notSelectedDTOs = new ArrayList<>();

        List<Activity> notSelectedActivities = activityService.loadActivitiesByService(profileService.getService().getServiceId());
        List<Activity> activitiesInProfile = activityService.loadActivityByProfileId(profileId).get("result");
        final List<Activity> non_activitiesInProfile = activityService.loadActivityByProfileId(profileId).get("non_result");

        for (Activity activity : notSelectedActivities) {
            ActivityDTO activityDTO = new ActivityDTO(activity.getActivityId(), activity.getActivityName());
            if (activitiesInProfile.contains(activity)) {
                activityDTOs.add(activityDTO);
                continue;
            }

            if (!activity.getEnabled().equals(Enabled.Y)) {
                continue;
            }
            if (non_activitiesInProfile.contains(activity)) {
                continue;
            }
            notSelectedDTOs.add(activityDTO);
        }

        serviceDTO.setActivityDTOs(activityDTOs);
        serviceDTO.setNotSelecteDtos(notSelectedDTOs);
        serviceDTO.setEnable(Enabled.Y.equals(profileService.getEnabled()));
        serviceDTO.setServiceId(id);
        serviceDTO.setServiceName(profileService.getService().getServiceName());
        serviceDTO.setVersion(profileService.getVersion());
        serviceDTO.setInstructor(profileService.getService().getRequiresInstructor().toString());
        if (RequiresInstructor.R.equals(profileService.getService().getRequiresInstructor())) {
            serviceDTO.setInstructor("Y");
        }
        return ResponseEntity.ok(serviceDTO);
    }


    @PostMapping(value = "/profileService/updatepProfileService", produces = "application/json")
    public ResponseEntity<UpdateMessageDTO> updatepProfileService(@RequestBody ServiceDTO serviceDTO, @RequestParam(PERSON_SESSION_KEY) Person person, @RequestParam(LOCATION_PROFILE_ID_SESSION_KEY) Long profileId, @RequestParam(SITE_ID_SESSION_KEY) Long siteId, @RequestParam(LOCATION_STRING) Location location) {

        UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO(UPDATE_SUCCESS, true);
        Site site = siteService.getSiteById(siteId);
        Long id = serviceDTO.getServiceId();
        ProfileService profileService = serviceService.getProfileService(id);
        Enabled enable = profileService.getEnabled();

        updateMessageDTO = checkUpdateProfileServie(serviceDTO, profileId, updateMessageDTO, profileService, enable);
        if (!updateMessageDTO.getStatus()) {
            return ResponseEntity.ok(updateMessageDTO);
        }

        profileService.setVersion(serviceDTO.getVersion());
        profileService.setEnabled(serviceDTO.getEnable() ? Enabled.Y : Enabled.N);

        List<ActivityDTO> activityDTOs = serviceDTO.getActivityDTOs();
        List<Long> activityIds = new ArrayList<>();

        for (ActivityDTO activityDTO : activityDTOs) {
            activityIds.add(activityDTO.getActivityId());
        }

        try {
            serviceService.updateService(profileService, person.getPersonId(), activityIds, profileId, site, location, enable);
        } catch (Exception e) {
            updateMessageDTO.setMessage(e.getMessage());
            updateMessageDTO.setStatus(false);
            LOGGER.info(e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(updateMessageDTO);
        }

        StringBuilder sbBuilder = new StringBuilder();
        List<Activity> notSelectedActivities = activityService.loadActivitiesByService(id);
        for (Activity activity : notSelectedActivities) {
            List<Activity> activitiesInProfile = activityService.loadActivityByProfileId(profileId).get("result");
            if (activitiesInProfile.contains(activity) && activity.getEnabled().equals(Enabled.Y)) {
                sbBuilder.append(activity.getActivityName()).append(COMMA_SPACE);
            }
        }

        serviceDTO.setVersion(profileService.getVersion());
        serviceDTO.setAvaibleAct(sbBuilder.length() > 0 ? sbBuilder.substring(0, sbBuilder.length() - COMMA_SPACE.length()) : "");
        updateMessageDTO.setObject(serviceDTO);
        return ResponseEntity.ok(updateMessageDTO);
    }

    //from Y to N check appointment
    private UpdateMessageDTO checkUpdateProfileServie(ServiceDTO serviceDTO, Long profileId, UpdateMessageDTO updateMessageDTO, ProfileService profileService, Enabled enable) {
        if (!serviceDTO.getEnable() && Enabled.Y.equals(enable)) {
            Boolean result = serviceAndActivityValidate.profileServiceCanDisable(profileService.getService().getServiceId(), profileId);
            if (!result) {
                updateMessageDTO.setMessage("profileActivity has been assigned to appoint,this profileService cannot be disabled!");
                updateMessageDTO.setStatus(false);
                return updateMessageDTO;
            }
        }
        updateMessageDTO.setStatus(true);
        return updateMessageDTO;
    }


    private Map<String, Object> checkServiceActivity(ProfileServiceCreateDTO pscDto) {
        Map<String, Object> map = new HashMap<String, Object>();
        boolean status = true;
        if (StringUtils.isBlank(pscDto.getServiceType().toString())) {
            status = false;
            map.put(MESSAGE_KEY, MessageConstants.VALIDATION_SERVICE_ACTIVITY);
        }
        map.put("status", status);
        return map;
    }

    public String getActivityName(long serviceId, List<ProfileActivity> activitySet) {
        List<Activity> list = activityService.loadActivitiesByService(serviceId);
        StringBuffer sb = new StringBuffer();
        String temp = "";
        for (ProfileActivity setActivity : activitySet) {
            for (Activity activity : list) {
                if (activity.getActivityId().toString().equals(setActivity.getActivity().getActivityId().toString()) && Enabled.Y.toString().equals(setActivity.getEnabled().toString())) {
                    sb.append(activity.getActivityName()).append(COMMA_SPACE);
                }
            }
        }

        if (sb.length() != 0) {
            temp = sb.substring(0, sb.length() - COMMA_SPACE.length());
        }
        return temp;
    }
}
