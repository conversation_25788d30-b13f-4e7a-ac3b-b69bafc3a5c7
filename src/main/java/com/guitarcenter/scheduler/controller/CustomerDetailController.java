package com.guitarcenter.scheduler.controller;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.JsonUtil;
import com.guitarcenter.scheduler.common.util.PageInfo;
import com.guitarcenter.scheduler.common.util.PageQueryParams;
import com.guitarcenter.scheduler.dao.criterion.dto.CustomerAppointmentDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorLessonLinkDTO;
import com.guitarcenter.scheduler.dto.*;
import com.guitarcenter.scheduler.model.InstructorAppointmentStatus;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.service.CustomerService;
import com.guitarcenter.scheduler.service.InstructorService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_PHONE;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_SEC_EMAIL;

/**
 * @Date 4/23/2020 6:08 PM
 * <AUTHOR>
 **/
@RestController
public class CustomerDetailController implements AppConstants {


    private static final String MESSAGE_KEY = "message";
    private static final String STATUS_KEY = "status";
    private static final String TIMEOFF_KEY = "timeoffs";
    private static final String MESSAGE_VALUE_APP = "Appointment Id is not valid!";
    //private static final String MESSAGE_VALUE_CMNT		    = "Internal Remarks should not exceeds 2048 characters";
    private static final String MESSAGE_VALUE_STS = "Show status should not exceeds 256 characters";
    //private static final String MESSAGE_VALUE_STU_CMNT		    = "Student Notes should not exceeds 2048 characters";
    private static final String RETURN_DTO = "dto";
    @Autowired
    private CustomerService customerService;
    @Resource
    private InstructorService instructorService;

    public static boolean checkValidNumber(Object obj) throws RuntimeException {

        boolean flag = false;
        try {
            // checking Long float using parseInt() method
            Long.parseLong((String) obj);
            flag = true;
        } catch (NumberFormatException e) {
            //LOGGER.info("Number validation failed :"+e);
            System.out.println("Error" + e);
        }


        return flag;
    }

    public static boolean isNumeric(String strNum) {
        if (strNum == null) {
            return false;
        }
        try {
            double d = Double.parseDouble(strNum);
        } catch (NumberFormatException nfe) {
            return false;
        }
        return true;
    }

    @PostMapping(value = "/customer/info/{customerId}", produces = "application/json")
    public ResponseEntity<CustomerDetailDTO> getCustomerInfo(@PathVariable("customerId") Long customerId) {
        return ResponseEntity.ok(customerService.getCustomerDetailById(customerId));
    }

    @PostMapping(value = "/customer/lessons/history/{params}", produces = "application/json")
    public ResponseEntity<PageInfo<CustomerLessonsHistoryDTO>> getCustomerLessonsHistory(@PathVariable(value = "params") String params) {
        PageQueryParams pageQueryParams = null;
        if (!StringUtils.isEmpty(params)) {
            pageQueryParams = JsonUtil.stringToObject(params, PageQueryParams.class);
        } else {
            pageQueryParams = PageQueryParams.generateFirstPage(5);
        }
        PageQueryParams.PageSqlQueryParams sqlQueryParams = pageQueryParams.buildPageSqlQueryParams();

        // TODO sql query

        PageInfo pageInfo = new PageInfo(6, 5, 50);
        List<CustomerLessonsHistoryDTO> list = new ArrayList<>();
        CustomerLessonsHistoryDTO dto = new CustomerLessonsHistoryDTO();
        dto.setLessonName("Online Guitar");
        dto.setLessonDuration("30 Minutes");
        Calendar date2 = Calendar.getInstance();
        date2.setTimeInMillis(new Date().getTime() - 400000L);
        dto.setDate1(new Date());
        dto.setDate2(date2.getTime());
        dto.setInstructorFullName("Jason Steinmetz");
        dto.setLessonStatusName("Completed");
        dto.setLessonStatusCode("completed");
        list.add(dto);
        pageInfo.setData(list);
        return ResponseEntity.ok(pageInfo);
    }

    @PostMapping(value = "/customer/appointments/{params}", produces = "application/json")
    public ResponseEntity<List> getCustomerAppointmentsInfo(@RequestParam String params, HttpSession session) {
        CustomerAppointmentsQueryDTO queryModel = JsonUtil.stringToObject(params, CustomerAppointmentsQueryDTO.class);
        return ResponseEntity.ok(customerService.getCustomerAppointments(queryModel));
    }

    @PostMapping(value = "/customer/sendReminder", produces = "application/json")
    public ResponseEntity<Boolean> sendReminderToCustomer(@RequestParam(value = "params", required = true) String params) {
        CustomerAppointmentDTO dto = JsonUtil.stringToObject(params, CustomerAppointmentDTO.class);
        return ResponseEntity.ok(customerService.sendEmailReminderToCustomer(dto));
    }

    @PostMapping(value = "/customer/internal_remarks", produces = "application/json")
    public ResponseEntity<PageInfo<InternalRemarkDTO>> getCustomerInternalRemarks(@RequestParam(value = "params", required = false) String params) {
        PageQueryParams pageQueryParams = null;
        if (!StringUtils.isEmpty(params)) {
            pageQueryParams = JsonUtil.stringToObject(params, PageQueryParams.class);
        } else {
            pageQueryParams = PageQueryParams.generateFirstPage(5);
        }
        PageQueryParams.PageSqlQueryParams sqlQueryParams = pageQueryParams.buildPageSqlQueryParams();

        // TODO sql query...

        PageInfo pageInfo = new PageInfo(5, 1, 50);
        List<InternalRemarkDTO> list = new ArrayList<>();
        InternalRemarkDTO dto = new InternalRemarkDTO();

        dto.setInstructorFullName("Jeff Steinmetz");
        dto.setRemark("Since there were connectivity issues, we have added the lessons credit back into your account. Make sure you reschedule using the....");
        dto.setRemarkDate(new Date());
        list.add(dto);
        pageInfo.setData(list);
        return ResponseEntity.ok(pageInfo);
    }

    @PostMapping(value = "/customer/getCustomerEditDetailsInfo", produces = "application/json")
    public ResponseEntity<CustomerDetailDTO> getCustomerEditDetailsInfo(long customerId) {
        CustomerDetailDTO customerDetailDTO = customerService.getCustomerEditDetails(customerId);
        return ResponseEntity.ok(customerDetailDTO);
    }

    @PostMapping(value = "/updateInstructorScheduleByStaff", produces = "application/json")
    public ResponseEntity<UpdateInstructorScheduleMessageDTO> updateInstructorSchedule(@RequestParam(value = "params", required = true) String params) {
        Map<String, Object> map = new HashMap<>();
        InstructorLessonLinkDTO dto = JsonUtil.stringToObject(params, InstructorLessonLinkDTO.class);

        UpdateInstructorScheduleMessageDTO updateMessage = new UpdateInstructorScheduleMessageDTO(UPDATE_SUCCESS, true);
        System.out.println("updateInstructorSchedule  $$ ");

        Person person = (Person) RequestContextHolder.currentRequestAttributes().getAttribute(PERSON_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
        System.out.println("updateInstructorSchedule  $$ person " + person.getPersonId());

        Long site = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);

        InstructorScheduleDetailDTO instructorScheduleDetailDTO = this.buildInstructorLessonLinkDTO(dto);

        map = this.checkInstructorScheduleAppt(instructorScheduleDetailDTO);
        if ((Boolean) map.get(STATUS_KEY)) {

            InstructorAppointmentStatus instructorAppSts = new InstructorAppointmentStatus();

            long appId = Long.parseLong(instructorScheduleDetailDTO.getAppointmentId());

            instructorAppSts.setAppointmentId(appId);
            instructorAppSts.setStatus(instructorScheduleDetailDTO.getShowStatus());
            instructorAppSts.setStudentNote(null);
            instructorAppSts.setSiteId(site);
            instructorAppSts.setVersion(1L);
            instructorAppSts.setUpdated(new Date());
            instructorAppSts.setUpdatedBy(person.getPersonId());
            instructorAppSts.setStudentNote(instructorScheduleDetailDTO.getStudentNote());
            instructorAppSts.setRemarks(instructorScheduleDetailDTO.getRemarks());

            try {
                instructorService.updateInstructorSchedule(instructorAppSts);

            } catch (Exception e) {
                updateMessage.setMessage("update failed!");
                updateMessage.setStatus(false);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(updateMessage);
            }
        }
        return ResponseEntity.ok(updateMessage);
    }

    private InstructorScheduleDetailDTO buildInstructorLessonLinkDTO(InstructorLessonLinkDTO instructorLessonLinkDTO) {

        InstructorScheduleDetailDTO instructorScheduleDetailDTO = null;

        if (null != instructorLessonLinkDTO) {
            instructorScheduleDetailDTO = new InstructorScheduleDetailDTO();
            instructorScheduleDetailDTO.setAppointmentId(instructorLessonLinkDTO.getAppointmentId());
            instructorScheduleDetailDTO.setComments((instructorLessonLinkDTO.getComments()));
            instructorScheduleDetailDTO.setShowStatus(instructorLessonLinkDTO.getShowStatus());
            instructorScheduleDetailDTO.setStudentNote((instructorLessonLinkDTO.getStudentNote()));
            //instructorScheduleDetailDTO.setLessonStatus(instructorLessonLinkDTO.getLessonStatus());
            //instructorScheduleDetailDTO.setNextLessonStatus(instructorLessonLinkDTO.getNextLessonStatus());
            instructorScheduleDetailDTO.setAssignment(instructorLessonLinkDTO.getAssignment());
            instructorScheduleDetailDTO.setPracticeNotes(instructorLessonLinkDTO.getPracticeNotes());
            instructorScheduleDetailDTO.setRemarks(instructorLessonLinkDTO.getRemarks());
            //instructorScheduleDetailDTO.setRate(instructorLessonLinkDTO.getRate());

        }
        System.out.println(instructorScheduleDetailDTO);
        return instructorScheduleDetailDTO;
    }

    @PostMapping(value = "/customer/updateCustomerEditDetailsInfo", produces = "application/json")
    public ResponseEntity<Map<String, Object>> updateCustomerEditDetailsInfo(@RequestBody CustomerDetailDTO dto) throws RuntimeException {
        Map<String, Object> map = new HashMap<>();
        map.put(STATUS_KEY, false);
        map = this.checkEditCustomer(dto);

        if ((Boolean) map.get(STATUS_KEY)) {
            try {
                customerService.saveEditCustomerDetails(dto);
                map.put(RETURN_DTO, "yes");
            } catch (Exception e) {
                map.put(STATUS_KEY, false);
                map.put(MESSAGE_KEY, "Customer edit update Failed");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(map);
            }
        }
        return ResponseEntity.ok(map);
    }

    public Map<String, Object> checkInstructorScheduleAppt(InstructorScheduleDetailDTO dto) throws RuntimeException {

        Map<String, Object> map = new HashMap<String, Object>();
        if (dto.getAppointmentId() == null) {
            map.put(MESSAGE_KEY, MESSAGE_VALUE_APP);
            map.put(STATUS_KEY, false);
            return map;
        }
        if (dto.getAppointmentId() != null && checkValidNumber(dto.getAppointmentId())) {
            map.put(STATUS_KEY, true);

        } else {
            map.put(MESSAGE_KEY, MESSAGE_VALUE_APP);
            map.put(STATUS_KEY, false);
            return map;
        }

        if (dto.getShowStatus() == null) {
            map.put(MESSAGE_KEY, MESSAGE_VALUE_STS);
            map.put(STATUS_KEY, false);
            return map;
        }
        if (dto.getShowStatus() != null && dto.getShowStatus().length() > 256) {
            map.put(MESSAGE_KEY, MESSAGE_VALUE_STS);
            map.put(STATUS_KEY, false);
            return map;
        } else {

            map.put(STATUS_KEY, true);
            return map;
        }
    }

    private Map<String, Object> checkEditCustomer(CustomerDetailDTO dto) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put(STATUS_KEY, true);
        String secondaryEmail = dto.getSecondaryEmail();

        if (secondaryEmail != null && !"".equals(secondaryEmail.trim())) {
            String regex = "^(.+)@(.+)$";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(secondaryEmail);
            if (matcher.matches()) {
                map.put(STATUS_KEY, matcher.matches());
            } else {
                map.put(MESSAGE_KEY, VALIDATION_SEC_EMAIL);
                map.put(STATUS_KEY, matcher.matches());
            }
            return map;
        }
        String phone = dto.getPhoneNumber();
        if (phone != null || !"".equals(phone.trim())) {

            if (isNumeric(phone)) {
                map.put(STATUS_KEY, isNumeric(phone));
            } else {
                map.put(MESSAGE_KEY, VALIDATION_PHONE);
                map.put(STATUS_KEY, isNumeric(phone));
            }
            return map;
        }
        return map;
    }
}
