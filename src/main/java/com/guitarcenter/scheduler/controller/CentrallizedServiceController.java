/**
 * centralize service controller. save update delete.
 *
 * <AUTHOR>
 */
package com.guitarcenter.scheduler.controller;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.AvailabilityDAO;
import com.guitarcenter.scheduler.dao.InstructorDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.InstructorCriterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.dto.*;
import com.guitarcenter.scheduler.model.*;
import com.guitarcenter.scheduler.model.enums.AllowBandName;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.RequiresInstructor;
import com.guitarcenter.scheduler.service.*;
import com.guitarcenter.scheduler.service.impl.ServiceAndActivityValidate;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.*;

/**
 * centralize service controller. save update delete.
 * <AUTHOR>
 */

@RestController
@RequestMapping("/centralized")
public class CentrallizedServiceController implements AppConstants {

    /**
     * logger.
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(CentrallizedServiceController.class);
    private static final String MESSAGE_KEY = "message";
    private static final String STATUS_KEY = "status";
    private static final String VALIDATE_FLAG = "flag";
    private static final String VALIDATE_MESSAGE = "message";
    private static final String VALIDATE_OBJECT = "object";
    private static final String BLANK_STRING = " ";

    @Resource
    private AvailabilityService availabilityService;
    @Resource
    private OnetimeService onetimeService;
    @Resource
    private TimeoffService timeoffService;
    @Autowired
    @Qualifier("serviceService")
    private ServiceService serviceService;

    @Autowired
    @Qualifier("serviceAndActivityValidate")
    private ServiceAndActivityValidate serviceAndActivityValidate;

    //GSSP-211 changes
    @Resource
    private InstructorService instructorService;
    @Autowired
    private InstructorDAO instructorDAO;
    @Autowired
    private PersonManagerService personManagerService;
    @Autowired
    private LocationManagerService locationManageService;
    @Autowired
    private AvailabilityDAO availabilityDAO;
    @Resource
    private EmployeeService employeeService;
    @Resource
    private PersonRoleService personRoleService;

    //GSSP-211 changes
    @RequestMapping("/dualInstructorAccess")
    public ModelAndView instructorLocation() {
        Map<String, Object> map = new HashMap<String, Object>();
        List<Location> locationList = null;
        locationList = serviceService.findLocation();
        map.put("locationlist", locationList);
        return new ModelAndView("Dual_Instructor_Access", map);
    }

    //GSSP-211 changes
    @PostMapping(value = "/updateDualInstructor", produces = "application/json")
    public ResponseEntity<Map<String, Object>> updateDualInstructor(@RequestParam  String externalId,@RequestParam  String locationId, @RequestParam long personId) {
        Map<String, Object> map = new HashMap<>();

        Person person = new Person();
        person.setPersonId(personId);

        try {
            Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByExternalId(1, externalId);
            List<Instructor> findAllInstructors = instructorDAO.search(criterion, DAOHelper.FETCH_PERSON);

            Instructor instructor, previouslyEnabledInstructor = null;
            List<Instructor> instructors = findEnabledInstructor(findAllInstructors);
            previouslyEnabledInstructor = findForUpdateInstructor(findAllInstructors, locationId);

            if (instructors != null && instructors.size() > 0) {
                if (previouslyEnabledInstructor != null) {
                    try {
                        previouslyEnabledInstructor.setStatus("A");
                        previouslyEnabledInstructor.setEnabled(Enabled.Y);
                        instructorService.updateInstructor(previouslyEnabledInstructor, person.getPersonId(), Enabled.Y);
                        map.put(STATUS_KEY, true);
                        map.put(MESSAGE_KEY, "Instructor access granted for the selected location");
                        return ResponseEntity.ok(map);
                    } catch (Exception e) {
                        map.put(STATUS_KEY, false);
                        map.put(MESSAGE_KEY, "Could not grant access to the instructor for the selected location");
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(map);
                    }
                } else {
                    instructor = instructors.get(0);
                    if (instructor.getLocation().getLocationId().toString().equalsIgnoreCase(locationId)) {
                        map.put(STATUS_KEY, false);
                        map.put(MESSAGE_KEY, "Instructor is already part of the mentioned studio");
                        return ResponseEntity.badRequest().body(map);
                    } else {
                        boolean isUpdateDualInstructor = serviceService.createDualInstructor(externalId, locationId, instructor, person);
                        if (isUpdateDualInstructor) {
                            map.put(STATUS_KEY, true);
                            map.put(MESSAGE_KEY, "Instructor access granted for the selected location");
                            return ResponseEntity.ok(map);
                        } else {
                            map.put(STATUS_KEY, false);
                            map.put(MESSAGE_KEY, "Could not grant access to the instructor for the selected location");
                            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(map);
                        }
                    }
                }
            } else {
                map.put(STATUS_KEY, false);
                map.put(MESSAGE_KEY, "No matching instructor for the external Id provided");
                return ResponseEntity.badRequest().body(map);
            }
        } catch (Exception e) {
            LOGGER.error("Caught an exception {} in CalendarController.cancelAppointment", e);
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, "Unable to add instructor to the selected location");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(map);
        }
    }


    //GSSP-211 changes
    @RequestMapping("/deleteDualInstructor")
    @ResponseBody
    public Map<String, Object> removeDualInstructorAccess(@RequestParam  String externalId, @RequestParam  String locationId, @RequestParam long personId) {
        Map<String, Object> map = new HashMap<String, Object>();

        Person person = new Person();
        person.setPersonId(personId);
        try {


            Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByExternalId(1, externalId);

            List<Instructor> instructors = instructorDAO.search(criterion, DAOHelper.FETCH_PERSON);

            Instructor instructor = null;


            if (null != instructors && instructors.size() > 0) {

                for (Instructor locationInstructor : instructors) {


                    if (locationInstructor.getLocation().getLocationId().toString().equalsIgnoreCase(locationId)) {

                        instructor = locationInstructor;

                        break;

                    }
                }

                if (null != instructor) {
                    if (instructor.getExternalSource().equalsIgnoreCase("EDW")) {
                        map.put(STATUS_KEY, false);
                        map.put(MESSAGE_KEY, "Instructor is part of primary studio, hence can't be removed");
                        return map;
                    } else if (instructor.getExternalSource().equalsIgnoreCase("GCSS")) {

                        boolean result = serviceAndActivityValidate.instructorCanDisable(instructor.getInstructorId());

                        if (result) {
                            //GSSP-223 CHANTES
                            long availabilityId = instructor.getAvailability().getAvailabilityId();
                            Availability instructorAvailability = availabilityService.getAvailability(Long.valueOf(availabilityId));


                            if (null != instructorAvailability) {
                                instructorAvailability.setMondayStartTime(null);
                                instructorAvailability.setMondayEndTime(null);
                                instructorAvailability.setTuesdayStartTime(null);
                                instructorAvailability.setTuesdayEndTime(null);
                                instructorAvailability.setWednesdayStartTime(null);
                                instructorAvailability.setWednesdayEndTime(null);
                                instructorAvailability.setThursdayStartTime(null);
                                instructorAvailability.setThursdayEndTime(null);
                                instructorAvailability.setFridayStartTime(null);
                                instructorAvailability.setFridayEndTime(null);
                                instructorAvailability.setSaturdayStartTime(null);
                                instructorAvailability.setSaturdayEndTime(null);
                                instructorAvailability.setSundayStartTime(null);
                                instructorAvailability.setSundayEndTime(null);

                            }
                            long instructorsID = instructor.getInstructorId();
                            List<Onetime> instructorOneTime = onetimeService.getOnetimeByInstructorId(instructorsID);
                            if (null != instructorOneTime) {
                                for (Onetime oT : instructorOneTime) {
                                    long oneTimeId = oT.getOnetimeId();
                                    onetimeService.deleteOneTime(oneTimeId);
                                }
                            }
                            List<Timeoff> instructorTimeoff = timeoffService.getTimeoffByInstructorId(instructorsID);
                            if (null != instructorTimeoff) {
                                for (Timeoff tO : instructorTimeoff) {

                                    timeoffService.deleteTimeoff(tO);
                                }
                            }
                            availabilityService.update(instructorAvailability, person.getPersonId());

                            instructor.setEnabled(Enabled.N);
                            instructor.setStatus("T");
                            instructorService.updateInstructor(instructor, person.getPersonId(), Enabled.N);

                            map.put(STATUS_KEY, true);
                            map.put(MESSAGE_KEY, "Instructor access removed from the selected  location");
                            return map;
                        } else {
                            map.put(STATUS_KEY, false);
                            map.put(MESSAGE_KEY, "Instructor has future appointments, please cancel to proceed further");
                            return map;

                        }

                    }


                } else {

                    map.put(STATUS_KEY, false);
                    map.put(MESSAGE_KEY, "Instructor does not belong to the studio");
                    return map;

                }


            } else {
                map.put(STATUS_KEY, false);
                map.put(MESSAGE_KEY, "Please Enter valid Instructor ID");
                return map;
            }
        } catch (Exception e) {
            LOGGER.error("Caught an exception {} in CalendarController.cancelAppointment", e);
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, "Unable to add instructor to the selected location");
        }
        return map;

    }

    @PostMapping(value = "/servicePage", produces = "application/json")
    public ResponseEntity<Map<String, Object>> serviceActivity() {
        Map<String, Object> map = new HashMap<>();

        List<DurationDTO> durationDTOs = new ArrayList<>();
        // Gssp-254 15 min break changes
        durationDTOs.add(new DurationDTO("15", "15 min"));
        durationDTOs.add(new DurationDTO("30", "30 min"));
        durationDTOs.add(new DurationDTO("60", "1 hr"));

        List<DurationDTO> builderDTOs = DurationBuilder.buildDurationListByMinAndMaxDuration(60, null, null, null, 30);
        durationDTOs.addAll(builderDTOs);

        map.put("durationDTOs", durationDTOs);
        return ResponseEntity.ok(map);
    }

    @PostMapping(value = "/loadServiceList", produces = "application/json")
    public ResponseEntity<List<ServiceDTO>> loadServiceList(@ModelAttribute(SITE_ID_SESSION_KEY) Long siteId) {
        List<ServiceDTO> dtoList = new LinkedList<>();

        List<Service> list = serviceService.findServiceBySite(siteId);
        Iterator<Service> it = list.iterator();

        while (it.hasNext()) {
            Service service = it.next();
            ServiceDTO dto = new ServiceDTO();
            dto.setServiceId(service.getServiceId());
            dto.setServiceName(service.getServiceName());
            dto.setEnable(Enabled.Y.equals(service.getEnabled()));
            dto.setInstructor(SystemUtil.getRequiresInscructor(service.getRequiresInstructor()));
            dtoList.add(dto);
        }

        return ResponseEntity.ok(dtoList);
    }

    @PostMapping(value = "/createService", produces = "application/json")
    public ResponseEntity<Map<String, Object>> createService(@RequestBody ServiceCreateDTO dto, @ModelAttribute(PERSON_SESSION_KEY) Person person, @ModelAttribute(SITE_ID_SESSION_KEY) Long siteId) {

        Map<String, Object> map = new HashMap<>();

        if (StringUtils.isBlank(dto.getServiceName())) {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, "Create service failed, service name is required!");
            return ResponseEntity.badRequest().body(map);
        }

        Boolean result = serviceService.hasSameServiceName(SystemUtil.createName(dto.getServiceName().trim()));
        if (result) {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, "Service name has been used!");
            return ResponseEntity.badRequest().body(map);
        }
        try {
            Service service = new Service();
            service.setServiceName(SystemUtil.createName(dto.getServiceName()));

            service.setEnabled("on".equalsIgnoreCase(dto.getEnable()) ? Enabled.Y : Enabled.N);

            if (RequiresInstructor.N.toString().equals(dto.getInstructor())) {
                service.setRequiresInstructor(RequiresInstructor.N);
            } else if (RequiresInstructor.O.toString().equals(dto.getInstructor())) {
                service.setRequiresInstructor(RequiresInstructor.O);
            } else if (RequiresInstructor.R.toString().equals(dto.getInstructor())) {
                service.setRequiresInstructor(RequiresInstructor.R);
            } else {
                map.put(STATUS_KEY, false);
                map.put(MESSAGE_KEY, "Instructor Default has not been selected. Please select an Instructor requirement and try again.");
                return ResponseEntity.badRequest().body(map);
            }

            Site site = new Site();
            site.setSiteId(siteId);
            service.setSite(site);
            service.setAllowBandName(AllowBandName.Y);
            serviceService.createService(service, person.getPersonId());

            ServiceDTO sDto = new ServiceDTO();
            sDto.setServiceId(service.getServiceId());
            sDto.setInstructor(SystemUtil.getRequiresInscructor(service.getRequiresInstructor()));
            sDto.setEnable(Enabled.Y.equals(service.getEnabled()));
            sDto.setServiceName(dto.getServiceName());

            map.put(STATUS_KEY, true);
            map.put(MESSAGE_KEY, "Create service successfully!");
            map.put("serviceDto", sDto);
            return ResponseEntity.ok(map);
        } catch (Exception e) {
            LOGGER.info(e.getMessage());
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, "Create service failed!");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(map);
        }
    }

    @PostMapping(value = "/deleteService", produces = "application/json")
    public ResponseEntity<UpdateMessageDTO> deleteCentralizedService(Long id) {
        UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO("Delete success!", true);

        Map<String, Object> map = serviceAndActivityValidate.serviceCanDelete(id);
        Boolean result = (Boolean) map.get(VALIDATE_FLAG);
        if (!result) {
            updateMessageDTO.setMessage((String) map.get(VALIDATE_MESSAGE));
            updateMessageDTO.setStatus(false);
            return ResponseEntity.badRequest().body(updateMessageDTO);
        }

        Service service = (Service) map.get(VALIDATE_OBJECT);
        try {
            serviceAndActivityValidate.deleteService(id);
            serviceService.removeCentralizedService(service);
        } catch (Exception e) {
            LOGGER.info(e.getMessage());
            updateMessageDTO.setMessage(e.getMessage());
            updateMessageDTO.setStatus(false);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(updateMessageDTO);
        }
        return ResponseEntity.ok(updateMessageDTO);
    }

    @PostMapping(value = "/loadServiceDetail", produces = "application/json")
    public ResponseEntity<UpdateMessageDTO> loadServiceDetail( @RequestParam  Long id) {
        UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO(null, true);
        ServiceDTO serviceDTO = new ServiceDTO();
        Service service;
        try {
            service = serviceService.getCentralizeService(id);
            serviceDTO.setServiceId(id);
            serviceDTO.setServiceName(service.getServiceName());
            serviceDTO.setInstructor(service.getRequiresInstructor().toString());
            serviceDTO.setEnable(Enabled.Y.equals(service.getEnabled()));
            serviceDTO.setVersion(service.getVersion());
            updateMessageDTO.setObject(serviceDTO);
        } catch (Exception e) {
            updateMessageDTO.setMessage("Service does not exist or has been deleted!");
            updateMessageDTO.setStatus(false);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(updateMessageDTO);
        }
        return ResponseEntity.ok(updateMessageDTO);
    }

    @PostMapping(value = "/updateService", produces = "application/json")
    public ResponseEntity<UpdateMessageDTO> updateCentralizedService(@RequestBody ServiceDTO serviceDTO, @ModelAttribute(PERSON_SESSION_KEY) Person person, @ModelAttribute(SITE_ID_SESSION_KEY) Long siteId) {
        UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO(UPDATE_SUCCESS, true);
        try {
            // if from enable to disable, disable all activity under this service
            // disable to enable, no validate only update profileService
            // global change must validation
            updateMessageDTO = checkServiceCanUpdate(serviceDTO, siteId);
            if (!updateMessageDTO.getStatus()) {
                return ResponseEntity.badRequest().body(updateMessageDTO);
            }
            // update
            serviceService.updateCentralizeService(serviceDTO, person);
            updateMessageDTO.setObject(serviceDTO);
        } catch (Exception e) {
            LOGGER.info(e.getMessage());
            updateMessageDTO.setStatus(false);
            updateMessageDTO.setMessage(e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(updateMessageDTO);
        }
        return ResponseEntity.ok(updateMessageDTO);
    }

    // check update service
    private UpdateMessageDTO checkServiceCanUpdate(ServiceDTO serviceDTO, Long siteId) {
        UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO();

        Service service = serviceService.getServiceById(serviceDTO.getServiceId());

        if (service == null) {
            updateMessageDTO.setMessage("Service does not exit,or has been deleted!");
            updateMessageDTO.setStatus(false);
            return updateMessageDTO;
        }

        if (service.getVersion() != serviceDTO.getVersion()) {
            updateMessageDTO.setMessage("Service has been updated!");
            updateMessageDTO.setStatus(false);
            return updateMessageDTO;
        }

        if (StringUtils.isBlank(serviceDTO.getServiceName())) {
            updateMessageDTO.setMessage("Service name is required!");
            updateMessageDTO.setStatus(false);
            return updateMessageDTO;
        }

        if (!SystemUtil.createName(serviceDTO.getServiceName()).equals(service.getServiceName())) {
            Boolean resultBoolean = serviceService.hasSameServiceName(SystemUtil.createName(serviceDTO.getServiceName()));
            if (resultBoolean) {
                updateMessageDTO.setMessage("Service name has been used!");
                updateMessageDTO.setStatus(false);
                return updateMessageDTO;
            }
        }

        // requireInstructor cannot be null
        if (StringUtils.isBlank(serviceDTO.getInstructor())) {
            updateMessageDTO.setStatus(false);
            updateMessageDTO.setMessage("Update service failed, requireInstructor  is required!");
            return updateMessageDTO;
        }
        // enable to disable
        // validate if has been assigned to appoint
        if (!serviceDTO.getEnable() && serviceDTO.getGlobalChange()) {
            Boolean result = serviceAndActivityValidate.serviceAssignedToAppoint(serviceDTO.getServiceId(), siteId);
            if (result) {
                updateMessageDTO.setStatus(false);
                updateMessageDTO.setMessage("Service has some activity assigned to appointment,it cannot be disabled");
                return updateMessageDTO;
            }
        }
        updateMessageDTO.setStatus(true);
        return updateMessageDTO;
    }


    //Code Change add for GSSP-211
    private List<Instructor> findEnabledInstructor(List<Instructor> allInstructors) {
        List<Instructor> allEnabledInstructor = new ArrayList<Instructor>();

        if (null != allInstructors) {
            for (Instructor instructors : allInstructors) {
                if (!instructors.getStatus().equalsIgnoreCase("T")) {
                    allEnabledInstructor.add(instructors);
                }
            }
        }
        return allEnabledInstructor;
    }

    //Code Change add for GSSP-211
    private Instructor findForUpdateInstructor(List<Instructor> allInstructors, String locationId) {
        Instructor disabledInstructor = null;

        if (null != allInstructors) {
            for (Instructor instructors : allInstructors) {

                if (instructors.getEnabled().equals(Enabled.N) && instructors.getLocation().getLocationId().toString().equalsIgnoreCase(locationId)) {

                    disabledInstructor = instructors;
                    break;
                }

            }

        }
        return disabledInstructor;
    }

    //GSSP-288 redirect to centralized staff manager page staff_list.jsp from other page
    @PostMapping(value = "/staffPage", produces = "application/json")
    public ResponseEntity<Map<String, Object>> adminStaffPage(@ModelAttribute(SITE_ID_SESSION_KEY) Long siteId) {
        Map<String, Object> map = new HashMap<>();
        map.put("siteId", siteId);
        return ResponseEntity.ok(map);
    }

    @PostMapping(value = "/loadStaffList", produces = "application/json")
    public ResponseEntity<ListStaffsDTO> loadStaffList(@ModelAttribute(SITE_ID_SESSION_KEY) Long siteId) {
        ListStaffsDTO listStaffsDTO = employeeService.findAllStaff(siteId);
        return ResponseEntity.ok(listStaffsDTO);
    }

    //GSSP-288 created for staff manager page update function
    @PostMapping(value = "/updateStaffManager", produces = "application/json")
    public ResponseEntity<UpdateMessageDTO> updateStaffManger(@RequestBody StaffDetailDTO staffDetailDto, @ModelAttribute(SITE_ID_SESSION_KEY) Long siteId, @ModelAttribute(PERSON_SESSION_KEY) Person person) {
        List<String> roleLocation = new ArrayList<>();
        StringBuilder roleLocationName = new StringBuilder();
        List<LocationDTO> dtoList = staffDetailDto.getSelectedLocationsList();
        UpdateMessageDTO updateMessage = new UpdateMessageDTO("updated success", true);
        Employee employee = employeeService.getEmployee(staffDetailDto.getId());
        Long personId = employee.getPerson().getPersonId();
        for (LocationDTO locationDto : dtoList) {
            final String role_location = locationDto.getRoleToLocation();
            roleLocation.add(role_location);
            roleLocationName.append(role_location).append(BLANK_STRING);
        }
        // delete person's authentication
        if (staffDetailDto.getActive()) {
            employee.setStatus(STAFF_STATUS);
        } else {
            employee.setStatus(STAFF_NO_STATUS);
        }
        personRoleService.updatePersonRole(siteId, personId, roleLocation, person, employee);
        StaffDTO staffDto = new StaffDTO();
        staffDto.setRoleLocation(roleLocation);
        staffDto.setRoleLocationName(roleLocationName.toString());
        staffDto.setActive(STAFF_STATUS.equals(employee.getStatus()));
        staffDto.setEmail(employee.getPerson().getEmail());
        staffDto.setId(staffDetailDto.getId());
        staffDto.setInstructorName(employee.getPerson().getFirstName() + BLANK_STRING + employee.getPerson().getLastName());
        updateMessage.setObject(staffDto);
        return ResponseEntity.ok(updateMessage);
    }
}
	