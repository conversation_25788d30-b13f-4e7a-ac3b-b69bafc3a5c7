package com.guitarcenter.scheduler.controller;

import com.guitarcenter.scheduler.common.util.ActivityAndServiceUtil;
import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.dto.ProfileActivityCreateDTO;
import com.guitarcenter.scheduler.dto.ServiceDTO;
import com.guitarcenter.scheduler.dto.UpdateMessageDTO;
import com.guitarcenter.scheduler.model.*;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.RequiresInstructor;
import com.guitarcenter.scheduler.service.ActivityService;
import com.guitarcenter.scheduler.service.LocationProfileService;
import com.guitarcenter.scheduler.service.ServiceService;
import com.guitarcenter.scheduler.service.impl.ServiceAndActivityValidate;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
public class ProfileActivityController implements AppConstants {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProfileActivityController.class);
    private static final String STATUS_KEY = "status";
    private static final String MESSAGE_KEY = "message";
    private static final String VALIDATE_FLAG = "flag";
    private static final String VALIDATE_MESSAGE = "message";
    private static final String VALIDATE_OBJECT = "object";


    @Autowired
    private ActivityService activityService;

    @Autowired
    private ServiceService serviceService;

    @Autowired
    private LocationProfileService locationProfileService;

    @Autowired
    private ServiceAndActivityValidate serviceAndActivityValidate;

    /**
     * @return List<ActivityDTO>
     */
    @GetMapping(value = "profileActivity/loadProfileActivityList", produces = "application/json")
    public ResponseEntity<List<ActivityDTO>> loadProfileActivityList(@RequestParam Long profileId) {

        List<ActivityDTO> list = new LinkedList<>();
        try {
            // List<ProfileActivity> alist = activityService.loadProfileActivityByProfileId(profileId);
            // For gcss-578, query the activity list that associated service is enabled
            List<ProfileActivity> alist = activityService.findActivityOfEnabledServiceAndProfile(profileId);
            for (ProfileActivity activity : alist) {
                list.add(ActivityAndServiceUtil.initActivityDTO(activity));
            }
            Collections.sort(list);
        } catch (Exception e) {
            LOGGER.error(e + " Get profileActivityDTO list Failed!");
        }
        return ResponseEntity.ok(list);
    }

    @PostMapping(value = "profileActivity/addProfileActivity", produces = "application/json")
    public ResponseEntity<Map<String, Object>> addProfileActivity(@RequestParam(LOCATION_PROFILE_ID_SESSION_KEY) long profileId, @RequestBody ProfileActivityCreateDTO pacd, @RequestParam(PERSON_SESSION_KEY) Person person, @RequestParam(SITE_ID_SESSION_KEY) Long siteId) {
        Site site = new Site();
        site.setSiteId(siteId);
        Map<String, Object> map = checkActivitySave(profileId, pacd.getServiceType(), pacd.getEnable());
        if (!(Boolean) map.get(STATUS_KEY)) {
            return ResponseEntity.badRequest().body(map);
        }
        try {
            map = locationProfileService.saveProfileServiceAndProfileActivity(pacd, profileId, site, person, map);
            map.put(STATUS_KEY, true);
            map.put(MESSAGE_KEY, "Save activity successfully!!");
        } catch (Exception e) {
            LOGGER.info(e.getMessage());
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, "Save activity failed!");
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(map);
        }
        return ResponseEntity.ok(map);
    }

    //Changes made for 263
    private Map<String, Object> checkActivitySave(Long profileId, Long serviceType, String enable) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put(STATUS_KEY, true);

        if (null != profileId && null != serviceType && "on".equals(enable) && (Boolean) map.get(STATUS_KEY)) {
            Boolean result = serviceAndActivityValidate.profileActivityCanEnable(profileId, serviceType);
            if (!result) {
                map.put(STATUS_KEY, false);
                map.put(MESSAGE_KEY, "Service is disabled,activity cannot be enabled!");
            }
        } else {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, "Invalid Input Data!");
        }
        return map;
    }

    @GetMapping(value = "profileActivity/searchActivityByName", produces = "application/json")
    public ResponseEntity<List<ActivityDTO>> searchActivityByName(@RequestParam String activityName, @RequestParam(LOCATION_PROFILE_ID_SESSION_KEY) long profileId) {

        List<Activity> qlist = activityService.queryActivityByDimName(activityName);
        List<ProfileActivity> plist = activityService.loadProfileActivityByProfileId(profileId);
        List<ActivityDTO> alist = new LinkedList<>();
        boolean tempFlag = true;

        for (Activity activityCen : qlist) {
            for (ProfileActivity activityPro : plist) {
                if (activityCen.getActivityId().equals(activityPro.getActivity().getActivityId())) {
                    tempFlag = false;
                    break;
                }
            }
            if (tempFlag) {
                ActivityDTO dto = new ActivityDTO();
                dto.setActivityId(activityCen.getActivityId());
                dto.setActivityName(activityCen.getActivityName());
                alist.add(dto);
            }
            tempFlag = true;
        }

        return ResponseEntity.ok(alist);
    }

    @GetMapping(value = "profileActivity/initProfileActivity", produces = "application/json")
    public ResponseEntity<Map<String, Object>> initialCreateActivity(@RequestParam Long activityId) {
        Activity activity = activityService.getActivityByActivityId(activityId);
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> tempMap = new HashMap<>();

        Long duration = activity.getMinimumDuration();
        String minimumDuration = String.valueOf(duration);
        map.put("activityId", activityId);
        tempMap.put("id", activity.getService().getServiceId());
        tempMap.put("serviceName", activity.getService().getServiceName());
        map.put("minimumDuration", minimumDuration);
        map.put("minimumDurationText", ActivityAndServiceUtil.getDuration(duration));
        map.put("maxmumDuration", String.valueOf(activity.getMaximumDuration()));
        map.put("maxmumDurationText", ActivityAndServiceUtil.getDuration(activity.getMaximumDuration()));
        map.put("minAttender", (activity.getMinimumAttendees() == -1 || activity.getMinimumAttendees() == null) ? "" : activity.getMinimumAttendees());
        map.put("maxAttender", activity.getMaximumAttendees());
        map.put("instructor", activity.getRequiresInstructor());
        map.put("service", tempMap);
        map.put("enable", !Enabled.N.equals(activity.getEnabled()));
        return ResponseEntity.ok(map);
    }

    //GCSS-657
    @DeleteMapping(value = "profileActivity/deleteProfileActivity", produces = "application/json")
    public ResponseEntity<UpdateMessageDTO> deleteProfileActivity(@RequestParam Long activityId, @RequestParam(PERSON_SESSION_KEY) Person person) {
        UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO("Delete success!", true);

        Map<String, Object> map = serviceAndActivityValidate.profileActivityCanDelete(activityId);
        Boolean result = (Boolean) map.get(VALIDATE_FLAG);
        if (!result) {
            updateMessageDTO.setMessage((String) map.get(VALIDATE_MESSAGE));
            updateMessageDTO.setStatus(false);
            return ResponseEntity.badRequest().body(updateMessageDTO);
        }

        ProfileActivity profileActivity = (ProfileActivity) map.get(VALIDATE_OBJECT);
        try {
            locationProfileService.deleteProfileActivity(profileActivity.getLocationProfile().getProfileId(), profileActivity.getActivity().getActivityId());
        } catch (Exception e) {
            LOGGER.info(e.getMessage());
            updateMessageDTO.setMessage(e.getMessage());
            updateMessageDTO.setStatus(false);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(updateMessageDTO);
        }
        return ResponseEntity.ok(updateMessageDTO);
    }


    @RequestMapping(value = "service/loadActivityDetail", produces = "application/json")
    public ResponseEntity<ActivityDTO> loadActivityDetail(@RequestParam Long id) {
        ActivityDTO activityDetailDto = new ActivityDTO();
        ProfileActivity profileactivity = activityService.getProfileActivity(id);
        activityDetailDto.setActivityId(profileactivity.getProfileActivityId());
        activityDetailDto.setActivityName(profileactivity.getActivity().getActivityName());
        activityDetailDto.setEnable(Enabled.Y.equals(profileactivity.getEnabled()));
        activityDetailDto.setMaximunAttendees(String.valueOf(profileactivity.getActivity().getMaximumAttendees()));
        Activity activity = activityService.getActivityByActivityId(profileactivity.getActivity().getActivityId());
        activityDetailDto.setServiceName(activity.getService().getServiceName());

        Long miniAttendees = activity.getMinimumAttendees();
        String attendee = null;
        if (miniAttendees == null || miniAttendees == -1) {
            attendee = String.valueOf((char) 32);
        } else {
            attendee = String.valueOf(miniAttendees);
        }
        activityDetailDto.setMinimunAttendees(attendee);
        activityDetailDto.setMinimumDuration(String.valueOf(activity.getMinimumDuration()));
        activityDetailDto.setMaxmumDuration(String.valueOf(activity.getMaximumDuration()));
        activityDetailDto.setVersion(profileactivity.getVersion());
        activityDetailDto.setMaxmumDurationText(ActivityAndServiceUtil.getDuration(activity.getMaximumDuration()));
        activityDetailDto.setMinimumDurationText(ActivityAndServiceUtil.getDuration(activity.getMinimumDuration()));
        String requiresInstructor = "";
        if (RequiresInstructor.N.equals(activity.getRequiresInstructor())) {
            requiresInstructor = "N";
        } else if (RequiresInstructor.O.equals(activity.getRequiresInstructor())) {
            requiresInstructor = "O";
        } else if (RequiresInstructor.R.equals(activity.getRequiresInstructor())) {
            requiresInstructor = "R";
        }
        activityDetailDto.setRequiresInstructor(requiresInstructor);
        ServiceDTO serviceDTO = new ServiceDTO(activity.getService().getServiceId(), activity.getService().getServiceName());
        activityDetailDto.setServiceDTO(serviceDTO);
        return ResponseEntity.ok(activityDetailDto);
    }

    @RequestMapping(value = "service/updateActivity", produces = "application/json")
    public ResponseEntity<UpdateMessageDTO> updateActivity(@RequestBody ActivityDTO activityDetailDTO, @RequestParam(PERSON_SESSION_KEY) Person person, @RequestParam("location") Location location) {
        ProfileActivity profileActivity = activityService.getProfileActivity(activityDetailDTO.getActivityId());
        Enabled enable = profileActivity.getEnabled();
        profileActivity.setEnabled(activityDetailDTO.getEnable() ? Enabled.Y : Enabled.N);
        UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO(UPDATE_SUCCESS, true);
        updateMessageDTO = checkActivityUpdate(profileActivity, enable, updateMessageDTO);
        if (!updateMessageDTO.getStatus()) {
            return ResponseEntity.badRequest().body(updateMessageDTO);
        }
        try {
            activityService.updateProfileActivity(profileActivity, person.getPersonId());
        } catch (Exception e) {
            updateMessageDTO = new UpdateMessageDTO(e.getMessage(), false);
            LOGGER.info(e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(updateMessageDTO);
        }
        activityDetailDTO.setVersion(profileActivity.getVersion());
        activityDetailDTO.setAttenders(profileActivity.getActivity().getMinimumAttendees() + (StringUtils.isNotBlank(String.valueOf(profileActivity.getActivity().getMaximumAttendees())) ? " to " + profileActivity.getActivity().getMaximumAttendees() : ""));
        updateMessageDTO.setObject(activityDetailDTO);
        return ResponseEntity.ok(updateMessageDTO);
    }


    private UpdateMessageDTO checkActivityUpdate(ProfileActivity profileActivity, Enabled enabled, UpdateMessageDTO updateMessageDTO) {
        Activity activity = profileActivity.getActivity();
        Long activityId = activity.getActivityId();
        Long serviceId = profileActivity.getActivity().getService().getServiceId();
        Long profileId = profileActivity.getLocationProfile().getProfileId();
        //from Y to N
        if (Enabled.Y.equals(enabled) && Enabled.N.equals(profileActivity.getEnabled())) {

            Boolean result = serviceAndActivityValidate.profileActivityCanDisable(profileId, activityId);
            if (!result) {
                updateMessageDTO.setMessage("profileActivity has been assigned to appointment,it cannot be disabled!");
                updateMessageDTO.setStatus(false);
                return updateMessageDTO;
            }

        } else if (Enabled.N.equals(enabled) && Enabled.Y.equals(profileActivity.getEnabled())) {


            Boolean result1 = serviceAndActivityValidate.profileActivityCanEnable(profileId, serviceId);
            if (!result1) {
                updateMessageDTO.setMessage("profileService is disabled,it cannot enable!");
                updateMessageDTO.setStatus(false);
                return updateMessageDTO;
            }

        }
        return updateMessageDTO;
    }
}


