/**
 * @Title: OnetimeController.java
 * @Package com.guitarcenter.scheduler.controller
 * @Description: Copyright: Copyright (c) 2014
 * Company:
 * <AUTHOR>
 * @date Jun 9, 2014 10:19:13 AM
 * @version V1.0
 */
package com.guitarcenter.scheduler.controller;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.CalendarUtil;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.AvailabilityDAO;
import com.guitarcenter.scheduler.dao.InstructorDAO;
import com.guitarcenter.scheduler.dao.criterion.AvailabilityCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.InstructorCriterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.dto.OnetimeDTO;
import com.guitarcenter.scheduler.dto.UpdateMessageDTO;
import com.guitarcenter.scheduler.model.*;
import com.guitarcenter.scheduler.service.*;
import jakarta.annotation.Resource;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.guitarcenter.scheduler.common.util.MessageConstants.*;

/**
 * @ClassName: OnetimeController
 * @Description:
 * <AUTHOR>
 * @date Jun 9, 2014 10:19:13 AM
 *
 */
@RestController
@RequestMapping("/onetime")
public class OnetimeController implements AppConstants {
    private static final Logger LOG = LoggerFactory.getLogger(OnetimeController.class);

    private static final String MESSAGE_KEY = "message";
    private static final String STATUS_KEY = "status";
    private static final String RETURN_DTO = "dto";
    private static final String ONETIME_KEY = "onetimes";
    //GSSP-211 CHANGES
    @Autowired
    private InstructorDAO instructorDAO;
    @Autowired
    private AvailabilityDAO availabilityDAO;
    @Autowired
    private OnetimeService onetimeService;
    @Autowired
    private ValidationService validationService;
    @Autowired
    private InstructorService instructorService;
    //GSSP-211 CHANGES
    @Resource
    private AvailabilityService availabilityService;

    @Autowired
    private LocationManagerService locationManagerService;
    /**
     * @throws ParseException
     * update a one time record
     * updateOnetime
     *
     *
     * @Title: updateOnetime
     * @Description:
     * @param @param dto
     * @param @return
     * @param @throws RuntimeException
     * @return Map<String, Object>
     * @throws
     */
    @PutMapping(value = "/onetime/updateOnetime", produces = "application/json")
    public ResponseEntity<Map<String, Object>> updateOnetime(@RequestBody OnetimeDTO dto,@RequestParam long personId,@RequestParam String externalId,@RequestParam long profileId,@RequestParam long siteId) throws RuntimeException, ParseException {
        if (LOG.isDebugEnabled()) {
            LOG.debug("OnetimeController.updateOnetime: start");
        }
        Map<String, Object> map = new HashMap<>();
        //Person person = (Person) RequestContextHolder.currentRequestAttributes().getAttribute("person", RequestAttributes.SCOPE_SESSION);
        //long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
        //Location location = (Location) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
        //long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);


        Location location =  locationManagerService.findByExternalId(externalId);
        List<Location> locations =
                locationManagerService.findByExternalId(siteId, externalId);
        if (locations.size() == 1) {
            location = locations.get(0);
        }
        Person person = new Person();
        person.setPersonId(personId);

        Instructor instructor1 = instructorService.getInstructor(dto.getInstructorId());
        Availability availability = availabilityService.getAvailability(instructor1.getAvailability().getAvailabilityId());
        UpdateMessageDTO updateMessageDTO = null;

        Availability studioAvailability = availabilityService.findByProfileId(profileId);
        Long studioAvailabilityID = studioAvailability.getAvailabilityId();
        String extId = "";
        long secondaryInstructorId = -1;
        List<Availability> secondaryAvailability = null;
        Criterion<Availability, Availability> avalabilityCriterion = null;

        if (dto.getInstructorId() != null) {
            Criterion<Instructor, Instructor> instructorCriterion = InstructorCriterion.findByInstructorIds(dto.getInstructorId());
            List<Instructor> instructorss = instructorDAO.search(instructorCriterion, DAOHelper.FETCH_PERSON);

            if (instructorss != null && !instructorss.isEmpty()) {
                extId = instructorss.get(0).getExternalId();
                Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByExternalId(siteId, extId);
                List<Instructor> instructors = instructorDAO.search(criterion, DAOHelper.FETCH_PERSON);

                for (Instructor instructor : instructors) {
                    secondaryInstructorId = Long.parseLong(instructor.getExternalId());
                    avalabilityCriterion = AvailabilityCriterion.getByInstructorId(availability.getAvailabilityId(), secondaryInstructorId);
                    secondaryAvailability = availabilityDAO.search(avalabilityCriterion, DAOHelper.FETCH_AVAILABILITY);
                    break;
                }

                if (availability != null && dto != null) {
                    updateMessageDTO = compareOneTimeAvailabilityWithinstructoravailability(availability, dto);
                    if (!updateMessageDTO.getStatus()) {
                        map.put(STATUS_KEY, false);
                        map.put(MESSAGE_KEY, updateMessageDTO.getMessage());
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(map);
                    }
                }

                if (availability != null && !secondaryAvailability.isEmpty()) {
                    updateMessageDTO = compareWithOneTimeAvailability(secondaryAvailability, dto);
                    if (!updateMessageDTO.getStatus()) {
                        map.put(STATUS_KEY, false);
                        map.put(MESSAGE_KEY, updateMessageDTO.getMessage());
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(map);
                    }
                }

                for (Instructor instructor : instructors) {
                    if ("Y".equalsIgnoreCase(instructor.getEnabled().toString()) && secondaryAvailability != null) {
                        Long secondaryInstructorId1 = instructor.getInstructorId();
                        List<Onetime> oneTimeList = onetimeService.getOnetimeByInstructorId(secondaryInstructorId1);
                        updateMessageDTO = compareWithOneTime(dto, oneTimeList);

                        if (!updateMessageDTO.getStatus()) {
                            map.put(STATUS_KEY, false);
                            map.put(MESSAGE_KEY, updateMessageDTO.getMessage());
                            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(map);
                        }
                    }
                }
            }
        }

        try {
            map = this.checkOnetime(dto,profileId);
            if ((Boolean) map.get(STATUS_KEY)) {
                Onetime onetime = getOnetime(dto, person);
                onetimeService.saveOnetime(onetime);
                List<Onetime> list = onetimeService.getDisplayOnetimeByInstructorId(dto.getInstructorId());
                List<OnetimeDTO> onetimeDtos = new ArrayList<>();
                for (Onetime t : list) {
                    OnetimeDTO onetimeDto = onetimeDateToString(t);
                    onetimeDtos.add(onetimeDto);
                }
                map.put(RETURN_DTO, onetimeDtos);
                map.put(STATUS_KEY, true);
                map.put(MESSAGE_KEY, "Create One Time Availability Successfully!");
            }
        } catch (Exception e) {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, "Create One Time Availability Failed");
            LOG.error("Caught an exception from OnetimeController.updateOnetime: {}", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(map);
        }

        if (LOG.isDebugEnabled()) {
            LOG.debug("OnetimeController.updateOnetime: end");
        }
        return ResponseEntity.ok(map);
    }


    private Map<String, Object> checkOnetime(OnetimeDTO dto,long profileId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put(STATUS_KEY, true);
        String startDate = dto.getStartDate();
        String startTime = dto.getFromTime();
        String endTime = dto.getToTime();
        //290 ISSUE no 3
//	List<Timeoff> Data = validationService.checkTimeoffByTime(false, startDate, startDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1), dto.getInstructorId());
        //long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
        if (startDate == null || "".equals(startDate.trim())) {
            map.put(MESSAGE_KEY, VALIDATION_START_DATE_NULL);
            map.put(STATUS_KEY, false);
            return map;
        }
        if (startTime == null || "".equals(startTime.trim())) {
            map.put(MESSAGE_KEY, VALIDATION_START_TIME_NULL);
            map.put(STATUS_KEY, false);
            return map;
        }
        if (endTime == null || "".equals(endTime.trim())) {
            map.put(MESSAGE_KEY, VALIDATION_END_TIME_NULL);
            map.put(STATUS_KEY, false);
            return map;
        }
        if (!CalendarUtil.checkTimeFormat(startDate, startTime)) {
            map.put(MESSAGE_KEY, VALIDATION_VALID_TIME_FORMAT);
            map.put(STATUS_KEY, false);
            return map;
        }
        if (!CalendarUtil.checkDate(startDate, startTime, startDate, endTime)) {
            map.put(MESSAGE_KEY, "The start time must be before the end time.");
            map.put(STATUS_KEY, false);
            return map;
        }
        if (!validationService.checkStartTime(startDate, startTime)) {
            map.put(MESSAGE_KEY, VALIDATION_START_DATE_ILLEGAL);
            map.put(STATUS_KEY, false);
            return map;
        }
        //290 ISSUE no 5
		/*if (Data.size()!=0) {	
			DateFormat timef = new SimpleDateFormat("HH:mm:ss");
			for (int i=0;i<Data.size();i++){
			Date start_time = Data.get(i).getStartTime();
			Date end_time =Data.get(i).getEndTime();			
				if( DateTimeUtil.isInBetweenTime(timef.format(start_time),startTime,
			    		timef.format(end_time),	endTime)){
					map.put(MESSAGE_KEY, VALIDATION_ONETIME_TIMEOFF);
					map.put(STATUS_KEY, false);
					return map;	
			}
			}
		}*/
        if (!validationService.checkTimeoffByTime(false, startDate, startDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1), dto.getInstructorId())) {
            map.put(MESSAGE_KEY, VALIDATION_ONETIME_TIMEOFF);
            map.put(STATUS_KEY, false);
            return map;
        }
        if (!validationService.checkProfileAvailabilityByTime(startDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1), profileId)) {
            map.put(MESSAGE_KEY, VALIDATION_ONETIME_PROFILE);
            map.put(STATUS_KEY, false);
            return map;
        }
        return map;
    }

    private Onetime getOnetime(OnetimeDTO dto, Person person) throws Exception {
        Onetime onetime = new Onetime();
        Long instrctorId = dto.getInstructorId();
        Instructor instructor = instructorService.getInstructor(instrctorId);
        onetime.setInstructor(instructor);

        String startDate = dto.getStartDate();
        String startTime = dto.getFromTime();
        String endTime = dto.getToTime();
        Date start = null;
        Date end = null;
        try {
            start = new SimpleDateFormat(DateTimeUtil.DATETIME_PATTERN_SLASH).parse(startDate + " " + startTime);
            end = new SimpleDateFormat(DateTimeUtil.DATETIME_PATTERN_SLASH).parse(startDate + " " + endTime);
        } catch (ParseException e) {
            LOG.error("OnetimeController.getOnetime: parse date error");
            throw new Exception("Date parse error");
        }
        onetime.setStartTime(start);
        onetime.setEndTime(end);
        onetime.setUpdated(new Date());
        onetime.setUpdatedBy(person);
        return onetime;
    }

    private OnetimeDTO onetimeDateToString(Onetime onetime) {
        String fromDate = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_DATE).format(onetime.getStartTime());
        String fromTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_TIME).format(onetime.getStartTime());
        String toTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_TIME).format(onetime.getEndTime());
        OnetimeDTO onetimeDto = new OnetimeDTO();
        onetimeDto.setStartDate(fromDate);
        onetimeDto.setFromTime(fromTime);
        onetimeDto.setToTime(toTime);
//		String startTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_FORMAT).format(onetime.getStartTime());
//		String endTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_FORMAT).format(onetime.getEndTime());
        onetimeDto.setInstructorId(onetime.getInstructor().getInstructorId());
        onetimeDto.setOnetimeId(onetime.getOnetimeId());
        onetimeDto.setOnetimeStartToEnd(DateTimeUtil.getWeekdayByDate(fromDate) + ": " + fromDate + ", " + fromTime + " - " + toTime);
        return onetimeDto;
    }

    /**
     * get instructor's one time list for display
     * getOnetimeDtos
     *
     *
     * @Title: getOnetimeDtos
     * @Description:
     * @param @param instructorId
     * @param @return
     * @return Map<String, Object>
     * @throws
     */
    @GetMapping(value = "/loadonetimes/{instructorId}", produces = "application/json")
    public ResponseEntity<Map<String, Object>> getOnetimeDtos(@PathVariable long instructorId) {
        Map<String, Object> map = new HashMap<>();
        List<Onetime> onetimes = onetimeService.getDisplayOnetimeByInstructorId(instructorId);
        List<OnetimeDTO> onetimeDtos = new ArrayList<>();
        for (Onetime dto : onetimes) {
            OnetimeDTO onetimeDto = onetimeDateToString(dto);
            onetimeDtos.add(onetimeDto);
        }
        map.put("onetimes", onetimeDtos);
        return ResponseEntity.ok(map);
    }

    /**
     * Delete one time availability of a instructor
     * If there has appointment existed, refused deleting current one time.
     * @param timeoffId
     * @return
     */
    @DeleteMapping(value = "/deleteOneTime", produces = "application/json")
    public ResponseEntity<Map<String, Object>> deleteOneTimeById(@RequestParam long oneTimeId) {
        Map<String, Object> map = new HashMap<>();
        try {
            if (!onetimeService.deleteOneTime(oneTimeId)) {
                map.put(STATUS_KEY, false);
                map.put(MESSAGE_KEY, "One time can't be deleted due to existing appointments");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(map);
            }
            map.put(STATUS_KEY, true);
        } catch (Exception e) {
            map.put(STATUS_KEY, false);
            map.put(MESSAGE_KEY, "Delete one time availability Failed");
            LOG.error("Caught an exception from OnetimeController.deleteOneTimeById: {}", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(map);
        }
        if (LOG.isDebugEnabled()) {
            LOG.debug("OnetimeController.deleteOneTimeById: end");
        }
        return ResponseEntity.ok(map);
    }

    //GSSP-290 changes- Issue no .1
    private UpdateMessageDTO compareOneTimeAvailabilityWithinstructoravailability(Availability availability, OnetimeDTO dto) throws ParseException {
        UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO(null, true);
        DateFormat timef = new SimpleDateFormat("HH:mm:ss");
        int dayOfWeek = 0;

        try {
            dayOfWeek = new DateTime(new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).parse(dto.getStartDate())).getDayOfWeek();
        } catch (Exception e) {
            LOG.error("Caught an exception , dayOfWeek number", e);
        }

        String oneTimeStart = dto.getFromTime();
        String oneTimeEnd = dto.getToTime();
        //GCSS-313 Changes:: Added DateTimeUtil.addMinute method in all the cases.
        switch (dayOfWeek) {

            case 1:

                if (dto.getFromTime() != null && dto.getToTime() != null && availability.getMondayStartTime() != null && availability.getMondayEndTime() != null) {

                    if (DateTimeUtil.isInBetweenTime(timef.format(availability.getMondayStartTime()), oneTimeStart, timef.format(DateTimeUtil.addMinute(availability.getMondayEndTime(), -1)), oneTimeEnd)) {
                        updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[0] + " Onetime overlaps with his availability of the same studio", false);

                        return updateMessageDTO;
                    }


                }

                break;


            case 2:

                if (dto.getFromTime() != null && dto.getToTime() != null && availability.getTuesdayStartTime() != null && availability.getTuesdayEndTime() != null) {

                    if (DateTimeUtil.isInBetweenTime(timef.format(availability.getTuesdayStartTime()), oneTimeStart, timef.format(DateTimeUtil.addMinute(availability.getTuesdayEndTime(), -1)), oneTimeEnd)) {
                        updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[1] + " Onetime overlaps with his availability of the same studio", false);

                        return updateMessageDTO;
                    }


                }

                break;

            case 3:

                if (dto.getFromTime() != null && dto.getToTime() != null && availability.getWednesdayStartTime() != null && availability.getWednesdayEndTime() != null) {

                    if (DateTimeUtil.isInBetweenTime(timef.format(availability.getWednesdayStartTime()), oneTimeStart, timef.format(DateTimeUtil.addMinute(availability.getWednesdayEndTime(), -1)), oneTimeEnd)) {
                        updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[2] + " Onetime overlaps with his availability of the same studio", false);

                        return updateMessageDTO;
                    }


                }

                break;

            case 4:

                if (dto.getFromTime() != null && dto.getToTime() != null && availability.getThursdayStartTime() != null && availability.getThursdayEndTime() != null) {

                    if (DateTimeUtil.isInBetweenTime(timef.format(availability.getThursdayStartTime()), oneTimeStart, timef.format(DateTimeUtil.addMinute(availability.getThursdayEndTime(), -1)), oneTimeEnd)) {
                        updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[3] + " Onetime overlaps with his availability of the same studio", false);

                        return updateMessageDTO;
                    }


                }

                break;

            case 5:

                if (dto.getFromTime() != null && dto.getToTime() != null && availability.getFridayStartTime() != null && availability.getFridayEndTime() != null) {

                    if (DateTimeUtil.isInBetweenTime(timef.format(availability.getFridayStartTime()), oneTimeStart, timef.format(DateTimeUtil.addMinute(availability.getFridayEndTime(), -1)), oneTimeEnd)) {
                        updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[4] + " Onetime overlaps with his availability of the same studio", false);

                        return updateMessageDTO;
                    }


                }

                break;


            case 6:

                if (dto.getFromTime() != null && dto.getToTime() != null && availability.getSaturdayStartTime() != null && availability.getSaturdayEndTime() != null) {

                    if (DateTimeUtil.isInBetweenTime(timef.format(availability.getSaturdayStartTime()), oneTimeStart, timef.format(DateTimeUtil.addMinute(availability.getSaturdayEndTime(), -1)), oneTimeEnd)) {
                        updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[5] + " Onetime overlaps with his availability of the same studio", false);

                        return updateMessageDTO;
                    }


                }

                break;
            case 7:

                if (dto.getFromTime() != null && dto.getToTime() != null && availability.getSundayStartTime() != null && availability.getSundayEndTime() != null) {

                    if (DateTimeUtil.isInBetweenTime(timef.format(availability.getSundayStartTime()), oneTimeStart, timef.format(DateTimeUtil.addMinute(availability.getSundayEndTime(), -1)), oneTimeEnd)) {
                        updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[6] + " instructor Onetime overlaps with his availability of the same studio", false);

                        return updateMessageDTO;
                    }


                }

                break;


        }


        return updateMessageDTO;
    }

    //GSSP-211 CHANGES
    private UpdateMessageDTO compareWithOneTimeAvailability(List<Availability> secondaryAvailability, OnetimeDTO dto) throws ParseException {
        UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO(null, true);

        DateFormat timef = new SimpleDateFormat("HH:mm:ss");

        DateFormat conver = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH);
        Date df1 = null;
        try {
            df1 = conver.parse(dto.getStartDate());
        } catch (Exception e) {
            LOG.error("Caught an exception , Date format ", e);
        }
        DateTime today = new DateTime(df1);
        int dayOfWeek = today.getDayOfWeek();

        String oneTimeStart = dto.getFromTime();
        String oneTimeEnd = dto.getToTime();
        // 292 –GSSP Multi location instructor comparison
        //GSSP-313 CHANGES add addMinuteOnString
        for (int i = 0; i < secondaryAvailability.size(); i++) {
            switch (dayOfWeek) {
                case 1:

                    if (dto.getFromTime() != null && dto.getToTime() != null && secondaryAvailability.get(i).getMondayStartTime() != null && secondaryAvailability.get(i).getMondayEndTime() != null) {

                        if (DateTimeUtil.isInBetweenTime(timef.format(secondaryAvailability.get(i).getMondayStartTime()), DateTimeUtil.addMinuteOnString(oneTimeStart, 1), timef.format(secondaryAvailability.get(i).getMondayEndTime()), DateTimeUtil.addMinuteOnString(oneTimeEnd, -1))) {
                            updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[0] + " instructor Onetime overlaps with his availability in other studio", false);

                            return updateMessageDTO;
                        }


                    }

                    break;


                case 2:

                    if (dto.getFromTime() != null && dto.getToTime() != null && secondaryAvailability.get(i).getTuesdayStartTime() != null && secondaryAvailability.get(i).getTuesdayEndTime() != null) {

                        if (DateTimeUtil.isInBetweenTime(oneTimeStart, DateTimeUtil.addMinuteOnString(secondaryAvailability.get(i).getTuesdayStartTime().toString(), 1), oneTimeEnd, DateTimeUtil.addMinuteOnString(secondaryAvailability.get(i).getTuesdayEndTime().toString(), -1))) {
                            updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[1] + " instructor Onetime overlaps with his availability in other studio", false);

                            return updateMessageDTO;
                        }


                    }

                    break;

                case 3:

                    if (dto.getFromTime() != null && dto.getToTime() != null && secondaryAvailability.get(i).getWednesdayStartTime() != null && secondaryAvailability.get(i).getWednesdayEndTime() != null) {

                        if (DateTimeUtil.isInBetweenTime(timef.format(secondaryAvailability.get(i).getWednesdayStartTime()), DateTimeUtil.addMinuteOnString(oneTimeStart, 1), timef.format(secondaryAvailability.get(i).getWednesdayEndTime()), DateTimeUtil.addMinuteOnString(oneTimeEnd, -1))) {
                            updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[2] + " instructor Onetime overlaps with his availability in other studio", false);

                            return updateMessageDTO;
                        }


                    }

                    break;

                case 4:

                    if (dto.getFromTime() != null && dto.getToTime() != null && secondaryAvailability.get(i).getThursdayStartTime() != null && secondaryAvailability.get(i).getThursdayEndTime() != null) {

                        if (DateTimeUtil.isInBetweenTime(timef.format(secondaryAvailability.get(i).getThursdayStartTime()), DateTimeUtil.addMinuteOnString(oneTimeStart, 1), timef.format(secondaryAvailability.get(i).getThursdayEndTime()), DateTimeUtil.addMinuteOnString(oneTimeEnd, -1))) {
                            updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[3] + " instructor Onetime overlaps with his availability in other studio", false);

                            return updateMessageDTO;
                        }


                    }

                    break;

                case 5:

                    if (dto.getFromTime() != null && dto.getToTime() != null && secondaryAvailability.get(i).getFridayStartTime() != null && secondaryAvailability.get(i).getFridayEndTime() != null) {

                        if (DateTimeUtil.isInBetweenTime(timef.format(secondaryAvailability.get(i).getFridayStartTime()), DateTimeUtil.addMinuteOnString(oneTimeStart, 1), timef.format(secondaryAvailability.get(i).getFridayEndTime()), DateTimeUtil.addMinuteOnString(oneTimeEnd, -1))) {
                            updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[4] + " instructor Onetime overlaps with his availability in other studio", false);

                            return updateMessageDTO;
                        }


                    }

                    break;


                case 6:

                    if (dto.getFromTime() != null && dto.getToTime() != null && secondaryAvailability.get(i).getSaturdayStartTime() != null && secondaryAvailability.get(i).getSaturdayEndTime() != null) {

                        if (DateTimeUtil.isInBetweenTime(timef.format(secondaryAvailability.get(i).getSaturdayStartTime()), DateTimeUtil.addMinuteOnString(oneTimeStart, 1), timef.format(secondaryAvailability.get(i).getSaturdayEndTime()), DateTimeUtil.addMinuteOnString(oneTimeEnd, -1))) {
                            updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[5] + " instructor Onetime overlaps with his availability in other studio", false);

                            return updateMessageDTO;
                        }


                    }

                    break;
                // Issue no. 2 fixed GSSP-290
                case 7:

                    if (dto.getFromTime() != null && dto.getToTime() != null && secondaryAvailability.get(i).getSundayStartTime() != null && secondaryAvailability.get(i).getSundayEndTime() != null) {

                        if (DateTimeUtil.isInBetweenTime(timef.format(secondaryAvailability.get(i).getSundayStartTime()), DateTimeUtil.addMinuteOnString(oneTimeStart, 1), timef.format(secondaryAvailability.get(i).getSundayEndTime()), DateTimeUtil.addMinuteOnString(oneTimeEnd, -1))) {
                            updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[6] + " instructor Onetime overlaps with his availability in other studio", false);

                            return updateMessageDTO;
                        }


                    }

                    break;


            }
        }

        return updateMessageDTO;
    }

    //GSSP-211 CHANGES
    private UpdateMessageDTO compareWithOneTime(OnetimeDTO dto, List<Onetime> oneTimeList) {


        UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO(null, true);

        DateFormat timef = new SimpleDateFormat("HH:mm");
        DateFormat conver = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH);
        for (Onetime oneTime : oneTimeList) {
            DateTime today = new DateTime(oneTime.getStartTime());
            int dayOfWeek = today.getDayOfWeek();

            String oneTimeStart = timef.format(oneTime.getStartTime());
            String oneTimeEnd = timef.format(oneTime.getEndTime());

            //GCSS-313 Changes:: Added DateTimeUtil.addMinuteOnString method in all the cases.


            if (dayOfWeek == 1 && dto.getFromTime() != null && dto.getToTime() != null && oneTimeStart != null && oneTimeEnd != null && dto.getStartDate().equals(conver.format(oneTime.getStartTime()))) {

                if (DateTimeUtil.isInBetweenTime(oneTimeStart, DateTimeUtil.addMinuteOnString(dto.getFromTime(), 1), oneTimeEnd, DateTimeUtil.addMinuteOnString(dto.getToTime(), -1))) {
                    updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[0] + " instructor OneTime overlaps with his OneTime in other studio", false);

                    return updateMessageDTO;
                }

            }
            if (dayOfWeek == 2 && dto.getFromTime() != null && dto.getToTime() != null && oneTimeStart != null && oneTimeEnd != null && dto.getStartDate().equals(conver.format(oneTime.getStartTime()))) {

                if (DateTimeUtil.isInBetweenTime(oneTimeStart, DateTimeUtil.addMinuteOnString(dto.getFromTime(), 1), oneTimeEnd, DateTimeUtil.addMinuteOnString(dto.getToTime(), -1))) {
                    updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[1] + " instructor OneTime overlaps with his OneTime in other studio", false);

                    return updateMessageDTO;
                }


            }

            if (dayOfWeek == 3 && dto.getFromTime() != null && dto.getToTime() != null && oneTimeStart != null && oneTimeEnd != null && dto.getStartDate().equals(conver.format(oneTime.getStartTime()))) {

                if (DateTimeUtil.isInBetweenTime(oneTimeStart, DateTimeUtil.addMinuteOnString(dto.getFromTime(), 1), oneTimeEnd, DateTimeUtil.addMinuteOnString(dto.getToTime(), -1))) {
                    updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[2] + " instructor OneTime overlaps with his OneTime in other studio", false);

                    return updateMessageDTO;
                }

            }

            if (dayOfWeek == 4 && dto.getFromTime() != null && dto.getToTime() != null && oneTimeStart != null && oneTimeEnd != null && dto.getStartDate().equals(conver.format(oneTime.getStartTime()))) {

                if (DateTimeUtil.isInBetweenTime(oneTimeStart, DateTimeUtil.addMinuteOnString(dto.getFromTime(), 1), oneTimeEnd, DateTimeUtil.addMinuteOnString(dto.getToTime(), -1))) {
                    updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[3] + " instructor OneTime overlaps with his OneTime in other studio", false);

                    return updateMessageDTO;
                }

            }
            if (dayOfWeek == 5 && dto.getFromTime() != null && dto.getToTime() != null && oneTimeStart != null && oneTimeEnd != null && dto.getStartDate().equals(conver.format(oneTime.getStartTime()))) {

                if (DateTimeUtil.isInBetweenTime(oneTimeStart, DateTimeUtil.addMinuteOnString(dto.getFromTime(), 1), oneTimeEnd, DateTimeUtil.addMinuteOnString(dto.getToTime(), -1))) {
                    updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[4] + " instructor OneTime overlaps with his OneTime in other studio", false);

                    return updateMessageDTO;
                }


            }
            if (dayOfWeek == 6 && dto.getFromTime() != null && dto.getToTime() != null && oneTimeStart != null && oneTimeEnd != null && dto.getStartDate().equals(conver.format(oneTime.getStartTime()))) {

                if (DateTimeUtil.isInBetweenTime(oneTimeStart, DateTimeUtil.addMinuteOnString(dto.getFromTime(), 1), oneTimeEnd, DateTimeUtil.addMinuteOnString(dto.getToTime(), -1))) {
                    updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[5] + " instructor OneTime overlaps with his OneTime in other studio", false);

                    return updateMessageDTO;
                }

            }
            //GSSP-290 ISSUE FIX-PART(2)
            if (dayOfWeek == 7 && dto.getFromTime() != null && dto.getToTime() != null && oneTimeStart != null && oneTimeEnd != null && dto.getStartDate().equals(conver.format(oneTime.getStartTime()))) {

                if (DateTimeUtil.isInBetweenTime(oneTimeStart, DateTimeUtil.addMinuteOnString(dto.getFromTime(), 1), oneTimeEnd, DateTimeUtil.addMinuteOnString(dto.getToTime(), -1))) {
                    updateMessageDTO = new UpdateMessageDTO(INSTRUC_WEEKEND_CHOSE[6] + " instructor OneTime overlaps with his OneTime in other studio", false);

                    return updateMessageDTO;
                }

            }
        }


        return updateMessageDTO;
    }


}
