package com.guitarcenter.scheduler.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/")
public class WelcomeController {

    @Value("${spring.profiles.active}")
    private String environment;

 @GetMapping
 public ResponseEntity<String> welcome() {
    // String environment = System.getenv("ENVIRONMENT"); // Replace "ENVIRONMENT" with your variable name
     String responseMessage = "Welcome to the Lessons API v1! Environment: " + (environment != null ? environment : "unknown");
     return ResponseEntity.ok(responseMessage);
 }
}