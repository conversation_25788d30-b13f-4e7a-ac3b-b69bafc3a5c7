package com.guitarcenter.scheduler.controller;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.dto.CustomerDTO;
import com.guitarcenter.scheduler.dto.SearchDTO;
import com.guitarcenter.scheduler.service.CustomerService;
import com.guitarcenter.scheduler.service.SearchService;
import org.apache.commons.lang3.StringUtils;
import org.apache.solr.client.solrj.SolrServerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.*;

@RestController
public class CustomerConstroller {
	
	private static final Logger LOG = LoggerFactory.getLogger(CustomerConstroller.class);
	
	@Autowired
	private SearchService searchService;
	
	@Autowired
	private CustomerService customerService;

	//Request Mapping constants
	private static final String SEARCH_ALL_CUSTOMER_LIST_MAPPING		     = "/searchAllCustomerList";
	private static final String CUSTOMER_QUICK_SERACH_MAPPING		 		 = "/customerQuickSearch";
	private static final String SEARCH_CUSTOMER_MAPPING 					 = "/customerSearch";
	private static final String GET_CUSTOMER_DETAIL_INFO_MAPPING 		     = "/getCustomerDetailInfo";
	
	/**
	 * get customer list when clicking on 'customer tab' in studio calendar.
	 * 
	 * Note: per GCSS-469 the list will be empty by default
	 * 
	 * @return
	 */
	@PostMapping(value = "/searchAllCustomerList", produces = "application/json")
public ResponseEntity<List<SearchDTO>> searchAllCustomers() {
    return ResponseEntity.ok(Collections.emptyList());
}

@PostMapping(value = "/customerSearch", produces = "application/json")
public ResponseEntity<List<SearchDTO>> searchCustomersByCriteria(String searchCriteria, boolean studioCustomers,long siteId) {
   // long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(AppConstants.SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
    if (LOG.isDebugEnabled()) {
        LOG.debug("Began to load customer list by siteId:{} in CustomerConstroller.loadCustomerListBySite", siteId);
    }
    List<String> solrFilter = new ArrayList<>();
    String studioNumber = (String) RequestContextHolder.currentRequestAttributes().getAttribute(AppConstants.STORE_STRING, RequestAttributes.SCOPE_SESSION);
    if (studioCustomers && StringUtils.isNotBlank(studioNumber)) {
        solrFilter.add("+(" + SearchDTO.SOLR_SEARCH_EXTERNAL_ID_FIELD + ":" + studioNumber + " OR " + SearchDTO.SOLR_SEARCH_LOCATION_EXTERNAL_ID_FIELD + ":" + studioNumber + ")*");
    }
    List<SearchDTO> dtos = new LinkedList<>();
    try {
        dtos = searchService.searchCustomersByCriteria(siteId, searchCriteria, solrFilter);
    } catch (SolrServerException e) {
        LOG.error("Caught {} when search customer from solr in CustomerConstroller.loadCustomerListBySite", e);
    }
    if (LOG.isDebugEnabled()) {
        LOG.debug("Got the searched customer list:{} in CustomerConstroller.loadCustomerListBySite", dtos);
    }
    if (dtos != null && !dtos.isEmpty()) {
        for (SearchDTO o : dtos) {
            o.setFullName(o.getFirstName(), o.getLastName());
        }
    }
    return ResponseEntity.ok(dtos);
}

@PostMapping(value = "/customerQuickSearch", produces = "application/json")
public ResponseEntity<List<SearchDTO>> quickSearch(String searchCriteria, boolean studioCustomers) {
    long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(AppConstants.SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
    if (LOG.isDebugEnabled()) {
        LOG.debug("Began to load customer list by searchCriteria:{} and siteId:{} in CustomerConstroller.searchCustomerByNameOrEmail", searchCriteria, siteId);
    }
    List<SearchDTO> dtos = new LinkedList<>();
    List<String> solrFilter = new ArrayList<>();
    String studioNumber = (String) RequestContextHolder.currentRequestAttributes().getAttribute(AppConstants.STORE_STRING, RequestAttributes.SCOPE_SESSION);
    if (studioCustomers && StringUtils.isNotBlank(studioNumber)) {
        solrFilter.add("+(" + SearchDTO.SOLR_SEARCH_EXTERNAL_ID_FIELD + ":" + studioNumber + " OR " + SearchDTO.SOLR_SEARCH_LOCATION_EXTERNAL_ID_FIELD + ":" + studioNumber + ")*");
    }
    try {
        dtos = searchService.quickSearch(siteId, searchCriteria, solrFilter);
    } catch (SolrServerException e) {
        LOG.error("Caught {} in CustomerConstroller.searchCustomerByNameOrEmail");
    }
    if (LOG.isDebugEnabled()) {
        LOG.debug("Got the customer list:{} in CustomerConstroller.searchCustomerByNameOrEmail", dtos);
    }
    if (dtos != null && !dtos.isEmpty()) {
        for (SearchDTO o : dtos) {
            o.setFullName(o.getFirstName(), o.getLastName());
        }
    }
    return ResponseEntity.ok(dtos);
}

@PostMapping(value = "/getCustomerDetailInfo", produces = "application/json")
public ResponseEntity<Map<String, Object>> getCustomerDetailInfo(Long customerId) {
    CustomerDTO c = customerService.getCustomerById(customerId);
    String instrumentName = customerService.getCustomerDetail(c.getRecordId());
    c.setInstrumentType(instrumentName);
    Map<String, Object> map = new HashMap<>();
    map.put("customerInfo", c);
    return ResponseEntity.ok(map);
}
	
}
