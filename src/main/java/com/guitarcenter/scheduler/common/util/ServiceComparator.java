package com.guitarcenter.scheduler.common.util;

import java.io.Serializable;
import java.util.Comparator;

import com.guitarcenter.scheduler.model.Service;

public class ServiceComparator implements Comparator<Service>, Serializable{

    private static final long serialVersionUID = 1L;

	@Override
	public int compare(Service s1, Service s2) {
		if(s1.getServiceId().equals(s2.getServiceId())) {
			return 0;
		} else if(s1.getServiceId() < s2.getServiceId()) {
			return -1;
		} else {
			return 1;
		}
	}

}
