package com.guitarcenter.scheduler.common.util;

import java.io.Serializable;
import java.util.Comparator;

import com.guitarcenter.scheduler.model.Activity;

public class ActivityComparator implements Comparator<Activity>, Serializable{

    private static final long serialVersionUID = 1L;

	@Override
	public int compare(Activity a1, Activity a2) {
		if(a1.getActivityId().equals(a2.getActivityId())) {
			return 0;
		} else if(a1.getActivityId() < a2.getActivityId()) {
			return -1;
		} else {
			return 1;
		}
	}

}