package com.guitarcenter.scheduler.common.util;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.dto.ProfileTimeOffDTO;
import com.guitarcenter.scheduler.dto.TimeOffDTO;
import com.guitarcenter.scheduler.model.ProfileTimeoff;
import com.guitarcenter.scheduler.model.Timeoff;

public class TimeoffUtil{
    private static Logger log = LoggerFactory.getLogger(CalendarUtil.class);
    
	//for story GCSS-590,get the timeoff
	//author :sheldonxie
	public static TimeOffDTO timeOffDateToString(Timeoff timeoff){
		String fromDate = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_DATE).format(timeoff.getStartTime());
		String fromTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_TIME).format(timeoff.getStartTime());
		String toDate = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_DATE).format(timeoff.getEndTime());
		String toTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_TIME).format(timeoff.getEndTime());
		TimeOffDTO timeOffDto = new TimeOffDTO();
		timeOffDto.setFromDate(fromDate);
		timeOffDto.setFromTime(fromTime);
		timeOffDto.setToDate(toDate);
		timeOffDto.setToTime(toTime);
		String startTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_FORMAT).format(timeoff.getStartTime());
		String endTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_FORMAT).format(timeoff.getEndTime());
		timeOffDto.setInstructorId(timeoff.getInstructor().getInstructorId());
		timeOffDto.setTimeoffId(timeoff.getTimeoffId());
		timeOffDto.setTimeOffStartToEnd(startTime+" to "+endTime);
		return timeOffDto;
	}
	
	//GSSP-334 
	public static ProfileTimeOffDTO profileTimeVerifyExistingDate(ProfileTimeoff profileTimeoff,ProfileTimeOffDTO profileTimeoffDto){
		
		ProfileTimeOffDTO profileTimeOffDTO = new ProfileTimeOffDTO();
		String fromDate = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_DATE).format(profileTimeoff.getStartTime());
		
		if(fromDate.equals(profileTimeoffDto.getFromDate())){
			profileTimeOffDTO.setProfiletimeoffId(profileTimeoff.getProfiletimeoffId());
		}
		
		return profileTimeOffDTO;
	}
	
	//GSSP-334 author :Raj
		public static ProfileTimeOffDTO profileTimeOffDateToString(ProfileTimeoff profileTimeoff){
			String fromDate = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_DATE).format(profileTimeoff.getStartTime());
			String fromTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_TIME).format(profileTimeoff.getStartTime());
			String toDate = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_DATE).format(profileTimeoff.getEndTime());
			String toTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_TIME).format(profileTimeoff.getEndTime());
			ProfileTimeOffDTO profileTimeOffDTO = new ProfileTimeOffDTO();
			profileTimeOffDTO.setFromDate(fromDate);
			profileTimeOffDTO.setFromTime(fromTime);
			profileTimeOffDTO.setToDate(toDate);
			profileTimeOffDTO.setToTime(toTime);
			profileTimeOffDTO.setProfileId(profileTimeoff.getProfileId());
			profileTimeOffDTO.setProfiletimeoffId(profileTimeoff.getProfiletimeoffId());
			profileTimeOffDTO.setTimeOffStartToEnd(fromDate+" "+fromTime+" to "+toTime);
			return profileTimeOffDTO;
		}

	public static Date getTimeoffDayTime(Date startTime, String date, String constantTime){
		DateFormat df1 = new SimpleDateFormat(DateTimeUtil.DATETIME_PATTERN_SLASH);
		DateFormat df2 = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH);
		Calendar s1 = Calendar.getInstance();
		Calendar s2 = Calendar.getInstance();
		try {
			s1.setTime(startTime);
			s2.setTime(df1.parse(date+ " " + constantTime));
		} catch (Exception e) {
		    if (log.isInfoEnabled()) {
		        log.info("Caught an time format exception", e);
		    }
		}
		String startTimeFormat = df2.format(s1.getTime());
		if(!startTimeFormat.equals(date)){
			return new Timestamp(s2.getTimeInMillis());
		}
		return startTime;
	}	
	
	
	
	public static String getTimeZoneConverstion(String timeZone){
	
		String resultTimeZone = null;	
		if(timeZone != null){
			Calendar calendar = Calendar.getInstance();       
	        TimeZone tz = TimeZone.getTimeZone(timeZone);
	        calendar.setTimeZone(tz);
	        resultTimeZone = tz.getDisplayName(Boolean.FALSE, 0);
	   }
		return resultTimeZone;

	}
}