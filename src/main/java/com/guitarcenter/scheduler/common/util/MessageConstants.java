/**
 * @Title: MessageConstant.java
 * @Package com.guitarcenter.scheduler.common.util
 * @Description: TODO
 * Copyright: Copyright (c) 2013 
 * Company:
 * 
 * <AUTHOR>
 * @date Aug 27, 2013 11:24:15 AM
 * @version V1.0
 */

package com.guitarcenter.scheduler.common.util;

/**
 * @ClassName: MessageConstant
 * @Description: TODO
 * <AUTHOR>
 * @date Aug 27, 2013 11:24:15 AM
 * 
 */
public class MessageConstants {
	//create appointment validation
	public static final String VALIDATION_INSTRUCTOR_REQUIRED = "instructor is required.";
	public static final String VALIDATION_INSTRUCTOR_AVAILABILITY = "The instructor is invalid with the appointment time specified.";
	public static final String VALIDATION_INSTRUCTOR_ACTIVITY = "The instructor is invalid with the activity specified.";
	public static final String VALIDATION_ROOM_ACTIVITY = "The room is invalid with the activity specified.";
	public static final String VALIDATION_ROOM_AVAILABILITY = "The room is invalid with the appointment time specified.";
	public static final String VALIDATION_ROOM_DISABLED = "The room specified is disabled.";
	public static final String VALIDATION_TIME_ILLEGAL = "The duration with the start time can't overflow to next day.";
	public static final String VALIDATION_DATE_ILLEGAL = "The start date must be before and equal to the end date.";
	public static final String VALIDATION_MINIMUM_DURATION  = "The duration doesn't meet the minimum duration of activity.";
	public static final String VALIDATION_ATTENDEES = "The number of customers can't meet the attendees number of activity.";
	public static final String VALIDATION_CUSTOMERS_NULL = "The customer's name can't be null.";
	public static final String VALIDATION_SERVICE_ACTIVITY = "The service type is invalid with the activity specified.";
	public static final String VALIDATION_INSTRUCTOR_APPOINTMENT = "The instructor has a scheduled appointment with the time specified.";
	public static final String VALIDATION_TIME_NULL = "The duration time can not be null.";
	public static final String VALIDATION_START_TIME_NULL = "No time has been added. Please add a time and try again.";
	public static final String VALIDATION_START_DATE_NULL = "The start date can't be null.";
	public static final String VALIDATION_SEC_EMAIL = "The secondary email format is invalid";
	public static final String VALIDATION_PHONE = "The phone number is invalid";
	//public static final String VALIDATION_SEC_EMAIL_NULL = "The secondary email can't be null.";
	public static final String VALIDATION_START_DATE_ILLEGAL = "The start time can't be early than present time.";
	public static final String VALIDATION_END_DATE_NULL = "The end date can't be null.";
	public static final String VALIDATION_ROOM_NULL = "A room has not has been added. Please select a room and try again.";
	public static final String VALIDATION_ACTIVITY_NULL = "A &REPLASE service type has been added. Please select a &REPLASE type and try again.";
	public static final String VALIDATION_SERVICE_NULL = "A Service type has not been selected. Please select a service type and try again.";
	public static final String VALIDATION_STATUS_INVALID = "Appointment Status is invalid.";
	public static final String VALIDATION_STATUS_API_EMAIL = "Email Id can't be null.";
	public static final String VALIDATION_STATUS_API_REFERENCE_NO = "Order Id can't be null.";
	public static final String VALIDATION_PROFILELOCATION_AVAILABILITY = "The profile location is invalid with the appointment time specified.";
	public static final String VALIDATION_CUSTOMER_APPOINTMENT = "The customer has a scheduled appointment with the time specified.";
	public static final String VALIDATION_CUSTOMER_NULL = "No customer has been added. Please select a customer and try again.";
	//gcss-590
	public static final String VALIDATION_TIMEOFF = "Time off can't be set due to existing appointments.";
	public static final String VALIDATION_END_TIME_NULL = "The end time can't be null.";
	public static final String VALIDATION_VALID_TIME_FORMAT = "Input dates and times must be valid";
	public static final String VALIDATION_DATE_TIME_ILLEGAL = "The start date and start time must be before the end date and end time.";
	//GSSP-334-Updated
	public static final String VALIDATION_TO_SAME_DATE = "The start date and end date must be same.";
	public static final String VALIDATION_TO_PROFILE_DATE_WITH_APPPOINTMENT = "Appointment can't be create due to existing Profile Time off.";
	public static final String VALIDATION_TO_PROFILE_DATE_WITH_APPPOINTMENT_UPDATE = "Appointment cannot be modified due to existing Profile Time off.";
	public static final String VALIDATION_TO_PROFILE_DATE_WITH_APPPOINTMENT_CREATION = "Appointment creation failed";
	
	public static final String VALIDATION_APPOINTMENT_TIMEOFF = "Appointment can't be set due to existing time off.";
	//GSSP-233
	public static final String VALIDATION_START_DATE_ONE_YEAR="Start date should not be greater than one year to the current date";
	//gcss-650
	public static final String VALIDATION_TIMEOFF_ONETIME = "Time off can't be set due to existing One Time availability.";
	public static final String VALIDATION_ONETIME_TIMEOFF = "One time availability can't be set due to existing time off.";
	public static final String VALIDATION_ONETIME_PROFILE = "One time availability can't be set due to conflicts with studio availability.";
	
	//create room template validation
	public static final String VALIDATION_ROOMTYPE_CAN_SPLIT = "The room type selected can not be splited.";
	public static final String VALIDATION_ROOMTEMPLATE_NAME_EXISTS = "A Room Template with the same name already exists. Please enter a different template name.";
	public static final String VALIDATION_ROOMTEMPLATE_NAME_NULL = "The room template's name can't be null.";
	public static final String VALIDATION_ROOMTEMPLATE_TYPE_NULL = "Room type has not been selected, Please select a room type and try again.";
	public static final String VALIDATION_ROOMTEMPLATE_SIZE_NULL = "Room size has not been selected, Please select a room size and try again.";
	//create room validation
	public static final String VALIDATION_ROOM_ROOMTEMPLATE_CAN_SPLIT = "The room template specified can not be splited.";
	public static final String VALIDATION_ROOM_TYPE_CAN_SPLIT = "The room type can not be splited.";
	public static final String VALIDATION_ROOM_TEMPLATE_ENABLED = "The room template specified can not be enabled.";
	public static final String VALIDATION_ROOM_NAME_EXISTS = "The room name already exists.";
	public static final String VALIDATION_ROOM_NAME_NULL = "The room's name can't be null.";
	public static final String VALIDATION_ROOM_SIZE_NULL = "The room's size can't be null.";
	public static final String VALIDATION_ROOM_NUMBER_NULL = "The room's number can't be null.";
	public static final String VALIDATION_ROOM_ROOMTEMPLATE_NULL = "The room's template can't be null.";
	public static final String VALIDATION_ROOM_ENABLED = "The room can not be enabled for the room's template specified is disabled.";
	public static final String VALIDATION_ROOM_ACTIVITY_APPOINTMENT = "The room activities specified can not be update for it has registered a appointment.";
	public static final String VALIDATION_ROOM_CAN_SPLIT = "The room specified can not be splited.";
	public static final String VALIDATION_ROOM_APPOINTMENT_ENABLED = "The room can not be disabled for it has registered a appointment.";
	public static final String VALIDATION_ROOM_APPOINTMENT_DELETE = "The room can not be delete for it has registered a appointment.";
	//279 GSSP-   Message display
	//LES-631 Activity changed from Jump Start to Trial Lesson 
	public static final String VALIDATION_JUMP_START_RECURRENT = "Recurring appointments not allowed for In-Store Trial lesson type.";
	public static final String VALIDATION_JUMP_START = "The lesson type cannot be updated from In-Store Trial Lesson to other.";
	public static final String VALIDATION_JUMP_START_UPDATE="The lesson cannot be update to In-Store Trial Lesson.";
	
	//LES-631 Activity changed from Online Trial Lesson 
	public static final String VALIDATION_ONLINE_TRIAL_RECURRENT = "Recurring appointments not allowed for Online Trial lesson type.";
	public static final String VALIDATION_ONLINE_TRIAL = "The lesson type cannot be updated from Online Trial Lesson to other.";
	public static final String VALIDATION_ONLINE_TRIAL_UPDATE="The lesson cannot be update to Online Trial Lesson.";
	public static final String VALIDATION_INSTRUCTOR = "Instructor is Terminated. Select Different Instructor.";
}

