/**
 * @Title: SystemUtil.java
 * @Package com.guitarcenter.scheduler.common.util
 * Copyright: Copyright (c) 2013 
 * Company:
 * 
 * <AUTHOR>
 * @date Sep 12, 2013 2:33:27 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.common.util;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.dto.InstructorInfoDTO;
import com.guitarcenter.scheduler.dto.RoomDTO;
import com.guitarcenter.scheduler.dto.ServiceDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.Service;
import com.guitarcenter.scheduler.model.enums.RequiresInstructor;


/**
 * @ClassName: SystemUtil
 * <AUTHOR>
 * @date Sep 12, 2013 2:33:27 PM
 *
 */
public class SystemUtil implements AppConstants {
	
	 private static Logger log = LoggerFactory.getLogger(SystemUtil.class);
	
	public static final String SPLIT_COMMA = ",";
	public static final String DIAPLAY_NULL = "N/A";
	public static final String DIAPLAY_N = "flase";
	public static final String DIAPLAY_SELECT_ON = "on";
	//Changes made for GSSP-157
	public static final int VALIDATION_SCOPE_MONTH = 36;
	// Variable added to fix endDate issue on recurring appointments //GSSP-264 changed from 36 to 12
	//GSSP-336 :: Corrected to 11 to 12 
	public static final int VALIDATION_SCOPE_MONTH_1 = 12;
	public static final int LAST_MONTH_OF_YEAR = 12;
	private static final String DASH_STRING	= " - ";
	public static final String ROOM_TYPE_LESSON = "Lesson";
	public static final String REPLACE_CONDITION = "AND a.appointment_id NOT IN ( &PARAMETER )";
	public static final String REPLACE_TEMP_SCRIPTS = "select &PARAMETER1 s, &PARAMETER2 e from dual";
    public static final String APPOINTMENT_START_TIME = "09:00";
    public static final String APPOINTMENT_END_TIME = "23:59";
    public static String pattern = "\\d{10}|(?:\\d{3}-){2}\\d{4}|\\(\\d{3}\\)\\d{3}-?\\d{4}";
    public static String regex = "^\\(?([0-9]{3})\\)?[-.\\s]?([0-9]{3})[-.\\s]?([0-9]{4})$";
    
      
    
    public static String phoneFormate(String s) {
    	
    	Pattern pattern = Pattern.compile(regex);
    	Matcher matcher = pattern.matcher(s);	
        if(matcher.matches())
        {
          // System.out.println(matcher.replaceFirst("($1) $2-$3"));
        	return matcher.replaceFirst("($1) $2-$3");
        }
		return s;
    	
    }
	
	public static List<Long> getIdList(String s) {
		List<Long> ids = new ArrayList<Long>();
		if(null != s && 0 != s.length()) {
			String[] arr = s.split(SPLIT_COMMA);
			for(int i=0; i<arr.length; i++) {
				ids.add(Long.parseLong(arr[i]));
			}
		}
		return ids;
	}
	
	public static List<Long> getIdListByServiceDTO(Collection<ServiceDTO> sList){
		List<Long> ids = new ArrayList<Long>();
		for(ServiceDTO dto : sList) {
			ids.add(dto.getServiceId());
		}
		return ids;
	}
	
	public static List<Long> getIdListByService(Collection<Service> sSet){
		List<Long> ids = new ArrayList<Long>();
		for(Service s : sSet) {
			ids.add(s.getServiceId());
		}
		return ids;
	}
	
	public static List<Long> getIdListByActivity(Collection<Activity> sActivity){
		List<Long> ids = new ArrayList<Long>();
		for(Activity a : sActivity) {
			ids.add(a.getActivityId());
		}
		return ids;
	}
	
	public static String getIdParamStringByIdList(Collection<Long> list){
        StringBuilder idParameter = new StringBuilder("");
		for(Long id : list) {
			idParameter.append(id);
			idParameter.append(SPLIT_COMMA);
		}
        if (idParameter.length() > 1) {
        	idParameter.deleteCharAt(idParameter.length() - 1);
        }
		return idParameter.toString();
	}
	
	public static Long[] getIdArray(String s) {
		Long[] ids = null;
		if(null != s && 0 != s.length()) {
			String[] arr = s.split(SPLIT_COMMA);
			ids = new Long[arr.length];
			for(int i=0; i<arr.length; i++) {
				ids[i] = Long.parseLong(arr[i]);
			}
		}
		return ids;
	}

	/**
	 * Cast RequiresInstructor to String.
	 * @param requiresInstructor RequiresInstructor instance
	 * @return String value of if require instructor
	 */
	public static String getRequiresInscructor(final RequiresInstructor requires) {
	    return requires.getValue();
	}

	/**
	 * Build the Avialibility Map consisted of from Sunday to Saturday from an Avialibility object.
	 * 
	 * @param availibility
	 * @return
	 */
	public Map<String, Object> buildAvialibilityMap(Availability availibility) {
		Map<String, Object> map = new HashMap<String, Object>();
		SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");

		String sundayStartTimeString = StringUtils.EMPTY;
		String sundayEndTimeString = StringUtils.EMPTY;
		
		String mondayStartTimeString = StringUtils.EMPTY;
		String mondayEndTimeString = StringUtils.EMPTY;
		
		String tuesdayStartTimesString = StringUtils.EMPTY;
		String tuesdayEndTimeString = StringUtils.EMPTY;
		
		String wednesdayStartTimeString = StringUtils.EMPTY;
		String wednesdayEndTimeString = StringUtils.EMPTY;
		
		String thursdayStartTimeString = StringUtils.EMPTY;
		String thursdayEndTimeString = StringUtils.EMPTY;
		
		String fridayStartTimeString = StringUtils.EMPTY;
		String fridayEndTimeString = StringUtils.EMPTY;
		
		String saturdayStartTimeString = StringUtils.EMPTY;
		String saturdayEndTimeString = StringUtils.EMPTY;
		
		Date sundayStartTime = availibility.getSundayStartTime();
		Date sundayEndTime = availibility.getSundayEndTime();
		if(null != sundayStartTime) {
			sundayStartTimeString = this.buildTimeString(sdf.format(sundayStartTime));
		}
		if(null != sundayEndTime) {
			sundayEndTimeString = this.buildTimeString(sdf.format(sundayEndTime));
		}
		map.put(SUNDAY_AVAILIBILITY_TIME, sundayStartTimeString + DASH_STRING + sundayEndTimeString);
		
		Date mondayStartTime = availibility.getMondayStartTime();
		Date mondayEndTime = availibility.getMondayEndTime();
		if(null != mondayStartTime) {
			mondayStartTimeString = this.buildTimeString(sdf.format(mondayStartTime));
		}
		if(null != mondayEndTime) {
			mondayEndTimeString = this.buildTimeString(sdf.format(mondayEndTime));
		}
		map.put(MONDAY_AVAILIBILITY_TIME, mondayStartTimeString + DASH_STRING + mondayEndTimeString);
		
		Date tuesdayStartTime = availibility.getTuesdayStartTime();
		Date tuesdayEndTime = availibility.getTuesdayEndTime();
		if(null != tuesdayStartTime) {
			tuesdayStartTimesString = this.buildTimeString(sdf.format(tuesdayStartTime));
		}
		if(null != tuesdayEndTime) {
			tuesdayEndTimeString = this.buildTimeString(sdf.format(tuesdayEndTime));
		}
		map.put(TUESDAY_AVAILIBILITY_TIME, tuesdayStartTimesString + DASH_STRING + tuesdayEndTimeString);
		
		Date wednesdayStartTime = availibility.getWednesdayStartTime();
		Date wednesdayEndTime = availibility.getWednesdayEndTime();
		if(null != wednesdayStartTime) {
			wednesdayStartTimeString = this.buildTimeString(sdf.format(wednesdayStartTime));
		}
		if(null != wednesdayEndTime) {
			wednesdayEndTimeString = this.buildTimeString(sdf.format(wednesdayEndTime));
		}
		map.put(WEDNESDAY_AVAILIBILITY_TIME, wednesdayStartTimeString + DASH_STRING + wednesdayEndTimeString);
		
		Date thursdayStartTime = availibility.getThursdayStartTime();
		Date thursdayEndTime = availibility.getThursdayEndTime();
		if(null != thursdayStartTime) {
			thursdayStartTimeString = this.buildTimeString(sdf.format(thursdayStartTime));
		}
		if(null != thursdayEndTime) {
			thursdayEndTimeString = this.buildTimeString(sdf.format(thursdayEndTime));
		}
		map.put(THURSDAY_AVAILIBILITY_TIME, thursdayStartTimeString + DASH_STRING + thursdayEndTimeString);
		
		Date fridayStartTime = availibility.getFridayStartTime();
		Date fridayEndTime = availibility.getFridayEndTime();
		if(null != fridayStartTime) {
			fridayStartTimeString = this.buildTimeString(sdf.format(fridayStartTime));
		}
		if(null != fridayEndTime) {
			fridayEndTimeString = this.buildTimeString(sdf.format(fridayEndTime));
		}
		map.put(FRIDAY_AVAILIBILITY_TIME, fridayStartTimeString + DASH_STRING + fridayEndTimeString);
		
		Date saturdayStartTime = availibility.getSaturdayStartTime();
		Date saturdayEndTime = availibility.getSaturdayEndTime();
		if(null != saturdayStartTime) {
			saturdayStartTimeString = this.buildTimeString(sdf.format(saturdayStartTime));
		}
		if(null != saturdayEndTime) {
			saturdayEndTimeString = this.buildTimeString(sdf.format(saturdayEndTime));
		}
		map.put(SATURDAY_AVAILIBILITY_TIME, saturdayStartTimeString + DASH_STRING + saturdayEndTimeString);
		
		return map;
	}
	
	/**
	 * Converting the time to the format of ' x:xx A(P)M : x:xx A(P)M '
	 * 
	 * @param time the time string with format 'HH:mm'
	 * @return
	 */
	private String buildTimeString(String time) {
		String str = "";
		if(null != time && 0 != time.length()) {
			if(time.contains(SPLITOR_COLON)) {
				int hour = Integer.parseInt(time.split(SPLITOR_COLON)[0]);
				int minutes = Integer.parseInt(time.split(SPLITOR_COLON)[1]);
				String minutesStr = (minutes < 10) ? ("0" + minutes) : String.valueOf(minutes);
				if(hour < 12) {
					str = hour + SPLITOR_COLON + minutesStr + " AM"; 
				} else if(12 == hour){
					str = hour + SPLITOR_COLON + minutesStr + " PM";
				} else {
					str = (hour - 12) + SPLITOR_COLON + minutesStr + " PM";
				}
			}
		}
		return str;
	}
	
	//Remove  name's first and last space, and retains intermediate spaces 
	public static String nameRegular(final String name){
	    String returnStr;
		if(StringUtils.isBlank(name)){
			returnStr = String.valueOf((char)32);
		}else{
		    returnStr = name.trim().replace(String.valueOf((char)32), "&nbsp;");
		}
		return returnStr;
	}
	
	public static String createName(final String name){
	    String returnStr;
		if(StringUtils.isBlank(name)){
		    returnStr = String.valueOf((char)32);
		}else{
		    returnStr = name.trim().replaceAll(" {2,}", " ");
		}
		return returnStr;
	}
	
	/**
	 *<p>Checks if a List is empty (has no element) or null.</p>
	 * 
	 * @param list the List to check, may be null
	 * @return <code>true</code> if the List is null or empty
	 */
	public static <T> Boolean listIsBlank(final List<T> list){
	    Boolean result;
	    if(list == null || list.isEmpty()){
	        result = true;
	    }else{
	        result = false;
	    }
	    return result;
	}
	
	/**
	 * Update the endTime of to 23:59:59 of current day appointment if the endTime from DTO is 12:00 AM
	 * 
	 * @param endTime
	 * @return
	 */
	public static Date getEndTimeIfOnMidnight(Date endTime) {
		DateTime afterAddDurationDate = new DateTime(endTime);
		int afterHour = afterAddDurationDate.getHourOfDay();
		if(0 == afterHour) {
			afterAddDurationDate = afterAddDurationDate.minusSeconds(1);
		}
		return afterAddDurationDate.toDate();
	}

	/**
	 * Get ids string seperated by comma from a instructor list
	 * 
	 * @param list
	 * @return
	 */
	public static String getInstructorIdsString(List<InstructorInfoDTO> list) {
		if(null == list || list.isEmpty()) {
			return StringUtils.EMPTY;
		}
		StringBuffer sb = new StringBuffer();
		for(InstructorInfoDTO dto : list) {
			sb.append(dto.getInstructorId().toString()).append(",");
		}
		sb = sb.deleteCharAt(sb.length() - 1);
		return sb.toString();
	}
	
	/**
	 * Get ids string seperated by comma from a room list
	 * 
	 * @param list
	 * @return
	 */
	public static String getRoomIdsString(List<RoomDTO> list) {
		if(null == list || list.isEmpty()) {
			return StringUtils.EMPTY;
		}
		StringBuffer sb = new StringBuffer();
		for(RoomDTO dto : list) {
			sb.append(dto.getRoomId().toString()).append(",");
		}
		sb = sb.deleteCharAt(sb.length() - 1);
		return sb.toString();
	}
	
	/**
	 * Get ids string seperated by comma from a activity list
	 * 
	 * @param list
	 * @return
	 */
	public static String getActivityIdsString(List<ActivityDTO> list) {
		if(null == list || list.isEmpty()) {
			return StringUtils.EMPTY;
		}
		StringBuffer sb = new StringBuffer();
		for(ActivityDTO dto : list) {
			sb.append(dto.getActivityId().toString()).append(",");
		}
		sb = sb.deleteCharAt(sb.length() - 1);
		return sb.toString();
	}
	
	/**
	 * Get ids string seperated by comma from a service list
	 * 
	 * @param list
	 * @return
	 */
	public static String getServiceIdsString(List<Service> list) {
		if(null == list || list.isEmpty()) {
			return StringUtils.EMPTY;
		}
		StringBuffer sb = new StringBuffer();
		for(Service s : list) {
			sb.append(s.getServiceId().toString()).append(",");
		}
		sb = sb.deleteCharAt(sb.length() - 1);
		return sb.toString();
	}
	//Log for conflict appointment in create appointment -GSSP 299
	 public static  String getHostAddress() {
	        try {
				return  InetAddress.getLocalHost().getHostAddress();
			} catch (UnknownHostException e) {
				 if (log.isInfoEnabled()) {
				        log.info("Caught an exception while getting IPAddress at getIpaddress", e);
				    }
					return null;
			}
	        		
	    }
}
