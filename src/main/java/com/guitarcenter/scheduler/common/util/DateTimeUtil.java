package com.guitarcenter.scheduler.common.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.util.*;

import com.guitarcenter.scheduler.common.exceptions.BizRuntimeException;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.dao.criterion.util.TimeIntervalComparator;
import com.guitarcenter.scheduler.dto.TimeIntervalDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAvailableHoursDTO;


public class DateTimeUtil {

    private static final Logger LOG = LoggerFactory.getLogger(DateTimeUtil.class);

    /**
     * The token to seperate the year and month
     */
    private static final String DATE_SEPERATOR_TOKEN = "[.]";

    public static final String DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_PATTERN = "yyyy-MM-dd";
    public static final String DATE_PATTERN_SLASH = "MM/dd/yyyy";
    public static final String DATE_PATTERN_SLASHS = "MM/dd/yy";
    public static final String DATETIME_PATTERN_SLASH = "MM/dd/yyyy HH:mm";
    public static final String TIME_FORMAT_HH_MM = "HH:mm";
    public static final String TIME_FORMAT_HH24_MI = "HH24:MI";
    public static final String TIME_FORMAT_MM_DD_YYYY_HH24_MI = "MM/dd/yyyy HH24:MI";
    public static final String TIME_OFF_DATE_FORMAT = "hh:mma MM/dd/yyyy";
    
    public static final String TIME_OFF_DATE_TIME = "hh:mma";
    public static final String TIME_OFF_DATE_DATE ="MM/dd/yyyy";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    //Added for Phase2_LES-27 Changes - 
    public static final String HOURS_PATTERN_AM = "HH:mma";
    public static final String HOURS_PATTERN	= "HH:mm";
    public static final String HOURS_SEC_PATTERN	= "HH:mm:ss";
    
    //Added for phase2 UPDATE LESSON SERVICE-LES-7 
    public static final String TIME_OFF_DATE_TIME2 = "hh:mm a";
    public static final String DATETIME_PATTERN_HYPHEN = "MM-dd-yyyy";    
    public static final String DATETIME_PATTERN2="yyyy-dd-MM HH:mm:ss";
    //Added for GSSP-200 Date pattern change
    public static final String DATE_PATTERN_HIPHEN = "dd-MMM-yyyy";
    //Added for GSSP-282 Changes.
    public static final String DATE_PATTERN_HIPHEN_MONTH = "dd-MMM-yy";
    //added for gssp-228
    public static final String DATETIME_WITH_TIMEZONE1="dd-MMM-yy hh.mm.ss aa";
    
    
    /**
     * As we know the time line is from START_CLOCK AM to END_CLOCK PM now
     */
    private static final int START_CLOCK = 5;
    private static final int END_CLOCK = 24;

    /**
     * get the days of the specified date, the format of date is: yyyy.mm
     *
     * @param strMonth
     * @return
     */
    public static int getDaysInAMonth(String strMonth) {
        String[] arr = strMonth.split(DATE_SEPERATOR_TOKEN);
        Calendar cal = new GregorianCalendar(Integer.parseInt(arr[0]), Integer.parseInt(arr[1]) - 1, 1);// Create a calendar object of the desired month
        int days = cal.getActualMaximum(Calendar.DAY_OF_MONTH);// Get the number of days in specified month
        return days;
    }

    /**
     * get the dayofweek by specified date whose format is: yyyy.mm
     *
     * @param strMonth
     * @return
     */
    public static int getWeekOfFirstDay(String strMonth) {
        String[] arr = strMonth.split(DATE_SEPERATOR_TOKEN);
        Calendar xmas = new GregorianCalendar(Integer.parseInt(arr[0]), Integer.parseInt(arr[1]) - 1, 1);
        int dayOfWeek = xmas.get(Calendar.DAY_OF_WEEK) - 1;
        return dayOfWeek;
    }
    
    /**
     * get the weekday by date whose format is MM/dd/yyyy
     * getWeekdayByDate
     * 
     *
     * @Title: getWeekdayByDate
     * @Description: 
     * @param @param date
     * @param @return
     * @return String
     * @throws
     */
    public static String getWeekdayByDate(String date){
    	Calendar calendar = Calendar.getInstance();
        SimpleDateFormat df = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_DATE);
    	try {
			calendar.setTime(df.parse(date));
		} catch (ParseException e) {
            LOG.error("Caught an exception , so used current date", e);
            calendar.setTime(new Date());
		}
    	return getDaysOfWeekList().get(calendar.get(Calendar.DAY_OF_WEEK)-1);
    }

    /**
     * a week days list
     *
     * @return
     */
    public static List<String> getDaysOfWeekList() {
        String[] DAY_OF_WEEK_ARRAY = {"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};
        return Arrays.asList(DAY_OF_WEEK_ARRAY);
    }

    /**
     * get the time line list
     *
     * @return
     */
    public static List<Integer> getTimeLineList() {
        List<Integer> list = new LinkedList<Integer>();
        for (int i = START_CLOCK; i <= END_CLOCK; i++) {
            list.add(i);
        }
        return list;
    }
    
    //Checking the In between time
    public  static boolean isInBetweenTime(String timeOneStart, String timeTwoStart, String timeOneEnd, String timeTwoEnd)
    {
    	//313 below change added for null check.
  	  if((null!= timeOneStart) && (null!= timeTwoStart) && (null!=  timeOneEnd) && (null!= timeTwoEnd)){
  	  if(((timeOneStart.compareTo(timeTwoStart) >= 0) && timeOneStart.compareTo(timeTwoEnd) <= 0 ) || 
  			    ((timeOneEnd.compareTo(timeTwoStart) >= 0) && timeOneEnd.compareTo(timeTwoEnd) <= 0 ) ||
  			    ((timeOneStart.compareTo(timeTwoStart) <= 0) && timeOneEnd.compareTo(timeTwoEnd) >= 0 ))
  			
  			    {
  		  
  		  			return true;
  			    }
  	  }
  	  return false;
    }
    

    /**
     * Genarating a time quantum from startDateStr to endDate Str
     * E.g startDate:10/01/2013, enDate:10/04/2013
     * then the list would be 10/01/2013,10/02/2013,10/03/2013,10/04/2013
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public List<String> generateDatePerids(Date startDate,
                                           Date endDate) {

        List<String> list = new LinkedList<String>();

        list.add(new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(startDate));

        Calendar c = Calendar.getInstance();
        c.setTime(startDate);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);

        Calendar c2 = Calendar.getInstance();
        c2.setTime(endDate);
        c2.set(Calendar.HOUR_OF_DAY, 0);
        c2.set(Calendar.MINUTE, 0);
        c2.set(Calendar.SECOND, 0);
        endDate = c2.getTime();

        SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");

        while (!sdf.format(c.getTime()).trim().equals(sdf.format(endDate).trim())) {
            c.add(Calendar.DATE, 1);
            list.add(sdf.format(c.getTime()));
        }

        return list;
    }

    public static Date handleEndDate(String endDate) {
        DateTime result = null;
        if (endDate == null || endDate.trim().isEmpty()) {
            result = DateTime.now();
        } else {
            try {
                result = DateTime.parse(endDate, DateTimeFormat.forPattern(DateTimeUtil.DATE_PATTERN_SLASH));
            } catch (IllegalArgumentException e) {
                LOG.error("Caught an exception , so used current date", e);
                result = DateTime.now();
            } catch (UnsupportedOperationException e) {
                LOG.error("Caught an exception , so used current date", e);
                result = DateTime.now();
            }
        }
        return result.withTime(23, 59, 59, 0).toDate();
    }
    //Added for GSSP-200
    public List<String> generateDatePeriods(Date startDate,
            Date endDate) {

List<String> list = new LinkedList<String>();

list.add(new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_HIPHEN).format(startDate));

Calendar c = Calendar.getInstance();
c.setTime(startDate);
c.set(Calendar.HOUR_OF_DAY, 0);
c.set(Calendar.MINUTE, 0);
c.set(Calendar.SECOND, 0);

Calendar c2 = Calendar.getInstance();
c2.setTime(endDate);
c2.set(Calendar.HOUR_OF_DAY, 0);
c2.set(Calendar.MINUTE, 0);
c2.set(Calendar.SECOND, 0);
endDate = c2.getTime();

SimpleDateFormat sdf = new SimpleDateFormat("dd-MMM-yyyy");

while (!sdf.format(c.getTime()).trim().equals(sdf.format(endDate).trim())) {
c.add(Calendar.DATE, 1);
list.add(sdf.format(c.getTime()));
}

return list;
}


public static Date handlesEndDate(String endDate) {
DateTime result = null;
if (endDate == null || endDate.trim().isEmpty()) {
result = DateTime.now();
} else {
try {
result = DateTime.parse(endDate, DateTimeFormat.forPattern(DateTimeUtil.DATE_PATTERN_HIPHEN));
} catch (IllegalArgumentException e) {
LOG.error("Caught an exception , so used current date", e);
result = DateTime.now();
} catch (UnsupportedOperationException e) {
LOG.error("Caught an exception , so used current date", e);
result = DateTime.now();
}
}
return result.withTime(23, 59, 59, 0).toDate();
}

    /**
     * Handle the input endDate
     *
     * @param startDate
     * @return
     */
    public static Date handleStartDate(String startDate) {
        DateTime result = null;
        if (startDate == null || startDate.trim().isEmpty()) {
            result = DateTime.now();
        } else {
            try {
                result = DateTime.parse(startDate, DateTimeFormat.forPattern(DateTimeUtil.DATE_PATTERN_SLASH));
            } catch (IllegalArgumentException e) {
                LOG.error("Caught an exception , so used current date", e);
                result = DateTime.now();
            } catch (UnsupportedOperationException e) {
                LOG.error("Caught an exception , so used current date", e);
                result = DateTime.now();
            }
        }
        return result.withTime(0, 0, 0, 0).toDate();
    }



    
    //added for GSSP-200
    public static Date handlesStartDate(String startDate) {
        DateTime result = null;
        if (startDate == null || startDate.trim().isEmpty()) {
            result = DateTime.now();
        } else {
            try {
                result = DateTime.parse(startDate, DateTimeFormat.forPattern(DateTimeUtil.DATE_PATTERN_HIPHEN));
            } catch (IllegalArgumentException e) {
                LOG.error("Caught an exception , so used current date", e);
                result = DateTime.now();
            } catch (UnsupportedOperationException e) {
                LOG.error("Caught an exception , so used current date", e);
                result = DateTime.now();
            }
        }
        return result.withTime(0, 0, 0, 0).toDate();
    }
    
    //GSSP-272
    public static DateTime handleStartDateTime(String startDate) {
        DateTime result = null;
        if (startDate == null || startDate.trim().isEmpty()) {
            result = DateTime.now();
        } else {
            try {
                result = DateTime.parse(startDate, DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN));
            } catch (IllegalArgumentException e) {
                LOG.error("Caught an exception , so used current date", e);
                result = DateTime.now();
            } catch (UnsupportedOperationException e) {
                LOG.error("Caught an exception , so used current date", e);
                result = DateTime.now();
            }
        }
        return result.withTimeAtStartOfDay();
    }
    
    //GSSP-272
    public static DateTime handleendDateTime(String startDate) {
        DateTime result = null;
        if (startDate == null || startDate.trim().isEmpty()) {
            result = DateTime.now();
        } else {
            try {
                result = DateTime.parse(startDate, DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN));
            } catch (IllegalArgumentException e) {
                LOG.error("Caught an exception , so used current date", e);
                result = DateTime.now();
            } catch (UnsupportedOperationException e) {
                LOG.error("Caught an exception , so used current date", e);
                result = DateTime.now();
            }
        }
        return result.millisOfDay().withMaximumValue();
    }
    
    
	public static List<TimeIntervalDTO> concatTime(List<TimeIntervalDTO> timeList){
		if (timeList == null || timeList.isEmpty()) {
			return null;
		}
		Collections.sort(timeList, new TimeIntervalComparator());
    	for (int i = 0; i < timeList.size() - 1; i++) {
			int j = i + 1;
			if (timeList.get(i).getEndTime().isAfter(timeList.get(j).getStartTime())|| timeList.get(i).getEndTime().isEqual(timeList.get(j).getStartTime())) {
				if (timeList.get(i).getEndTime().isAfter(timeList.get(j).getEndTime())|| timeList.get(i).getEndTime().isEqual(timeList.get(j).getEndTime())) {
					timeList.remove(j);
					timeList.add(j, timeList.get(i));
				} else {
					timeList.get(j).setStartTime(timeList.get(i).getStartTime());
				}
				timeList.remove(i);
				i--;
			} else {
				i++;
			}
		}
		return timeList;
	}
	

	//GSSP-313 Write the addMinute method 
	 public static Date addMinute(Date actualDate,Integer noOfMinutes) {
	
		 if (actualDate != null) {
	        	 Calendar cal = Calendar.getInstance();
		    	 cal.setTime(actualDate);
		    	 cal.add(Calendar.MINUTE, noOfMinutes);
		    	return cal.getTime();
	        } 
	        return actualDate;
	    }
	 
	/*GSSP-313 Changes,Input String as Time format convert to min and 
	 Add/sub one minute and return time in String format*/ 
	 public static String addMinuteOnString(String actualDate,int minVal) {
			String timeValue=null;
	 		try {
				if(actualDate!=null){
					int intMintueVal = (Integer.parseInt(actualDate.split(":")[0])* 60)+Integer.parseInt(actualDate.split(":")[1]);
					intMintueVal = intMintueVal+minVal;
				    Long longOfInt= new Long(intMintueVal);
				    timeValue =LocalTime.MIN.plus(Duration.ofMinutes(longOfInt)).toString();
				    return timeValue;
				}
			} catch (NumberFormatException e) {
				LOG.error("Caught an exception ,  converting number", e);
			}
	 		catch (Exception e) {
				// TODO Auto-generated catch block
	 			LOG.error("Caught an exception , converting number", e);
			}
			return timeValue;
	 }
	 
	 //POS-808 Changes.
	 public static Date stringToDate(String dateInString,String dateFormat){
		 
		  SimpleDateFormat formatter = new SimpleDateFormat(dateFormat);
		  
	      	   try {

	            Date date = formatter.parse(dateInString); 
	            return date;

	        } catch (Exception e) {
	        	LOG.error("Caught an exception , converting number", e);
	        	return null;
	        }

	    }

    public static Date handleStartDateTime(Date date){
        if(date == null){
            throw new RuntimeException("date is null");
        }
        DateTime jodaDateTime = castToJodaDateTime(date);
        return jodaDateTime.withTimeAtStartOfDay().toDate();
    }


    //GSSP-272
    public static Date handleEndDateTime(Date endDate) {
        if(endDate == null){
            throw new RuntimeException("date is null");
        }
        DateTime jodaDateTime = castToJodaDateTime(endDate);
        return jodaDateTime.millisOfDay().withMaximumValue().toDate();
    }

    private static DateTime castToJodaDateTime(Date date){
        DateFormat format = new SimpleDateFormat(DateTimeUtil.DATETIME_PATTERN);
        String startDateStr = format.format(date);
        DateTime result = null;
        try {
            result = DateTime.parse(startDateStr, DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN));
        } catch (Exception e){
            LOG.error("format date time failed",e);
            throw new BizRuntimeException("cast to joda datetime failed");
        }
        return result;
    }

    //MOVING some util methods from services to here
    public static boolean isStartDateAfterCurrentDate(Date dt,String tz){ boolean flag=false;
	 String DATE_FORMAT = "dd-M-yyyy hh:mm:ss a";
	 if(null != dt && null!= tz){

		 	//Timezone ZonedDateTime
			      DateFormat formatter = new SimpleDateFormat(DATE_FORMAT);
			      String formattedDate = formatter.format(dt);
		          LocalDateTime ldt = LocalDateTime.parse(formattedDate, java.time.format.DateTimeFormatter.ofPattern(DATE_FORMAT));
		          ZoneId timeZoneZodeId = ZoneId.of(tz);
		          ZonedDateTime timeZonedDateTime = ldt.atZone(timeZoneZodeId);
		          
		          java.time.Instant ins = java.time.Instant.now(); 
		          ZoneId z = ZoneId.of(tz);
		          ZonedDateTime currentUTCTime = ins.atZone( z );
			    
		          if(timeZonedDateTime.isAfter((currentUTCTime))){
						 flag= true;
				}
	 }						
	return flag;
	}
	 
	 public static  boolean isStartDateBeforeCurrentDate(Date dt,String tz){
		 
		 boolean flag=false;
		 String DATE_FORMAT = "dd-M-yyyy hh:mm:ss a";
		 if(null != dt && null!= tz){
	
			 	//Timezone ZonedDateTime
				      DateFormat formatter = new SimpleDateFormat(DATE_FORMAT);
				      String formattedDate = formatter.format(dt);
			          LocalDateTime ldt = LocalDateTime.parse(formattedDate, java.time.format.DateTimeFormatter.ofPattern(DATE_FORMAT));
			          ZoneId timeZoneZodeId = ZoneId.of(tz);
			          ZonedDateTime timeZonedDateTime = ldt.atZone(timeZoneZodeId);
			          
			          java.time.Instant ins = java.time.Instant.now(); 
			          ZoneId z = ZoneId.of(tz);
			          ZonedDateTime currentUTCTime = ins.atZone( z );
				    
			          if(timeZonedDateTime.isBefore((currentUTCTime))){
							 flag= true;
					}
		 }						
	 	return flag;
	 	
	 }

    public static  boolean isValidZoomLinkTimeStamp1(Date dt, String tz, Date utcStartTime) {
        boolean flag=false;
        String DATE_FORMAT = "dd-M-yyyy hh:mm:ss a";

        if(null != dt && null!= tz && null != utcStartTime){

            //Timezone ZonedDateTime
            DateFormat formatter = new SimpleDateFormat(DATE_FORMAT);
            String formattedDate = formatter.format(dt);
            LocalDateTime ldt = LocalDateTime.parse(formattedDate, java.time.format.DateTimeFormatter.ofPattern(DATE_FORMAT));
            ZoneId timeZoneZodeId = ZoneId.of(tz);
            ZonedDateTime timeZonedDateTime = ldt.atZone(timeZoneZodeId);

            //UTC ZonedDateTime
            String utcformattedDate = formatter.format(utcStartTime);
            LocalDateTime utcldt = LocalDateTime.parse(utcformattedDate, java.time.format.DateTimeFormatter.ofPattern(DATE_FORMAT));
            ZoneId utcZodeId = ZoneId.of("UTC");
            ZonedDateTime utcDateTime = utcldt.atZone(utcZodeId);
            //TimeZone to UTC Conversation.
            ZonedDateTime  timeZonedDateTimetoUTC =  timeZonedDateTime.withZoneSameInstant(utcZodeId);

            if(timeZonedDateTimetoUTC.isEqual(utcDateTime)){
                flag = true;
            }
        }
        return flag;
    }

    public static InstructorAvailableHoursDTO getMinAndMaxTimes(List<InstructorAvailableHoursDTO> timePeriodList){
    	
    	org.joda.time.LocalTime minTime = new org.joda.time.LocalTime(23, 59, 59, 999);
    	org.joda.time.LocalTime maxTime = new org.joda.time.LocalTime(0, 0, 0, 0);
          
          for (InstructorAvailableHoursDTO timePeriod : timePeriodList) {
              org.joda.time.LocalTime startTime = timePeriod.getAppointmentStartTime();
              org.joda.time.LocalTime endTime = timePeriod.getAppointmentEndTime();
              if (startTime.isBefore(minTime)) {
                  minTime = startTime;
              }
              if (endTime.isAfter(maxTime)) {
                  maxTime = endTime;
              }
          }
          
          InstructorAvailableHoursDTO instructorAvailableHoursminMaxDTO = new InstructorAvailableHoursDTO(minTime,maxTime);
          
         
          
          System.out.println("startTime"+minTime);
          System.out.println("endTime"+maxTime);
          
          return instructorAvailableHoursminMaxDTO;
          
    }

}
