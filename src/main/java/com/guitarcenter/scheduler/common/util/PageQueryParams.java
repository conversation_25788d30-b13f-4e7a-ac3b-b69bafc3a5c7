package com.guitarcenter.scheduler.common.util;

import com.guitarcenter.scheduler.common.exceptions.BizRuntimeException;

/**
 * use for json converting encapsulation
 *
 * @Date 4/27/2020 6:15 PM
 * <AUTHOR>
 **/
public class PageQueryParams {


    Integer pageSize;

    Integer targetPage;

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTargetPage() {
        return targetPage;
    }

    public void setTargetPage(Integer targetPage) {
        this.targetPage = targetPage;
    }


    public static PageQueryParams generateFirstPage(int pageSize){
        PageQueryParams pageQueryParams = new PageQueryParams();
        pageQueryParams.setPageSize(pageSize);
        pageQueryParams.setTargetPage(1);
        return pageQueryParams;
    }

    /**
     * generate page query params for sql query
     *
     * @return
     */
    public PageSqlQueryParams buildPageSqlQueryParams() {
        return PageSqlQueryParams.build(pageSize,targetPage);
    }



    public static class PageSqlQueryParams {
        private int startIndex;

        private int endIndex;

        private int pageSize;

        /**
         * generate page query params for sql query
         *
         * @return
         */
        public static PageSqlQueryParams build(Integer pageSize,Integer targetPage){
            if (pageSize == null || targetPage == null) {
                throw new BizRuntimeException("generate page query params failed, params are null");
            }
            PageSqlQueryParams params = new PageSqlQueryParams();
            params.setStartIndex(pageSize * (targetPage - 1)+1);
            params.setEndIndex(pageSize * targetPage);
            params.setPageSize(pageSize);
            return params;
        }

        public int getStartIndex() {
            return startIndex;
        }

        public void setStartIndex(int startIndex) {
            this.startIndex = startIndex;
        }

        public int getEndIndex() {
            return endIndex;
        }

        public void setEndIndex(int endIndex) {
            this.endIndex = endIndex;
        }

        public int getPageSize() {
            return pageSize;
        }

        public void setPageSize(int pageSize) {
            this.pageSize = pageSize;
        }
    }

}
