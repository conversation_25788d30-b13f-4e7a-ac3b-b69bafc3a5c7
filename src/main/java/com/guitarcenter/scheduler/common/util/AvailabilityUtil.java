package com.guitarcenter.scheduler.common.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

import com.guitarcenter.scheduler.dto.EditHourShowDTO;
import com.guitarcenter.scheduler.dto.EditHourShowListDTO;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.LocationProfile;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.service.SiteService;

public class AvailabilityUtil implements AppConstants {

	private static final String COMMA_SPACE = ", ";
	private static final String WEEKEND_MORE_CHAR = " - ";

	private static ThreadLocal<DateFormat> threadLocal = new ThreadLocal<DateFormat>() {

		@Override
		protected DateFormat initialValue() {
			return new SimpleDateFormat("hh:mm aa");
		}

	};

	public static Date parse(final String dateTime) throws ParseException {
		return threadLocal.get().parse(dateTime);
	}

	@SuppressWarnings("deprecation")
	public static String format(final Date date) {
		String dateString;
		if (date.getMinutes() == 59) {
			date.setSeconds(date.getSeconds() + 1);
			dateString = threadLocal.get().format(date);
		} else {
			dateString = threadLocal.get().format(date);
		}
		return dateString;
	}
	
	public static String format24(final Date date) {
		String dateString;
		DateFormat df2 = new SimpleDateFormat("HH:mm");
		if(null != date) {
			dateString = df2.format(date);
		}else{
			return null;
		}
		return dateString;
	}

	/**
	 * give availability returns the every day's startTime and endTime
	 * 
	 * @param availability
	 * @return editHourShowList
	 * 
	 */
	/*
	public static EditHourShowListDTO getAvailabilityEditHourShowList(Long instructorId, Availability availability,
			long siteId, Boolean isAvailability) {

		EditHourShowListDTO editHourShowList = new EditHourShowListDTO();
				System.out.println("OLD instructorId  ``  "+instructorId);
		LinkedList<EditHourShowDTO> list = new LinkedList<EditHourShowDTO>();
		StringBuilder availabilityString = new StringBuilder();

		String sundayStart = "";
		String sundayEnd = "";
		if (availability.getSundayStartTime() != null && availability.getSundayEndTime() != null) {
			System.out.println("getSundayEndTime    "+availability.getSundayEndTime());
			
			System.out.println("format  SundayEndTime    "+format24(availability.getSundayEndTime()));
			System.out.println("getSundayStartTime    "+availability.getSundayStartTime());
			System.out.println("format  SundayStartTime    "+format(availability.getSundayStartTime()));
			sundayStart = format(availability.getSundayStartTime());
			sundayEnd = format(availability.getSundayEndTime());
			availabilityString.append(WEEKEND_CHOSE[0]).append(COMMA_SPACE);

		}
		String mondayStart = "";
		String mondayEnd = "";
		if (availability.getMondayStartTime() != null && availability.getMondayEndTime() != null) {
			mondayStart = format(availability.getMondayStartTime());
			mondayEnd = format(availability.getMondayEndTime());
			availabilityString.append(WEEKEND_CHOSE[1]).append(COMMA_SPACE);
		}
		String tuesdayStart = "";
		String tuesdayEnd = "";
		if (availability.getTuesdayEndTime() != null && availability.getTuesdayStartTime() != null) {
			tuesdayStart = format(availability.getTuesdayStartTime());
			tuesdayEnd = format(availability.getTuesdayEndTime());
			availabilityString.append(WEEKEND_CHOSE[2]).append(COMMA_SPACE);
		}
		String wednesdayStart = "";
		String wednesdayEnd = "";
		if (availability.getWednesdayEndTime() != null && availability.getWednesdayStartTime() != null) {
			wednesdayStart = format(availability.getWednesdayStartTime());
			wednesdayEnd = format(availability.getWednesdayEndTime());
			availabilityString.append(WEEKEND_CHOSE[3]).append(COMMA_SPACE);
		}
		String thursdayStart = "";
		String thursdayEnd = "";
		if (availability.getThursdayStartTime() != null && availability.getThursdayEndTime() != null) {
			thursdayStart = format(availability.getThursdayStartTime());
			thursdayEnd = format(availability.getThursdayEndTime());
			availabilityString.append(WEEKEND_CHOSE[4]).append(COMMA_SPACE);
		}
		String fridayStart = "";
		String fridayEnd = "";
		if (availability.getFridayEndTime() != null && availability.getFridayStartTime() != null) {
			fridayStart = format(availability.getFridayStartTime());
			fridayEnd = format(availability.getFridayEndTime());
			availabilityString.append(WEEKEND_CHOSE[5]).append(COMMA_SPACE);
		}
		String saturdayStart = "";
		String saturdayEnd = "";
		if (availability.getSaturdayEndTime() != null && availability.getSaturdayStartTime() != null) {
			saturdayStart = format(availability.getSaturdayStartTime());
			saturdayEnd = format(availability.getSaturdayEndTime());
			availabilityString.append(WEEKEND_CHOSE[6]).append(COMMA_SPACE);
		}
		list.add(new EditHourShowDTO(sundayStart, sundayEnd, WEEKEND_CHOSE[0]));
		list.add(new EditHourShowDTO(mondayStart, mondayEnd, WEEKEND_CHOSE[1]));
		list.add(new EditHourShowDTO(tuesdayStart, tuesdayEnd, WEEKEND_CHOSE[2]));
		list.add(new EditHourShowDTO(wednesdayStart, wednesdayEnd, WEEKEND_CHOSE[3]));
		list.add(new EditHourShowDTO(thursdayStart, thursdayEnd, WEEKEND_CHOSE[4]));
		list.add(new EditHourShowDTO(fridayStart, fridayEnd, WEEKEND_CHOSE[5]));
		list.add(new EditHourShowDTO(saturdayStart, saturdayEnd, WEEKEND_CHOSE[6]));

		editHourShowList.setAvailabilityString(availabilityString.length() > 0
				? availabilityString.toString().trim().substring(0, availabilityString.toString().trim().length() - 1)
				: "");
		editHourShowList.setIsAvailability(isAvailability);
		editHourShowList.setList(list);
		editHourShowList.setVersionString(String.valueOf(availability.getVersion()));
		editHourShowList.setExternalId(availability.getExternalId());
		editHourShowList.setIdString(String.valueOf(availability.getAvailabilityId()));
		editHourShowList.setSiteIdString(String.valueOf(siteId));
		editHourShowList.setInstructorId(instructorId);
		return editHourShowList;
	}
	
	*/
	public static EditHourShowListDTO getAvailabilityEditHourShowList(Long instructorId, Availability availability,
			long siteId, Boolean isAvailability) {

		EditHourShowListDTO editHourShowList = new EditHourShowListDTO();

		LinkedList<EditHourShowDTO> list = new LinkedList<EditHourShowDTO>();
		LinkedList<EditHourShowDTO> newFormatList = new LinkedList<EditHourShowDTO>();
		//insAvl24HrsList
		StringBuilder availabilityString = new StringBuilder();

		String sundayStart = "";
		String sundayEnd = "";
		String sundayStartTm = "";
		String sundayEndTm = "";
		if (availability.getSundayStartTime() != null && availability.getSundayEndTime() != null) {
			/*System.out.println("getSundayEndTime    "+availability.getSundayEndTime());
			
			System.out.println("format  SundayEndTime    "+format24(availability.getSundayEndTime()));
			System.out.println("getSundayStartTime    "+availability.getSundayStartTime());
			System.out.println("format  SundayStartTime    "+format(availability.getSundayStartTime()));*/
			sundayStart = format(availability.getSundayStartTime());
			sundayEnd = format(availability.getSundayEndTime());
			sundayStartTm = format24(availability.getSundayStartTime());
			sundayEndTm = format24(availability.getSundayEndTime());
			availabilityString.append(WEEKEND_CHOSE[0]).append(COMMA_SPACE);

		}
		String mondayStart = "";
		String mondayEnd = "";
		String mondayStartTm = "";
		String mondayEndTm = "";
		if (availability.getMondayStartTime() != null && availability.getMondayEndTime() != null) {
			mondayStart = format(availability.getMondayStartTime());
			mondayEnd = format(availability.getMondayEndTime());
			mondayStartTm = format24(availability.getMondayStartTime());
			mondayEndTm = format24(availability.getMondayEndTime());
			availabilityString.append(WEEKEND_CHOSE[1]).append(COMMA_SPACE);
		}
		String tuesdayStart = "";
		String tuesdayEnd = "";
		String tuesdayStartTm = "";
		String tuesdayEndTm = "";
		if (availability.getTuesdayEndTime() != null && availability.getTuesdayStartTime() != null) {
			tuesdayStart = format(availability.getTuesdayStartTime());
			tuesdayEnd = format(availability.getTuesdayEndTime());
			tuesdayStartTm = format24(availability.getTuesdayStartTime());
			tuesdayEndTm = format24(availability.getTuesdayEndTime());
			availabilityString.append(WEEKEND_CHOSE[2]).append(COMMA_SPACE);
		}
		String wednesdayStart = "";
		String wednesdayEnd = "";
		String wednesdayStartTm = "";
		String wednesdayEndTm = "";
		if (availability.getWednesdayEndTime() != null && availability.getWednesdayStartTime() != null) {
			wednesdayStart = format(availability.getWednesdayStartTime());
			wednesdayEnd = format(availability.getWednesdayEndTime());
			wednesdayStartTm = format24(availability.getWednesdayStartTime());
			wednesdayEndTm = format24(availability.getWednesdayEndTime());
			availabilityString.append(WEEKEND_CHOSE[3]).append(COMMA_SPACE);
		}
		String thursdayStart = "";
		String thursdayEnd = "";
		String thursdayStartTm = "";
		String thursdayEndTm = "";
		if (availability.getThursdayStartTime() != null && availability.getThursdayEndTime() != null) {
			thursdayStart = format(availability.getThursdayStartTime());
			thursdayEnd = format(availability.getThursdayEndTime());
			thursdayStartTm = format24(availability.getThursdayStartTime());
			thursdayEndTm = format24(availability.getThursdayEndTime());
			availabilityString.append(WEEKEND_CHOSE[4]).append(COMMA_SPACE);
		}
		String fridayStart = "";
		String fridayEnd = "";
		String fridayStartTm = "";
		String fridayEndTm = "";
		if (availability.getFridayEndTime() != null && availability.getFridayStartTime() != null) {
			fridayStart = format(availability.getFridayStartTime());
			fridayEnd = format(availability.getFridayEndTime());
			fridayStartTm = format24(availability.getFridayStartTime());
			fridayEndTm = format24(availability.getFridayEndTime());
			availabilityString.append(WEEKEND_CHOSE[5]).append(COMMA_SPACE);
		}
		String saturdayStart = "";
		String saturdayEnd = "";
		String saturdayStartTm = "";
		String saturdayEndTm = "";
		if (availability.getSaturdayEndTime() != null && availability.getSaturdayStartTime() != null) {
			saturdayStart = format(availability.getSaturdayStartTime());
			saturdayEnd = format(availability.getSaturdayEndTime());
			saturdayStartTm = format24(availability.getSaturdayStartTime());
			saturdayEndTm = format24(availability.getSaturdayEndTime());
			availabilityString.append(WEEKEND_CHOSE[6]).append(COMMA_SPACE);
		}
		list.add(new EditHourShowDTO(sundayStart, sundayEnd, WEEKEND_CHOSE[0]));
		list.add(new EditHourShowDTO(mondayStart, mondayEnd, WEEKEND_CHOSE[1]));
		list.add(new EditHourShowDTO(tuesdayStart, tuesdayEnd, WEEKEND_CHOSE[2]));
		list.add(new EditHourShowDTO(wednesdayStart, wednesdayEnd, WEEKEND_CHOSE[3]));
		list.add(new EditHourShowDTO(thursdayStart, thursdayEnd, WEEKEND_CHOSE[4]));
		list.add(new EditHourShowDTO(fridayStart, fridayEnd, WEEKEND_CHOSE[5]));
		list.add(new EditHourShowDTO(saturdayStart, saturdayEnd, WEEKEND_CHOSE[6]));
		
		newFormatList.add(new EditHourShowDTO(sundayStartTm, sundayEndTm, WEEKEND_CHOSE[0]));
		newFormatList.add(new EditHourShowDTO(mondayStartTm, mondayEndTm, WEEKEND_CHOSE[1]));
		newFormatList.add(new EditHourShowDTO(tuesdayStartTm, tuesdayEndTm, WEEKEND_CHOSE[2]));
		newFormatList.add(new EditHourShowDTO(wednesdayStartTm, wednesdayEndTm, WEEKEND_CHOSE[3]));
		newFormatList.add(new EditHourShowDTO(thursdayStartTm, thursdayEndTm, WEEKEND_CHOSE[4]));
		newFormatList.add(new EditHourShowDTO(fridayStartTm, fridayEndTm, WEEKEND_CHOSE[5]));
		newFormatList.add(new EditHourShowDTO(saturdayStartTm, saturdayEndTm, WEEKEND_CHOSE[6]));

		
		//newFormatList
		editHourShowList.setAvailabilityString(availabilityString.length() > 0
				? availabilityString.toString().trim().substring(0, availabilityString.toString().trim().length() - 1)
				: "");
		editHourShowList.setIsAvailability(isAvailability);
		editHourShowList.setList(list);
		editHourShowList.setInsAvl24HrsList(newFormatList);
		editHourShowList.setVersionString(String.valueOf(availability.getVersion()));
		editHourShowList.setExternalId(availability.getExternalId());
		editHourShowList.setIdString(String.valueOf(availability.getAvailabilityId()));
		editHourShowList.setSiteIdString(String.valueOf(siteId));
		editHourShowList.setInstructorId(instructorId);
		return editHourShowList;
	}

	/**
	 * give availability determine if the availability is null if not show it
	 * (instructor/staff)
	 * 
	 * @param availability
	 * @return
	 */
	public static String getAvailabilityString(Availability availability) {

		StringBuilder sb = new StringBuilder();
		if (availability.getSundayStartTime() != null && availability.getSundayEndTime() != null) {
			sb.append(WEEKEND_CHOSE[0]).append(COMMA_SPACE);
		}
		if (availability.getMondayStartTime() != null && availability.getMondayEndTime() != null) {
			sb.append(WEEKEND_CHOSE[1]).append(COMMA_SPACE);
		}
		if (availability.getTuesdayEndTime() != null && availability.getTuesdayStartTime() != null) {
			sb.append(WEEKEND_CHOSE[2]).append(COMMA_SPACE);
		}
		if (availability.getWednesdayEndTime() != null && availability.getWednesdayStartTime() != null) {
			sb.append(WEEKEND_CHOSE[3]).append(COMMA_SPACE);
		}
		if (availability.getThursdayStartTime() != null && availability.getThursdayEndTime() != null) {
			sb.append(WEEKEND_CHOSE[4]).append(COMMA_SPACE);
		}
		if (availability.getFridayEndTime() != null && availability.getFridayStartTime() != null) {
			sb.append(WEEKEND_CHOSE[5]).append(COMMA_SPACE);
		}
		if (availability.getSaturdayEndTime() != null && availability.getSaturdayStartTime() != null) {
			sb.append(WEEKEND_CHOSE[6]).append(COMMA_SPACE);
		}

		return sb.substring(0, sb.length() == 0 ? 0 : sb.length() - COMMA_SPACE.length());
	}

	// use lcs get the max substring of two string o(m+n) str1 and str2 no order
	// relation
	private static List<String> getMaxString(final String str1, final String str2) {
		if ("".equals(str1.trim()) || str1 == null || "".equals(str1.trim()) || str1 == null)
			return new ArrayList<String>();

		List<String> list = new ArrayList<String>();
		if (str1.contains(str2)) {
			list.add(str2);
			return list;
		} else if (str2.contains(str1)) {
			list.add(str1);
			return list;
		}

		Integer len1 = str1.length();
		Integer len2 = str2.length();

		Integer[][] arrayIntegers = new Integer[len1][len2];
		int[] maxIntegers = new int[len1];
		int max = 0;

		for (int i = 0; i < len1; i++) {
			for (int j = 0; j < len2; j++) {
				if (str1.charAt(i) == str2.charAt(j)) {
					if (i == 0 || j == 0) {
						max = max <= 1 ? 1 : max;
						arrayIntegers[i][j] = 1;
					} else {
						arrayIntegers[i][j] = arrayIntegers[i - 1][j - 1] + 1;
						max = max < arrayIntegers[i][j] ? arrayIntegers[i][j] : max;
					}
					maxIntegers[i] = arrayIntegers[i][j];
				} else {
					arrayIntegers[i][j] = 0;
				}
			}
		}
		for (int i = 0; i < maxIntegers.length; i++) {
			if (maxIntegers[i] == max) {
				list.add(str1.substring(i - max + 1, i + 1));
			}
		}
		return list;
	}

	// get edithourString key set
	public static List<String> getEditHourSting(String str1, String str2) {

		List<String> showList = new ArrayList<String>();
		while (str1.length() > 0) {
			List<String> list = getMaxString(str1, str2);
			for (String string : list) {
				str1 = StringUtils.remove(str1, string);
				if (string.length() >= 3) {
					showList.add(WEEKEND_CHOSE[Integer.valueOf(string.substring(0, 1))] + WEEKEND_MORE_CHAR
							+ WEEKEND_CHOSE[Integer.valueOf(string.substring(string.length() - 1))]);
				} else if (string.length() == 2) {
					showList.add(WEEKEND_CHOSE[Integer.valueOf(String.valueOf(string.charAt(0)))]);
					showList.add(WEEKEND_CHOSE[Integer.valueOf(String.valueOf(string.charAt(1)))]);
				} else {
					showList.add(WEEKEND_CHOSE[Integer.valueOf(String.valueOf(string.charAt(0)))]);
				}
			}
		}
		return showList;
	}

	// GSSP-228 Create a new Availabilty template

	public Availability getAvailabiltiyDefaultTemplate(LocationProfile l, Site site, SiteService siteService, Person person, List<String> studioAvailabilityList) throws ParseException {

		Availability availId = l.getAvailability();
		Availability availability1 = new Availability();
		availability1.setAvailabilityId(availId.getAvailabilityId());
		
		availability1.setSite(site);
		availability1.setVersion(0);
		availability1.setUpdated(new Date());
		availability1.setUpdatedBy(person);
		
		DateFormat format1 = new SimpleDateFormat(DateTimeUtil.DATETIME_PATTERN2);
		if(studioAvailabilityList.get(12) != null)  availability1.setSundayStartTime(format1.parse(studioAvailabilityList.get(12)));
		if(studioAvailabilityList.get(13) != null)  availability1.setSundayEndTime(format1.parse(studioAvailabilityList.get(13)));
		if(studioAvailabilityList.get(0) != null)  availability1.setMondayStartTime(format1.parse(studioAvailabilityList.get(0)));
		if(studioAvailabilityList.get(1) != null)  availability1.setMondayEndTime(format1.parse(studioAvailabilityList.get(1)));
		if(studioAvailabilityList.get(2) != null)  availability1.setTuesdayStartTime(format1.parse(studioAvailabilityList.get(2)));
		if(studioAvailabilityList.get(3) != null)  availability1.setTuesdayEndTime(format1.parse(studioAvailabilityList.get(3)));
		if(studioAvailabilityList.get(4) != null)  availability1.setWednesdayStartTime(format1.parse(studioAvailabilityList.get(4)));
		if(studioAvailabilityList.get(5) != null)  availability1.setWednesdayEndTime(format1.parse(studioAvailabilityList.get(5)));
		if(studioAvailabilityList.get(6) != null)  availability1.setThursdayStartTime(format1.parse(studioAvailabilityList.get(6)));
		if(studioAvailabilityList.get(7) != null)  availability1.setThursdayEndTime(format1.parse(studioAvailabilityList.get(7)));
		if(studioAvailabilityList.get(8) != null)  availability1.setFridayStartTime(format1.parse(studioAvailabilityList.get(8)));
		if(studioAvailabilityList.get(9) != null)  availability1.setFridayEndTime(format1.parse(studioAvailabilityList.get(9)));
		if(studioAvailabilityList.get(10) != null)  availability1.setSaturdayStartTime(format1.parse(studioAvailabilityList.get(10)));
		if(studioAvailabilityList.get(11) != null)  availability1.setSaturdayEndTime(format1.parse(studioAvailabilityList.get(11)));
		return availability1;


	}
	
	// GSSP-146 :: This Method will partition List into by Size given.
	
	public static  <T> Collection<List<T>> partitionListBySize(List<T> list, int size) {
		final AtomicInteger counter = new AtomicInteger(0);
		return list.stream()
				.collect(Collectors.groupingBy(it -> counter.getAndIncrement() / size))
				.values();
	}
}
