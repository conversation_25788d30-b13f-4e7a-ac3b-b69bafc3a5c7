package com.guitarcenter.scheduler.common.util;

import com.guitarcenter.scheduler.common.exceptions.BizRuntimeException;

import java.util.List;

/**
 * @Date 4/27/2020 4:27 PM
 * <AUTHOR>
 **/
public class PageInfo<T> {

    private List<T> data;


    private int currentPage;

    private int nextPage;

    private int prePage;

    private int pageSize;

    private int totalPages;

    private int endIndex;

    private int startIndex;

    private int totalCount;


    public PageInfo(Integer pageSize, Integer currentPage, Integer totalCount){
        this.setPage(pageSize, currentPage, totalCount);
    }

    /**
     * set page basic attributes
     * @param pageSize
     * @param currentPage
     * @param totalCount
     */
    public void setPage(Integer pageSize, Integer currentPage, Integer totalCount){
        if(pageSize ==null|| currentPage ==null||totalCount==null){
            throw new BizRuntimeException("page set failed, params cannot be null");
        }
        this.pageSize = pageSize;
        this.currentPage = currentPage;
        this.totalCount = totalCount;

        this.startIndex = pageSize * (currentPage - 1)+1;
        this.endIndex = pageSize*currentPage>totalCount?totalCount:pageSize*currentPage;
        this.totalPages = totalCount % pageSize == 0 ? totalCount/pageSize:totalCount/pageSize+1;
        if(currentPage == totalPages){
            this.nextPage = totalPages;
        }else{
            this.nextPage = currentPage+1;
        }

        if(currentPage == 1){
            this.prePage = 1;
        }else{
            this.prePage = currentPage - 1;
        }
    }



    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public int getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
    }

    public int getNextPage() {
        return nextPage;
    }

    public void setNextPage(int nextPage) {
        this.nextPage = nextPage;
    }

    public int getPrePage() {
        return prePage;
    }

    public void setPrePage(int prePage) {
        this.prePage = prePage;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public int getEndIndex() {
        return endIndex;
    }

    public void setEndIndex(int endIndex) {
        this.endIndex = endIndex;
    }

    public int getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(int startIndex) {
        this.startIndex = startIndex;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }


}
