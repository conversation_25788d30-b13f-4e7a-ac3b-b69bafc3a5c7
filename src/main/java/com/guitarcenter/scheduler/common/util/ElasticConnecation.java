package com.guitarcenter.scheduler.common.util;

import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.sql.Connection;
import java.util.Map;

import javax.net.ssl.SSLContext;

import org.apache.http.HttpHost;
import org.apache.http.HttpRequestInterceptor;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.SSLContexts;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;

import com.amazonaws.auth.AWS4Signer;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.DefaultAWSCredentialsProviderChain;
import com.guitarcenter.scheduler.elesticsearch.AWSRequestSigningApacheInterceptor;

public class ElasticConnecation { 
	
     RestHighLevelClient con=null;
    
    private    String serviceName = "";
	private    String region = "";
	private    String aesEndpoint = ""; 
	/*private static   String serviceName = "es";
	private static   String region = "us-west-2";
	private static   String aesEndpoint = "https://search-gc-es-487-prod-rhanh3ea25k5getrxi3c3ltqba.us-west-2.es.amazonaws.com"; */
	private    AWSCredentialsProvider credentialsProvider = new DefaultAWSCredentialsProviderChain();
    
    public  RestHighLevelClient getConnection(Map<String, String> elasticConnec){
    	
    	  serviceName =elasticConnec.get("serviceName");
		  region =elasticConnec.get("region");
		  aesEndpoint =elasticConnec.get("aesEndpoint");
        if (con != null) return con;
        // get db, user, pass from settings file
        return getConnection(serviceName, region, aesEndpoint,credentialsProvider);
    }

    private  RestHighLevelClient getConnection(String servicename2, String region2, String aesendpoint2,
			AWSCredentialsProvider credentialsprovider2) {
    
    	try {
        		 con = esClientDisableSSL(serviceName, region);
    		} catch (KeyManagementException | NoSuchAlgorithmException | KeyStoreException e1) {
    			e1.printStackTrace();
    			try {
    				con.close();
    			} catch (IOException e2) {
    				e2.printStackTrace();
    			}
    		}
        return con;
    }
    
    public RestHighLevelClient esClientDisableSSL(String serviceName, String region) throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
		 
		SSLContextBuilder sslBuilder = SSLContexts.custom()
	                .loadTrustMaterial(null, (x509Certificates, s) -> true);
	                final SSLContext sslContext = sslBuilder.build();
	                
		AWS4Signer signer = new AWS4Signer();
		signer.setServiceName(serviceName);
		signer.setRegionName(region);
		HttpRequestInterceptor interceptor = new AWSRequestSigningApacheInterceptor(serviceName, signer,
				credentialsProvider);
		
		return new RestHighLevelClient(RestClient.builder(HttpHost.create(aesEndpoint))
				.setHttpClientConfigCallback(hacb -> hacb.addInterceptorLast(interceptor).setSSLContext(sslContext).setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)));
		
		
	}

	
} 