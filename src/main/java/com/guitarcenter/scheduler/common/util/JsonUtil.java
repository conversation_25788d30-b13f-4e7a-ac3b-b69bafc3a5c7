package com.guitarcenter.scheduler.common.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.guitarcenter.scheduler.common.exceptions.BizRuntimeException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * convert json str to object
 * convert object to json str
 *
 * @Date 4/20/2020 5:52 PM
 * <AUTHOR>
 **/
public class JsonUtil {

    private final static Logger logger  = LoggerFactory.getLogger(JsonUtil.class);

    private static ObjectMapper objectMapper = new ObjectMapper();

    public static String objectToString(Object object) {
        String convertedStr = null;
        try{
            convertedStr = objectMapper.writeValueAsString(object);
        }catch (Exception e){
            logger.error("convert object to str failed",e);
            throw new BizRuntimeException(e.getMessage());
        }
        return convertedStr;
    }

    public static <T> T stringToObject(String json,Class<T> object) {
        T t = null;
        try{
            t = objectMapper.readValue(json,object);
        }catch (Exception e){
            logger.error("convert json to object failed, source str is {}",json,e);
            throw new BizRuntimeException(e.getMessage());
        }
        return t;
    }
}