package com.guitarcenter.scheduler.common.util.view;

import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.design.JasperDesign;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.util.JRLoader;
import net.sf.jasperreports.engine.xml.JRXmlLoader;
import org.springframework.context.ApplicationContextException;
import org.springframework.context.support.MessageSourceResourceBundle;
import org.springframework.core.io.Resource;
import org.springframework.util.ClassUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.support.RequestContext;
import org.springframework.web.servlet.view.AbstractUrlBasedView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import javax.sql.DataSource;
import java.io.*;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.*;

/**
 * @Date 11/16/2020 3:20 PM
 * <AUTHOR>
 **/
public class JasperReportPDFView extends AbstractUrlBasedView {

  private static final String HEADER_CONTENT_DISPOSITION = "Content-Disposition";
  private static final String CONTENT_DISPOSITION_INLINE = "inline";
  private String reportDataKey;
  private Properties subReportUrls;
  private String[] subReportDataKeys;
  private Properties headers;
  private Map<?, ?> exporterParameters = new HashMap();
  private Map<JRExporterParameter, Object> convertedExporterParameters;
  private DataSource jdbcDataSource;
  private JasperReport report;
  private Map<String, JasperReport> subReports;


  public void setReportDataKey(String reportDataKey) {
    this.reportDataKey = reportDataKey;
  }

  public void setSubReportUrls(Properties subReports) {
    this.subReportUrls = subReports;
  }

  public void setSubReportDataKeys(String[] subReportDataKeys) {
    this.subReportDataKeys = subReportDataKeys;
  }

  public void setHeaders(Properties headers) {
    this.headers = headers;
  }

  public void setExporterParameters(Map<?, ?> parameters) {
    this.exporterParameters = parameters;
  }

  public Map<?, ?> getExporterParameters() {
    return this.exporterParameters;
  }

  public void setConvertedExporterParameters(Map<JRExporterParameter, Object> convertedExporterParameters) {
    this.convertedExporterParameters = convertedExporterParameters;
  }

  public Map<JRExporterParameter, Object> getConvertedExporterParameters() {
    return this.convertedExporterParameters;
  }

  public void setJdbcDataSource(DataSource jdbcDataSource) {
    this.jdbcDataSource = jdbcDataSource;
  }

  public DataSource getJdbcDataSource() {
    return this.jdbcDataSource;
  }

  public boolean isUrlRequired() {
    return false;
  }

  protected final void initApplicationContext() throws ApplicationContextException {
    this.report = this.loadReport();
    if (this.subReportUrls != null) {
      if (this.subReportDataKeys != null && this.subReportDataKeys.length > 0 && this.reportDataKey == null) {
        throw new ApplicationContextException("'reportDataKey' for main report is required when specifying a value for 'subReportDataKeys'");
      }

      this.subReports = new HashMap(this.subReportUrls.size());
      Enumeration urls = this.subReportUrls.propertyNames();

      while(urls.hasMoreElements()) {
        String key = (String)urls.nextElement();
        String path = this.subReportUrls.getProperty(key);
        Resource resource = this.getApplicationContext().getResource(path);
        this.subReports.put(key, this.loadReport(resource));
      }
    }

    this.convertExporterParameters();
    if (this.headers == null) {
      this.headers = new Properties();
    }

    if (!this.headers.containsKey("Content-Disposition")) {
      this.headers.setProperty("Content-Disposition", "inline");
    }

    this.onInit();
  }

  private void onInit() {
  }

  private final void convertExporterParameters() {
    if (!CollectionUtils.isEmpty(this.exporterParameters)) {
      this.convertedExporterParameters = new HashMap(this.exporterParameters.size());
      Iterator var1 = this.exporterParameters.entrySet().iterator();

      while(var1.hasNext()) {
        Map.Entry<?, ?> entry = (Map.Entry)var1.next();
        JRExporterParameter exporterParameter = this.getExporterParameter(entry.getKey());
        this.convertedExporterParameters.put(exporterParameter, this.convertParameterValue(exporterParameter, entry.getValue()));
      }
    }

  }

  private Object convertParameterValue(JRExporterParameter parameter, Object value) {
    if (value instanceof String) {
      String str = (String)value;
      if ("true".equals(str)) {
        return Boolean.TRUE;
      }

      if ("false".equals(str)) {
        return Boolean.FALSE;
      }

      if (str.length() > 0 && Character.isDigit(str.charAt(0))) {
        try {
          return new Integer(str);
        } catch (NumberFormatException var5) {
          return str;
        }
      }
    }

    return value;
  }

  private JRExporterParameter getExporterParameter(Object parameter) {
    if (parameter instanceof JRExporterParameter) {
      return (JRExporterParameter)parameter;
    } else if (parameter instanceof String) {
      return this.convertToExporterParameter((String)parameter);
    } else {
      throw new IllegalArgumentException("Parameter [" + parameter + "] is invalid type. Should be either String or JRExporterParameter.");
    }
  }

  private JRExporterParameter convertToExporterParameter(String fqFieldName) {
    int index = fqFieldName.lastIndexOf(46);
    if (index != -1 && index != fqFieldName.length()) {
      String className = fqFieldName.substring(0, index);
      String fieldName = fqFieldName.substring(index + 1);

      try {
        Class<?> cls = ClassUtils.forName(className, this.getApplicationContext().getClassLoader());
        Field field = cls.getField(fieldName);
        if (JRExporterParameter.class.isAssignableFrom(field.getType())) {
          try {
            return (JRExporterParameter)field.get((Object)null);
          } catch (IllegalAccessException var8) {
            throw new IllegalArgumentException("Unable to access field [" + fieldName + "] of class [" + className + "]. " + "Check that it is static and accessible.");
          }
        } else {
          throw new IllegalArgumentException("Field [" + fieldName + "] on class [" + className + "] is not assignable from JRExporterParameter - check the type of this field.");
        }
      } catch (ClassNotFoundException var9) {
        throw new IllegalArgumentException("Class [" + className + "] in key [" + fqFieldName + "] could not be found.");
      } catch (NoSuchFieldException var10) {
        throw new IllegalArgumentException("Field [" + fieldName + "] in key [" + fqFieldName + "] could not be found on class [" + className + "].");
      }
    } else {
      throw new IllegalArgumentException("Parameter name [" + fqFieldName + "] is not a valid static field. " + "The parameter name must map to a static field such as " + "[net.sf.jasperreports.engine.export.JRHtmlExporterParameter.IMAGES_URI]");
    }
  }

  private JasperReport loadReport() {
    String url = this.getUrl();
    if (url == null) {
      return null;
    } else {
      Resource mainReport = this.getApplicationContext().getResource(url);
      return this.loadReport(mainReport);
    }
  }

  private final JasperReport loadReport(Resource resource) {
    try {
      String filename = resource.getFilename();
      if (filename != null) {
        InputStream is;
        if (filename.endsWith(".jasper")) {
          if (this.logger.isInfoEnabled()) {
            this.logger.info("Loading pre-compiled Jasper Report from " + resource);
          }

          is = resource.getInputStream();

          JasperReport var19;
          try {
            var19 = (JasperReport) JRLoader.loadObject(is);
          } finally {
            is.close();
          }

          return var19;
        }

        if (filename.endsWith(".jrxml")) {
          if (this.logger.isInfoEnabled()) {
            this.logger.info("Compiling Jasper Report loaded from " + resource);
          }

          is = resource.getInputStream();

          JasperReport var5;
          try {
            JasperDesign design = JRXmlLoader.load(is);
            var5 = JasperCompileManager.compileReport(design);
          } finally {
            is.close();
          }

          return var5;
        }
      }

      throw new IllegalArgumentException("Report filename [" + filename + "] must end in either .jasper or .jrxml");
    } catch (IOException var17) {
      throw new ApplicationContextException("Could not load JasperReports report from " + resource, var17);
    } catch (JRException var18) {
      throw new ApplicationContextException("Could not parse JasperReports report from " + resource, var18);
    }
  }

  protected void renderMergedOutputModel(Map<String, Object> model, HttpServletRequest request, HttpServletResponse response) throws Exception {
    if (this.subReports != null) {
      model.putAll(this.subReports);
      if (this.subReportDataKeys != null) {
        String[] var4 = this.subReportDataKeys;
        int var5 = var4.length;

        for(int var6 = 0; var6 < var5; ++var6) {
          String key = var4[var6];
          model.put(key, this.convertReportData(model.get(key)));
        }
      }
    }

    this.exposeLocalizationContext(model, request);
    JasperPrint filledReport = this.fillReport(model);
    this.postProcessReport(filledReport, model);
    this.populateHeaders(response);
    this.renderReport(filledReport, model, response);
  }

  private void exposeLocalizationContext(Map<String, Object> model, HttpServletRequest request) {
    RequestContext rc = new RequestContext(request, this.getServletContext());
    Locale locale = rc.getLocale();
    if (!model.containsKey("REPORT_LOCALE")) {
      model.put("REPORT_LOCALE", locale);
    }

    TimeZone timeZone = rc.getTimeZone();
    if (timeZone != null && !model.containsKey("REPORT_TIME_ZONE")) {
      model.put("REPORT_TIME_ZONE", timeZone);
    }

    JasperReport report = this.getReport();
    if ((report == null || report.getResourceBundle() == null) && !model.containsKey("REPORT_RESOURCE_BUNDLE")) {
      model.put("REPORT_RESOURCE_BUNDLE", new MessageSourceResourceBundle(rc.getMessageSource(), locale));
    }

  }

  private JasperPrint fillReport(Map<String, Object> model) throws Exception {
    JasperReport report = this.getReport();
    if (report == null) {
      throw new IllegalStateException("No main report defined for 'fillReport' - specify a 'url' on this view or override 'getReport()' or 'fillReport(Map)'");
    } else {
      JRDataSource jrDataSource = null;
      DataSource jdbcDataSourceToUse = null;
      if (this.reportDataKey != null) {
        Object reportDataValue = model.get(this.reportDataKey);
        if (reportDataValue instanceof DataSource) {
          jdbcDataSourceToUse = (DataSource)reportDataValue;
        } else {
          jrDataSource = this.convertReportData(reportDataValue);
        }
      } else {
        Collection<?> values = model.values();
        jrDataSource = (JRDataSource)CollectionUtils.findValueOfType(values, JRDataSource.class);
        if (jrDataSource == null) {
          JRDataSourceProvider provider = (JRDataSourceProvider)CollectionUtils.findValueOfType(values, JRDataSourceProvider.class);
          if (provider != null) {
            jrDataSource = this.createReport(provider);
          } else {
            jdbcDataSourceToUse = (DataSource)CollectionUtils.findValueOfType(values, DataSource.class);
            if (jdbcDataSourceToUse == null) {
              jdbcDataSourceToUse = this.jdbcDataSource;
            }
          }
        }
      }

      if (jdbcDataSourceToUse != null) {
        return this.doFillReport(report, model, jdbcDataSourceToUse);
      } else {
        if (jrDataSource == null) {
          jrDataSource = this.getReportData(model);
        }

        if (jrDataSource != null) {
          if (this.logger.isDebugEnabled()) {
            this.logger.debug("Filling report with JRDataSource [" + jrDataSource + "]");
          }

          return JasperFillManager.fillReport(report, model, jrDataSource);
        } else {
          this.logger.debug("Filling report with plain model");
          return JasperFillManager.fillReport(report, model);
        }
      }
    }
  }

  private JasperPrint doFillReport(JasperReport report, Map<String, Object> model, DataSource ds) throws Exception {
    if (this.logger.isDebugEnabled()) {
      this.logger.debug("Filling report using JDBC DataSource [" + ds + "]");
    }

    Connection con = ds.getConnection();

    JasperPrint var5;
    try {
      var5 = JasperFillManager.fillReport(report, model, con);
    } finally {
      try {
        con.close();
      } catch (Throwable var12) {
        this.logger.debug("Could not close JDBC Connection", var12);
      }

    }

    return var5;
  }

  private void populateHeaders(HttpServletResponse response) {
    Enumeration en = this.headers.propertyNames();

    while(en.hasMoreElements()) {
      String key = (String)en.nextElement();
      response.addHeader(key, this.headers.getProperty(key));
    }

  }

  private JasperReport getReport() {
    return this.report;
  }

  private JRDataSource getReportData(Map<String, Object> model) {
    Object value = CollectionUtils.findValueOfType(model.values(), this.getReportDataTypes());
    return value != null ? this.convertReportData(value) : null;
  }

  private JRDataSource convertReportData(Object value) throws IllegalArgumentException {
    return value instanceof JRDataSourceProvider ? this.createReport((JRDataSourceProvider)value) : convertReportSourceData(value);
  }

  private JRDataSource createReport(JRDataSourceProvider provider) {
    try {
      JasperReport report = this.getReport();
      if (report == null) {
        throw new IllegalStateException("No main report defined for JRDataSourceProvider - specify a 'url' on this view or override 'getReport()'");
      } else {
        return provider.create(report);
      }
    } catch (JRException var3) {
      throw new IllegalArgumentException("Supplied JRDataSourceProvider is invalid", var3);
    }
  }

  private Class<?>[] getReportDataTypes() {
    return new Class[]{Collection.class, Object[].class};
  }

  private void postProcessReport(JasperPrint populatedReport, Map<String, Object> model) throws Exception {
  }

  protected boolean generatesDownloadContent() {
    return !this.useWriter();
  }

  private void renderReport(JasperPrint populatedReport, Map<String, Object> model, HttpServletResponse response) throws Exception {
    JRExporter exporter = this.createExporter();
    Map<JRExporterParameter, Object> mergedExporterParameters = this.getConvertedExporterParameters();
    if (!CollectionUtils.isEmpty(mergedExporterParameters)) {
      exporter.setParameters(mergedExporterParameters);
    }

    if (this.useWriter()) {
      this.renderReportUsingWriter(exporter, populatedReport, response);
    } else {
      this.renderReportUsingOutputStream(exporter, populatedReport, response);
    }

  }

  private void renderReportUsingWriter(JRExporter exporter, JasperPrint populatedReport, HttpServletResponse response) throws Exception {
    String contentType = this.getContentType();
    String encoding = (String)exporter.getParameter(JRExporterParameter.CHARACTER_ENCODING);
    if (encoding != null && contentType != null && !contentType.toLowerCase().contains(";charset=")) {
      contentType = contentType + ";charset=" + encoding;
    }

    response.setContentType(contentType);
    render(exporter, populatedReport, response.getWriter());
  }

  private void renderReportUsingOutputStream(JRExporter exporter, JasperPrint populatedReport, HttpServletResponse response) throws Exception {
    ByteArrayOutputStream baos = this.createTemporaryOutputStream();
    render(exporter, populatedReport, baos);
    this.writeToResponse(response, baos);
  }

  public JasperReportPDFView() {
    this.setContentType("application/pdf");
  }

  private JRExporter createExporter() {
    return new JRPdfExporter();
  }

  private boolean useWriter() {
    return false;
  }


  private void render(JRExporter exporter, JasperPrint print, OutputStream outputStream) throws JRException {
    exporter.setParameter(JRExporterParameter.JASPER_PRINT, print);
    exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, outputStream);
    exporter.exportReport();
  }

  private void render(JRExporter exporter, JasperPrint print, Writer writer) throws JRException {
    exporter.setParameter(JRExporterParameter.JASPER_PRINT, print);
    exporter.setParameter(JRExporterParameter.OUTPUT_WRITER, writer);
    exporter.exportReport();
  }


  private JRDataSource convertReportSourceData(Object value) throws IllegalArgumentException {
    if (value instanceof JRDataSource) {
      return (JRDataSource)value;
    } else if (value instanceof Collection) {
      return new JRBeanCollectionDataSource((Collection)value);
    } else if (value instanceof Object[]) {
      return new JRBeanArrayDataSource((Object[])((Object[])value));
    } else {
      throw new IllegalArgumentException("Value [" + value + "] cannot be converted to a JRDataSource");
    }
  }


}
