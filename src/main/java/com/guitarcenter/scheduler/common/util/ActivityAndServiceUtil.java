package com.guitarcenter.scheduler.common.util;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileActivity;
import com.guitarcenter.scheduler.model.ProfileService;
import com.guitarcenter.scheduler.model.Service;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.service.ActivityService;
import com.guitarcenter.scheduler.service.LocationProfileService;
import com.guitarcenter.scheduler.service.ServiceService;
	
/**
 * activity and service util.
 * <AUTHOR>
 *
 */
/**
 * <AUTHOR>
 *
 */
public final class ActivityAndServiceUtil {

	/**
	 * one hour has 60 minutes.
	 */
	private static final Integer MINUTE_ONE_HOUR = 60;

	/**
	 * default constructor,nobody can new this util class.
	 */
	   

	/**
	 * Give a minute,return hour and minute eg:give 130 return 2hrs 10min.
	 * 
	 * @param duration
	 *            how many minutes
	 * @return a string of hours and minute
	 */
	public static String getDuration(final Long duration) {
		if (duration == null) {
			return "";
		}
		Long hour = duration / MINUTE_ONE_HOUR;
		Long min = duration % MINUTE_ONE_HOUR;

		Double minDouble = (double) (min / 60.0);
		String showTime = String.valueOf(hour + minDouble);
		if (showTime.endsWith(".0")) {
			showTime = showTime.replace(".0", "");
		}

		StringBuilder sbBuilder = new StringBuilder();
		if (hour > 1 || (hour == 1 && min != 0)) {
			sbBuilder.append(showTime).append(" hrs ");
		} else if (hour == 1) {
			sbBuilder.append(hour).append(" hr ");
		} else {
			//Changes made for 15 minute code-GSSP- 254
			sbBuilder.append(duration + " min");
		}
		/*
		 * if (hour > 1) { sbBuilder.append(hour).append(" hrs "); } else if
		 * (hour == 1) { sbBuilder.append(hour).append(" hr "); } if (min > 0) {
		 * sbBuilder.append(min).append(" min"); }
		 */ return sbBuilder.toString();
	}

	/**
	 * ProfileActivity packaged to ActivityDTO.
	 * 
	 * @param activity
	 *            an ProfileActivity instance
	 * @return ActivityDTO
	 */
	public static ActivityDTO initActivityDTO(final ProfileActivity activity) {
		ActivityDTO dto = new ActivityDTO();
		dto.setActivityId(activity.getProfileActivityId());
		dto.setActivityName(activity.getActivity().getActivityName());
		dto.setServiceName(activity.getActivity().getService().getServiceName());

		String minAtt = activity.getActivity().getMinimumAttendees() == null ? ""
				: (activity.getActivity().getMinimumAttendees() == -1L ? "&nbsp;&nbsp;"
						: activity.getActivity().getMinimumAttendees().toString());
		String maxAtt = activity.getActivity().getMaximumAttendees() == null ? ""
				: activity.getActivity().getMaximumAttendees().toString();
		String attenders = minAtt;

		if (StringUtils.isNotBlank(maxAtt)) {
			attenders = minAtt + "  to " + maxAtt;
		}

		dto.setAttenders(attenders);

		String duration = getDuration(activity.getActivity().getMinimumDuration());

		dto.setMinimumDuration(duration);
		dto.setMaxmumDuration(getDuration(activity.getActivity().getMaximumDuration()));

		dto.setEnable(Enabled.N.equals(activity.getEnabled()) ? false : true);
		String requiresInstructor = SystemUtil.getRequiresInscructor(activity.getActivity().getRequiresInstructor());
		dto.setRequiresInstructor(requiresInstructor);
		return dto;
	}

	/**
	 * init activityDTO from activity.
	 * 
	 * @param activity
	 * @return ActivityDTO instance
	 */
	public static ActivityDTO initActivityDTO(Activity activity) {
		ActivityDTO dto = new ActivityDTO();
		dto.setActivityId(activity.getActivityId());
		dto.setActivityName(activity.getActivityName());
		dto.setServiceName(activity.getService().getServiceName());

		String minAtt = activity.getMinimumAttendees() == null ? ""
				: (activity.getMinimumAttendees() == -1L ? "&nbsp;&nbsp;" : activity.getMinimumAttendees().toString());
		String maxAtt = activity.getMaximumAttendees() == null ? "" : activity.getMaximumAttendees().toString();
		String attenders = minAtt;

		if (StringUtils.isNotBlank(maxAtt)) {
			attenders = minAtt + "  to " + maxAtt;
		}

		dto.setAttenders(attenders);

		String duration = getDuration(activity.getMinimumDuration());
		String maxduration = getDuration(activity.getMaximumDuration());

		dto.setMinimumDuration(duration);
		dto.setMaxmumDuration(maxduration);

		dto.setEnable(Enabled.N.equals(activity.getEnabled()) ? false : true);
		dto.setRequiresInstructor(SystemUtil.getRequiresInscructor(activity.getRequiresInstructor()));
		return dto;
	}

	/**
	 * check if number >= 0
	 * 
	 * @param str
	 * @return
	 */
	public static Boolean positiveAndZero(final String str) {
		return str.trim().matches("^[1-9]\\d{0,}|0$");
	}

	/**
	 * check if number >= 1
	 * 
	 * @param str
	 * @return
	 */
	public static Boolean positiveNumber(final String str) {
		return str.trim().matches("^[1-9]\\d{0,}$");
	}

	//GSSP-228 Changes Added Service Template
	public void saveServiceDefaultTemplate(long profileId, Site site, Person person, LocationProfileService locationProfileService, ServiceService serviceService, ProfileService profileservice, List<String> serviceList)  {
		
		for(String serviceId : serviceList){
			Service ser = serviceService.getServiceById(Long.valueOf(serviceId));
			ProfileService pService=new ProfileService();
			pService.setVersion(1);
			pService.setUpdated(new Date());
			pService.setUpdatedBy(person);
			pService.setSite(site);
			pService.setLocationProfile(locationProfileService.getLocationProfile(profileId));	
			pService.setService(ser);
			pService.setEnabled(Enabled.Y);
			locationProfileService.saveProfileService(pService, person);
	}
	
	
}
	//GSSP-228 changes
	public void saveActivitesDefaultTemplate(long profileId, Site site, LocationProfileService locationProfileService, ActivityService activityService, Person person, ProfileActivity profileActivity, List<String> activityList) {
	
		for(String activityId : activityList){
			Activity activity = activityService.getActivityByActivityId(Long.valueOf(activityId));
			ProfileActivity pActivity=new ProfileActivity();
			pActivity.setVersion(1);
			pActivity.setUpdated(new Date());
			pActivity.setUpdatedBy(person);
			pActivity.setSite(site);
			pActivity.setLocationProfile(locationProfileService.getLocationProfile(profileId));
			pActivity.setActivity(activity);
			pActivity.setEnabled(Enabled.Y);
			locationProfileService.saveProfileActivity(pActivity, person);
		}
		
	}
	
}
