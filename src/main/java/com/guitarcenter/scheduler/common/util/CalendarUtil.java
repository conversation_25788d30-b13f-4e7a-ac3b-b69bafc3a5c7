package com.guitarcenter.scheduler.common.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.dto.CalendarViewUnavailableHourDTO;
import com.guitarcenter.scheduler.dto.ProfileTimeOffDTO;
import com.guitarcenter.scheduler.service.ProfileTimeOffService;

/**
 * this class is used to generate month calendar for GCS's month view
 * 
 * <AUTHOR>
 *
 */
public class CalendarUtil {
    private static Logger log = LoggerFactory.getLogger(CalendarUtil.class);
    
	/**
	 * the month and year,eg:2013.07
	 */
	private String yearMonth;
	
	/**
	 * AS the calendar generated by windows(like June 2013),
	 * if the 1st of current month is Saturday,
	 * the generated month calendar would be 42 days at most
	 */
	private static final int MOST_DAYS_A_MONTH = 42;
	
	/**
	 * As mentioned above, the most days would be 42,if not, it would be 35 days at most in a month
	 */
	private static final int NORMAL_DAYS_A_MONTH = 35;

	/**
	 * days of the current month
	 */
	private List<String> days;

	/**
	 * constructor with yearmonth parameter
	 * @param yearMonth
	 */
	public CalendarUtil(String yearMonth) {
		this.yearMonth = yearMonth;
		fillDays();
	}

	/**
	 * generete the days list
	 */
	private void fillDays() {
		days = new ArrayList<String>();
		
		// the days belong to previous month
		int spacesBeforeFirstDay = DateTimeUtil.getWeekOfFirstDay(yearMonth);
		
		int year = Integer.parseInt(yearMonth.split("[.]")[0]);
		int currentMonth = Integer.parseInt(yearMonth.split("[.]")[1]);//the current month
		Date currentDate = DateTime.parse(year + "-" + currentMonth).toDate();
		
		Calendar c = Calendar.getInstance();
		c.setTime(currentDate);
		c.add(Calendar.MONTH, -1);//set to the previous month

		int maxDay=c.getActualMaximum(Calendar.DAY_OF_MONTH);//the last date of previous month
		for (int i = spacesBeforeFirstDay-1; i >= 0; i--) {
			days.add("p" + (maxDay - i));
		}

		// days belong to current month
		int dayCountInaMonth = DateTimeUtil.getDaysInAMonth(yearMonth);
		for (int i = 0; i < dayCountInaMonth; i++) {
			days.add(String.valueOf((i + 1)));
		}
		
		//days belong to next month
		int nextNeedSize = 0;
		if(days.size() > NORMAL_DAYS_A_MONTH) { //the days of generated month calendar is more than 5 weeks(35days)
			nextNeedSize = MOST_DAYS_A_MONTH - days.size();
		} else {
			nextNeedSize = NORMAL_DAYS_A_MONTH - days.size();
		}
		for(int i=1; i<=nextNeedSize; i++) {
            days.add("n" + i);
		}
	}
	
	/**
	 * get the calendar days list
	 * 
	 * @return
	 */
	public List<String> getCalendarList() {
		List<String> list = new LinkedList<String>();
		for (int i = 0; i < MOST_DAYS_A_MONTH; i++) {
			if(i <= days.size() -1 ) {
				if(null != days.get(i) && !"".equals(days.get(i))) {
					list.add(days.get(i));
				} 
			} 
		}
		return list;
	}
	/**
	 * check the input time format
	  * checkTime
	  * TODO
	  *
	  * @Title: checkTime
	  * @Description: TODO
	  * @param @param dateTime
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public static boolean checkTime(String dateTime){
		DateFormat df1 = new SimpleDateFormat(DateTimeUtil.DATETIME_PATTERN_SLASH);
		DateFormat df2 = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH);
		Calendar s1 = Calendar.getInstance();
		Calendar s2 = Calendar.getInstance();
		try {
			s1.setTime(df1.parse(dateTime));
			s2.setTime(df2.parse(dateTime));
		} catch (Exception e) {
			// TODO: handle exception
		    if (log.isInfoEnabled()) {
		        log.info("Caught an exception", e);
		    }
			return false;
		}
		/* XXX: MEmes: this method assumes the date time string is correct only
		 * if the day of month matches.
		 */
		if(s1.get(Calendar.DATE)==s2.get(Calendar.DATE)){
			return true;
		}else{
			return false;
		}
	}
	/**
	 * check the if the duration time out of the day
	  * checkTime
	  * TODO
	  *
	  * @Title: checkTime
	  * @Description: TODO
	  * @param @param startTime
	  * @param @param duration
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public static boolean checkTime(String startTime, String duration){
		DateFormat df1 = new SimpleDateFormat(DateTimeUtil.TIME_FORMAT_HH_MM);
		Calendar s1 = Calendar.getInstance();
		Calendar s2 = Calendar.getInstance();
		Calendar s3 = Calendar.getInstance();
		try {
			s1.setTime(df1.parse(startTime));
			s2.setTime(new DateTime(s1).plusMinutes(Integer.parseInt(duration)).toDate());
			//20131217 steveren fix the bug of GCSS-468
			s3.setTime(SystemUtil.getEndTimeIfOnMidnight(s2.getTime()));
		} catch (Exception e) {
			// TODO: handle exception
            if (log.isInfoEnabled()) {
                log.info("Caught an exception", e);
            }
			return false;
		}
		if(s1.get(Calendar.DATE)==s3.get(Calendar.DATE)){
			return true;
		}else{
			return false;
		}
	}
	/**
	 * change minute to the time when check appointment start and end time
	  * plusFormatTime
	  * TODO
	  *
	  * @Title: plusFormatTime
	  * @Description: TODO
	  * @param @param time
	  * @param @param minutes
	  * @param @return
	  * @return String
	  * @throws
	 */
	public static String plusFormatTime(String time, int minutes){
		DateFormat df1 = new SimpleDateFormat(DateTimeUtil.TIME_FORMAT_HH_MM);
		Date s1 = null;
		Date s2 = null;
		try {
			s1 = df1.parse(time);
			s2 = new DateTime(s1).plusMinutes(minutes).toDate();
		} catch (Exception e) {
            if (log.isInfoEnabled()) {
                log.info("Caught an exception", e);
            }
		}
		return df1.format(s2);
	}
	/**
	 * check the start date and end date
	  * checkDate
	  * TODO
	  *
	  * @Title: checkDate
	  * @Description: TODO
	  * @param @param startDate
	  * @param @param endDate
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public static boolean checkDate(String startDate, String endDate){
		DateFormat df = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH);
		Date start;
		Date end;
		try {
			start = df.parse(startDate);
			end = df.parse(endDate);
		} catch (Exception e) {
			// TODO: handle exception
            if (log.isInfoEnabled()) {
                log.info("Caught an exception", e);
            }
			return false;
		}
		if(start.after(end)){
			return false;
		}else{
			return true;
		}
	}
	public static String getEndTime(String startTime, String duration){
		DateFormat df1 = new SimpleDateFormat(DateTimeUtil.TIME_FORMAT_HH_MM);
		Date s1 = null;
		Date s2 = null;
		Date s3 = null;
		try {
			s1 = df1.parse(startTime);
			s2 = new DateTime(s1).plusMinutes(Integer.parseInt(duration)).toDate();
			//20131217 steveren fix the bug of GCSS-468
			s3 = SystemUtil.getEndTimeIfOnMidnight(s2);
		} catch (Exception e) {
			// TODO: handle exception
            if (log.isInfoEnabled()) {
                log.info("Caught an exception", e);
            }
		}
		/* XXX: MEmes: This could throw a NPE if an exception happens prior to
		 * creation of s2.
		 */
		return df1.format(s3);
	}
	
	public static String getDefaultEndDate(String date){
        DateTime endDate = null;
        try {
            endDate = DateTime.parse(date, DateTimeFormat.forPattern(DateTimeUtil.DATE_PATTERN_SLASH));
        } catch (IllegalArgumentException e) {
            log.error("Caught an exception", e);
            endDate = DateTime.now();
        } catch (UnsupportedOperationException e) {
            log.error("Caught an exception", e);
            endDate = DateTime.now();
        }
        // Modified to fix endDate issue on recurring appointments:: Setting the endDate from 3 months to 36 months.
        return  endDate.plusMonths(SystemUtil.VALIDATION_SCOPE_MONTH_1).toString(DateTimeUtil.DATE_PATTERN_SLASH);
	}
	
	public static String getDefaultEndDate(String date,int noOfMonths){
        DateTime endDate = null;
        try {
            endDate = DateTime.parse(date, DateTimeFormat.forPattern(DateTimeUtil.DATE_PATTERN_SLASH));
        } catch (IllegalArgumentException e) {
            log.error("Caught an exception", e);
            endDate = DateTime.now();
        } catch (UnsupportedOperationException e) {
            log.error("Caught an exception", e);
            endDate = DateTime.now();
        }
        // Modified to fix endDate issue on recurring appointments:: Setting the endDate from 3 months to 36 months.
        return  endDate.plusMonths(noOfMonths).toString(DateTimeUtil.DATE_PATTERN_SLASH);
	}
	
	public static boolean checkTimeFormat(String date, String time){
		DateFormat df = new SimpleDateFormat(DateTimeUtil.DATETIME_PATTERN_SLASH);
		try {
			Date d = df.parse(date + " " + time);
		} catch (Exception e) {
			return false;
		}
		return true;
	}
	
	public static boolean checkDate(String startDate, String startTime, String endDate, String endTime){
		DateFormat df = new SimpleDateFormat(DateTimeUtil.DATETIME_PATTERN_SLASH);
		Date start;
		Date end;
		try {
			start = df.parse(startDate + " " + startTime);
			end = df.parse(endDate + " " + endTime);
		} catch (Exception e) {
            if (log.isInfoEnabled()) {
                log.info("Caught an exception", e);
            }
			return false;
		}
		if(!start.before(end)){
			return false;
		}else{
			return true;
		}
	}
	
	//GSSP-334 changes
	public static boolean checkDateIsSame(String startDate, String startTime, String endDate, String endTime){
		DateFormat df = new SimpleDateFormat(DateTimeUtil.DATETIME_PATTERN_SLASH);
		if(!startDate.equals(endDate)){
			return false;
		}else{
			return true;
		}
	}
	//GSSP-233 Changes
	public static boolean isDateafterAnYear(String startDate){
		try {
			if(LocalDate.parse(startDate,DateTimeFormatter.ofPattern(DateTimeUtil.DATE_PATTERN_SLASH)).minusYears(1).isAfter(LocalDate.now())){
				return true;
			}else{
				return false;
			}
		} catch (Exception e) {
            if (log.isInfoEnabled()) {
                log.info("Caught an exception", e);
            }
			return false;
		}
	}
	
	public static String getNextWeekDay(String date){
		DateFormat df1 = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH);
		Date s1 = null;
		Date s2 = null;
		try {
			s1 = df1.parse(date);
			s2 = new DateTime(s1).plusDays(7).toDate();
		} catch (Exception e) {
            if (log.isInfoEnabled()) {
                log.info("Caught an exception", e);
            }
		}
		return df1.format(s2);
	}
	
	public static List<String> getPeriodWeekDate(String startDate, String endDate){
		List<String> dateList = new ArrayList<String>();
		dateList.add(startDate);
		String nextWeek = getNextWeekDay(startDate);
		while(true){
			if(!CalendarUtil.checkDate(nextWeek, endDate)){
				break;
			}
			dateList.add(nextWeek);
			nextWeek = getNextWeekDay(nextWeek);
		}
		return dateList;
	}
	//GSSP-334 Changes Add to get ProfileTimeOff 
	public static CalendarViewUnavailableHourDTO getProfileTimeOffUnavaialableHours(
			ProfileTimeOffDTO profileTimeOffDTO) {
	
		 long diffInMinutes =0L;
		 CalendarViewUnavailableHourDTO cDto = null;
		 
		 if(null != profileTimeOffDTO && null != profileTimeOffDTO.getFromTime() 
				 && null != profileTimeOffDTO.getToTime()){
			 cDto = new CalendarViewUnavailableHourDTO();
			 SimpleDateFormat sdf = new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN);
						try {
							diffInMinutes = TimeUnit.MILLISECONDS.toMinutes(sdf.parse(profileTimeOffDTO.getToTime()).getTime() 
									- sdf.parse(profileTimeOffDTO.getFromTime()).getTime());
						} catch (ParseException e) {
							log.info("Caught an getProfileTimeOffUnavaialableHours", e);
						}
						cDto.setStartTime( profileTimeOffDTO.getFromTime());
						cDto.setDuration(diffInMinutes+"");
		 }
		return cDto;
	}

}
