package com.guitarcenter.scheduler.common.util.editor;

import java.beans.PropertyEditorSupport;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.common.util.AppConstants;

public class CalendarDateEditor extends PropertyEditorSupport implements AppConstants{
	
	private static final Logger LOG = LoggerFactory.getLogger(CalendarDateEditor.class);
	
	private static final String FORMAT_PATTERN = "yyyy-MM-dd";
	private static final String FORMAT_PATTERN_WITH_TIME = "yyyy-MM-dd HH:mm";

	/* 
	 * Transfor input parameter from String to Date object
	 * 
	 */
	@Override
	public void setAsText(String text) throws IllegalArgumentException {
		if(null == text || 0 == text.length()) {
			setValue(new Date());
		} else {
			if(LOG.isDebugEnabled()) {
				LOG.debug("CalendarDateEditor: begin");
			}
			Date value = null;
			SimpleDateFormat sdf = null;
			if(text.contains(SPLITOR_COLON)) {
				sdf = new SimpleDateFormat(FORMAT_PATTERN_WITH_TIME);
			} else {
				sdf = new SimpleDateFormat(FORMAT_PATTERN);
			}
			try {
				value = sdf.parse(text);
			} catch (ParseException e) {
				LOG.error("CalendarDateEditor: date format error");
			}
			if(LOG.isDebugEnabled()) {
				LOG.debug("CalendarDateEditor: finish");
			}
			setValue(value);
		}
	}
	
}
