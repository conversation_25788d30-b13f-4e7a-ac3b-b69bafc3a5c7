/**
 * @Title: RoomSizeService.java
 * @Package com.guitarcenter.scheduler.service
 * @Description: TODO
 * Copyright: Copyright (c) 2013 
 * Company:
 * 
 * <AUTHOR>
 * @date Sep 11, 2013 2:16:48 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.service;

import java.util.List;

import com.guitarcenter.scheduler.model.RoomSize;

/**
 * @ClassName: RoomSizeService
 * @Description: TODO
 * <AUTHOR>
 * @date Sep 11, 2013 2:16:48 PM
 *
 */
public interface RoomSizeService {
	public List<RoomSize> getRoomSizeListBySiteId(long siteId);
	public List<RoomSize> getRoomSizeListByRoomTypeSiteId(long siteId, long roomTypeId);
	public RoomSize getRoomSize(Long id);
}
