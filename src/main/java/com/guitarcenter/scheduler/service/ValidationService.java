/**
 * @Title: ValidationService.java
 * @Package com.guitarcenter.scheduler.service
 * @Description: 
 * Copyright: Copyright (c) 2013 
 * Company:
 * 
 * <AUTHOR>
 * @date Sep 26, 2013 4:51:44 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.service;

import java.util.Date;
import java.util.List;

import com.guitarcenter.scheduler.model.Appointment;
/**
 * @ClassName: ValidationService
 * @Description: 
 * <AUTHOR>
 * @date Sep 26, 2013 4:51:44 PM
 *
 */
public interface ValidationService {
	/**
	 * check whether the instructorId has relationship with activityId and profileId
	  * checkInstructorByProfileActivityId
	  * 
	  *
	  * @Title: checkInstructorByProfileActivityId
	  * @Description: 
	  * @param @param instructorId
	  * @param @param activityId
	  * @param @param profileId
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkInstructorByProfileActivityId(long instructorId, long activityId, long profileId);
	/**
	 * check whether the instructorId's availability is free or not with the time parameters
	  * checkInstructorByTime
	  * 
	  *
	  * @Title: checkInstructorByTime
	  * @Description: 
	  * @param @param instructorId
	  * @param @param startDate
	  * @param @param endDate
	  * @param @param startTime
	  * @param @param endTime
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkInstructorByTime(long instructorId, String startDate, String endDate, String startTime, String endTime);
	/**
	 * check whether the instructorId has registered a appointment with the time parameters
	  * checkInstructorByAppointmentTime
	  * 
	  *
	  * @Title: checkInstructorByAppointmentTime
	  * @Description: 
	  * @param @param recurring
	  * @param @param instructorId
	  * @param @param startDate
	  * @param @param endDate
	  * @param @param startTime
	  * @param @param endTime
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkInstructorByAppointmentTime(boolean recurring, long instructorId, String startDate, String endDate, String startTime, String endTime);
	/**
	 * check whether the roomId has relationship with the activityId
	  * checkRoomByActivity
	  * 
	  *
	  * @Title: checkRoomByActivity
	  * @Description: 
	  * @param @param roomId
	  * @param @param activityId
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkRoomByActivity(long roomId, long activityId);
	/**
	 * check whether the roomId has registered a appointment with the time parameters
	  * checkRoomByTime
	  * 
	  *
	  * @Title: checkRoomByAppointmentTime
	  * @Description: 
	  * @param @param recurring
	  * @param @param roomId
	  * @param @param startDate
	  * @param @param endDate
	  * @param @param startTime
	  * @param @param endTime
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkRoomByAppointmentTime(boolean recurring, long roomId, String startDate, String endDate, String startTime, String endTime, long profileId);
	/**
	 * check whether the room name is used
	  * checkRoomName
	  * 
	  *
	  * @Title: checkRoomName
	  * @Description: 
	  * @param @param profileId
	  * @param @param name
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkRoomName(long profileId, String name);
	/**
	 * check whether the roomId has split rooms
	  * checkSplitByRoomId
	  * 
	  *
	  * @Title: checkSplitByRoomId
	  * @Description: 
	  * @param @param roomId
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkSplitByRoomId(long roomId);
	/**
	 * check whether the roomId has registered a appointment, include the split room if it has been split, in 3 months
	  * checkRoomScheduleByRoomId
	  * 
	  *
	  * @Title: checkRoomScheduleByRoomId
	  * @Description: 
	  * @param @param roomId
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkRoomScheduleByRoomId(long roomId, long profileId);
	/**
	 * when delete, check whether the roomId has registered a appointment, include the split room if it has been split
	  * checkRoomScheduleByRoomId
	  * 
	  *
	  * @Title: checkRoomScheduleByRoomId
	  * @Description: 
	  * @param @param roomId
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkRoomScheduleByRoomId(long roomId);
	/**
	 * check whether the roomId has registered a appointment with the profile and activityIds parameters
	  * checkRoomActivities
	  * 
	  *
	  * @Title: checkRoomActivities
	  * @Description: 
	  * @param @param roomId
	  * @param @param activityIds
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkRoomActivities(long roomId, List<Long> activityIds, long profileId);
	/**
	 * check whether the LocationProfile's availability is free or not with the time parameters
	  * checkLocationProfileByTime
	  * 
	  *
	  * @Title: checkLocationProfileByTime
	  * @Description: 
	  * @param @param profileId
	  * @param @param startDate
	  * @param @param endDate
	  * @param @param startTime
	  * @param @param endTime
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkLocationProfileByTime(long profileId, String startDate, String endDate, String startTime, String endTime);
	/**
	 * check whether the activityId has registered a appointment with the time parameters
	  * checkActivityByAppointmentTime
	  * 
	  *
	  * @Title: checkActivityByAppointmentTime
	  * @Description: 
	  * @param @param recurring
	  * @param @param activityId
	  * @param @param startDate
	  * @param @param endDate
	  * @param @param startTime
	  * @param @param endTime
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkActivityByAppointmentTime(boolean recurring, long activityId, String startDate, String endDate, String startTime, String endTime, long profileId);
	/**
	 * check the profile whether registered a appointment
	  * checkAppointmentByProfile
	  * 
	  *
	  * @Title: checkAppointmentByProfile
	  * @Description: 
	  * @param @param profileId
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkAppointmentByProfileId(long profileId);
	/**
	 * check start date and start time whether before present time
	  * checkStartDate
	  * 
	  *
	  * @Title: checkStartDate
	  * @Description: 
	  * @param @param startDate
	  * @param @param startTime
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkStartTime(String startDate, String startTime);
	/**
	 * check appointment with source room and target room before update appointment
	  * checkUpdateRoomByAppointmentTime
	  * 
	  *
	  * @Title: checkUpdateRoomByAppointmentTime
	  * @Description: 
	  * @param @param recurring
	  * @param @param sourceRoomId
	  * @param @param targetRoomId
	  * @param @param startDate
	  * @param @param endDate
	  * @param @param startTime
	  * @param @param endTime
	  * @param @param profileId
	  * @param @param excludeAppointmentIdParam
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkUpdateRoomByAppointmentTime(boolean recurring, long sourceRoomId, long targetRoomId, String startDate, String endDate, String startTime, String endTime, long profileId, String excludeAppointmentIdParam);
	/**
	 * check appointment with the instructorId before update appointment
	  * checkUpdateInstructorByAppointmentTime
	  * 
	  *
	  * @Title: checkUpdateInstructorByAppointmentTime
	  * @Description: 
	  * @param @param recurring
	  * @param @param instructorId
	  * @param @param startDate
	  * @param @param endDate
	  * @param @param startTime
	  * @param @param endTime
	  * @param @param excludeAppointmentIdParam
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkUpdateInstructorByAppointmentTime(boolean recurring, long instructorId, String startDate, String endDate, String startTime, String endTime, String excludeAppointmentIdParam);
	
	public Boolean checkRoomupdateByTemplateId(Long templateId);
	/**
	 * check whether split room registered appointment
	  * checkSplitRoomScheduleByParentRoomId
	  * 
	  *
	  * @Title: checkSplitRoomScheduleByParentRoomId
	  * @Description: 
	  * @param @param roomId
	  * @param @param profileId
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkSplitRoomScheduleByParentRoomId(long roomId, long profileId);
	/**
	 * check whether the roomId has registered a appointment with the activityIds parameters
	  * checkRoomActivities
	  * 
	  *
	  * @Title: checkRoomActivities
	  * @Description: 
	  * @param @param roomTemplateId
	  * @param @param activityIds
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkRoomActivities(long roomTemplateId, List<Long> activityIds);
	/**
	 * check the customers whether registered appointment with the time specified when create a appointment
	  * checkCustomerByAppointmentTime
	  * 
	  *
	  * @Title: checkCustomerByAppointmentTime
	  * @Description: 
	  * @param @param recurring
	  * @param @param customerIds
	  * @param @param startDate
	  * @param @param endDate
	  * @param @param startTime
	  * @param @param endTime
	  * @param @param profileId
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkCustomerByAppointmentTime(boolean recurring, List<Long> customerIds, String startDate, String endDate, String startTime, String endTime, long profileId);
	/**
	 * check the customers whether registered appointment with the time specified when update a appointment
	  * checkUpdateCustomerByAppointmentTime
	  * 
	  *
	  * @Title: checkUpdateCustomerByAppointmentTime
	  * @Description: 
	  * @param @param recurring
	  * @param @param customerIds
	  * @param @param startDate
	  * @param @param endDate
	  * @param @param startTime
	  * @param @param endTime
	  * @param @param profileId
	  * @param @param excludeAppointmentIdParam
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkUpdateCustomerByAppointmentTime(boolean recurring, List<Long> customerIds, String startDate, String endDate, String startTime, String endTime, long profileId, String excludeAppointmentIdParam);
	/**
	  * checkTimeoffByTime
	  * 
	  *
	  * @Title: checkTimeoffByTime
	  * @Description: 
	  * @param @param recurring
	  * @param @param startDate
	  * @param @param endDate
	  * @param @param startTime
	  * @param @param endTime
	  * @param @param instructorId
	  * @param @return
	  * @return boolean
	  * @throws
	  */
	public boolean checkTimeoffByTime(boolean recurring, String startDate,
			String endDate, String startTime, String endTime, long instructorId);
	/**
	  * checkInstructorByAppointmentTime
	  *
	  * @Title: checkInstructorByAppointmentTime
	  * @Description: 
	  * @param @param instructorId
	  * @param @param startDate
	  * @param @param endDate
	  * @param @param startTime
	  * @param @param endTime
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	public boolean checkInstructorByAppointmentTime(long instructorId, String startDate, String endDate, String startTime, String endTime);
	/**
	 * check time off with one time
	 * checkTimeoffOnetime
	 * 
	 *
	 * @Title: checkTimeoffOnetime
	 * @Description: 
	 * @param @param startDate
	 * @param @param startTime
	 * @param @param endTime
	 * @param @param instructorId
	 * @param @return
	 * @return boolean
	 * @throws
	 */
	public boolean checkTimeoffOnetime(String startDate, String startTime,String endDate, String endTime, long instructorId);
	/**
	 * check location profile availability
	 * checkProfileAvailabilityByTime
	 * 
	 *
	 * @Title: checkProfileAvailabilityByTime
	 * @Description: 
	 * @param @param startDate
	 * @param @param startTime
	 * @param @param endTime
	 * @param @param profileId
	 * @param @return
	 * @return boolean
	 * @throws
	 */
	public boolean checkProfileAvailabilityByTime(String startDate, String startTime, String endTime, long profileId);
	/**
	 * check one time for schedule
	 * checkOnetime
	 *
	 * @Title: checkOnetime
	 * @Description: 
	 * @param @param recurring
	 * @param @param startDate
	 * @param @param endDate
	 * @param @param startTime
	 * @param @param endTime
	 * @param @param instructorId
	 * @param @return
	 * @return boolean
	 * @throws
	 */
	boolean checkOnetime(boolean recurring, String startDate, String endDate,
			String startTime, String endTime, long instructorId);
	/**
	 * check whether the instructorId's availability and onetime is free or not with the time parameters
	 * checkAvailabilityAndOntime
	 *
	 * @Title: checkAvailabilityAndOntime
	 * @Description: 
	 * @param @param instructorId
	 * @param @param pStartDate
	 * @param @param pEndDate
	 * @param @return
	 * @return boolean
	 * @throws
	 */
	public boolean checkAvailabilityAndOntime(long instructorId,Date pStartDate, Date pEndDate);
	
	//264 changes
	public boolean validateStartEndDiff(String startDate, String endDate);
	
	//Issue fix 2: Start time can't be later than the end date of the series.
	public boolean validateStartTime(String startTime, Date endDate, Appointment a);//ProdIssue
	
	//---GSSP-334	Req 3
	public boolean checkProfileTimeoffByTime(long profileId, String startDate, String startTime, String endTime)throws Exception;
	
}
