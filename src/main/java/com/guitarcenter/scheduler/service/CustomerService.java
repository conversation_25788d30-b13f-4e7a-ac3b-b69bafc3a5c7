package com.guitarcenter.scheduler.service;

import com.guitarcenter.scheduler.dao.criterion.dto.CustomerAppointmentDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorLessonLinkDTO;
import com.guitarcenter.scheduler.dto.CustomerAppointmentsQueryDTO;
import com.guitarcenter.scheduler.dto.CustomerDTO;
import com.guitarcenter.scheduler.dto.CustomerDetailDTO;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.Customer;
import org.apache.solr.client.solrj.SolrServerException;

import java.io.IOException;
import java.util.List;

public interface CustomerService {
	
	public List<Customer> getAllCustomer();
	
	public List<Customer> searchCustomerByName(String name);
	
	public List<CustomerDTO> loadCustomerListBySite(long siteId);
	
	public CustomerDTO getCustomerById(long customerId);
    
    /**
     * Return a Customer records for the site id provided that
     * has a matching external id value.
     * 
     * @param siteId identifier for the site that results must be restricted to
     * @param externalId String containing the external id to lookup
     * @return a Customer that matches the external id in the site provided
     */
	
	
	
    public Customer getCustomerByExternalId(long siteId, String externalId);
    
    /**
     * Create or updates an Scheduler Customer record with values taken from the
     * supplied CustomerDTO record. Any exceptions are passed up to caller for
     * decision and handling.
     * 
     * The method will retrieve an existing customer record from persistent
     * storage that matches the external id of the update record.
     * 
     * @param update a CustomerDTO instance containing values to update
     * @throws IOException 
     * @throws SolrServerException 
     */
    public void updateFromExternal(CustomerDTO update) throws SolrServerException, IOException;
    
    /**
     * Get the instrument information of customer name, email, phone and instrument name
     * 
     * @param customerId
     * @return
     */
    public String getCustomerDetail(long customerId);

	public boolean checkCustomerByAppointmentTime(boolean recurring, long customerId, String startDate, String endDate, String startTime, String endTime, long profileId);
	
	public boolean checkUpdateCustomerByAppointmentTime(boolean recurring, long customerId, String startDate, String endDate, String startTime, String endTime, long profileId, String excludeAppointmentIdParam);
	
	//249 customer updated appointments
	public void changeToCustomerUpdated(List<Appointment> list);
	
	public Boolean isUpdatedByCustomer(long personID);
	//249 customer updated appointments end

	//LES-624
	CustomerDetailDTO getCustomerDetailById(Long customerId);

	//LES-624
	List<CustomerAppointmentDTO> getCustomerAppointments(CustomerAppointmentsQueryDTO queryDTO);

	public boolean sendEmailReminderToCustomer (CustomerAppointmentDTO dto);
	public CustomerDetailDTO getCustomerEditDetails(Long customerId);
	public CustomerDetailDTO saveEditCustomerDetails(CustomerDetailDTO dto);

	public void saveCustomerHistory(CustomerDTO update) throws SolrServerException, IOException;

	
	
	

}
