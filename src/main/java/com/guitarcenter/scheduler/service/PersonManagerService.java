package com.guitarcenter.scheduler.service;

import com.guitarcenter.scheduler.model.Person;

public interface PersonManagerService {
    /**
     * The identifier for the System Update person entity.
     */
    public static final Long SYSTEM_UPDATE_PERSON_ID = Long.valueOf(1);
    
	public Person findPersonByExtlId(String extId);
	
	/**
	 * Returns the Person entity used to indicate a record was updated by the
	 * system and not an interactive user.
	 *  
	 * @return a Person record representing internal system updates
	 * @throws IllegalStateException if a suitable Person record is not found.
	 */
	public Person getSystemUpdatePerson() throws IllegalStateException;
}
