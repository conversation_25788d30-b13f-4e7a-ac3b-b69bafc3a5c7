package com.guitarcenter.scheduler.service;

import java.io.IOException;
import java.util.List;
import java.util.Set;
import org.apache.solr.client.solrj.SolrServerException;
import com.guitarcenter.scheduler.dto.ListStaffsDTO;
import com.guitarcenter.scheduler.dto.StaffDTO;
import com.guitarcenter.scheduler.integration.dto.EmployeeDTO;
import com.guitarcenter.scheduler.model.Employee;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Site;

public interface EmployeeService {

	public Set<StaffDTO> findByStoreNumber(Site site, String storeNumber);//created for GSSP-288
	public Set<Employee> findBySite(Site site);
	public ListStaffsDTO findAllStaff(long siteId);
	
	/**
     * Returns a List of Employee instances that are associated with the site.
     * 
     * @param site Site to match
     * @return List of Employee instances
     */
	
	public Employee getEmployee(long id);
    
    /**
     * Return a collection of Employee records for the site id provided that
     * have a matching external id value.
     * 
     * Note: this is returning a collection simply because there is not a
     * database constraint on the external identifier; it is possible that
     * multiple instructors *could* be updated from a single external record in
     * the future.
     * 
     * @param siteId identifier for the site that results must be restricted to
     * @param externalId String containing the external id to lookup
     * @return List of Employee instances that match the external id in the site
     *         provided
     */
    public List<Employee> findEmployeesByExternalId(long siteId, String externalId);
    
    /**
     * Create or updates an Scheduler Employee record with values taken from the
     * supplied EmployeeDTO record. Any exceptions are passed up to caller for
     * decision and handling.
     * 
     * The method will retrieve an existing employee record from persistent
     * storage that matches the external id of the update record.
     * 
     * @param update an EmployeeDTO instance containing values to update
     * @throws IOException 
     * @throws SolrServerException 
     */
    public void updateFromExternal(EmployeeDTO update) throws SolrServerException, IOException;
    
    /**
     * Return a list of Employee instances that match the supplied person id.
     * 
     * @param personId a person identifier to use]
     * @return a List of Employee instances that match
     */
    public List<Employee> findByPersonId(long personId);
    
    
    //Changes made for GSSP-209
    public void updateFromAuthIdForRVPDM(EmployeeDTO update) throws SolrServerException, IOException;

	List<Person> findPersonByAuthId(long siteId, String authId);

	List<StaffDTO> getAllByStoreNumber(Site site, String storeNumber); //created for GSSP-288
	List<Employee> getAllBySite(Site site);
}
