package com.guitarcenter.scheduler.service;

import java.util.List;
import java.util.Set;

import com.guitarcenter.scheduler.model.Employee;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.PersonRole;

public interface PersonRoleService {

	public PersonRole findByPersonLocation(long siteId,long personId,long locationId);
	
	public Set<PersonRole> findByPerson(long siteId,long personId);
    
    public List<PersonRole> findByAuthId(long siteId, String authId);
	
	public void updatePersonRole(long siteId, long personId, List<String> list,
			Person updateBy, Employee employee);
	//GSSP-146
	public List<PersonRole> findRolesByPersondIds(List<Long> personId);
}
