/**
 * @Title: ProfileTimeOffService.java
 * @Package com.guitarcenter.scheduler.service
 * @Description: 
 * Copyright: Copyright (c) 2014 
 * Company:
* <AUTHOR> 
 * @date Oct 10, 2019 4:48:06 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.service;


import java.util.Date;
import java.util.List;
import java.util.Set;

import com.guitarcenter.scheduler.dto.ProfileTimeOffDTO;
import com.guitarcenter.scheduler.dto.StartTimeDTO;
import com.guitarcenter.scheduler.dto.StudioHourDTO;
import com.guitarcenter.scheduler.model.ProfileTimeoff;

/**
 * @ClassName: ProfileTimeOff
 * @Description: 
 * <AUTHOR> 
 * @date Oct 10, 2019 4:48:06 PM
 *
 */
public interface ProfileTimeOffService {
	public Set<StudioHourDTO> getUpcomingTimeOffByProfileId(Long profileId,Date sdate );
	public void saveOrUpdateProfileTimeOff(ProfileTimeoff profileTimeoff);	
	public long saveProfileTimeoff(ProfileTimeoff profileTimeoff);
	public void deleteProfileTimeoff(ProfileTimeoff profileTimeoff);
	public List<ProfileTimeoff> getDisplayProfileTimeoffByProfileId(long profileId);
	public  ProfileTimeoff  getProfileTimeoffByProfileTimeoffId(long TimeoffId);
	public List<StartTimeDTO> getStartTimeByProfileId(Long profileId, Date startDate);
	public boolean getAppointmentTimeForProfile(ProfileTimeOffDTO dto,long profileId);
	public boolean checkProfileTimeoffByTime(long profileId,String startDate, String startTime, String endTime)throws Exception;
	public List<ProfileTimeOffDTO> getAppointmentTimeListForProfileAndDate(long profileId,String profileTimeOffDate);
	public ProfileTimeOffDTO getProfileTimeOffIdbyDate(long profileId, String prfDate);
	 
}
