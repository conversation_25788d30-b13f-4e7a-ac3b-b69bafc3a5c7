package com.guitarcenter.scheduler.service;

import java.util.List;

import com.guitarcenter.scheduler.dto.OnLineAvailableDTO;
import com.guitarcenter.scheduler.model.OnlineAvailability;

public interface OnlineAvailabilityService {

	public List<OnlineAvailability> getOnlineAvailabilityByInstructorId(long instructorId);
	public List<OnLineAvailableDTO> getOnlineAvailabilityDtoByInstructorId(long instructorId);
	public List<OnLineAvailableDTO> getOnlineAvlFormatByInstructorId(long instructorId);

	public long saveOnlineAvailablity(OnlineAvailability onlineAvailability);

	public long saveOrUpdate(OnlineAvailability onlineAvailability,Long personId);
	public Boolean deleteOnLineTime(long oneTimeId);
	
}
