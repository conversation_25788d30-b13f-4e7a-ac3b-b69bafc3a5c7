/**
 * @Title: RoomTypeService.java
 * @Package com.guitarcenter.scheduler.service
 * @Description: TODO
 * Copyright: Copyright (c) 2013 
 * Company:
 * 
 * <AUTHOR>
 * @date Sep 12, 2013 2:07:41 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.service;

import java.util.List;

import com.guitarcenter.scheduler.model.RoomType;

/**
 * @ClassName: RoomTypeService
 * @Description: TODO
 * <AUTHOR>
 * @date Sep 12, 2013 2:07:41 PM
 *
 */
public interface RoomTypeService {
	public List<RoomType> getRoomTypeList(long siteId);
	public RoomType getRoomType(long roomTypeId);
}
