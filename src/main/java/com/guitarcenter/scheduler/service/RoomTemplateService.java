/**
 * @Title: RoomTemplateService.java
 * @Package com.guitarcenter.scheduler.service
 * @Description: TODO
 * Copyright: Copyright (c) 2013 
 * Company:
 * 
 * <AUTHOR>
 * @date Sep 11, 2013 10:36:38 AM
 * @version V1.0
 */
package com.guitarcenter.scheduler.service;

import java.util.List;

import com.guitarcenter.scheduler.dto.RoomTemplateDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.RoomTemplate;
import com.guitarcenter.scheduler.model.RoomType;
import com.guitarcenter.scheduler.model.Service;
import com.guitarcenter.scheduler.model.enums.Enabled;

/**
 * @ClassName: RoomTemplateService
 * @Description: TODO
 * <AUTHOR>
 * @date Sep 11, 2013 10:36:38 AM
 * 
 */
public interface RoomTemplateService {
	public List<RoomTemplateDTO> getRoomTemplateList(long siteId);
	public void createRoomTemplate(RoomTemplate roomTemplate);
	public boolean checkRoomTemplateName(long siteId, String name);
	public RoomTemplate getRoomTemplate(long roomTemplateId);
	public List<RoomTemplate> getRoomTemplateListBySiteId(long siteId);
	public void deleteRoomTemplate(long roomTemplateId) throws Exception;
	public void updateRoomTemplate(RoomTemplate template,
			Long updatedById, Enabled enabled, Boolean globalChange,
			RoomType roomType, List<Service> addedServices,
			List<Service> deletedServices, List<Activity> addedActivities,
			List<Activity> deletedActivities) throws Exception;
	public Boolean hasSameName(String createName);
	
}
