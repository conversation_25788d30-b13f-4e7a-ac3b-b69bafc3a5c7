package com.guitarcenter.scheduler.service;

import org.springframework.web.client.RestTemplate;

import com.guitarcenter.scheduler.dto.CustomerRegistrationRequest;
import com.guitarcenter.scheduler.dto.CustomerResetPasswordLink;
import com.guitarcenter.scheduler.dto.RegistrationResponse;

public interface CustomerRegistrationService {
   public RegistrationResponse registerCustomer(CustomerRegistrationRequest request, RestTemplate restTemplate);


public RegistrationResponse resetPasswordEmailTrigger(CustomerResetPasswordLink customerResetPasswordLink,
		RestTemplate restTemplate);
}
