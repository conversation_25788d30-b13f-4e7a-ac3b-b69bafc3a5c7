package com.guitarcenter.scheduler.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.dto.ProfileActivityCreateDTO;
import com.guitarcenter.scheduler.dto.ProfileServiceCreateDTO;
import com.guitarcenter.scheduler.dto.ServiceDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.LocationProfile;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileActivity;
import com.guitarcenter.scheduler.model.ProfileService;
import com.guitarcenter.scheduler.model.Service;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.enums.Enabled;

public interface LocationProfileService {

	public LocationProfile getLocationProfile(long profileId);



	public LocationProfile createProfileFromLocation(long locationId, long siteId, Person person);



	public void saveProfileActivity(ProfileActivity pProfileActivity, Person pPerson);



	public void saveProfileService(ProfileService pProfileService, Person pPerson);



	public Set<Service> getProfileSevices(long pProfileId);



	public void deleteProfileActivity(long pProfileId, long pActivityId);



	public List<Service> getProfileServiceList(long pProfileId);



	public List<Activity> getProfileActivityList(long pProfileId);



	public void deleteProfileService(long pProfileId, long pServiceId);
	
	public ProfileService getProfileService(long pProfileIdServiceId);


	Map<String, Object> saveServiceAndActivity(ProfileServiceCreateDTO pscDto,
			Long profileId, Site site, Person person, Map<String, Object> map);
	
	public List<ActivityDTO> getActivityListByProfileAndService(long profileId, long serviceId);
	
	public Map<String, Object> saveProfileServiceAndProfileActivity(ProfileActivityCreateDTO pacd,long profileId,Site site,Person person,Map<String, Object> map);



	List<ProfileService> getRealProfileServiceList(long pProfileId);
	
	List<Service> findServiceByProfileIdAndEnabled(long profileId, Enabled enabled);
	
	List<ServiceDTO> findServiceDTOListByProfileAndEnabled(long profileId, Enabled enabled);
	
	List<Activity> findActivityByProfileIdAndEnabled(long profileId, Enabled enabled);
	
	public boolean checkLocationProfileByTime(long profileId, String startDate, String endDate, String startTime, String endTime);
	
	public void disableProfile(long profileId, long personId);
	
	public void enableProfile(long profileId, long personId);
	
	public List<ActivityDTO> findByProfileIdAndServiceIds(long profileId, Long... serviceIds);

	public void batchSaveProServiceAndProActivity(Long profileId,
			List<Long> serviceIds, List<Long> activityIds, Person updatedBy,
			Long siteId) throws Exception;
}
