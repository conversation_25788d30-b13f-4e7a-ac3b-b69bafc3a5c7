package com.guitarcenter.scheduler.service;

import java.io.IOException;
import java.util.List;

import org.apache.solr.client.solrj.SolrServerException;

import com.guitarcenter.scheduler.dto.SearchDTO;


public interface SearchService {
    
    /**
     * Defines a method to update the search engine storage for the record
     * provided.
     * 
     * @param record the entity being updated
     * @throws SolrServerException
     * @throws IOException
     */
    public void updateRecord(Object record)
        throws SolrServerException, IOException;
    
    public void updateRecordSec(Object record)
            throws SolrServerException, IOException;
    
    /**
     * Defines a method to perform 'quick' in-calendar searching. The user
     * cannot influence pagination, sorting, etc.
     * 
     * @param siteId a required parameter that is used to limit the search to
     *               records belonging to a specific site
     * @param searchTerm a String containing the user supplied search term
     * @param solrFilter a List of Strings containing additional filters to apply
     * @return a List of SearchDTO records matching the 
     * @throws SolrServerException 
     */
    public List<SearchDTO> quickSearch(long siteId, String searchTerm, List<String> solrFilter)
        throws SolrServerException;
    
    /**
     * Searches customer list which belong to a specific site
     * 
     * @param siteId id of the site
     * @param solrFilter a List of Strings containing additional filters to apply
     * @return
     */
    public List<SearchDTO> searchCustomersByCriteria(long siteId, String searchCriteria,
                                                     List<String> solrFilter) throws SolrServerException;
    
    /**
     * This method is to work for Location search when user log in as site administrator 
     * 
     * @param siteId a required parameter that is used to limit the search to
     *               records belonging to a specific site
     * @param searchTerm a String containing the user supplied search term
     * @return a List of SearchDTO records
     * @throws SolrServerException
     */
    public List<SearchDTO> locationSearch(long siteId, String searchTerm) 
    		throws SolrServerException;
}
