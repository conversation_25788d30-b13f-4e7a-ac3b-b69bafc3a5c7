package com.guitarcenter.scheduler.service.util.jasperreports;

import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletResponse;
import javax.sql.DataSource;
import java.io.File;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * @Date 11/13/2020 4:13 PM
 * <AUTHOR>
 **/
@Component
public class JasperReportsService {
  private static Logger logger = LoggerFactory.getLogger(JasperReportsService.class);

  private Connection connection;

  @Autowired
  public void setDataSource(DataSource dataSource) throws SQLException {
    this.connection = dataSource.getConnection();
  }

  private void setDownloadName(String fileName, String fileType, HttpServletResponse response) {
    try {
      response.setHeader("Content-Disposition", "attachment;filename="+ new String(fileName.getBytes("utf-8"),"iso8859-1")+"."+fileType);
    } catch (Exception e) {
      logger.error("",e);
    }
  }

  /**
   *
   * @param jasperName jasper文件全名
   * @param isPreview 是否预览
   * @param response
   */
  public void exportPdf(String jasperName, boolean isPreview, HttpServletResponse response){
    jasperName = this.getClass().getClassLoader().getResource("").getPath()+ File.separator+"jasperreports"+File.separator+jasperName;
    try {
      JasperPrint jasperPrint = JasperFillManager.fillReport(jasperName, null,connection);
      if(isPreview) {
        setDownloadName(jasperPrint.getName(),"pdf",response);
      }
      JasperExportManager.exportReportToPdfStream(jasperPrint,  response.getOutputStream());
    } catch (Exception e) {
      logger.error("",e);
    }
  }


}
