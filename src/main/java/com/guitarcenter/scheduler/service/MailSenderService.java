package com.guitarcenter.scheduler.service;

import java.io.ByteArrayInputStream;
import java.util.Map;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import com.itextpdf.text.Document;


public interface MailSenderService {

	public void sendMail(Map<String, Object> dataMap, String templatePath);
	//GSSP-238 Changes
	//public void sendMailWithAttachment(Map<String, Object> dataMap, String templatePath, HSSFWorkbook workbook) throws MessagingException,Exception;
	//GSSP-240 Changes
	public void sendMailWithAttachment(Map<String, Object> dataMap, String templatePath, HSSFWorkbook workbook,
		String filePath, String fileName) throws Exception;
	//GSSP -265-PDF and ExcelAttachment.
	public void sendMailWithpdfAttachment(Map<String, Object> dataMap,
				String templatePath, HSSFWorkbook workbook,ByteArrayInputStream boatXL,Document document, ByteArrayInputStream boat);
		
	
}
