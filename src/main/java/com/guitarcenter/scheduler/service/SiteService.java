package com.guitarcenter.scheduler.service;

import java.util.List;

import com.guitarcenter.scheduler.model.Site;

public interface SiteService {

	public Site getSiteById(long id);
	
	/**
	 * Return a collection of Site records that have matching external id value.
	 * 
	 * Note: this is returning a collection simply because there is not a
	 * database constraint on the external identifiers; it is possible that
	 * multiple sites *could* be updated from a single external record in the
	 * future. Callers will need to determine the best course of action if there
	 * are multiple sites returned.
	 * 
	 * @param externalId String containing the external identifier to use for
	 *                   lookup
	 * @return a List of matching Site records
	 */
	public List<Site> findSitesByExternalId(String externalId);
}
