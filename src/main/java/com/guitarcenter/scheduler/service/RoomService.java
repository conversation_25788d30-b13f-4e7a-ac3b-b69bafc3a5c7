package com.guitarcenter.scheduler.service;

import java.util.Date;
import java.util.List;

import com.guitarcenter.scheduler.dto.ConflictingAppointmentListDTO;
import com.guitarcenter.scheduler.dto.RoomDTO;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.enums.Enabled;

public interface RoomService {
	public Room getRoom(long roomId);
	public List<Room> loadRoomList(long profileId);
	public RoomDTO getRoomDTOByRoomId(long roomId);
	public boolean checkRoomByAppointmentTime(boolean recurring, long roomId, String startDate, String endDate, String startTime, String endTime, long profileId);
	public boolean checkRoomByActivity(long roomId, long activityId);
	public List<Room> getRoomListByIds(Long... ids);
	public List<RoomDTO> getRoomListByServiceType(long serviceId);
    public long createRoom(Room room);
    public List<RoomDTO> getRoomList(long profileId);
    public void deleteRoom(Room room);
    public RoomDTO updateRoom(Room room);
    public void deleteSplitRooms(long roomId);
    public RoomDTO getRoomDTO(long roomId);
    public List<Room> loadEnabledRoomList(long profileId, Enabled enabled);
    public List<RoomDTO> loadEnabledRoomListByActivity(long activityId, Enabled enabled);
    public boolean checkSplitByRoomId(long roomId);
    public List<Room> getSplitRoomsByParentId(long roomId);
    public List<RoomDTO> findByProfileIdAndActivityIds(long profileId, Long... activityIds);
    public List<Room> findByTemplateId(Long templateId);
    public boolean checkUpdateRoomByAppointmentTime(boolean recurring, long roomId, String startDate, String endDate, String startTime, String endTime, long profileId, String excludeAppointmentIdParam);
	
    //Changes made for GSSP-241
    public ConflictingAppointmentListDTO checkRoomByConflictingAppointmentRecurringTime(long roomId, String startDate, String endDate, String startTime, String endTime, long profileId);
    public ConflictingAppointmentListDTO checkInstructorByConflictingAppointmentRecurringTime(long profileId, String startDate,
			String endDate, String startTime, String endTime, Long InstructorId);

    /**
     * Fetch room list 
     * 
     * @param profileId
     * @param serviceId
     * @param activityId
     * @param roomId
     * @param startTime
     * @param endTime
     * @return
     */
    public List<RoomDTO> loadRoomListByProfileIdAndActivityIdAndDateTime(Long profileId, Long serviceId, Long activityId, Long instructorId, Date startTime, Date endTime, Long appId);
}
