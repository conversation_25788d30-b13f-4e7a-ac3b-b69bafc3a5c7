package com.guitarcenter.scheduler.service;

import java.util.List;
import java.util.Map;

import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceResponseDTO;

public interface GetInstructorAvailabiltyService {

	public List<InstructorAVLServiceResponseDTO> getInstructorAvailabilitySlots(); 
	
	public List<InstructorAVLServiceResponseDTO> getDisabledInstructorsAvailabiltySlots();
	
	public Map<String,String> getPropertiesDataForElasticLoader();
	
	public String getEnvironmentName();
 
}