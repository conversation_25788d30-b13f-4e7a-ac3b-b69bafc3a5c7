package com.guitarcenter.scheduler.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.criterion.AppointmentCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.service.AppointmentStatusDataService;
import com.guitarcenter.scheduler.webservice.dto.AppointmentStatusDTO;
 

@Service
public class AppointmentStatusDataServiceImpl implements AppointmentStatusDataService, AppConstants {

	
	@Autowired
    private AppointmentDAO appointmentDAO;
    
	//-----------------For Appointment Status API  by Customer Email ID----------------
	@Override
	public List<AppointmentStatusDTO> findCustomerAppointmentStatusByEmailId(List<String> customerEmailId,Date startDate)
	{
		   	List<AppointmentStatusDTO> search = null;
			Criterion<Appointment, AppointmentStatusDTO> criterion = AppointmentCriterion
					.findCustomerAppointmentStatusByEmailId(customerEmailId,startDate);
			search = appointmentDAO.search(criterion);
 
		return search;
	}
 
}
