/**
 * @Title: RoomTemplateServiceImpl.java
 * @Package com.guitarcenter.scheduler.service.impl
 * @Description: TODO
 * Copyright: Copyright (c) 2013 
 * Company:
 * 
 * <AUTHOR>
 * @date Sep 11, 2013 10:41:20 AM
 * @version V1.0
 */
package com.guitarcenter.scheduler.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.RoomDAO;
import com.guitarcenter.scheduler.dao.RoomTemplateDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.RoomCriterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.dto.RoomTemplateDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.RoomTemplate;
import com.guitarcenter.scheduler.model.RoomType;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.service.RoomTemplateService;
import com.guitarcenter.scheduler.service.ValidationService;

/**
 * @ClassName: RoomTemplateServiceImpl
 * @Description: TODO
 * <AUTHOR>
 * @date Sep 11, 2013 10:41:20 AM
 * 
 */
@Service(value = "roomTemplateService")
public class RoomTemplateServiceImpl implements RoomTemplateService {
	@Autowired
	@Qualifier("roomTemplateDAO")
	private RoomTemplateDAO roomTemplateDAO;

	@Autowired
	@Qualifier("roomDAO")
	private RoomDAO roomDAO;
	
	@Autowired
	private ValidationService validationService;

	/*
	 * <p>Title: createRoomTemplate</p> <p>Description: </p>
	 * 
	 * @param roomTemplate
	 * 
	 * @see
	 * com.guitarcenter.scheduler.service.RoomTemplateService#createRoomTemplate
	 * (com.guitarcenter.scheduler.model.RoomTemplate)
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void createRoomTemplate(RoomTemplate roomTemplate) {
		roomTemplateDAO.save(roomTemplate, roomTemplate.getUpdatedBy());
	}

	/*
	 * <p>Title: getRoomTemplateList</p> <p>Description: </p>
	 * 
	 * @param profileId
	 * 
	 * @return
	 * 
	 * @see
	 * com.guitarcenter.scheduler.service.RoomTemplateService#getRoomTemplateList
	 * (long)
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public List<RoomTemplateDTO> getRoomTemplateList(long siteId) {
		List<RoomTemplateDTO> l = new ArrayList<RoomTemplateDTO>();
		List<RoomTemplate> list = roomTemplateDAO.getRoomTemplateList(siteId);
		for (RoomTemplate r : list) {
			RoomTemplateDTO dto = this.getDTO(r);
			l.add(dto);
		}
		return l;
	}
	
	private RoomTemplateDTO getDTO(RoomTemplate r){
		StringBuilder aSb = new StringBuilder();
		for (Activity a : r.getActivities()) {
			if(Enabled.Y.equals(a.getEnabled())){
				aSb.append(a.getActivityName());
				aSb.append(SystemUtil.SPLIT_COMMA);
			}
		}
        if (aSb.length() > 1) {
        	aSb.deleteCharAt(aSb.length() - 1);
        }
		StringBuilder sSb = new StringBuilder();
		for (com.guitarcenter.scheduler.model.Service s : r.getServices()) {
			if(Enabled.Y.equals(s.getEnabled())){
				sSb.append(s.getServiceName());
				sSb.append(SystemUtil.SPLIT_COMMA);
			}
		}
        if (sSb.length() > 1) {
        	sSb.deleteCharAt(sSb.length() - 1);
        }
		RoomTemplateDTO dto = new RoomTemplateDTO(r.getRoomTemplateId(),
				r.getRoomSize() == null ? "" : r.getRoomSize()
						.getRoomSizeName(), r.getRoomType().getRoomType(),
				r.getRoomTemplateName(), r.getIsSplitRoom()==null?"N":r.getIsSplitRoom().toString(), r
						.getEnabled().toString(), aSb.toString(), sSb.toString());
		
		return dto;
	}


	/**
	  * <p>Title: checkRoomTemplateName</p>
	  * <p>Description: </p>
	  * @param siteId
	  * @param name
	  * @return
	  * @see com.guitarcenter.scheduler.service.RoomTemplateService#checkRoomTemplateName(long, java.lang.String)
	  */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public boolean checkRoomTemplateName(long siteId, String name) {
		List<RoomTemplate> list = roomTemplateDAO.getRoomTemplateList(siteId);
		for(RoomTemplate r : list){
			//Bug GCSS-652
			if(name.toUpperCase().equals(r.getRoomTemplateName().toUpperCase()))
				return false;
		}
		return true;
	}

	/**
	  * <p>Title: getRoomTemplate</p>
	  * <p>Description: </p>
	  * @param roomTemplateId
	  * @return
	  * @see com.guitarcenter.scheduler.service.RoomTemplateService#getRoomTemplate(long)
	  */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public RoomTemplate getRoomTemplate(long roomTemplateId) {
		return roomTemplateDAO.get(roomTemplateId, DAOHelper.FETCH_MORE_ACTIVITIES|DAOHelper.FETCH_MORE_SERVICES|DAOHelper.FETCH_ROOM_SIZE|DAOHelper.FETCH_ROOM_TYPE);
	}

	/**
	  * <p>Title: getRoomTemplateListBySiteId</p>
	  * <p>Description: </p>
	  * @param siteId
	  * @return
	  * @see com.guitarcenter.scheduler.service.RoomTemplateService#getRoomTemplateListBySiteId(long)
	  */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public List<RoomTemplate> getRoomTemplateListBySiteId(long siteId) {
		List<RoomTemplate> list = new ArrayList<RoomTemplate>();
		for(RoomTemplate rt : roomTemplateDAO.getRoomTemplateList(siteId)){
			if(Enabled.Y.equals(rt.getEnabled())){
				list.add(rt);
			}
		}
		return list;
	}

	/**
	  * <p>Title: deleteRoomTemplate</p>
	  * <p>Description: </p>
	  * @param roomTemplateId
	  * @see com.guitarcenter.scheduler.service.RoomTemplateService#deleteRoomTemplate(long)
	  */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void deleteRoomTemplate(long roomTemplateId) throws Exception{
		roomTemplateDAO.delete(getRoomTemplate(roomTemplateId));
	}

	@Override
	@Transactional
	public void updateRoomTemplate(RoomTemplate template, Long updatedById,
			Enabled enabled, Boolean globalChange,RoomType roomType, List<com.guitarcenter.scheduler.model.Service> addedServices, List<com.guitarcenter.scheduler.model.Service> deletedServices, List<Activity> addedActivities, List<Activity> deletedActivities)throws Exception {
		//not globalChange only change roomTemplate
		
		if(globalChange){
			//disable roomTemplate or enable roomTemplate,room upon it will disable or enable
			//split changed room upon it will changed split
				Criterion<Room, Room> criterion = RoomCriterion.findByRoomTemplateId(template.getRoomTemplateId());
				List<Room> rooms = roomDAO.search(criterion,DAOHelper.FETCH_MORE_ACTIVITIES | DAOHelper.FETCH_MORE_SERVICES | DAOHelper.FETCH_LOCATION_PROFILE);
				for (Room room : rooms) {
					room.setEnabled(template.getEnabled());
					room.setRoomType(template.getRoomType());
					//if changed activities it be not null,if not changed activities it be null
					List<Activity> roomActivities = new ArrayList<Activity>(room.getActivities());
					List<com.guitarcenter.scheduler.model.Service> roomServices = new ArrayList<com.guitarcenter.scheduler.model.Service>(room.getServices());
					
					//add service to room
					for (com.guitarcenter.scheduler.model.Service service : addedServices) {
						if(!roomServices.contains(service)){
							roomServices.add(service);
						}
					}
					
					//add activity to room
					for (Activity activity : addedActivities) {
						if(!roomActivities.contains(activity)){
							roomActivities.add(activity);
						}
					}
					
					//delete room's service,check if any appointment in the room used activity under the service
					for (com.guitarcenter.scheduler.model.Service service : deletedServices) {
						if(roomServices.contains(service)){
							roomServices.remove(service);
						}
					}
					
					//delete room's activity,check if any appointment in the room use it
					for (Activity activity : deletedActivities) {
						if(roomActivities.contains(activity)){
						    //validation removed to the controller
							roomActivities.remove(activity);
						}
					}
					
					room.setActivities(new HashSet<Activity>(roomActivities));
					room.setServices(new HashSet<com.guitarcenter.scheduler.model.Service>(roomServices));
					
					roomDAO.update(room, updatedById);
				}
		}
		roomTemplateDAO.update(template, updatedById);
	}
	
	@Override
	public Boolean hasSameName(final String createName) {
		final RoomTemplate roomTemplate = new RoomTemplate();
		roomTemplate.setRoomTemplateName(createName);
		final List<RoomTemplate> list = roomTemplateDAO.search(roomTemplate);
		return SystemUtil.listIsBlank(list) ? false : true;
	}
}
