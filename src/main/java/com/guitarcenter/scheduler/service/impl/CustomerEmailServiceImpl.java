package com.guitarcenter.scheduler.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.CustomerDAO;
import com.guitarcenter.scheduler.dao.CustomerEmailDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.CustomerCriterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.CustomerEmail;
import com.guitarcenter.scheduler.service.CustomerEmailService;
import com.guitarcenter.scheduler.service.PersonManagerService;
import com.guitarcenter.scheduler.webservice.constants.WebServiceConstants;


@Service(value="customerEmailService")
public class CustomerEmailServiceImpl implements CustomerEmailService {
	
	
	@Autowired
	private CustomerDAO	customerDAO;
		
	
	
		@Autowired	
		@Qualifier("customerEmailDAO")
		private CustomerEmailDAO customerEmailDAO;	
		
		

		 @Autowired
	     private PersonManagerService personManagerService;
		 
	
	  @Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public  void checkEmail(List<String> externalIDList, String email)
	  {
		  
		  
		  Boolean isMatching = false;
		  
		  CustomerEmail customerEmail = null;
		  if(null != externalIDList && externalIDList.size() > 0 && null != email)
		  {
			  
			  String [] externalIDArray = externalIDList.toArray(new String[0]);
				 
			  Criterion<Customer,Boolean> emailCriterion = CustomerCriterion
							.isEmailMatchingWithExistingEmail(externalIDArray, email);
				  
			  isMatching = customerDAO.get(emailCriterion, DAOHelper.FETCH_APPOINTMENT_SERIES);		
			  
			  if(!isMatching)
			  {
				  
				  for(String externalID : externalIDList)
				  {
					  
					  customerEmail = new CustomerEmail();
					  customerEmail.setCustomerEmail(email);
					  customerEmail.setExternalCustomerId(externalID);		
					  customerEmail.setExternalSource(WebServiceConstants.lessonServiceExternalSource);
					  customerEmailDAO.save(customerEmail,
                               personManagerService.getSystemUpdatePerson());
					  
				  }
			  }
			  
		  }
	  }

}
