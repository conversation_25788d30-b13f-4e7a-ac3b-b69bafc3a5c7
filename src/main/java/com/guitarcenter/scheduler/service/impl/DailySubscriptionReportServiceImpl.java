package com.guitarcenter.scheduler.service.impl;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.dao.InstructorDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.InstructorCriterion;
import com.guitarcenter.scheduler.dto.DailySubscriptionReportDTO;
import com.guitarcenter.scheduler.dto.InstructorActivitiesDTO;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.service.DailySubscriptionReportService;
import com.guitarcenter.scheduler.service.InstructorActivitiesService;
import com.guitarcenter.scheduler.service.JobNotificationEmailServiceImpl;
import com.guitarcenter.scheduler.service.MailSenderService;

// Created for GSSP-240 - Instructor Activities report generation

@Service("dailySubscriptionReportService")
public class DailySubscriptionReportServiceImpl implements DailySubscriptionReportService {

	private static final Logger		LOGGER	= LoggerFactory.getLogger(DailySubscriptionReportServiceImpl.class);


	@Autowired
	@Qualifier("instructorDAO")
	private InstructorDAO mInstructorDAO;

	//	String EMAIL_TEMPLATE_INSTRUCTOR_ACTIVITIES= "instructorActivitiesTemplate.ftl";

	String EMAIL_TEMPLATE_DAILY_SUBCRPTION_REPORT= "dailySubscriptionReport.ftl";

	@Autowired
	@Qualifier("mailSenderService")
	private MailSenderService mMailSenderService;
	//Miscellaneous task
	@Autowired
	private JobNotificationEmailServiceImpl jobNotificationEmailServiceImpl;

	public InstructorDAO getInstructorDAO() {
		return mInstructorDAO;
	}

	public void setInstructorDAO(InstructorDAO instrDAO) {
		this.mInstructorDAO = instrDAO;
	}

	public MailSenderService getMailSenderService() {
		return mMailSenderService;
	}

	public void setMailSenderService(MailSenderService mailService) {
		this.mMailSenderService = mailService;
	}

	@Override
	public void generateDailySubscriptionReportDetails() throws Exception {

		HSSFWorkbook workbook = new HSSFWorkbook();
		Map<String, Object> model = new HashMap<String, Object>();
		model.put("instructorActivities",getInstructorActivities());

		createDailySubscriptionReport(model,workbook);
		Map<String,Object> dataMap =  renderDataForDailySubcriptionEmail();
		String file_name = "GC_DailySubscriptionReport_"+getfiledate()+".xls";
		mMailSenderService.sendMailWithAttachment(dataMap, EMAIL_TEMPLATE_DAILY_SUBCRPTION_REPORT, workbook,
				AppConstants.FILE_PATH,file_name);

	}
	private static final SimpleDateFormat simpleDateFormat01 = new SimpleDateFormat("MMddyyyy");
	private static String getfiledate() {

		Calendar day = Calendar.getInstance();

		day.set(Calendar.MILLISECOND, 0);
		day.set(Calendar.SECOND, 0);
		day.set(Calendar.MINUTE, 0);
		day.set(Calendar.HOUR_OF_DAY, 0);
		day.add(Calendar.DATE, -1);

		return simpleDateFormat01.format(day.getTime());
	}

	private  void createDailySubscriptionReport(
			Map<String, Object> model, HSSFWorkbook workbook) {
		HSSFRow row;
		HSSFSheet sheet = workbook
				.createSheet("DailySubscriptionReport");
		HSSFCellStyle style = workbook.createCellStyle();

		HSSFFont font = workbook.createFont();
		font.setFontHeightInPoints((short) 10);
		//font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
		font.setBold(true);
		style.setFont(font);

		sheet.setColumnWidth(0, 5500);
		sheet.setColumnWidth(1, 6000);
		sheet.setColumnWidth(2, 4000);
		sheet.setColumnWidth(3, 6000);
		sheet.setColumnWidth(4, 6000);
		sheet.setColumnWidth(5, 6000);
		sheet.setColumnWidth(6, 6000);
		sheet.setColumnWidth(7, 6000);
		int rownum = 0;
		row = sheet.createRow(rownum++);
		row.createCell(0).setCellValue("CUSTOMER#");
		row.getCell(0).setCellStyle(style);
		row.createCell(1).setCellValue("FIRST_NAME");
		row.getCell(1).setCellStyle(style);
		row.createCell(2).setCellValue("LAST_NAME");
		row.getCell(2).setCellStyle(style);
		row.createCell(3).setCellValue("EMAIL");
		row.getCell(3).setCellStyle(style);
		row.createCell(4).setCellValue("PHONE");
		row.getCell(4).setCellStyle(style);
		row.createCell(5).setCellValue("LOCATION");
		row.getCell(5).setCellStyle(style);
		row.createCell(6).setCellValue("STATUS_NAME");
		row.getCell(6).setCellStyle(style);
		row.createCell(7).setCellValue("SIGN_UP_DATE");
		row.getCell(7).setCellStyle(style);

		//CUSTOMER#,FIRST_NAME,LAST_NAME,EMAIL,LOCATION,STATUS_NAME
		for ( DailySubscriptionReportDTO instrActDto : getInstructorActivities()) {
			row = sheet.createRow(rownum++);

			String customeNo = instrActDto.getCustomerNo();
			row.createCell(0).setCellValue(customeNo);

			String firstName = instrActDto.getFirstName();
			row.createCell(1).setCellValue(firstName);

			row.createCell(2).setCellValue(instrActDto.getLastName());
			row.createCell(3).setCellValue(instrActDto.getEmail());
			row.createCell(4).setCellValue(instrActDto.getPhone());
			row.createCell(5).setCellValue(instrActDto.getLocation());
			row.createCell(6).setCellValue(instrActDto.getStatusName());
			row.createCell(7).setCellValue(instrActDto.getSignUpDate());
		}

		//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
		//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
		HSSFRow row2;
		HSSFSheet sheet2 = workbook
				.createSheet("NO SIGNUP");
		sheet2.setColumnWidth(0, 5500);

		int rownum2 = 0;
		row2 = sheet2.createRow(rownum2++);
		row2.createCell(0).setCellValue("LOCATION");
		row2.getCell(0).setCellStyle(style);

		for ( DailySubscriptionReportDTO instrActDto : getSingUplocation()) {
			row2 = sheet2.createRow(rownum2++);

			String location = instrActDto.getLocation();
			row2.createCell(0).setCellValue(location);

		}
		//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

	}


	private List<DailySubscriptionReportDTO> getSingUplocation() {
		Criterion<Instructor, DailySubscriptionReportDTO> criterion = InstructorCriterion.findDailySubcribtionLocation();
		List<DailySubscriptionReportDTO> instrList = mInstructorDAO.search(criterion);
		if(instrList != null){
			LOGGER.error(" getInstructorActivities(), instrList size= "+instrList.size());
		}else{
			LOGGER.error(" getInstructorActivities(), instrList size is null");
		}
		return instrList;
	}


	private Map<String, Object> renderDataForDailySubcriptionEmail() {
		Map<String, Object> dataMap = new HashMap<String, Object>();
		dataMap.put(AppConstants.SUBJECT, AppConstants.DAILY_SUBCRPTION_REPORT_SUBJECT);

		List<InstructorActivitiesDTO> recipientList = getRecipientEmailIds();
		if(recipientList !=null && recipientList.size() >0 )
		{
			String[] recipientArray = new String[recipientList.size()];
			int cnt=0;
			for ( InstructorActivitiesDTO instrActDto : recipientList) {
				recipientArray[cnt] = instrActDto.getRecipientId();
				cnt++;
			}
			dataMap.put(AppConstants.EMAIL_TYPE_TO, recipientArray);

		}
		else{
			LOGGER.info(" getRecipientEmailIds(), recipientList null ");
		}



		String environment=jobNotificationEmailServiceImpl.getEnvironment();
		//Miscellaneous task
		if(environment.equalsIgnoreCase("Production"))
		{
			dataMap.put(AppConstants.EMAIL_TYPE_CC, AppConstants.JOB_NOTIFICATION_EMAIL_SEND_LIST);
		}
		else
		{
			dataMap.put(AppConstants.EMAIL_TYPE_CC, AppConstants.JOB_NOTIFICATION_EMAIL_SEND_LIST_QA);
		}
		return dataMap;
	}


	private List<DailySubscriptionReportDTO> getInstructorActivities() {
		Criterion<Instructor, DailySubscriptionReportDTO> criterion = InstructorCriterion.findCustomerReportRecords();
		List<DailySubscriptionReportDTO> instrList = mInstructorDAO.search(criterion);
		if(instrList != null){
			LOGGER.info(" getInstructorActivities(), instrList size= "+instrList.size());
		}else{
			LOGGER.info(" getInstructorActivities(), instrList size is null");
		}
		return instrList;
	}



	private List<InstructorActivitiesDTO> getRecipientEmailIds() {
		Criterion<Instructor, InstructorActivitiesDTO> criterion = InstructorCriterion.getRecipientDailySubcEmailIds();
		List<InstructorActivitiesDTO> dtoList = mInstructorDAO.search(criterion);
		if(dtoList != null){
			LOGGER.info(" getRecipientEmailIds(), dtoList size= "+dtoList.size());
		}else{
			LOGGER.info(" getRecipientEmailIds(), dtoList size is null");
		}

		return dtoList;
	}

}
