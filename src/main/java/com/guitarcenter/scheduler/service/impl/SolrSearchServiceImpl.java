package com.guitarcenter.scheduler.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.apache.solr.client.solrj.SolrClient;
import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrQuery.ORDER;
import org.apache.solr.client.solrj.SolrQuery.SortClause;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.common.params.DisMaxParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.dto.SearchDTO;
import com.guitarcenter.scheduler.dto.SearchDTOBuilder;
import com.guitarcenter.scheduler.model.CustomerStatus;
import com.guitarcenter.scheduler.service.SearchService;

/**
 * Implements a Solr based search-service for Scheduler.
 *
 * <AUTHOR> Emes <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

@Service("searchService")
public class SolrSearchServiceImpl implements SearchService {
    private static final Logger LOG = LoggerFactory.getLogger(SolrSearchServiceImpl.class);

    /* Various Solr parameter components */

    private static final String ANY_WORDS = "*";
    private static final String SOLR_FIELD_VALUE_SEPARATOR = ":";
    private static final String SOLR_MANDATORY_FIELD = "+";
    private static final String SOLR_MUST_NOT_MATCH_FIELD = "-";
    private static final String SOLR_SCORE_FIELD = "score";
    private static final String SOLR_QUICK_SEARCH_FILTER_QUERY =
            SOLR_MANDATORY_FIELD + SearchDTO.SOLR_TYPE_FIELD +
                    SOLR_FIELD_VALUE_SEPARATOR + AppConstants.SEARCH_CUSTOMER_TYPE_STRING +
                    " " + SOLR_MUST_NOT_MATCH_FIELD + SearchDTO.SOLR_STATUS_FIELD +
                    SOLR_FIELD_VALUE_SEPARATOR + CustomerStatus.CUSTOMER_STATUS_CANCELLED;

    private static final String SOLR_LOCATION_SEARCH_FILTER_QUERY =
            SOLR_MANDATORY_FIELD + SearchDTO.SOLR_TYPE_FIELD +
                    SOLR_FIELD_VALUE_SEPARATOR + AppConstants.SEARCH_LOCATION_TYPE_STRING;

    private static final String SOLR_SITE_FILTER_QUERY_PREFIX =
            SearchDTO.SOLR_SITE_ID_FIELD + SOLR_FIELD_VALUE_SEPARATOR;

    private static final Integer SOLR_QUICK_SEARCH_NUM_RESULTS = Integer.valueOf(3);
    private static final String SOLR_QUICK_SEARCH_DEF_TYPE = "edismax";
    private static final String SOLR_CUSTOMER_SEARCH_DEF_TYPE = "edismax";

    private static final String SOLR_QUICK_SEARCH_BOOST_FIELDS =
            SearchDTO.SOLR_SEARCH_FIRST_NAME_FIELD + "^1.5 " +
                    SearchDTO.SOLR_SEARCH_LAST_NAME_FIELD;

    private static final String SOLR_LOCATION_SEARCH_BOOST_FIELDS =
            SearchDTO.SOLR_SEARCH_LOCATION_NAME_FIELD;

    private static final String SOLR_CUSTOMER_SEARCH_BOOST_FIELDS =
            SearchDTO.SOLR_SEARCH_FIRST_NAME_FIELD + "^1.5 " +
                    SearchDTO.SOLR_SEARCH_LAST_NAME_FIELD + " " +
                    SearchDTO.SOLR_SEARCH_EMAIL_FIELD + " " +
                    SearchDTO.SOLR_SEARCH_PHONE_FIELD + " " +
                    SearchDTO.SOLR_SEARCH_EXTERNAL_ID_FIELD;

    private static final List<SortClause> SOLR_QUICK_SEARCH_SORT_CLAUSES = new ArrayList<>();
    private static final List<SortClause> SOLR_LOCATION_SEARCH_SORT_CLAUSES = new ArrayList<>();
    private static final List<SortClause> SOLR_CUSTOMER_SEARCH_SORT_CLAUSES = new ArrayList<>();

    private static final Pattern SOLR_QUICK_SEARCH_MULTI_WORD_REGEX = Pattern.compile("\\s+");

    @Autowired
    private SolrClient solrClient;

    static {
        SOLR_QUICK_SEARCH_SORT_CLAUSES.add(new SortClause(SOLR_SCORE_FIELD, ORDER.desc));
        SOLR_QUICK_SEARCH_SORT_CLAUSES.add(new SortClause(SearchDTO.SOLR_FIRST_NAME_FIELD, ORDER.asc));
        SOLR_QUICK_SEARCH_SORT_CLAUSES.add(new SortClause(SearchDTO.SOLR_LAST_NAME_FIELD, ORDER.asc));

        SOLR_LOCATION_SEARCH_SORT_CLAUSES.add(new SortClause(SOLR_SCORE_FIELD, ORDER.desc));
        SOLR_LOCATION_SEARCH_SORT_CLAUSES.add(new SortClause(SearchDTO.SOLR_LOCATION_NAME_FIELD, ORDER.asc));

        SOLR_CUSTOMER_SEARCH_SORT_CLAUSES.add(new SortClause(SearchDTO.SOLR_FIRST_NAME_FIELD, ORDER.asc));
        SOLR_CUSTOMER_SEARCH_SORT_CLAUSES.add(new SortClause(SearchDTO.SOLR_LAST_NAME_FIELD, ORDER.asc));
    }

    @Override
    public void updateRecord(Object record) throws SolrServerException, IOException {
        if (LOG.isDebugEnabled()) {
            LOG.debug("Updating record {}", record);
        }
        SearchDTO dto = SearchDTOBuilder.buildSearchDTO(record);
        if (dto != null) {
            solrClient.addBean(dto);
            solrClient.commit();
        } else {
            if (LOG.isInfoEnabled()) {
                LOG.info("Unable to build a Solr document for record {}", record);
            }
        }
    }

    @Override
    public void updateRecordSec(Object record) throws SolrServerException, IOException {
        if (LOG.isDebugEnabled()) {
            LOG.debug("Updating record {}", record);
        }
        SearchDTO dto = SearchDTOBuilder.buildSearchSecDTO(record);
        if (dto != null) {
            solrClient.addBean(dto);
            solrClient.commit();
        } else {
            if (LOG.isInfoEnabled()) {
                LOG.info("Unable to build a Solr document for record {}", record);
            }
        }
    }

    @Override
    public List<SearchDTO> quickSearch(long siteId, String searchTerm, List<String> solrFilter) throws SolrServerException {
        if (LOG.isDebugEnabled()) {
            LOG.debug("about to search for term {} in site {}", searchTerm, siteId);
        }
        if (StringUtils.isBlank(searchTerm)) {
            if (LOG.isDebugEnabled()) {
                LOG.debug("skipping search because searchTerm is blank");
            }
            return Collections.emptyList();
        }
        searchTerm = searchTerm.trim();
        SolrQuery query = new SolrQuery();
        query.setFilterQueries(SOLR_SITE_FILTER_QUERY_PREFIX + siteId, SOLR_QUICK_SEARCH_FILTER_QUERY);

        if (solrFilter != null) {
            for (String fq : solrFilter) {
                if (StringUtils.isNotBlank(fq)) {
                    query.addFilterQuery(fq);
                }
            }
        }

        query.setRows(SOLR_QUICK_SEARCH_NUM_RESULTS);
        query.setFields(
                SearchDTO.SOLR_TYPE_FIELD,
                SearchDTO.SOLR_RECORD_ID_FIELD,
                SearchDTO.SOLR_FIRST_NAME_FIELD,
                SearchDTO.SOLR_LAST_NAME_FIELD,
                SearchDTO.SOLR_EMAIL_FIELD,
                SearchDTO.SOLR_PHONE_FIELD,
                SearchDTO.SOLR_EXTERNAL_ID_FIELD,
                SearchDTO.SOLR_LESSON_COUNT,
                SearchDTO.SOLR_INSTRUMENT_TYPE_FIELD
        );
        query.set(DisMaxParams.QF, SOLR_QUICK_SEARCH_BOOST_FIELDS);
        query.set("defType", SOLR_QUICK_SEARCH_DEF_TYPE);
        query.setSorts(SOLR_QUICK_SEARCH_SORT_CLAUSES);

        Matcher multiWord = SOLR_QUICK_SEARCH_MULTI_WORD_REGEX.matcher(searchTerm);
        if (multiWord.find()) {
            searchTerm = buildNameSearch(SOLR_QUICK_SEARCH_MULTI_WORD_REGEX.split(searchTerm));
        }
        query.setQuery(searchTerm);

        QueryResponse response = null;
        try {
            response = solrClient.query(query);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        List<SearchDTO> results = response.getBeans(SearchDTO.class);

        if (response.getResults().getNumFound() > results.size()) {
            results.add(SearchDTOBuilder.buildMoreResultsSearchDTO(response.getResults().getNumFound() - results.size()));
        }
        if (LOG.isDebugEnabled()) {
            LOG.debug("returning search results {}", results);
        }
        return results;
    }

    @Override
    public List<SearchDTO> locationSearch(long siteId, String searchTerm) throws SolrServerException {
        if (LOG.isDebugEnabled()) {
            LOG.debug("about to search for term {} in site {}", searchTerm, siteId);
        }
        SolrQuery query = new SolrQuery();
        searchTerm = searchTerm.trim();
        query.setFields(
                SearchDTO.SOLR_TYPE_FIELD,
                SearchDTO.SOLR_RECORD_ID_FIELD,
                SearchDTO.SOLR_LOCATION_NAME_FIELD,
                SearchDTO.SOLR_ADDRESS_ONE_FIELD,
                SearchDTO.SOLR_EXTERNAL_ID_FIELD,
                SearchDTO.SOLR_ADDRESS_TWO_FIELD,
                SearchDTO.SOLR_CITY_FIELD,
                SearchDTO.SOLR_COUNTRY_FIELD,
                SearchDTO.SOLR_ENABLED_FIELD,
                SearchDTO.SOLR_FAX_FIELD,
                SearchDTO.SOLR_PHONE_FIELD,
                SearchDTO.SOLR_STATE_FIELD,
                SearchDTO.SOLR_STATUS_FIELD,
                SearchDTO.SOLR_ZIP_FIELD
        );
        query.set(DisMaxParams.QF, SOLR_LOCATION_SEARCH_BOOST_FIELDS);
        query.set("defType", SOLR_QUICK_SEARCH_DEF_TYPE);
        query.setSorts(SOLR_LOCATION_SEARCH_SORT_CLAUSES);

        query.setFilterQueries(SOLR_SITE_FILTER_QUERY_PREFIX + siteId, SOLR_LOCATION_SEARCH_FILTER_QUERY);
        query.setRows(Integer.MAX_VALUE);

        String queryParam = "";
        if (StringUtils.isBlank(searchTerm)) {
            queryParam = "+" + SearchDTO.SOLR_SEARCH_LOCATION_NAME_FIELD + ":*";
        } else {
            try {
                Long.parseLong(searchTerm);
                if (LOG.isDebugEnabled()) {
                    LOG.debug("The search item is Studio number:{}", searchTerm);
                }
                queryParam = "+" + SearchDTO.SOLR_SEARCH_STUDIO_NUMBER + ":" + Long.parseLong(searchTerm);
            } catch (NumberFormatException e) {
                if (LOG.isDebugEnabled()) {
                    LOG.debug("The search item is LocationName:{}", searchTerm);
                }
                queryParam = "+" + SearchDTO.SOLR_SEARCH_LOCATION_NAME_FIELD + ":" + ANY_WORDS + searchTerm + ANY_WORDS;
            }
        }
        query.setQuery(queryParam);

        QueryResponse response = null;
        try {
            response = solrClient.query(query);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        List<SearchDTO> results = response.getBeans(SearchDTO.class);
        if (LOG.isDebugEnabled()) {
            LOG.debug("The returning search results {}", results);
        }
        return results;
    }

    @Override
    public List<SearchDTO> searchCustomersByCriteria(long siteId, String searchCriteria, List<String> solrFilter) throws SolrServerException {
        if (LOG.isDebugEnabled()) {
            LOG.debug("about to search customer list in site {}", siteId);
        }
        searchCriteria = searchCriteria.trim();
        SolrQuery query = new SolrQuery();
        query.setFilterQueries(SOLR_SITE_FILTER_QUERY_PREFIX + siteId, SOLR_QUICK_SEARCH_FILTER_QUERY);

        if (solrFilter != null) {
            for (String fq : solrFilter) {
                if (StringUtils.isNotBlank(fq)) {
                    query.addFilterQuery(fq);
                }
            }
        }

        query.setRows(Integer.MAX_VALUE);
        query.setFields(
                SearchDTO.SOLR_TYPE_FIELD,
                SearchDTO.SOLR_RECORD_ID_FIELD,
                SearchDTO.SOLR_FIRST_NAME_FIELD,
                SearchDTO.SOLR_LAST_NAME_FIELD,
                SearchDTO.SOLR_EMAIL_FIELD,
                SearchDTO.SOLR_EXTERNAL_ID_FIELD,
                SearchDTO.SOLR_STATUS_FIELD,
                SearchDTO.SOLR_PHONE_FIELD,
                SearchDTO.SOLR_LESSON_COUNT,
                SearchDTO.SOLR_INSTRUMENT_TYPE_FIELD,
                SearchDTO.SOLR_SECONDARY_EMAIL
        );
        query.set(DisMaxParams.QF, SOLR_CUSTOMER_SEARCH_BOOST_FIELDS);
        query.set("defType", SOLR_CUSTOMER_SEARCH_DEF_TYPE);
        query.setSorts(SOLR_CUSTOMER_SEARCH_SORT_CLAUSES);
        query.setQuery(searchCriteria);

        QueryResponse response = null;
        try {
            response = solrClient.query(query);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        List<SearchDTO> results = response.getBeans(SearchDTO.class);
        if (LOG.isDebugEnabled()) {
            LOG.debug("returning search results {}", results);
        }
        return results;
    }

    private String buildNameSearch(String[] words) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("building a special name search for {}", Arrays.toString(words));
        }
        StringBuilder buf = new StringBuilder()
                .append("+")
                .append(SearchDTO.SOLR_SEARCH_FIRST_NAME_FIELD)
                .append(":")
                .append(words[0])
                .append(" +")
                .append(SearchDTO.SOLR_SEARCH_LAST_NAME_FIELD)
                .append(":\"");
        for (int i = 1; i < words.length; i++) {
            if (i > 1) {
                buf.append(" ");
            }
            buf.append(words[i]);
        }
        String query = buf.append("\"").toString();
        if (LOG.isDebugEnabled()) {
            LOG.debug("returning query {}", query);
        }
        return query;
    }
}
