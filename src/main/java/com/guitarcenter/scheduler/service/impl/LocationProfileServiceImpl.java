package com.guitarcenter.scheduler.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.ActivityAndServiceUtil;
import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.ActivityDAO;
import com.guitarcenter.scheduler.dao.AvailabilityDAO;
import com.guitarcenter.scheduler.dao.LocationDAO;
import com.guitarcenter.scheduler.dao.LocationProfileDAO;
import com.guitarcenter.scheduler.dao.ProfileActivityDAO;
import com.guitarcenter.scheduler.dao.ProfileServiceDAO;
import com.guitarcenter.scheduler.dao.ServiceDAO;
import com.guitarcenter.scheduler.dao.SiteDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.ProfileActivityCriterion;
import com.guitarcenter.scheduler.dao.criterion.ProfileServiceCriterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.dto.ProfileActivityCreateDTO;
import com.guitarcenter.scheduler.dto.ProfileServiceCreateDTO;
import com.guitarcenter.scheduler.dto.ServiceDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.LocationProfile;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileActivity;
import com.guitarcenter.scheduler.model.ProfileService;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.service.LocationProfileService;

@Service
public class LocationProfileServiceImpl implements LocationProfileService, AppConstants {

	private static final Logger	LOG	= LoggerFactory.getLogger(LocationProfileServiceImpl.class);

	@Autowired
	private LocationProfileDAO	locationProfileDAO;
	
	@Autowired
	private ProfileServiceDAO	profileServiceDAO;

	@Autowired
	private AvailabilityDAO		availabilityDAO;

	@Autowired
	private LocationDAO			locationDAO;
	
	@Autowired
	private ServiceDAO			serviceDAO;
	
	@Autowired
	private ActivityDAO			activityDAO;
	
	@Autowired
	private SiteDAO			siteDAO;

	@Autowired
	@Qualifier("profileActivityDAO")
	private ProfileActivityDAO	mProfileActivityDAO;

	@Autowired
	@Qualifier("profileServiceDAO")
	private ProfileServiceDAO	mProfileServiceDAO;



	@Override
	@Transactional
	public LocationProfile getLocationProfile(long profileId) {
		return locationProfileDAO.get(profileId, DAOHelper.FETCH_MORE_SERVICES | DAOHelper.FETCH_MORE_ACTIVITIES);
	}



	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	@Override
	public LocationProfile createProfileFromLocation(long locationId, long siteId, Person updatedBy) {
		if (LOG.isDebugEnabled()) {
			LOG.debug(
					"Began to create LocationProfile by locationId:{} and siteId:{} and updatedBy:{} in LocationProfileService.createProfileFromLocation",
					locationId, siteId, updatedBy);
		}
		LocationProfile l = null;
		Date updated = new Date();
		Location location = new Location();
		location.setLocationId(locationId);
		try {
			// 1.create availibility
			Availability a = this.constructAvailability(siteId, updated, updatedBy);
			availabilityDAO.save(a, updatedBy.getPersonId());
			// 2.create profile
			l = this.constructLocationProfile(siteId, updated, updatedBy, a);
			locationProfileDAO.save(l, updatedBy.getPersonId());
			location = locationDAO.get(locationId);
			location.setLocationProfile(l);
			// 3.update location with created locationprofile
			locationDAO.update(location, updatedBy.getPersonId());
		} catch (Exception e) {
			LOG.error("Caught {} when creating LocationiProfile from LocationProfileService.createProfileFromLocation",
					e);
			throw new RuntimeException();
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug(
					"Created LocationProfile by locationId:{} and siteId:{} and updatedBy:{} in LocationProfileService.createProfileFromLocation finished",
					locationId, siteId, updatedBy);
		}
		return l;
	}



	/**
	 * A internal method to construct LocationProfile object
	 * 
	 * @param siteId
	 * @param updated
	 * @param updatedBy
	 * @param a
	 * @return
	 */
	private LocationProfile constructLocationProfile(long siteId, Date updated, Person updatedBy, Availability a) {
		LocationProfile l = new LocationProfile();
		l.setUpdated(updated);
		l.setEnabled(Enabled.Y);
		l.setUpdatedBy(updatedBy);
		Site site = new Site();
		site.setSiteId(siteId);
		l.setSite(site);
		/* XXX: MEmes:
		 * Later phases will support multiple timezones and per-profile TZs, but
		 * for now use the system default TZ.
		 */
		l.setTimeZone(DEFAULT_TIME_ZONE);
		l.setAvailability(a);
		return l;
	}



	/**
	 * A internal method to construct Availability object for LocationProfile
	 * 
	 * @param siteId
	 * @param updated
	 * @param updatedBy
	 * @return
	 */
	private Availability constructAvailability(long siteId, Date updated, Person updatedBy) {
		Availability a = new Availability();
		Site site = new Site();
		site.setSiteId(siteId);
		a.setSite(site);
		a.setUpdated(updated);
		a.setUpdatedBy(updatedBy);
		return a;
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void saveProfileActivity(ProfileActivity pProfileActivity, Person pPerson) {
		mProfileActivityDAO.save(pProfileActivity, pPerson);
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void saveProfileService(ProfileService pProfileService, Person pPerson) {
		mProfileServiceDAO.save(pProfileService, pPerson);
	}



	@Override
	public Set<com.guitarcenter.scheduler.model.Service> getProfileSevices(long pProfileId) {
		Set<com.guitarcenter.scheduler.model.Service> result = new HashSet<com.guitarcenter.scheduler.model.Service>();
		Criterion<ProfileService, ProfileService> criterion = ProfileServiceCriterion.findByProfileId(pProfileId);
		List<ProfileService> profileServiceList = mProfileServiceDAO.search(criterion, DAOHelper.FETCH_SERVICE);
		for (ProfileService profileService : profileServiceList) {
			result.add(profileService.getService());
		}
		return result;
	}



	@Override
	public List<com.guitarcenter.scheduler.model.Service> getProfileServiceList(long pProfileId) {
		Criterion<ProfileService, ProfileService> criterion = ProfileServiceCriterion.findByProfileId(pProfileId);
		List<ProfileService> list = mProfileServiceDAO.search(criterion, DAOHelper.FETCH_SERVICE);
		List<com.guitarcenter.scheduler.model.Service> result = new ArrayList<com.guitarcenter.scheduler.model.Service>();
		com.guitarcenter.scheduler.model.Service service = null;
		for (ProfileService profileService : list) {
			service = profileService.getService();
			service.setEnabled(profileService.getEnabled());
			result.add(service);
		}
		return result;
	}
	
	@Override
	public List<ProfileService> getRealProfileServiceList(long pProfileId) {
		Criterion<ProfileService, ProfileService> criterion = ProfileServiceCriterion.findByProfileId(pProfileId);
		List<ProfileService> list = mProfileServiceDAO.search(criterion, DAOHelper.FETCH_SERVICE);
		List<ProfileService> result = new ArrayList<ProfileService>();
		for (ProfileService profileService : list) {
			result.add(profileService);
		}
		return result;
	}



	@Override
	public List<Activity> getProfileActivityList(long pProfileId) {
		Criterion<ProfileActivity, ProfileActivity> criterion = ProfileActivityCriterion.findByProfileId(pProfileId);
		List<ProfileActivity> list = mProfileActivityDAO.search(criterion, DAOHelper.FETCH_ACTIVITY);
		List<Activity> result = new ArrayList<Activity>();
		Activity activity = null;
		for (ProfileActivity profileActivity : list) {
			if(Enabled.N.equals(profileActivity.getEnabled())){
				continue;
			}
			activity = profileActivity.getActivity();
			activity.setEnabled(profileActivity.getEnabled());
			result.add(activity);
		}
		return result;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void deleteProfileActivity(long pProfileId, long pActivityId) {
		Criterion<ProfileActivity, ProfileActivity> criterion = ProfileActivityCriterion.getByProfileIdAndActivityId(
				pProfileId, pActivityId);
		ProfileActivity profileActivity = mProfileActivityDAO.get(criterion);
		mProfileActivityDAO.delete(profileActivity);
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void deleteProfileService(long pProfileId, long pServiceId) {
		Criterion<ProfileService, ProfileService> criterion = ProfileServiceCriterion.getByProfileIdAndServiceId(
				pProfileId, pServiceId);
		ProfileService profileService = mProfileServiceDAO.get(criterion);
		mProfileServiceDAO.delete(profileService);
	}

	/**
	 * <p>Title: getProfileService</p>
	 * <p>Description: </p>
	 * @param pProfileIdServiceId
	 * @return ProfileService
	 * @see com.guitarcenter.scheduler.service.LocationProfileService#getProfileService(long)
	 */
	@Override
	public ProfileService getProfileService(long pProfileIdServiceId) {
		Criterion<ProfileService, ProfileService> criterion = ProfileServiceCriterion.getByProfileServiceId(pProfileIdServiceId);
		return mProfileServiceDAO.get(criterion);
	}
	
	@Override
	@Transactional
	public Map<String, Object> saveServiceAndActivity(ProfileServiceCreateDTO pscDto,Long profileId,Site site ,Person person,Map<String, Object> map){
		if ((Boolean) map.get("status")) {
			LocationProfile profile = getLocationProfile(profileId);

			// set service to profile
			com.guitarcenter.scheduler.model.Service ser = serviceDAO.get(pscDto.getServiceType());
			ProfileService profileService = new ProfileService();
			profileService.setLocationProfile(profile);
			profileService.setService(ser);
			profileService.setSite(site);
			profileService.setEnabled("on".equals(pscDto.getEnable())?Enabled.Y:Enabled.N);
			saveProfileService(profileService, person);

			// set Activity to profile
			List<Long> idList = SystemUtil.getIdList(pscDto.getActivity());
			Iterator<Long> it = idList.iterator();
			while (it.hasNext()) {
				Long id = it.next();
				Activity act = activityDAO.get(id);
				ProfileActivity profileActivity = new ProfileActivity();
				profileActivity.setLocationProfile(profile);
				profileActivity.setActivity(act);
				profileActivity.setSite(site);
				profileActivity.setEnabled("on".equals(pscDto.getEnable())?Enabled.Y:Enabled.N);
				saveProfileActivity(profileActivity, person);
			}

			map.put("status", true);
			map.put("message", "Save Profile Info successfully!");
		}
		return map;
		
	}
	
	@Override
	public List<ActivityDTO> getActivityListByProfileAndService(long profileId, long serviceId) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("Began to create LocationProfile by profileId:{} and serviceId:{} in LocationProfileService.createProfileFromLocation", profileId, serviceId);
		}
		
		List<ProfileActivity> list = new LinkedList<ProfileActivity>();
		try {
			Criterion<ProfileActivity, ProfileActivity> criterion = ProfileActivityCriterion.findByProfileIdAndServiceIdAndEnabled(profileId, serviceId, Enabled.Y);
			list = mProfileActivityDAO.search(criterion, DAOHelper.FETCH_ACTIVITY);
		} catch (Exception e) {
			LOG.error("Caught exception {} when loading ActivityListByProfileAndService");
		}
		List<ActivityDTO> dtos = new LinkedList<ActivityDTO>();
		if (LOG.isDebugEnabled()) {
			LOG.debug("Got list {} in LocationProfileService.createProfileFromLocation", list);
		}
		if(null != list && !list.isEmpty()) {
			for(ProfileActivity pa : list) {
				Activity a = pa.getActivity();
				dtos.add(new ActivityDTO(a.getActivityId(), a.getActivityName()));
			}
		}
		return dtos;
	}
	
	@Override
	@Transactional
	public Map<String, Object> saveProfileServiceAndProfileActivity(
			ProfileActivityCreateDTO pacd, long profileId, Site site,
			Person person, Map<String, Object> map) {
		
		LocationProfile profile = getLocationProfile(profileId);
		
		Criterion<ProfileService, Boolean> criterion = ProfileServiceCriterion.hasByProfileIdAndServiceIdAndEnabled(profileId, pacd.getServiceType(),Enabled.Y);
		Boolean result = profileServiceDAO.get(criterion);
		
		Criterion<ProfileService, Boolean> criterion1 = ProfileServiceCriterion.hasByProfileIdAndServiceIdAndEnabled(profileId, pacd.getServiceType(),Enabled.N);
		Boolean result1 = profileServiceDAO.get(criterion1);
		
		//there's no profileService
		if(!result && !result1){
			ProfileService profileService = new ProfileService();
			profileService.setEnabled(Enabled.Y);
			profileService.setLocationProfile(profile);
			
			com.guitarcenter.scheduler.model.Service service = new com.guitarcenter.scheduler.model.Service();
			service.setServiceId(pacd.getServiceType());
			profileService.setService(service);
			
			profileService.setSite(site);
			saveProfileService(profileService,person);
		}
		
		Activity activity = activityDAO.get(pacd.getActivityId(),DAOHelper.FETCH_SERVICE);
		ProfileActivity profileActivity = new ProfileActivity();
		profileActivity.setLocationProfile(profile);
		profileActivity.setActivity(activity);
		
		profileActivity.setSite(site);
		profileActivity.setEnabled("on".equals(pacd.getEnable())?Enabled.Y:Enabled.N);
		saveProfileActivity(profileActivity, person);
		map.put("activity", activity);
		map.put("activityDTO", ActivityAndServiceUtil.initActivityDTO(activity));
		return map;
	}
	
	@Override
	public List<com.guitarcenter.scheduler.model.Service> findServiceByProfileIdAndEnabled(
			long profileId, Enabled enabled) {
		Criterion<ProfileService, ProfileService> criterion = ProfileServiceCriterion.findByProfileIdAndEnabled(profileId, enabled);
		List<ProfileService> list = mProfileServiceDAO.search(criterion, DAOHelper.FETCH_SERVICE);
		List<com.guitarcenter.scheduler.model.Service> result = new ArrayList<com.guitarcenter.scheduler.model.Service>();
		for (ProfileService profileService : list) {
			result.add(profileService.getService());
		}
        return result;
	}
	
	@Override
	public List<Activity> findActivityByProfileIdAndEnabled(long profileId,
			Enabled enabled) {
		Criterion<ProfileActivity, ProfileActivity> criterion = ProfileActivityCriterion.findByProfileIdAndEnabled(profileId, enabled);
		List<ProfileActivity> list = mProfileActivityDAO.search(criterion, DAOHelper.FETCH_ACTIVITY);
		List<Activity> result = new ArrayList<Activity>();
		for (ProfileActivity profileActivity : list) {
			result.add(profileActivity.getActivity());
		}
		return result;
	}


	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public boolean checkLocationProfileByTime(long profileId, String startDate, String endDate, String startTime, String endTime) {
		return locationProfileDAO.getLocationProfileByTime(profileId, startDate, startTime, endTime)==null?false:true;
	}
	
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	@Override
	public void disableProfile(long profileId, long personId) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("Disable LocationProfile by profileId {} in LocationProfileService.createProfileFromLocation", profileId);
		}
		try {
			LocationProfile profile = this.getLocationProfile(profileId);
			profile.setEnabled(Enabled.N);
			locationProfileDAO.update(profile, personId);
		} catch (Exception e) {
			LOG.error("Disabling profile error", e);
			throw new RuntimeException();
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("Disable LocationProfile by profileId {} successfully in LocationProfileService.createProfileFromLocation", profileId);
		}
	}
	
	@Transactional
	@Override
	public void batchSaveProServiceAndProActivity(Long profileId, List<Long> serviceIds, List<Long> activityIds,Person updatedBy,Long siteId)throws Exception{
		
		LocationProfile profile = locationProfileDAO.get(profileId);
		Site site = siteDAO.get(siteId);
		
		//save profileActivities
		first:for (Long activityId : activityIds) {
			Criterion<ProfileActivity, ProfileActivity>  criterion = ProfileActivityCriterion.getByProfileIdAndActivityId(profileId, activityId);
			if(mProfileActivityDAO.get(criterion) != null){
				continue first;
			}
			ProfileActivity profileActivity = new ProfileActivity();
			profileActivity.setActivity(activityDAO.get(activityId));
			profileActivity.setEnabled(Enabled.Y);
			profileActivity.setLocationProfile(profile);
			profileActivity.setSite(site);
			profileActivity.setUpdatedBy(updatedBy);
			mProfileActivityDAO.save(profileActivity, updatedBy);
		}
		
		//save profileService
		second:for (Long serviceId : serviceIds) {
			Criterion<ProfileService, ProfileService>  criterion = ProfileServiceCriterion.getByProfileIdAndServiceId(profileId, serviceId);
			if(mProfileServiceDAO.get(criterion) != null){
				continue second;
			}
			ProfileService profileService = new ProfileService();
			profileService.setEnabled(Enabled.Y);
			profileService.setLocationProfile(profile);
			profileService.setService(serviceDAO.get(serviceId));
			profileService.setSite(site);
			profileService.setUpdatedBy(updatedBy);
			mProfileServiceDAO.save(profileService, updatedBy);
		}
	}
	
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	@Override
	public void enableProfile(long profileId, long personId) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("Enable LocationProfile by profileId {} in LocationProfileService.createProfileFromLocation", profileId);
		}
		try {
			LocationProfile profile = this.getLocationProfile(profileId);
			profile.setEnabled(Enabled.Y);
			locationProfileDAO.update(profile, personId);
		} catch (Exception e) {
			LOG.error("Enabling profile error", e);
			throw new RuntimeException();
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("Enable LocationProfile by profileId {} successfully in LocationProfileService.createProfileFromLocation", profileId);
		}
	}
	
	@Transactional
	@Override
	public List<ActivityDTO> findByProfileIdAndServiceIds(long profileId,
			Long... serviceIds) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("Find activity list in profile {} & serviceIds {} in LocationProfileService.findByProfileIdAndServiceIds", profileId, serviceIds);
		}
		List<ActivityDTO> dtos = new LinkedList<ActivityDTO>();
        try {
            Criterion<ProfileActivity, ProfileActivity> criterion = ProfileActivityCriterion.findByProfileIdAndServiceIds(profileId, serviceIds);
            List<ProfileActivity> list = mProfileActivityDAO.search(criterion);
            if (LOG.isDebugEnabled()) {
                LOG.debug("Find activity list {} in LocationProfileService.findByProfileIdAndServiceIds", list);
            }
            if(null != list && !list.isEmpty()) {
                for(ProfileActivity pa : list) {
                    Activity a = pa.getActivity();
                    dtos.add(new ActivityDTO(a.getActivityId(), a.getActivityName()));
                }
            }
        } catch (Exception e) {
            LOG.error("Caught an exception when {} loading activity list by serviceIds {}", e, serviceIds);
        }
		return dtos;
	}
	
	@Override
	public List<ServiceDTO> findServiceDTOListByProfileAndEnabled(
			long profileId, Enabled enabled) {
		List<com.guitarcenter.scheduler.model.Service> list = this.findServiceByProfileIdAndEnabled(profileId, enabled);
		List<ServiceDTO> dtos = new LinkedList<ServiceDTO>();
		if(null != list && !list.isEmpty()) {
			for(com.guitarcenter.scheduler.model.Service s : list) {
				dtos.add(new ServiceDTO(s.getServiceId(), s.getServiceName()));
			}
		}
		return dtos;
	}
	
}
