package com.guitarcenter.scheduler.service.impl;

import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

import jakarta.annotation.Resource;

import org.jfree.util.Log;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.AvailabilityUtil;
import com.guitarcenter.scheduler.dao.AvailabilityDAO;
import com.guitarcenter.scheduler.dao.LocationProfileDAO;
import com.guitarcenter.scheduler.dao.criterion.AvailabilityCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.dto.StudioHourDTO;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.LocationProfile;
import com.guitarcenter.scheduler.service.AvailabilityService;

@Service("availabilityService")
public class AvailabilityServiceImpl implements AvailabilityService,AppConstants {

	private static final String AVALIBILITY_KEY  = "loadAvailability";
	private static final String WEEKEND_STRING = "0123456";
	private static final String WEEKEND_MORE_CHAR =  " - ";

	@Resource
	private AvailabilityDAO availabilityDAO;
	
	@Resource
	private LocationProfileDAO locationProfileDAO;
	
	@Override
	public Set<StudioHourDTO> mergeMap(final Availability availability) {
		Set<StudioHourDTO> set = new TreeSet<StudioHourDTO>(
				new Comparator<StudioHourDTO>() {
					@Override
					public int compare(StudioHourDTO dto1, StudioHourDTO dto2) {
						
						String weekendString = Arrays.toString(WEEKEND_CHOSE);
						
						return weekendString.indexOf(dto1.getDayString().substring(0,3).trim()) - weekendString.indexOf(dto2.getDayString().substring(0,3).trim());
					}
				}
		);
		
		//only used to get key=day1,day2 day1-dayn value=time fastly
		final Map<String, String> timeMap = new HashMap<String, String>();
		//key is day  value is time
		final Map<String, String> returnMap = new HashMap<String, String>();
		
		if(availability.getSundayStartTime() != null && availability.getSundayEndTime() != null){
			String sundayStart = AvailabilityUtil.format(availability.getSundayStartTime());
			String sundayEnd = AvailabilityUtil.format(availability.getSundayEndTime());
			timeMap.put(sundayStart+WEEKEND_MORE_CHAR+sundayEnd, "0");
		}
		
		if(availability.getMondayEndTime() != null && availability.getMondayStartTime() != null){
			String mondayStart = AvailabilityUtil.format(availability.getMondayStartTime());
			String mondayEnd = AvailabilityUtil.format(availability.getMondayEndTime());
			if(timeMap.get(mondayStart+WEEKEND_MORE_CHAR+mondayEnd) != null){
				timeMap.put(mondayStart+WEEKEND_MORE_CHAR+mondayEnd, timeMap.get(mondayStart+WEEKEND_MORE_CHAR+mondayEnd)+"1");
			}else{
				timeMap.put(mondayStart+WEEKEND_MORE_CHAR+mondayEnd, "1");
			}
		}
		
		if(availability.getTuesdayEndTime() != null && availability.getTuesdayStartTime() != null){
			final String tuesdayStart = AvailabilityUtil.format(availability.getTuesdayStartTime());
			final String tuesdayEnd = AvailabilityUtil.format(availability.getTuesdayEndTime());
			if(timeMap.get(tuesdayStart+WEEKEND_MORE_CHAR+tuesdayEnd) != null){
				timeMap.put(tuesdayStart+WEEKEND_MORE_CHAR+tuesdayEnd, timeMap.get(tuesdayStart+WEEKEND_MORE_CHAR+tuesdayEnd)+"2");
			}else{
				timeMap.put(tuesdayStart+WEEKEND_MORE_CHAR+tuesdayEnd, "2");
			}
		}
		
		if(availability.getWednesdayEndTime() != null && availability.getWednesdayStartTime() != null){
			final String wednesdayStart = AvailabilityUtil.format(availability.getWednesdayStartTime());
			final String wednesdayEnd = AvailabilityUtil.format(availability.getWednesdayEndTime());
			if(timeMap.get(wednesdayStart+WEEKEND_MORE_CHAR+wednesdayEnd) != null){
				timeMap.put(wednesdayStart+WEEKEND_MORE_CHAR+wednesdayEnd, timeMap.get(wednesdayStart+WEEKEND_MORE_CHAR+wednesdayEnd)+"3");
			}else{
				timeMap.put(wednesdayStart+WEEKEND_MORE_CHAR+wednesdayEnd, "3");
			}
		}
		
		if(availability.getThursdayEndTime() != null && availability.getThursdayStartTime() != null){
		    final String thursdayStart = AvailabilityUtil.format(availability.getThursdayStartTime());
		    final String thursdayEnd = AvailabilityUtil.format(availability.getThursdayEndTime());
			if(timeMap.get(thursdayStart+WEEKEND_MORE_CHAR+thursdayEnd) != null){
				timeMap.put(thursdayStart+WEEKEND_MORE_CHAR+thursdayEnd, timeMap.get(thursdayStart+WEEKEND_MORE_CHAR+thursdayEnd)+"4");
			}else{
				timeMap.put(thursdayStart+WEEKEND_MORE_CHAR+thursdayEnd, "4");
			}
		}
		
		if(availability.getFridayEndTime() != null && availability.getFridayStartTime() != null){
		    final String fridayStart = AvailabilityUtil.format(availability.getFridayStartTime());
		    final String fridayEnd = AvailabilityUtil.format(availability.getFridayEndTime());
			if(timeMap.get(fridayStart+WEEKEND_MORE_CHAR+fridayEnd) != null){
				timeMap.put(fridayStart+WEEKEND_MORE_CHAR+fridayEnd, timeMap.get(fridayStart+WEEKEND_MORE_CHAR+fridayEnd)+"5");
			}else{
				timeMap.put(fridayStart+WEEKEND_MORE_CHAR+fridayEnd, "5");
			}
		}
		
		if(availability.getSaturdayEndTime() != null && availability.getSaturdayStartTime() != null){
			final String saturdayStart = AvailabilityUtil.format(availability.getSaturdayStartTime());
			final String saturdayEnd = AvailabilityUtil.format(availability.getSaturdayEndTime());
			if(timeMap.get(saturdayStart+WEEKEND_MORE_CHAR+saturdayEnd) != null){
				timeMap.put(saturdayStart+WEEKEND_MORE_CHAR+saturdayEnd, timeMap.get(saturdayStart+WEEKEND_MORE_CHAR+saturdayEnd)+"6");
			}else{
				timeMap.put(saturdayStart+WEEKEND_MORE_CHAR+saturdayEnd, "6");
			}
		}
		
		for (Map.Entry<String, String> entry : timeMap.entrySet()) {
			returnMap.put(entry.getValue(), entry.getKey());
		}
		
		//return map, merge "," and WEEKEND_MORE_CHAR
		for (Map.Entry<String, String> entry : returnMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            //get showList such as Sun-Thu
            List<String> keys =  AvailabilityUtil.getEditHourSting(key, WEEKEND_STRING);
            for (String string : keys) {
				set.add(new StudioHourDTO(string, value));
			}
			
		}
		
		return set;
	}

	@Transactional
	@Override
	public void update(Availability avalAvailability,
			Long personId) {
		availabilityDAO.update(avalAvailability, personId);
	}

	@Transactional
	@Override
	public Availability findByProfileId(final Long profileId) {
		LocationProfile profile = locationProfileDAO.get(profileId, DAOHelper.FETCH_AVAILABILITY | DAOHelper.FETCH_SITE);
		return profile.getAvailability();
	}

	@Override
	public ModelMap getMap(Long profileId,ModelMap map) {
		Availability availability = findByProfileId(profileId);
		Set<StudioHourDTO> set = mergeMap(availability);
		map.put(AVALIBILITY_KEY, set);
		return map;
	}

	@Override
	public Availability getAvailability(Long id) {
		return availabilityDAO.get(id);
	}
	
	@Transactional
	@Override
	public Availability loadCommonTimeRangeByProfileAndInstructor(
			Long profileId, Long instructorId) {
		try {
			Criterion<Availability, Availability> criterion = AvailabilityCriterion.getByProfileIdAndInstructorId(profileId, instructorId);
			return availabilityDAO.get(criterion);
		} catch (Exception e) {
			Log.error("Caught exception {} when loadCommonTimeRangeByProfileAndInstructor", e);
		}
		return null;
	}
}
