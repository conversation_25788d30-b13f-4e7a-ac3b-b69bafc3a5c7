package com.guitarcenter.scheduler.service.impl;

import java.text.DateFormat;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.AppointmentLogDAO;
import com.guitarcenter.scheduler.dao.AppointmentSeriesDAO;
import com.guitarcenter.scheduler.dao.AvailabilityDAO;
import com.guitarcenter.scheduler.dao.PersonDAO;
import com.guitarcenter.scheduler.dao.criterion.AppointmentSeriesCriterion;
import com.guitarcenter.scheduler.dao.criterion.AvailabilityCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.dto.AppointmentSeriesDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.CheckingResultAvailabilityDTO;
import com.guitarcenter.scheduler.dto.ProfileTimeOffDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.AppointmentLog;
import com.guitarcenter.scheduler.model.AppointmentSeries;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.LocationProfile;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.enums.Canceled;
import com.guitarcenter.scheduler.service.LackingAppointmentService;
import com.guitarcenter.scheduler.service.ProfileTimeOffService;

@Service("lackingAppointmentService")
public class LackingAppointmentServiceImpl implements LackingAppointmentService {

	private static final Logger		LOGGER	= LoggerFactory.getLogger(LackingAppointmentServiceImpl.class);

	@Autowired
	@Qualifier("availabilityDAO")
	private AvailabilityDAO			mAvailabilityDAO;

	@Autowired
	@Qualifier("appointmentSeriesDAO")
	private AppointmentSeriesDAO	mAppointmentSeriesDAO;
	
	@Autowired
	private AppointmentLogDAO appointmentLogDAO;

	@Autowired
	@Qualifier("appointmentDAO")
	private AppointmentDAO			mAppointmentDAO;

	@Autowired
	@Qualifier("personDAO")
	private PersonDAO				mPersonDAO;

	@Autowired
	ProfileTimeOffService profileTimeOffService;



	public AvailabilityDAO getAvailabilityDAO() {
		return mAvailabilityDAO;
	}



	public void setAvailabilityDAO(AvailabilityDAO pAvailabilityDAO) {
		mAvailabilityDAO = pAvailabilityDAO;
	}



	public AppointmentDAO getAppointmentDAO() {
		return mAppointmentDAO;
	}



	public void setAppointmentDAO(AppointmentDAO pAppointmentDAO) {
		mAppointmentDAO = pAppointmentDAO;
	}



	public AppointmentSeriesDAO getAppointmentSeriesDAO() {
		return mAppointmentSeriesDAO;
	}



	public void setAppointmentSeriesDAO(AppointmentSeriesDAO pAppointmentSeriesDAO) {
		mAppointmentSeriesDAO = pAppointmentSeriesDAO;
	}



	private Person getSystemUpdater() {
		Person example = new Person();
		example.setPersonId(1l);
		example.setFirstName("System");
		example.setLastName("Update");
		Person person = mPersonDAO.get(example);
		return person;
	}



	private void saveAppointment(Date createDate, Person systemUpdater, Date startTime, Date endTime, String bandName,
			String note, Set<Customer> customers, Activity activity, LocationProfile profile, Instructor instructor,
			Room room, AppointmentSeries appointmentSeries, Site site) {
		Appointment bean = new Appointment();
		bean.setActivity(activity);
		bean.setAppointmentSeries(appointmentSeries);
		bean.setBandName(bandName);
		bean.setCanceled(Canceled.N);
		bean.setCreateTime(createDate);
		bean.setCustomers(customers);
		bean.setEndTime(endTime);
        if (instructor.getInstructorId() != null) {
		    bean.setInstructor(instructor);
        }
		bean.setLocationProfile(profile);
		bean.setNote(note);
		bean.setRoom(room);
		bean.setSite(site);
		bean.setStartTime(startTime);
		mAppointmentDAO.save(bean, systemUpdater);
		
		//GSSP-210 changes
		AppointmentLog appointmentLog = new AppointmentLog();
		
		appointmentLog = new AppointmentLog();
		
		appointmentLog.setAppointmentId(bean.getAppointmentId());
		
		appointmentLog.setSiteID(bean.getSite().getSiteId());
		appointmentLog.setActivityID(bean.getActivity().getActivityId());
		appointmentLog.setCanceled(bean.getCanceled());
		appointmentLog.setDuration(bean.getDuration());
		appointmentLog.setEndTime(bean.getEndTime());
		
		if(null !=  bean.getInstructor())
			appointmentLog.setInstructorID(bean.getInstructor().getInstructorId());
		
		
		appointmentLog.setNote(bean.getNote());
		appointmentLog.setRoomID(bean.getRoom().getRoomId());
		appointmentLog.setSiteID(bean.getSite().getSiteId());	
		appointmentLog.setStartTime(bean.getStartTime());
		
		StringBuffer sb = new StringBuffer();
		
		if(null != bean.getCustomers() && bean.getCustomers().size() > 0 )
		{	
			Set<Customer> customerSet = bean.getCustomers();
			for(Customer customer : customerSet)
			{
				
				sb.append(customer.getCustomerId());
				sb.append(";");
			}
		}	
		
		appointmentLog.setCustomerId(sb.toString());
		
		appointmentLog.setUpdatedBy(systemUpdater.getPersonId());
	
		//GSSP-322 save method changed.
		appointmentLogDAO.saveEntity(appointmentLog, new Person());

	}



	@Override
	@Transactional
	public Map<String,String> scheduedTask(Date pBeginningDate) //GSSP-268 lack Appointment job issue, changed the return type from void to Map<String, String> 
	{
		//GSSP-268 lack Appointment job issue start
		Map<String,String> mapOfFailedAppointments=new HashMap<String,String>();
		mapOfFailedAppointments.put(AppConstants.FLAG_1, "");
		mapOfFailedAppointments.put(AppConstants.FLAG_2, "");
		mapOfFailedAppointments.put(AppConstants.FLAG_3, "");
		mapOfFailedAppointments.put(AppConstants.FLAG_4, "");
		mapOfFailedAppointments.put(AppConstants.FLAG_5, ""); //GSSP-268 lack Appointment job issue end
		Person systemUpdater = null;
        DateTime maximumDate = null;
		Criterion<AppointmentSeries, AppointmentSeriesDTO> criterion = AppointmentSeriesCriterion
				.findLackingAppointmentSeries(pBeginningDate);
		List<AppointmentSeriesDTO> search = mAppointmentSeriesDAO.search(criterion);
		if (!search.isEmpty()) {
			systemUpdater = getSystemUpdater();
            maximumDate = new DateTime(pBeginningDate).withTime(0,0,0,0).plusDays(7 * 52 * 1); //GSSP-248
		}
		//--GSSP-334
		HashMap<Long,List<ProfileTimeOffDTO>> hm = new HashMap<Long,List<ProfileTimeOffDTO>>();
		
		for (AppointmentSeriesDTO appointmentSeriesDTO : search) {
			int dayOfWeek = appointmentSeriesDTO.getDayOfWeek();
			Long parentRoomId = appointmentSeriesDTO.getParentRoomId();
            DateTime startTime = new DateTime(appointmentSeriesDTO.getNextAppointmentStartTime());
            DateTime endTime = new DateTime(appointmentSeriesDTO.getNextAppointmentEndTime());
			String bandName = appointmentSeriesDTO.getBandName();
			String note = appointmentSeriesDTO.getNote();
			Set<Customer> customers = appointmentSeriesDTO.getCustomers();
			Activity activity = new Activity();
			activity.setActivityId(appointmentSeriesDTO.getActivityId());
			LocationProfile profile = new LocationProfile();
			profile.setProfileId(appointmentSeriesDTO.getProfileId());
			Instructor instructor = new Instructor();
			instructor.setInstructorId(appointmentSeriesDTO.getInstructorId());
			Room room = new Room();
			room.setRoomId(appointmentSeriesDTO.getRoomId());
			AppointmentSeries appointmentSeries = new AppointmentSeries();
			appointmentSeries.setAppointmentSeriesId(appointmentSeriesDTO.getAppointmentSeriesId());
			Site site = new Site();
			site.setSiteId(appointmentSeriesDTO.getSiteId());
			for (int i = 0, count = appointmentSeriesDTO.getNumbers().intValue(); i < count; i++) {
                if (startTime.withTime(0,0,0,0).isAfter(maximumDate)){
                    break;
                }
				Criterion<Availability, CheckingResultAvailabilityDTO> criterion2 = AvailabilityCriterion
						.checkProfileAndInstructorAndRoomAvailability(profile.getProfileId(),
								instructor.getInstructorId(), dayOfWeek, startTime.toDate(), endTime.toDate(), room.getRoomId(),
								parentRoomId,appointmentSeriesDTO.getAppointmentSeriesId().toString()); //GSSP-268 lack Appointment job issue, added a new parameter (AppointmentSeriesId)
				CheckingResultAvailabilityDTO availabilityDTO = mAvailabilityDAO.get(criterion2);
				if (!availabilityDTO.getResult()) {
					LOGGER.error(availabilityDTO.getErorrMessage());
					//GSSP-268 lack Appointment job issue start
					String flag=availabilityDTO.getIssueFlag();
					String value=availabilityDTO.getAppointmentSeriesId().toString();
					if(mapOfFailedAppointments.get(flag).equals(""))
					{
						mapOfFailedAppointments.put(flag, value);
					}
					else
					{
						value=mapOfFailedAppointments.get(flag)+","+value;
						mapOfFailedAppointments.put(flag, value);
					}//GSSP-268 lack Appointment job issue end
					
					break;
				}
				//--GSSP-334
				Format f = new SimpleDateFormat("MM/dd/yy");
		        String strDateApp = f.format(new Date());
		        
				List<ProfileTimeOffDTO> profileTimeList = null;
					
				
				//---GSSP-334	Req 3 -- Lack Appointment job --------------------------------
				boolean flag = true;
				try {
					if(!hm.containsKey(profile.getProfileId())){
						profileTimeList = profileTimeOffService.getAppointmentTimeListForProfileAndDate(profile.getProfileId(),strDateApp);
						hm.put(profile.getProfileId(), profileTimeList);	
					}else{
						profileTimeList =hm.get(profile.getProfileId());
					}
					flag =  checkProfileTimeOffAtLackAppointments(profileTimeList,startTime.toDate(),endTime.toDate());
					
					} catch (Exception e) {
						flag = true;
						LOGGER.error("Caught an exception from Lack Appointment job at profile timeOff for all dates" + " "+ " {}", e);
					}
 
				if(flag){
				saveAppointment(pBeginningDate, systemUpdater, startTime.toDate(), endTime.toDate(), bandName, note, customers, activity,
						profile, instructor, room, appointmentSeries, site);
				}
				//------------------------------------------------------- 
				startTime = new DateTime(startTime).plusDays(7);
				endTime = new DateTime(endTime).plusDays(7);
			}

		}
		mapOfFailedAppointments.values().removeAll(Collections.singleton(""));//GSSP-268 lack Appointment job issue
		return mapOfFailedAppointments;
	}

	//------GSSP-334 for Lack appointment skip for profile time off
	public static boolean checkProfileTimeOffAtLackAppointments(List<ProfileTimeOffDTO> profileTimeOffList,Date appointmentStart,Date appointmentEnd){
		boolean flag = true;
		try {
			
				DateFormat sdf = new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN);
				Format fr =  new SimpleDateFormat("MM/dd/yyyy");
				Format f = new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN);
		        String strFromTime = f.format(appointmentStart);
		        String strToTime = f.format(appointmentEnd);
		        String strDate = fr.format(appointmentStart);

				Date appStart = sdf.parse(strFromTime);
				Date appEnd = sdf.parse(strToTime);
				Date profileStart = null;
				Date profileEnd = null;
				 
				 
				for(ProfileTimeOffDTO pto:profileTimeOffList){

					 if(strDate.equals(pto.getFromDate())){
						 profileStart =  sdf.parse(pto.getFromTime());
						 profileEnd = sdf.parse(pto.getToTime());

						 if(profileStart.before(profileEnd) && appStart.before(appEnd)){
								if(!appStart.after(profileEnd))
									flag = false;
								else if (!appEnd.before(profileStart)){
									flag = false;
								}
								if(appStart.after(profileEnd) && appEnd.after(profileEnd)){
									flag = true;
								}
								if(appStart.before(profileStart) && appEnd.before(profileStart)){
									flag = true;
								}
							} 
     
					  }

				}
	
				} catch (Exception e) {
					LOGGER.error("Caught an exception from Create Lack Appointments with profile timeOff" + "CREATE_APP_LOG "+ " {}", e);	 
				}
 
		    return flag;
		} 
}
