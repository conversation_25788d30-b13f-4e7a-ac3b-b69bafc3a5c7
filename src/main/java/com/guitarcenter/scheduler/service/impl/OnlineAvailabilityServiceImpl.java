package com.guitarcenter.scheduler.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.InstructorDAO;
import com.guitarcenter.scheduler.dao.OnlineAvailabilityDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.InstructorCriterion;
import com.guitarcenter.scheduler.dto.OnLineAvailableDTO;
import com.guitarcenter.scheduler.dto.OnetimeDTO;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Onetime;
import com.guitarcenter.scheduler.model.OnlineAvailability;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.service.OnlineAvailabilityService;
 

@Service("onlineAvailabilityService")
public class OnlineAvailabilityServiceImpl implements OnlineAvailabilityService {
	
	@SuppressWarnings("unused")
    private static final Logger LOG = LoggerFactory.getLogger(OnlineAvailabilityServiceImpl.class);
	
	@Autowired
	private OnlineAvailabilityDAO onlineAvailabilityDAO;
 
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public List<OnlineAvailability> getOnlineAvailabilityByInstructorId(long instructorId) {
		List<OnlineAvailability> onlineAvailabilities = onlineAvailabilityDAO.getOnlineAvailabilityByInstructorId(instructorId);
		//onlineAvailabilityDAO.get
		
		return onlineAvailabilities;
	}
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public List<OnLineAvailableDTO> getOnlineAvailabilityDtoByInstructorId(long instructorId){
		List<OnlineAvailability> onlineAvailabilities = onlineAvailabilityDAO.getOnlineAvailabilityByInstructorId(instructorId);
		List<OnLineAvailableDTO>  onLineDtoList = new ArrayList<OnLineAvailableDTO>();
 
		for (OnlineAvailability dto : onlineAvailabilities) {
			OnLineAvailableDTO onetimeDto = onetimeDateToString(dto);
			onLineDtoList.add(onetimeDto);
		}
		return onLineDtoList;
	}
	
	//getOnlineAvlFormatByInstructorId
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public List<OnLineAvailableDTO> getOnlineAvlFormatByInstructorId(long instructorId){
		List<OnlineAvailability> onlineAvailabilities = onlineAvailabilityDAO.getOnlineAvailabilityByInstructorId(instructorId);
		List<OnLineAvailableDTO>  onLineDtoList = new ArrayList<OnLineAvailableDTO>();
 
		for (OnlineAvailability dto : onlineAvailabilities) {
			OnLineAvailableDTO onetimeDto = onetimeDateFormatToString(dto);
			onLineDtoList.add(onetimeDto);
		}
		return onLineDtoList;
	}
	//HOURS_PATTERN
	private OnLineAvailableDTO onetimeDateFormatToString(OnlineAvailability onetime){
		String fromDate = new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN).format(onetime.getStartTime());
		String fromTime = new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN).format(onetime.getStartTime());
		String toTime = new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN).format(onetime.getEndTime());
		OnLineAvailableDTO onetimeDto = new OnLineAvailableDTO();
		onetimeDto.setStartDate(fromDate);
		onetimeDto.setFromTime(fromTime);
		onetimeDto.setToTime(toTime);
		onetimeDto.setWeekDay(onetime.getDay());
		onetimeDto.setInstructorId(onetime.getInstructor().getInstructorId());
		onetimeDto.setOnlineAvailabilityId(onetime.getOnlineAvailabilityId());
		onetimeDto.setOnetimeStartToEnd(getDayStr(onetime.getDay()) +": "+"" +fromTime+" - "+toTime);
		return onetimeDto;
	}
	
	private OnLineAvailableDTO onetimeDateToString(OnlineAvailability onetime){
		String fromDate = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_DATE).format(onetime.getStartTime());
		String fromTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_TIME).format(onetime.getStartTime());
		String toTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_TIME).format(onetime.getEndTime());
		OnLineAvailableDTO onetimeDto = new OnLineAvailableDTO();
		onetimeDto.setStartDate(fromDate);
		onetimeDto.setFromTime(fromTime);
		onetimeDto.setToTime(toTime);
 
		onetimeDto.setInstructorId(onetime.getInstructor().getInstructorId());
		onetimeDto.setOnlineAvailabilityId(onetime.getOnlineAvailabilityId());
		onetimeDto.setOnetimeStartToEnd(getDayStr(onetime.getDay()) +": "+"" +fromTime+" - "+toTime);
		return onetimeDto;
	}
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public long saveOnlineAvailablity(OnlineAvailability onlineAvailability) {
		return onlineAvailabilityDAO.save(onlineAvailability, onlineAvailability.getUpdatedBy());
	}
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public long saveOrUpdate(OnlineAvailability onlineAvailability,Long personId) {
		Person pUpdatedBy = new Person();
		pUpdatedBy.setPersonId(personId);
		onlineAvailabilityDAO.saveOrUpdate(onlineAvailability, pUpdatedBy);
		
		return 1l;
	}
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED)
	public Boolean deleteOnLineTime(long oneTimeId) {
	
		OnlineAvailability t = new OnlineAvailability();
		t.setOnlineAvailabilityId(oneTimeId);
		onlineAvailabilityDAO.delete(t);
		 
		return true;
	}
	
	private String getDayStr(String num){
		Map<String,String> mb = new HashMap<String,String>();
		String output = "";
		mb.put("0", "Sun");
		mb.put("1", "Mon");
		mb.put("2", "Tue");
		mb.put("3", "Wed");
		mb.put("4", "Thu");
		mb.put("5", "Fri");
		mb.put("6", "Sat");
		output = mb.get(num);
		return output;
	 
	}
}

