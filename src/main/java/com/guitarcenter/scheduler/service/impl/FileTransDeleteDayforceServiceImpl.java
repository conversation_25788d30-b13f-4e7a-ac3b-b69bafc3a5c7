package com.guitarcenter.scheduler.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import com.guitarcenter.scheduler.dto.Break;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Marshaller;
import com.guitarcenter.scheduler.webservice.dto.InstructorAvailableHoursDTO;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.AvailabilityDAO;
import com.guitarcenter.scheduler.dao.criterion.AvailabilityCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dto.AvailabilityValueDTO2;
import com.guitarcenter.scheduler.dto.EmployeeScheduleDTO;
import com.guitarcenter.scheduler.dto.EmployeeScheduleDTO2;
import com.guitarcenter.scheduler.dto.EmployeeScheduleImport;
import com.guitarcenter.scheduler.dto.FinalFreeSlotsAndMaxMinDates;
import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.service.AvailabilityService;
import com.guitarcenter.scheduler.service.FileTransDeleteDayforceService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.service.MailSenderService;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceDTO;
import com.guitarcenter.scheduler.webservice.dto.LocationProfileInfoDTO;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;

@Service("fileTransDeleteDayforceService")
@Transactional
public class FileTransDeleteDayforceServiceImpl implements FileTransDeleteDayforceService {

    private static final Logger LOG = LoggerFactory.getLogger(FileTransDeleteDayforceServiceImpl.class);

    @Autowired
    private AppointmentDAO appointmentDAO;

    @Value("${adp.hostnameGcTraficDel}")
    private String hostnameGcTraficDel;

    @Value("${adp.usernameGcTraficDel}")
    private String usernameGcTraficDel;

    @Value("${adp.connectKeyGcTrafic}")
    private String connectKeyGcTrafic;

    @Value("${adp.fromPathGcTrafic}")
    private String fromPathGcTrafic;

    @Value("${adp.hostPathGcTraficDayForceDel}")
    private String hostPathGcTraficDayForceDel;

    @Value("${adp.usernameDayForce}")
    private String usernameDayForce;

    @Value("${adp.hostnameDayForce}")
    private String hostnameDayForce;

    @Value("${adp.privateKeyPathDayForceDel}")
    private String privateKeyPathDayForceDel;

    @Value("${adp.knownHostsPathDayForceDel}")
    private String knownHostsPathDayForceDel;

    @Value("${adp.encryptFilePath}")
    private String encryptFilePath;

    @Value("${adp.encryptFilePathExtn}")
    private String encryptFilePathExtn;

    @Autowired
    @Qualifier("availabilityDAO")
    private AvailabilityDAO availabilityDAO;

    @Autowired
    private InstructorService instructorService;

    @Autowired
    private AvailabilityService availabilityService;

    @Autowired
    private MailSenderService mailSenderService;

    String EMAIL_TEMPLATE_FOR_CONFLICTING_APPOINTMENT = "conflictingAppointmentsTemplate.ftl";

    @Override
    public List<Map<String, Object>> generateDeleteDayforceInstructorApptRecordsJob(String startDate, String endDate) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("Query master appointment list by profileId {} and time range in {} and {}", startDate, endDate);
        }

        LOG.error("generateDeleteDayforceInstructorApptRecordsJob GT");

        List<EmployeeScheduleDTO> list = new ArrayList<>();

        String startPeriod = startDate.replace(" ", "T");
        String endPeriod = endDate.replace(" ", "T");

        List<AvailabilityValueDTO2> instructorAvailability = new ArrayList<>();
        Criterion<Availability, List<AvailabilityValueDTO2>> criterion = AvailabilityCriterion.getInstructorIdByUpdated();
        instructorAvailability = availabilityDAO.get(criterion);

        LOG.error("DeleteDayforceInstructorAppt  -----");

        List<Map<String, Object>> listObj = new ArrayList<>();
        encryptFilePath = System.getProperty("catalina.base") + "/temp/";
        LOG.error("encryptFilePath  ......... " + encryptFilePath);

        convertObjectToXMLDelete(instructorAvailability, startPeriod, endPeriod, encryptFilePath);

        Calendar cal = Calendar.getInstance();
        String pattern = "MMddyy";
        cal.setTime(new Date());
        String currentDate = new SimpleDateFormat(pattern).format(cal.getTime());
        encryptFilePathExtn = "EMPLOYEE_Delete_Schedule_" + currentDate + ".Ready";

        ChannelSftp sftp = null;
        try {
            sftp = getChannel();
            try {
                sftp.cd(hostPathGcTraficDayForceDel);
                File f1 = new File(fromPathGcTrafic);
                sftp.put(new FileInputStream(f1), f1.getName(), ChannelSftp.OVERWRITE);
            } catch (Exception sftpe) {
                LOG.warn("Caught a JSchException setting up CRM transfer", sftpe);
            }
        } catch (Exception sftpe) {
            LOG.warn("Caught a JSchException setting up CRM transfer", sftpe);
        } finally {
            if (sftp != null) {
                sftp.disconnect();
                try {
                    sftp.getSession().disconnect();
                } catch (JSchException jse) {
                    LOG.warn("Caught a JSchException setting up CRM transfer", jse);
                }
            }
        }
        return listObj;
    }

    private ChannelSftp getChannel() throws IntegrationServiceException {
        if (LOG.isDebugEnabled()) {
            LOG.debug("trying to setup channel for SFTP");
        }
        ChannelSftp channel = null;
        if (isValidEnvironment()) {
            try {
                JSch jsch = new JSch();
                LOG.error("privateKeyPath " + privateKeyPathDayForceDel);
                jsch.addIdentity(privateKeyPathDayForceDel);
                Session session = jsch.getSession(usernameDayForce, hostnameDayForce);
                session.connect();
                channel = (ChannelSftp) session.openChannel("sftp");
                LOG.error("before Connect");
                channel.connect();
                LOG.error("After Connect");
            } catch (JSchException jse) {
                LOG.error("Caught a JSchException setting up Dayforce transfer", jse);
                throw new IntegrationServiceException("SSH setup issue", jse);
            }
        } else {
            LOG.error("Dayforce integration properties are invalid");
            throw new IntegrationServiceException("Dayforce integration properties are invalid");
        }
        return channel;
    }

    private boolean isValidEnvironment() {
        if (LOG.isDebugEnabled()) {
            LOG.debug("validating environment: hostname = {}, username = {}, privateKeyPath = {}, knownHostsPath = {}",
                    hostnameDayForce, usernameDayForce, privateKeyPathDayForceDel, knownHostsPathDayForceDel);
        }
        boolean isValid = StringUtils.isNotBlank(hostnameDayForce) &&
                StringUtils.isNotBlank(usernameDayForce) &&
                StringUtils.isNotBlank(privateKeyPathDayForceDel) &&
                new File(privateKeyPathDayForceDel).canRead();
        return isValid;
    }

    private void convertObjectToXMLDelete(List<AvailabilityValueDTO2> instructorAvailability, String startPeriod, String endPeriod, String encryptFilePath) {
        try {
            JAXBContext context = JAXBContext.newInstance(EmployeeScheduleDTO2.class);
            Marshaller m = context.createMarshaller();
            m.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);
            // m.setProperty("com.sun.xml.bind.xmlDeclaration", true);

            String staDate = startPeriod.replace("T00:00:00", "");
            String ensDate = endPeriod.replace("T23:59:50", "");

            String strMonDateMain = getDateDayStrRVs(staDate) + "T00:00:00";
            String endMonDateMain = getDateDayStrRVs(ensDate) + "T23:59:59";

            List<EmployeeScheduleDTO> finalListDto = new ArrayList<>();

            String startDt = getDateDayStrRVs(staDate);
            String endDt = getDateDayStrRVs(endPeriod.replace("T00:00:00", ""));

            for(int i=0;i<1;i++ ){


                System.out.println("startDt     "+startDt);


                String startMonDt =startDt;
                //staDate = getDateDayStrRVs(staDate);
                staDate= getMMddToDDmm(startDt);
                Date startDateDy = getDateFromStrDDMM(staDate);
                int numb = getDayNumber(startDateDy);
                //List<Break> breakListA = new ArrayList<Break>();
                for(AvailabilityValueDTO2 alvInstr : instructorAvailability){
                    try{

                        System.out.println(" ....   "+alvInstr.getProfilelId());
                        System.out.println(" ....   "+alvInstr.getProfilelId());
                        System.out.println(" ....   "+alvInstr.getInstructor_id());
                        System.out.println(" ....   "+alvInstr.getLocationId());
                        System.out.println(" ....   "+alvInstr.getInsExternalId());

                        // alvInstr.setInstructor_id(alvInstr.getInsExternalId());

                        List<Break> breakListA = new ArrayList<Break>();

                        EmployeeScheduleDTO insAvalbility = new EmployeeScheduleDTO();
                        if(numb ==1)  insAvalbility.setStartTime(alvInstr.getSundayStartTime());
                        if(numb ==1)  insAvalbility.setEndTime(alvInstr.getSundayEndTime());
                        if(numb ==2)  insAvalbility.setStartTime(alvInstr.getMondayStartTime());
                        if(numb ==2)  insAvalbility.setEndTime(alvInstr.getMondayEndTime());
                        if(numb ==3)  insAvalbility.setStartTime(alvInstr.getTuesdayStartTime());
                        if(numb ==3)  insAvalbility.setEndTime(alvInstr.getTuesdayEndTime());
                        if(numb ==4)  insAvalbility.setStartTime(alvInstr.getWednesdayStartTime());
                        if(numb ==4)  insAvalbility.setEndTime(alvInstr.getWednesdayEndTime());
                        if(numb ==5)  insAvalbility.setStartTime(alvInstr.getThursdayStartTime());
                        if(numb ==5)  insAvalbility.setEndTime(alvInstr.getThursdayEndTime());
                        if(numb ==6)  insAvalbility.setStartTime(alvInstr.getFridayStartTime());
                        if(numb  ==6)  insAvalbility.setEndTime(alvInstr.getFridayEndTime());
                        if(numb ==7)  insAvalbility.setStartTime(alvInstr.getSaturdayStartTime());
                        if(numb ==7)  insAvalbility.setEndTime(alvInstr.getSaturdayEndTime());


                        //==System.out.println("  getStartTime  "+insAvalbility.getStartTime());
                        //==System.out.println("  getEndDateTime  "+insAvalbility.getEndTime());


                        //if(insAvalbility.getStartTime() != null && !"null".equals(insAvalbility.getStartTime())){
                        AvailabilityValueDTO2 availValDTO2  = new AvailabilityValueDTO2();

     		/*availValDTO2.setInstructor_id(instructor_id);
     		availValDTO2.setProfilelId(profilelId);
     		availValDTO2.setLocationId(locationId);*/

                        FinalFreeSlotsAndMaxMinDates finalFreeSlotsAndMaxMinDates =getInstructorAvailabilitySlots(alvInstr,staDate);
                        finalFreeSlotsAndMaxMinDates = null;
                        //++++++++++++++++++================================================
                        EmployeeScheduleDTO esd = new EmployeeScheduleDTO();
                        //FinalFreeSlotsAndMaxMinDates finalFreeSlotsAndMaxMinDates2 =getInstructorAvailabilitySlots(alvInstr,endDt);
                        if(finalFreeSlotsAndMaxMinDates == null){

                            esd.setInstructorId(alvInstr.getInsExternalId());
                            esd.setStore(alvInstr.getLocationId());

                            esd.setStartTime(strMonDateMain);
                            esd.setEndTime(endMonDateMain);



                        }else{
                            //++++++++++++++++++================================================

                            List<InstructorAvailableHoursDTO> breakTime = null;

                            try {
                                breakTime = finalFreeSlotsAndMaxMinDates.getFreeSlotsList();
                            } catch (Exception e) {
                                LOG.error("Exception  ...1 "+e.getMessage());

                            }

                            InstructorAvailableHoursDTO instructorAvailableHoursDTO = finalFreeSlotsAndMaxMinDates.getInstructorAvailableHoursminMaxDTO();

           /* InstructorAvailableHoursDTO instructorAvailableHoursDTO2 = null;
            		if(finalFreeSlotsAndMaxMinDates2 != null){
            				instructorAvailableHoursDTO2 = finalFreeSlotsAndMaxMinDates2.getInstructorAvailableHoursminMaxDTO();
            }*/
                            // breakTime = null;
                            for(InstructorAvailableHoursDTO hy:breakTime ){

                                //System.out.println(hy);

                            }

                            //**********************************####################################  alvInstr
                            String insDD = "";
                            String storeDD ="";

                            //String staDate = startPeriod.replace("T00:00:00","");
                            //InstructorServiceResponseDTO result = instructorService.getInstructorDayForceAvailability(insDD,storeDD,staDate);

                            //List<Lessons> lsr = result.getLessons();
                            //List<Break> breakListA = new ArrayList<Break>();
                            //	List<Times> tms= new ArrayList<Times>();

                            for(InstructorAvailableHoursDTO tmsVal :breakTime){
                                //==System.out.println(tmsVal);
                                Break brkDTO= new Break();
                                String avlStartTime =startMonDt+"T"+tmsVal.getAppointmentStartTime();
                                String avlEndTime =startMonDt+"T"+tmsVal.getAppointmentEndTime();

                                avlStartTime =avlStartTime.replace(":00.000", ":00");
                                avlEndTime =avlEndTime.replace(":00.000", ":00");
                                brkDTO.setStartTime(avlStartTime);
                                brkDTO.setEndTime(avlEndTime);

                                brkDTO.setBreakType("m");

                                breakListA.add(brkDTO);
                            }

//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                            //**********************************##################################################


                            // esd.setBreakActvity(breakListA);





                            esd.setInstructorId(alvInstr.getInsExternalId());
                            esd.setStore(alvInstr.getLocationId());

                            ///AVL Time
                            //instructorAvailableHoursDTO2
                            String inStartTime =instructorAvailableHoursDTO.getAppointmentStartTime()+"";
                            String inEndTime =instructorAvailableHoursDTO.getAppointmentEndTime()+"";

                            inStartTime = inStartTime.replace(":00.000", ":00");
                            inEndTime = inEndTime.replace(":00.000", ":00");

                            esd.setStartTime(startMonDt+"T"+inStartTime+"");
                            esd.setEndTime(startMonDt+"T"+inEndTime+"");

                            //esd.setStartTime(strMonDateMain);
                            //esd.setEndTime(endMonDateMain);

                        }
                        finalListDto.add(esd);

                        // }

                        //~~~~~~~~~~~~~~~~~~~~~~
                    }
                    catch(Exception e){
                        //e.printStackTrace();
                        LOG.error("Exception  ...1 "+e.getMessage());
                    }
                }
                startDt =addOneDay(startDt);
            }

            EmployeeScheduleImport output = new EmployeeScheduleImport();
            output.setEmployeeSchedule(finalListDto);
            output.setStartTime(strMonDateMain);
            output.setEndTime(endMonDateMain);
            output.setDeleteLevel("ALL");
            output.setValidationLevel("NONE");

            EmployeeScheduleDTO2 output2 = new EmployeeScheduleDTO2();
            output2.setEmployeeScheduleImport(output);

            String currentDate = new SimpleDateFormat("MMddyy").format(new Date());
            m.marshal(output2, new File(encryptFilePath + "EMPLOYEE_Delete_Schedule_" + currentDate + ".Ready"));
        } catch (Exception e) {
            LOG.error("DELE DAY Force Exception .........3 " + e.getMessage());
        }
    }

    public FinalFreeSlotsAndMaxMinDates getInstructorAvailabilitySlots(AvailabilityValueDTO2 availabilityValueDTO2, String staDate) {
        FinalFreeSlotsAndMaxMinDates finalFreeSlotsAndMaxMinDates = null;
        try {
            InstructorAVLServiceDTO instructorServiceDTO = new InstructorAVLServiceDTO();
            instructorServiceDTO.setInstructorStatus("Active");
            instructorServiceDTO.setInstructorId(availabilityValueDTO2.getInstructor_id());
            instructorServiceDTO.setStartDate(getDateDayStrRVs(staDate));

            LocationProfileInfoDTO locationProfileDetails = new LocationProfileInfoDTO();
            locationProfileDetails.setInstructorId(Long.parseLong(availabilityValueDTO2.getInstructor_id()));
            locationProfileDetails.setProfileID(Long.parseLong(availabilityValueDTO2.getProfilelId()));

            finalFreeSlotsAndMaxMinDates = instructorService.getInstructorFreeSLots(locationProfileDetails, instructorServiceDTO);
        } catch (Exception e) {
            LOG.error("Exception ...freeslot " + e.getMessage());
        }
        return finalFreeSlotsAndMaxMinDates;
    }

    private static String addOneDay(String date) {
        return LocalDate.parse(date).plusDays(1).toString();
    }

    private static String getDateDayStrRVs(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-dd-MM");
        Date insAvlStart = null;
        try {
            insAvlStart = sdf.parse(date);
            Calendar c = Calendar.getInstance();
            c.setTime(insAvlStart);
            insAvlStart = c.getTime();
        } catch (Exception e) {
            LOG.error("Exception getDateDayStrRVs ...1 " + e.getMessage());
        }
        return new SimpleDateFormat("yyyy-MM-dd").format(insAvlStart);
    }

    private String buildDateStr(Date date) {
        return new DateTime(date).toString(DateTimeFormat.forPattern(DateTimeUtil.DATE_PATTERN_SLASH));
    }

    private String buildDateStr1(Date date) {
        return new DateTime(date).toString(DateTimeFormat.forPattern(DateTimeUtil.DATE_PATTERN_HIPHEN));
    }

    private String buildTimeStr(Date date) {
        String result = new DateTime(date).toString(DateTimeFormat.forPattern("hh:mm a"));
        if ("11:59 PM".equals(result)) {
            result = "12:00 AM";
        }
        return result;
    }

    private String buildTimeStr1(String string) {
        return string;
    }

    private String buildInstructorName(Instructor i) {
        String instructorName = "";
        if (null != i) {
            String firstName = i.getPerson().getFirstName();
            String lastName = i.getPerson().getLastName();
            if (StringUtils.isNotBlank(firstName)) {
                instructorName += firstName + " ";
            }
            if (StringUtils.isNotBlank(lastName)) {
                instructorName += lastName;
            }
        }
        return instructorName;
    }

    private String buildCancelledUser(Instructor i) {
        String cancelledUser = "";
        if (null != i) {
            String firstName = i.getPerson().getFirstName();
            String lastName = i.getPerson().getLastName();
            if (StringUtils.isNotBlank(firstName)) {
                cancelledUser += firstName + " ";
            }
            if (StringUtils.isNotBlank(lastName)) {
                cancelledUser += lastName;
            }
        }
        return cancelledUser;
    }

    private String buildCustomerName(Collection<Customer> customerList) {
        StringBuilder name = new StringBuilder();
        if (null != customerList && !customerList.isEmpty()) {
            for (Customer c : customerList) {
                String firstName = c.getPerson().getFirstName();
                String lastName = c.getPerson().getLastName();
                if (StringUtils.isNotBlank(firstName)) {
                    name.append(firstName).append(" ");
                }
                if (StringUtils.isNotBlank(lastName)) {
                    name.append(lastName);
                }
                name.append(", ");
            }
            name.deleteCharAt(name.length() - 2);
        }
        return name.toString();
    }

    private String buildDuration(float minutes) {
        String duration = "";
        if (minutes < 60) {
            duration = minutes + " minutes";
        } else if (60 == minutes) {
            duration = "1 Hour";
        } else {
            duration = (minutes / 60) + " Hrs";
        }
        duration = duration.replace(".0", "");
        return duration;
    }

    private Date getDateFromStrDDMM(String date) {
        DateFormat sdf = new SimpleDateFormat("yyyy-dd-MM");
        Date insAvlStart = null;
        try {
            insAvlStart = sdf.parse(date);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return insAvlStart;
    }

    public static int getDayNumber(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.DAY_OF_WEEK);
    }

    private static String getMMddToDDmm(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date insAvlStart = null;
        try {
            insAvlStart = sdf.parse(date);
            Calendar c = Calendar.getInstance();
            c.setTime(insAvlStart);
            insAvlStart = c.getTime();
        } catch (Exception e) {
            LOG.error("Exception getDateDayStrRVs ...1 " + e.getMessage());
        }
        return new SimpleDateFormat("yyyy-dd-MM").format(insAvlStart);
    }

    private String getDayStr(String num){
        Map<String,String> mb = new HashMap<String,String>();
        String output = "";
        mb.put("0", "Sun");
        mb.put("1", "Mon");
        mb.put("2", "Tue");
        mb.put("3", "Wed");
        mb.put("4", "Thu");
        mb.put("5", "Fri");
        mb.put("6", "Sat");
        output = mb.get(num);
        return output;

    }
    private static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-dd-MM HH:mm:ss");
    private static String getStartTime() {

        Calendar day = Calendar.getInstance();

        day.set(Calendar.MILLISECOND, 0);
        day.set(Calendar.SECOND, 0);
        day.set(Calendar.MINUTE, 0);
        day.set(Calendar.HOUR_OF_DAY, 0);
        // day.add(Calendar.DATE, -1);

        return simpleDateFormat.format(day.getTime());
    }

    private static String getEndTime() {

        Calendar day = Calendar.getInstance();

        day.set(Calendar.MILLISECOND, 0);
        day.set(Calendar.SECOND, 0);
        day.set(Calendar.MINUTE, 59);
        day.set(Calendar.HOUR_OF_DAY, 23);
        //day.add(Calendar.DATE, -1);
        day.add(Calendar.DATE, 15);

        return simpleDateFormat.format(day.getTime());
    }

}
