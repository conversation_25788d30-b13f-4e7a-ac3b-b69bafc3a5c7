/**
 * @Title: RoomNumberServiceImpl.java
 * @Package com.guitarcenter.scheduler.service.impl
 * @Description: TODO
 * Copyright: Copyright (c) 2013 
 * Company:
 * 
 * <AUTHOR>
 * @date Sep 16, 2013 10:27:12 AM
 * @version V1.0
 */
package com.guitarcenter.scheduler.service.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.RoomNumberDAO;
import com.guitarcenter.scheduler.model.RoomNumber;
import com.guitarcenter.scheduler.service.RoomNumberService;

/**
 * @ClassName: RoomNumberServiceImpl
 * @Description: TODO
 * <AUTHOR>
 * @date Sep 16, 2013 10:27:12 AM
 *
 */
@Service(value="roomNumberService")
public class RoomNumberServiceImpl implements RoomNumberService {
	@SuppressWarnings("unused")
    private static final Logger LOG = LoggerFactory.getLogger(RoomNumberServiceImpl.class);
	@Autowired
	@Qualifier("roomNumberDAO")
	private RoomNumberDAO roomNumberDAO;

	/**
	  * <p>Title: getRoomNumberBySiteId</p>
	  * <p>Description: </p>
	  * @param siteId
	  * @return
	  * @see com.guitarcenter.scheduler.service.RoomNumberService#getRoomNumberBySiteId(long)
	  */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public List<RoomNumber> getRoomNumberBySiteId(long siteId) {
		return roomNumberDAO.getRoomNumberBySiteId(siteId);
	}

}
