package com.guitarcenter.scheduler.service.impl;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import jakarta.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.RoleDAO;
import com.guitarcenter.scheduler.model.Role;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.service.RoleService;

@Service("roleService")
public class RoleServiceImpl implements RoleService {

	@Resource
	private RoleDAO roleDAO;
	
	@Transactional
	@Override
	public Set<Role> findBySite(Site site) {
		Role example = new Role();
		example.setSite(site);
		List<Role> roles = roleDAO.search(example);
		return new HashSet<Role>(roles);
	}

}
