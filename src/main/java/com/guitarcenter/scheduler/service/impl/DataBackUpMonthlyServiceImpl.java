package com.guitarcenter.scheduler.service.impl;

import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.guitarcenter.scheduler.dao.DataBackUpMonthlyDAO;
import com.guitarcenter.scheduler.service.DataBackUpMonthlyService;



@Service("dataBackUpMonthlyService")
public class DataBackUpMonthlyServiceImpl implements DataBackUpMonthlyService {

	@Autowired
	private DataBackUpMonthlyDAO mdataBackUpMonthlyDAO;

	@Override
	public Map<String,String> scheduedTask() 
	{
		Map<String,String> mdata = mdataBackUpMonthlyDAO.doBackUpForAppointmentTable();
		return mdata;
	}
	
}
