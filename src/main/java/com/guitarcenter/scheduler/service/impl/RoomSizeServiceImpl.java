/**
 * @Title: RoomSizeServiceImpl.java
 * @Package com.guitarcenter.scheduler.service.impl
 * @Description: TODO
 * Copyright: Copyright (c) 2013 
 * Company:
 * 
 * <AUTHOR>
 * @date Sep 11, 2013 2:17:54 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.RoomSizeDAO;
import com.guitarcenter.scheduler.model.RoomSize;
import com.guitarcenter.scheduler.service.RoomSizeService;

/**
 * @ClassName: RoomSizeServiceImpl
 * @Description: TODO
 * <AUTHOR>
 * @date Sep 11, 2013 2:17:54 PM
 *
 */
@Service(value="roomSizeService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class RoomSizeServiceImpl implements RoomSizeService {
	@Autowired
	@Qualifier("roomSizeDAO")
	private RoomSizeDAO roomSizeDAO;

	/**
	  * <p>Title: getRoomSizeListBySiteId</p>
	  * <p>Description: </p>
	  * @param siteId
	  * @return
	  * @see com.guitarcenter.scheduler.service.RoomSizeService#getRoomSizeListBySiteId(long)
	  */
	@Override
	public List<RoomSize> getRoomSizeListBySiteId(long siteId) {
		return roomSizeDAO.getRoomSizeList(siteId);
	}
	
	@Transactional
	@Override
	public RoomSize getRoomSize(Long id){
		return roomSizeDAO.get(id);
	}

	/**
	  * <p>Title: getRoomSizeListByRoomTypeSiteId</p>
	  * <p>Description: </p>
	  * @param siteId
	  * @param roomTypeId
	  * @return
	  * @see com.guitarcenter.scheduler.service.RoomSizeService#getRoomSizeListByRoomTypeSiteId(long, long)
	  */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public List<RoomSize> getRoomSizeListByRoomTypeSiteId(long siteId,
			long roomTypeId) {
		List<RoomSize> rlist = getRoomSizeListBySiteId(siteId);
		List<RoomSize> list  = new ArrayList<RoomSize>();
		for(RoomSize r : rlist){
			if(r.getRoomType().getRoomTypeId().equals(roomTypeId)){
				list.add(r);
			}
		}
		return list;
	}

}
