/**
 * @Title: RoomTypeServiceImpl.java
 * @Package com.guitarcenter.scheduler.service.impl
 * @Description: TODO
 * Copyright: Copyright (c) 2013 
 * Company:
 * 
 * <AUTHOR>
 * @date Sep 12, 2013 2:08:15 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.RoomTypeDAO;
import com.guitarcenter.scheduler.model.RoomType;
import com.guitarcenter.scheduler.service.RoomTypeService;

/**
 * @ClassName: RoomTypeServiceImpl
 * @Description: TODO
 * <AUTHOR>
 * @date Sep 12, 2013 2:08:15 PM
 *
 */
@Service(value="roomTypeService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class RoomTypeServiceImpl implements RoomTypeService {
	@Autowired
	@Qualifier("roomTypeDAO")
	private RoomTypeDAO roomTypeDAO;
	/**
	 * <p>Title: getRoomTypeList</p>
	 * <p>Description: </p>
	 * @param siteId
	 * @return
	 * @see com.guitarcenter.scheduler.service.RoomTypeService#getRoomTypeList(long)
	 */
	@Override
	public List<RoomType> getRoomTypeList(long siteId) {
		return roomTypeDAO.getRoomTypeList(siteId);
	}
	/**
	  * <p>Title: getRoomType</p>
	  * <p>Description: </p>
	  * @param roomTypeId
	  * @return
	  * @see com.guitarcenter.scheduler.service.RoomTypeService#getRoomType(long)
	  */
	@Override
	public RoomType getRoomType(long roomTypeId) {
		return roomTypeDAO.get(roomTypeId);
	}
	
	 
}
