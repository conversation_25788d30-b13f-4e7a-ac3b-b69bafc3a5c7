/**
 * @Title: OnetimeServiceImpl.java
 * @Package com.guitarcenter.scheduler.service.impl
 * @Description: 
 * Copyright: Copyright (c) 2014 
 * Company:
 * 
 * <AUTHOR>
 * @date May 30, 2014 3:48:13 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.InstructorDAO;
import com.guitarcenter.scheduler.dao.OnetimeDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.InstructorCriterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Onetime;
import com.guitarcenter.scheduler.service.OnetimeService;

/**
 * @ClassName: OnetimeServiceImpl
 * @Description: 
 * <AUTHOR>
 * @date May 30, 2014 3:48:13 PM
 *
 */
@Service("onetimeService")
public class OnetimeServiceImpl implements OnetimeService {
	
	@SuppressWarnings("unused")
    private static final Logger LOG = LoggerFactory.getLogger(OnetimeServiceImpl.class);
	
	@Autowired
	@Qualifier("onetimeDAO")
	private OnetimeDAO onetimeDAO;
	
	@Autowired
	private InstructorDAO instructorDAO;

	/**
	 * <p>Title: getOnetimeByInstructorId</p>
	 * <p>Description: </p>
	 * @param instructorId
	 * @return
	 * @see com.guitarcenter.scheduler.service.OnetimeService#getOnetimeByInstructorId(long)
	 */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public List<Onetime> getOnetimeByInstructorId(long instructorId) {
		return onetimeDAO.getOnetimeByInstructorId(instructorId);
	}

	/**
	 * <p>Title: saveOnetime</p>
	 * <p>Description: </p>
	 * @param onetime
	 * @return
	 * @see com.guitarcenter.scheduler.service.OnetimeService#saveOnetime(com.guitarcenter.scheduler.model.Onetime)
	 */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public long saveOnetime(Onetime onetime) {
		return onetimeDAO.save(onetime, onetime.getUpdatedBy());
	}

	/**
	 * <p>Title: getOnetimeByOnetimeId</p>
	 * <p>Description: </p>
	 * @param onetimeId
	 * @return
	 * @see com.guitarcenter.scheduler.service.OnetimeService#getOnetimeByOnetimeId(long)
	 */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public Onetime getOnetimeByOnetimeId(long onetimeId) {
		return onetimeDAO.get(onetimeId, DAOHelper.FETCH_INSTRUCTOR);
	}

	/**
	 * <p>Title: checkOnetimeAvailabilityByProfileId</p>
	 * <p>Description: </p>
	 * @param startDate
	 * @param startTime
	 * @param endTime
	 * @param profileId
	 * @return
	 * @see com.guitarcenter.scheduler.service.OnetimeService#checkOnetimeAvailabilityByProfileId(java.lang.String, java.lang.String, java.lang.String, long)
	 */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public boolean checkOnetimeAvailabilityByProfileId(String startDate,
			String startTime, String endTime, long profileId) {
		return onetimeDAO.checkOnetimeAvailabilityByProfileId(startDate, startTime, endTime, profileId);
	}

	/**
	 * <p>Title: checkOnetimeByTime</p>
	 * <p>Description: </p>
	 * @param startDate
	 * @param startTime
	 * @param endTime
	 * @param instructorId
	 * @return
	 * @see com.guitarcenter.scheduler.service.OnetimeService#checkOnetimeByTime(java.lang.String, java.lang.String, java.lang.String, long)
	 */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public boolean checkOnetimeByTime(String startDate, String startTime,String endDate,
			String endTime, long instructorId) {
		return onetimeDAO.checkOnetimeByTime(startDate, startTime, endDate,endTime, instructorId);
	}

	/**
	 * <p>Title: getDisplayOnetimeByInstructorId</p>
	 * <p>Description: </p>
	 * @param instructorId
	 * @return
	 * @see com.guitarcenter.scheduler.service.OnetimeService#getDisplayOnetimeByInstructorId(long)
	 */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public List<Onetime> getDisplayOnetimeByInstructorId(long instructorId) {
		List<Onetime> list = onetimeDAO.getDisplayOnetimeByInstructorId(instructorId);
		List<Onetime> l = new ArrayList<Onetime>();
		for(int i = 0; i < list.size(); i++){
			l.add(list.get(i));
			if(l.size()==7){
				break;
			}
		}
		return l;
	}

	/* Jun 12, 2014
	 * 
	 * @see com.guitarcenter.scheduler.service.OnetimeService#deleteOneTime(com.guitarcenter.scheduler.model.Onetime)
	 * If current one time availability has appointment, return false to indicate delete operation has failed.
	 */
	@Override
	@Transactional(propagation=Propagation.REQUIRED)
	public Boolean deleteOneTime(Long oneTimeId) {
		Onetime oneTime = getOnetimeByOnetimeId(oneTimeId);
		Long instructorId = oneTime.getInstructor().getInstructorId();
		
		Criterion<Instructor, Boolean> criterion = InstructorCriterion.checkOneTimeHasAppointment(instructorId, oneTime.getStartTime(),oneTime.getEndTime());
		/**
		 * instructorDAO.get(criterion)
		 * If there has appointment existed, return true.
		 */
		if(instructorDAO.get(criterion)){
			return false;
		}
		
		onetimeDAO.delete(oneTime);
		return true;
	}

	/**
	 * <p>Title: getOnetimeByTime</p>
	 * <p>Description: </p>
	 * @param startDate
	 * @param startTime
	 * @param endTime
	 * @param instructorId
	 * @return
	 * @see com.guitarcenter.scheduler.service.OnetimeService#getOnetimeByTime(java.lang.String, java.lang.String, java.lang.String, long)
	 */
	@Override
	@Transactional(propagation=Propagation.REQUIRED)
	public List<Onetime> getOnetimeByTime(String startDate, String startTime,
			String endTime, long instructorId) {
		return onetimeDAO.getOnetimeByTime(startDate, startTime, endTime, instructorId);
	}

}
