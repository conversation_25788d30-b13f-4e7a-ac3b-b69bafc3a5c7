package com.guitarcenter.scheduler.service.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.service.GetInstructorFullAvailabiltyService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.service.JobNotificationEmailServiceImpl;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceResponseDTO;


@Service("getInstructorFullAvailabiltyService")
public class GetInstructorFullAvailabiltyServiceImpl implements GetInstructorFullAvailabiltyService{

	@Autowired
	private InstructorService instructorService;
	
	@Autowired
    private JobNotificationEmailServiceImpl jobNotificationEmailServiceImpl;
	
	@Value("${adp.elasticIndex}")
    private String elasticIndex;
	
	@Value("${adp.accessKeyId}")
	private String accessKeyId;

	@Value("${adp.secretKey}")
	private String secretKey;
	
	@Value("${adp.serviceName}")
    private String serviceName;
	
	@Value("${adp.region}")
	private String region;

	@Value("${adp.aesEndpoint}")
	private String aesEndpoint;
 
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public List<InstructorAVLServiceResponseDTO> getInstructorFullAvailabilitySlots(List<Instructor> insList){ 
		
		System.out.println("getInstructorFullAvailabilitySlots serv");
		List<InstructorAVLServiceResponseDTO> InstructorAVLServiceList = new ArrayList<InstructorAVLServiceResponseDTO>();
		Calendar c = Calendar.getInstance();
		c.add(Calendar.MINUTE, -15);
		Date currentDatePlusOne = c.getTime();
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");  
        String strDate1 = dateFormat.format(currentDatePlusOne );
		//List<Instructor> insList = instructorService.getFullInstructorsAffectRecent(strDate1);
		DateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
		
		Map<String, InstructorAVLServiceResponseDTO>  locDstRgn = instructorService.getLocationDistRegion();

		  Calendar cal = Calendar.getInstance();
		  cal.add(Calendar.DATE, 2);
		  String dateStr = dateFormat2.format(cal.getTime());
		  //System.out.println(dateStr);
		  InstructorAVLServiceDTO instructorServiceDTO = new InstructorAVLServiceDTO();
		for(Instructor ins:insList){
			InstructorAVLServiceResponseDTO insAvl = new InstructorAVLServiceResponseDTO();
			//---Set Instructor active status - Start
			if("A".equals(ins.getStatus()) && "Y".equals(ins.getEnabled().toString())){
				instructorServiceDTO.setInstructorStatus("Active");
			}else{
				instructorServiceDTO.setInstructorStatus("InActive");
			}
			//---Set Instructor active status - End
			
			/*
			 * String[] filteredStores = new
			 * String[]{"0013","0016","1312","4764","6906","6907","0028","2207","0091",
			 * "0014","0037","0043","0074","1305","1307","1308",
			 * "1310","3020","3021","6606","6901","6902","6903","6908","6909","6921","6922",
			 * "4752","4753","4754","4755","4756","4758","4759","4760","4761",
			 * "4763","0025","0026","0064","0071","0077","0078","5066","5075","5092","5901",
			 * "5902","5904","5906","6401","6402","3450","3451","3503","3504",
			 * "3505","3506","5502","5505","5507","5508","5516","5519","5520","0049","0069",
			 * "0073","0086","0089","0090","7333","7336","7452","7470","8280",
			 * "8400","8551","8606","8607","8609","8703","0023","0054","0068","0080","0099",
			 * "7280","7602","7603","7604","7606","7607","7608","7609","7610",
			 * "6620","6621","6622","6623","6624","6911","6912","6913","6914","6915","6916",
			 * "6918","6920","0030","0031","0032","0033","0034","0035","0036",
			 * "0079","0082","0084","2208","0010","0011","0012","0015","0017","0021","0042",
			 * "0072","1304","1311","1313","7170","7171","7172","6651","6652",
			 * "6654","6655","6919","6924","6925","6926","6927","6928","6929","3452"};
			 */

	        // Convert String Array to List
	        //List<String> filteredStoreslist = Arrays.asList(filteredStores);
			
			instructorServiceDTO.setInstructorId(ins.getInstructorId()+"");
			instructorServiceDTO.setStartDate(dateStr);
			insAvl= instructorService.getInstructorAvailability(instructorServiceDTO);
			
			 InstructorAVLServiceResponseDTO  lst = locDstRgn.get(insAvl.getStoreNumber());
			 if(lst != null){
				 insAvl.setStoreDistrict(lst.getStoreDistrict());
				 insAvl.setStoreRegion(lst.getStoreRegion());
				 insAvl.setStoreLocation(lst.getStoreLocation());
				}
			//if(null != insAvl && insAvl.getStoreNumber() != null && filteredStoreslist.contains(insAvl.getStoreNumber())) {
		//	System.out.println("insAvl:"+insAvl);
                InstructorAVLServiceList.add(insAvl);
            //}
		}
		
		return InstructorAVLServiceList;
 
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public List<InstructorAVLServiceResponseDTO> getDisabledInstructorsFullAvailabiltySlots(){ 
		
	
		List<InstructorAVLServiceResponseDTO> InstructorAVLServiceList = new ArrayList<InstructorAVLServiceResponseDTO>();
		Calendar c = Calendar.getInstance();
		c.add(Calendar.MINUTE, -60);
		Date currentDatePlusOne = c.getTime();
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");  
        String strDate1 = dateFormat.format(currentDatePlusOne );
		List<Instructor> insList = instructorService.getDisabledFullInstructorsAffectRecent(strDate1);
		DateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
		  Calendar cal = Calendar.getInstance();
		  cal.add(Calendar.DATE, 2);
		  String dateStr = dateFormat2.format(cal.getTime());
		   
		  InstructorAVLServiceDTO instructorServiceDTO = new InstructorAVLServiceDTO();
		  //System.out.println("date :"+c.get(Calendar.DATE));
		for(Instructor ins:insList){
			InstructorAVLServiceResponseDTO insAvl = new InstructorAVLServiceResponseDTO();
			//---Set Instructor active status - Start
			if("A".equals(ins.getStatus()) && "Y".equals(ins.getEnabled().toString())){
				instructorServiceDTO.setInstructorStatus("Active");
			}else{
				instructorServiceDTO.setInstructorStatus("InActive");
			}
		//	String[] filteredStores = new String[]{"101","770","771","772","773","774","775","776","777","781","782","783","784","785","789","791"};

	        // Convert String Array to List
	    //    List<String> filteredStoreslist = Arrays.asList(filteredStores);
			
			instructorServiceDTO.setInstructorId(ins.getInstructorId()+"");
			instructorServiceDTO.setStartDate(dateStr);
			insAvl= instructorService.getInstructorAvailability(instructorServiceDTO);
		//	if(null != insAvl && insAvl.getStoreNumber() != null && filteredStoreslist.contains(insAvl.getStoreNumber())) {
                InstructorAVLServiceList.add(insAvl);
         //   }
		}
		
		return InstructorAVLServiceList;
 
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public String getEnvironmentName(){
		String environment=jobNotificationEmailServiceImpl.getEnvironment();
		return environment;
	}

	@Override
	public List<Instructor> getFullInstructorsAffectRecent() { 
		List<Instructor> list = new ArrayList<Instructor>();
	
	
			 list = instructorService.getFullInstructorsAffectRecent();
				  
			 
			 return list;}

	@Override
	public Map<String, String> getPropertiesDataForElasticLoader() {
		Map<String, String> m= new HashMap<String, String>();
		
		m.put("elasticIndex", elasticIndex);
		m.put("accessKeyId", accessKeyId);
		m.put("secretKey", secretKey);
		m.put("serviceName", serviceName);
		m.put("region", region);
		m.put("aesEndpoint", aesEndpoint);
		
		return m;
	 
	}
	
}
