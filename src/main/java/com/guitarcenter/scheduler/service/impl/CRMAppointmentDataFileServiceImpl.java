package com.guitarcenter.scheduler.service.impl;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.criterion.AppointmentCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.dto.CRMAppointmentDataFileDTO;
import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;
import com.guitarcenter.scheduler.integration.service.RemoteCRMAppointmentDataFileService;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.service.CRMAppointmentDataFileService;


@Service("cRMAppointmentDataFileService")
public class CRMAppointmentDataFileServiceImpl implements CRMAppointmentDataFileService {

	private Map<String,CRMAppointmentDataFileDTO> mapOfAppointmentData;
	
	@Autowired
	@Qualifier("appointmentDAO")
	private AppointmentDAO			mAppointmentDAO;
 
	public AppointmentDAO getAppointmentDAO() {
		return mAppointmentDAO;
	}
    
	public void setAppointmentDAO(AppointmentDAO pAppointmentDAO) {
		mAppointmentDAO = pAppointmentDAO;
	}
	
	@Autowired
	private RemoteCRMAppointmentDataFileService remoteCRMAppointmentDataFileService;
 
	@Override
	@Transactional
	public Map<String,CRMAppointmentDataFileDTO> scheduedTask(String dateInString) 
	{
		Criterion<Appointment,Map<String,CRMAppointmentDataFileDTO>> criterion = AppointmentCriterion.getCRMAppointmentDataFile(dateInString);  
		mapOfAppointmentData   = mAppointmentDAO.get(criterion);
		return mapOfAppointmentData;
	}
	
	@Override
    public void encryptAndTransferCRMApppointmentData(Map<String, CRMAppointmentDataFileDTO> cRMAppointmentDataFileMap) throws IntegrationServiceException
	{
		remoteCRMAppointmentDataFileService.encryptAndUpLoadtoRemote(cRMAppointmentDataFileMap);
    
    }

}
