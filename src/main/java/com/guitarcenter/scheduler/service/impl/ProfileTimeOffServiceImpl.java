/**
 * @Title: TimeoffServiceImpl.java
 * @Package com.guitarcenter.scheduler.service.impl
 * Copyright: Copyright (c) 2014 
 * Company:
 * 
 * <AUTHOR>
 * @date Mar 10, 2014 4:52:52 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.service.impl;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.AvailabilityDAO;
import com.guitarcenter.scheduler.dao.ProfileTimeoffDAO;
import com.guitarcenter.scheduler.dao.criterion.AvailabilityCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dto.ProfileTimeOffDTO;
import com.guitarcenter.scheduler.dto.StartTimeDTO;
import com.guitarcenter.scheduler.dto.StudioHourDTO;
import com.guitarcenter.scheduler.dto.TimeLineDTO;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.ProfileTimeoff;
import com.guitarcenter.scheduler.service.ProfileTimeOffService;

/**
 * @ClassName: TimeoffServiceImpl
 * <AUTHOR>
 * @date Mar 10, 2014 4:52:52 PM
 *
 */
@Service(value="profileTimeOffService")
public class ProfileTimeOffServiceImpl implements ProfileTimeOffService {

    private static final Logger LOG = LoggerFactory.getLogger(ProfileTimeOffServiceImpl.class);
	
	@Autowired
	@Qualifier("profileTimeoffDAO")
	private ProfileTimeoffDAO profileTimeoffDAO;
 
	@Autowired
	private AvailabilityDAO availabilityDAO;
	
	//----Changes for GSSP-334 for get the profileTimeOffDetails from DB --------
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public Set<StudioHourDTO> getUpcomingTimeOffByProfileId(Long profileId,Date startDate ) {
		List<ProfileTimeoff> profileTimeoffList = null;
		Set<StudioHourDTO> set = null;
		try {
			
			 Calendar c = Calendar.getInstance(); 
			 c.setTime(startDate); 
			 c.add(Calendar.DATE, 2);
			 profileTimeoffList = profileTimeoffDAO.getUpcomingTimeOffByProfileId(profileId,startDate);
			 set = mergeProfileTimeOffList(profileTimeoffList);
			
		} catch (Exception e) {
			LOG.error("Error during fetching of DateFormat ", e);			
		}
		return set;
		 
	}
	
	public void saveOrUpdateProfileTimeOff(ProfileTimeoff profileTimeoff){
		profileTimeoffDAO.saveOrUpdateProfileTimeOff(profileTimeoff);;
	}

	
	//Req 2 -----------------GSSP334------------------------------------
	/**
	  * <p>Title: saveProfileTimeoff</p>
	  * <p>Description: </p>
	  * @param ProfileTimeoff
	  * @return
	  * @see com.guitarcenter.scheduler.service.ProfileTimeoffService#saveTimeoff(com.guitarcenter.scheduler.model.ProfileTimeoff)
	  */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public long saveProfileTimeoff(ProfileTimeoff profileTimeoff) {
		return profileTimeoffDAO.save(profileTimeoff, profileTimeoff.getUpdatedBy());
	}

	/**
	  * deleteProfileTimeoff
	  * @Title: deleteProfileTimeoff
	  * @param @param  ProfileTimeoff
	  * @return void
	  * @throws
	 */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public void deleteProfileTimeoff(ProfileTimeoff profileTimeoff) {
		profileTimeoffDAO.delete(profileTimeoff);
	}
	
	/**
	  * <p>Title: getDisplayProfileTimeoffByProfileId</p>
	  * <p>Description: </p>
	  * @param profileId
	  * @return
	  * @see com.guitarcenter.scheduler.service.ProfileTimeoffByProfileIdService#getDisplayProfileTimeoffByProfileId(long)
	  */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	
	public List<ProfileTimeoff> getDisplayProfileTimeoffByProfileId(long profileId) {
		List<ProfileTimeoff> list = profileTimeoffDAO.getDisplayProfileTimeoffById(profileId);
		return list;
	}
	
	public ProfileTimeOffDTO getProfileTimeOffIdbyDate(long profileId, String prfDate) {
		 ProfileTimeOffDTO profileTimeOffDto = profileTimeoffDAO.getProfileTimeOffIdbyDate(profileId,prfDate);
		return profileTimeOffDto;
	}
	

	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public ProfileTimeoff getProfileTimeoffByProfileTimeoffId(long profileTimeoffId) {
		ProfileTimeoff pto= new ProfileTimeoff();
		pto = profileTimeoffDAO.getProfileTimeoffByProfileTimeoffId(profileTimeoffId);
		return pto;
	}
	
	
	//Code added for GSSP-334 from 188
	/**
	 * Load profile time off day 
	 * 
	 * @param profile Id
	 * @return
	 */
	@Override
	public  List<StartTimeDTO> getStartTimeByProfileId(Long profileId,Date startDate) {
	
			DateTime today = new DateTime(startDate);
	        int dayOfWeek = today.getDayOfWeek();
	        TimeLineDTO profileTimeOffLine = null;

	        Criterion<Availability, Availability> criterion = AvailabilityCriterion.getByProfileIdAndOnly(profileId);
	        Availability profileTimeOffAvailability = availabilityDAO.get(criterion);
	        Date profileTimeOffStartTimeByDayOfWeek = profileTimeOffAvailability.getStartTimeByDayOfWeek(dayOfWeek);
	        Date profileTimeOffEndTimeByDayOfWeek = profileTimeOffAvailability.getEndTimeByDayOfWeek(dayOfWeek);
 
	        if (profileTimeOffStartTimeByDayOfWeek != null || profileTimeOffEndTimeByDayOfWeek != null) {
	            DateTime profileTimeOffStartDateTime = new DateTime(profileTimeOffStartTimeByDayOfWeek);
	            DateTime profileTimeOffEndDateTime = new DateTime(profileTimeOffEndTimeByDayOfWeek);
		        
	            profileTimeOffLine = new TimeLineDTO(
	                    today.withTime(profileTimeOffStartDateTime.getHourOfDay(), profileTimeOffStartDateTime.getMinuteOfHour(),
	                    profileTimeOffStartDateTime.getSecondOfMinute(), profileTimeOffStartDateTime.getMillisOfSecond()),
	                    today.withTime(profileTimeOffEndDateTime.getHourOfDay(), profileTimeOffEndDateTime.getMinuteOfHour(), 
	                    profileTimeOffEndDateTime.getSecondOfMinute(), profileTimeOffEndDateTime.getMillisOfSecond())
	            );
	        }
	        
	        List<TimeLineDTO> list = Lists.newLinkedList();
	        
	        if (profileTimeOffLine != null) {
	             list.add(profileTimeOffLine);		          
	        }

	        List<StartTimeDTO> startTimeDTOs = Lists.newLinkedList();

	        list = Ordering.natural().onResultOf(new Function<TimeLineDTO, Date>() {
	            @Override
	            public Date apply(TimeLineDTO input) {
	                return input.getStartDateTime().toDate();
	            }
	        }).sortedCopy(list);
	        for (TimeLineDTO timeLineDTO : list) {
	            startTimeDTOs.addAll(generateListByTimeRangeForTimeoff(timeLineDTO.getStartDateTime().toDate(), timeLineDTO.getEndDateTime().toDate()));
	        }

	        return startTimeDTOs;
	}
	
	//Code added for GSSP-188
	public List<StartTimeDTO> generateListByTimeRangeForTimeoff(Date startTime, Date endTime) {

        if (startTime == null || endTime == null) {
            return Collections.emptyList();
        }

		List<StartTimeDTO> startTimeDTOs = Lists.newLinkedList();
        int minutes = new DateTime(endTime).getMinuteOfDay() - new DateTime(startTime).getMinuteOfDay();
        minutes = (int) Math.round(minutes / 30d);
        Date genDate = new DateTime(startTime).toDate();
        for (int i = 0; i <= minutes; i++) {
            if (i > 0) {
                genDate = new DateTime(genDate).plusMinutes(30).toDate();
            }
            String genTime = new DateTime(genDate).toString(DateTimeUtil.TIME_FORMAT_HH_MM);
            int genHour = Integer.parseInt(genTime.split(":")[0]);
            if (i == minutes && 0 == genHour) {
                startTimeDTOs.add(new StartTimeDTO("23:59", "12:00 AM"));
            } else if (genHour < 12) {
                String genTimeText = genTime + " AM";
                startTimeDTOs.add(new StartTimeDTO(genTime, genTimeText));
            } else if (12 == genHour) {
                String genTimeText = genTime + " PM";
                startTimeDTOs.add(new StartTimeDTO(genTime, genTimeText));
            } else {
                int hour = genHour - 12;
                int min = Integer.parseInt(genTime.split(":")[1]);
                String time = (hour < 10 ? ("0" + hour) : String.valueOf(hour)) + ":" + ((min == 0 ? ("00") : min));
                String genTimeText = time + " PM";
                startTimeDTOs.add(new StartTimeDTO(genTime, genTimeText));
            }
        }
		return startTimeDTOs;
	}
	

	//----------------------------------------------------------------------334 end
	
	private static final String WEEKEND_MORE_CHAR =  " - ";
	public static final String[] WEEKEND_CHOSE = {"Sun","Mon","Tue","Wed","Thu","Fri","Sat"};
	public Set<StudioHourDTO> mergeProfileTimeOffList(List<ProfileTimeoff> profileTimeoffList) {
		Set<StudioHourDTO> set = new LinkedHashSet<StudioHourDTO>();
 		 
		DateFormat dateFormat = new SimpleDateFormat("hh.mm aa");
		Date startDay = null;
		String stringDay,value= null;
		String startTime,endTime = null;
		Calendar cal=Calendar.getInstance();
 		
		for (ProfileTimeoff pt : profileTimeoffList) {
            startDay = pt.getStartTime();
            cal.setTime(startDay);
            stringDay = getFormattedDate(cal.getTime());
            
            	startTime = dateFormat.format(pt.getStartTime()).toString();
            	endTime =  dateFormat.format(pt.getEndTime()).toString();
            	if("11.59 PM".equals(endTime))endTime = "12.00 AM";
            	value=startTime+WEEKEND_MORE_CHAR+endTime;
            	set.add(new StudioHourDTO(stringDay, value));
 
			
		}
		return set;
	}
	public static String getFormattedDate(Date date33){
        Calendar cal33=Calendar.getInstance();
        cal33.setTime(date33);
        int day=cal33.get(Calendar.DATE);

        if(!((day>10) && (day<19)))
        switch (day % 10) {
        case 1:  
            return new SimpleDateFormat("d'st' MMM ").format(date33);
        case 2:  
            return new SimpleDateFormat("d'nd' MMM ").format(date33);
        case 3:  
            return new SimpleDateFormat("d'rd' MMM ").format(date33);
        default: 
            return new SimpleDateFormat("d'th' MMM ").format(date33);
    }
    return new SimpleDateFormat("d'th' MMM ").format(date33);
} 

	@Override 
	public boolean getAppointmentTimeForProfile(ProfileTimeOffDTO dto,long profileId)
	{
		ProfileTimeOffDTO appointmentDTO = profileTimeoffDAO.getAppointmentTimeForProfile(profileId,dto.getFromDate());
		if(appointmentDTO.getFromTime() == null|| appointmentDTO.getToTime() == null){
			return false;
		}
		boolean flag=false;
		flag =checkProfileTimeIsUnderAppointmentTime(dto,appointmentDTO);
		return flag;
	}
	
	public static boolean checkProfileTimeIsUnderAppointmentTime(ProfileTimeOffDTO dto,ProfileTimeOffDTO appointmentDTO){
		
		boolean flag = true;
		
		try {
			DateFormat sdf = new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN);
			String appointmentStart = appointmentDTO.getFromTime();
			String appointmentEnd = appointmentDTO.getToTime();
			String profileTimeOffStart = dto.getFromTime();
			String profileTimeOffEnd = dto.getToTime();
			
			//  GSSP-334 for Free Appointments
			if (appointmentStart == null && appointmentEnd ==null){
				 flag = false;
				 return flag;
			}
			
			Date profileStart = sdf.parse(profileTimeOffStart);
			Date profileEnd =sdf.parse(profileTimeOffEnd);
			Date appStart = sdf.parse(appointmentStart);
			Date appEnd = sdf.parse(appointmentEnd);
			
			if(profileStart.before(profileEnd) && appStart.before(appEnd)){
				if(!profileEnd.after(appStart))
					flag = false;
				else if (!profileStart.before(appEnd)){
					flag = false;
				}
			}
		
		} catch (ParseException e) {
			LOG.error("Error during  of DateFormat ", e);
			 
		}

    return flag;
} 

// -- GSSP-334 for Free Appointments
public static boolean checkProfileTimeIsUnderGivenTime(String appointmentStart,String appointmentEnd,String profileTimeOffStart,String profileTimeOffEnd) throws Exception{
		
		boolean flag = true;
		
		try {
			DateFormat sdf = new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN);
			Date appStart = sdf.parse(appointmentStart);
			Date appEnd = sdf.parse(appointmentEnd);
			Date profileStart = sdf.parse(profileTimeOffStart);
			Date profileEnd = sdf.parse(profileTimeOffEnd);
			
			if(profileStart.before(profileEnd) && appStart.before(appEnd)){
				if(!appStart.after(profileEnd))
					flag = false;
				else if (!appEnd.before(profileStart)){
					flag = false;
				}
				if(appStart.after(profileEnd) && appEnd.after(profileEnd)){
					flag = true;
				}
				if(appStart.before(profileStart) && appEnd.before(profileStart)){
					flag = true;
				}
			}

		} catch (ParseException e) {
			throw  e;	 
		}
  
    return flag;
} 

	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public boolean checkProfileTimeoffByTime(long profileId,String startDate, String startTime,String endTime) throws Exception {
		
		boolean flag=true;
		ProfileTimeOffDTO profileTimeOffDTO = profileTimeoffDAO.verifySingleAppointmentWithProfileTimeOff(profileId,startDate);
		if(profileTimeOffDTO.getFromTime()!= null || profileTimeOffDTO.getToTime() != null ){
		flag =  checkProfileTimeIsUnderGivenTime(startTime,endTime,profileTimeOffDTO.getFromTime(),profileTimeOffDTO.getToTime());
		}
		return flag;
	}
	
	public List<ProfileTimeOffDTO> getAppointmentTimeListForProfileAndDate(long profileId,String profileTimeOffDate){
		return profileTimeoffDAO.getAppointmentTimeListForProfileAndDate(profileId,profileTimeOffDate);
	}
}
