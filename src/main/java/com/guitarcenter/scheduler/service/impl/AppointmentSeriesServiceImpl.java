package com.guitarcenter.scheduler.service.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_MORE_CUSTOMERS;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.TimeZone;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.AppointmentSeriesDAO;
import com.guitarcenter.scheduler.dao.criterion.AppointmentCriterion;
import com.guitarcenter.scheduler.dao.criterion.AppointmentSeriesCriterion;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.AppointmentSeries;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.enums.Canceled;
import com.guitarcenter.scheduler.service.AppointmentSeriesService;
import com.guitarcenter.scheduler.service.PersonManagerService;

@Service
public class AppointmentSeriesServiceImpl implements AppointmentSeriesService, AppConstants {

	private static final Logger LOG = LoggerFactory.getLogger(AppointmentSeriesServiceImpl.class);
	
	@Autowired
	private AppointmentSeriesDAO appointmentSeriesDAO;

    @Autowired
    private AppointmentDAO appointmentDAO;
    
    @Autowired
    private PersonManagerService personManagerService;
	
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	@Override
	public AppointmentSeries createAppointmentSeries(AppointmentSeries appointmentSeries, Person person) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("AppointmentSeriesService.createAppointmentSeries: start");
		}
		try {
			appointmentSeriesDAO.save(appointmentSeries, person);
			appointmentSeries.setAppointmentSeriesId(appointmentSeries.getAppointmentSeriesId());
		} catch (Exception e) {
			LOG.error("AppointmentSeriesService.createAppointmentSeries: error");
			appointmentSeries = null;
			throw new RuntimeException();
		}
		if(LOG.isDebugEnabled()) {
			LOG.debug("AppointmentSeriesService.createAppointmentSeries: end");
		}
		return appointmentSeries;
	}
	
	@Override
	public AppointmentSeries getAppointmentSeries(AppointmentSeries series) {
		AppointmentSeries ser = null;
		if(LOG.isDebugEnabled()) {
			LOG.debug("AppointmentSeriesService.getAppointmentSeries: start");
		}
		try {
			ser = appointmentSeriesDAO.get(series);
		} catch (Exception e) {
			LOG.error("AppointmentSeriesService.getAppointmentSeries: error");
		}
		if(LOG.isDebugEnabled()) {
			LOG.debug("AppointmentSeriesService.getAppointmentSeries: end");
		}
		return ser;
	}
    
    /**
     * When a customer is cancelled, all current and future appointments for that
     * customer must be removed. This is a hard-delete, there is no value in
     * keeping a cancelled customers future appointments. If the customer ever
     * becomes active again the operational procedure will be to reschedule
     * appointments manually - no need to recover deleted appointments.
     * 
     * @param customerId Identifier of the customer being cancelled
     */
    @Override
    @Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
    public void deleteCancelledCustomerAppointments(long customerId) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("cancelling customer appointments for {}", customerId);
        }
        /* Set the cutoff to start of current day in default timezone, that
         * should be prior to any real appointment time unless stores open super
         * early in Hawaii.
         */
        Calendar cutoff = Calendar.getInstance(TimeZone.getTimeZone(DEFAULT_TIME_ZONE));
        cutoff.set(Calendar.HOUR_OF_DAY, 0);
        cutoff.set(Calendar.MINUTE, 0);
        cutoff.set(Calendar.SECOND, 0);
        cutoff.set(Calendar.MILLISECOND, 0);
        
        /* Find all appointments that contain the customer, remove the customer
         * and optionally delete the appointment and series.
         * 
         * Note: it would be quicker to just delete the appointment but there is
         * a risk that there is more than one customer in an appointment, so do
         * it the iterative way.
         */
        List<AppointmentSeries> allSeries =
            appointmentSeriesDAO.search(AppointmentSeriesCriterion.findSeriesForCustomerEndingAfter(customerId, cutoff.getTime()), FETCH_MORE_CUSTOMERS);
        for (AppointmentSeries series: allSeries) {
            removeCustomerFromSeries(series, customerId, cutoff.getTime());
        }
        if (LOG.isDebugEnabled()) {
            LOG.debug("customer appointments cancelled");
        }
    }
	
    /**
     * This method will examine each appointment associated with the series and
     * remove the customer as appropriate, updating the stored copy. If there
     * are appointments for which the customer is the only customer, then the
     * appointment will be deleted.
     * 
     * @param series an instance of AppointmentSeries to update
     * @param customerId identifier of the customer to remove
     * @param cutoff timestamp to use for appointment cutoff
     */
    private void removeCustomerFromSeries(AppointmentSeries series,
                                          long customerId, Date cutoff) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("examining series {} for customers matching {}", series,
                      customerId);
        }
        /* Get all the appointments in the series, then iterate through them.
         * 
         * Note: all appointments are fetched so that the series can be
         * updated efficiently if there are appointments left.
         */
        Appointment lastAppointment = null;
        boolean keepCustomerInSeries = false;
		String multiCustFlag = "";
        List<Appointment> appointments =
            appointmentDAO.search(AppointmentCriterion.findBySeries(series.getAppointmentSeriesId().longValue()), FETCH_MORE_CUSTOMERS);
        for (Appointment appointment: appointments) {
            if (appointment == null) {
                continue;
            }
            
            /* Skip any existing appointments that start and end before the
             * cutoff timestamp, but be sure to update lastAppointment if the
             * current appointment ends after lastAppointment.
             */
            if (cutoff.compareTo(appointment.getStartTime()) > 0 &&
                cutoff.compareTo(appointment.getEndTime()) > 0) {
                if (lastAppointment == null ||
                    lastAppointment.getEndTime().compareTo(appointment.getEndTime()) < 0) {
                    lastAppointment = appointment;
					multiCustFlag = "PREVIOUS_APPOINTMENT";
                }
                if (!keepCustomerInSeries &&
                    appointmentContainsCustomer(appointment, customerId)) {
                    keepCustomerInSeries = true;
                }
                continue;
            }
            if (appointmentContainsCustomer(appointment, customerId)) {
                
            	//---GSSP 381 Capture Lesson Appointment by cancelled customer changes
                if (appointment.getCustomers().size()==1) {
                	if(!"Y".equals(appointment.getCanceled().toString())){
                	appointment.setCanceled(Canceled.Y);
                    appointmentDAO.update(appointment,
                                          personManagerService.getSystemUpdatePerson());
                	}
                }else if (appointment.getCustomers().size()>1){
					removeCustomerFromAppointment(appointment, customerId);
					if(!"Y".equals(appointment.getCanceled().toString())){
                    appointmentDAO.update(appointment,
                                          personManagerService.getSystemUpdatePerson());
					}
                    /* The appointment was not deleted, so make sure that
                     * lastApppointment is updated to refer to this appointment
                     * if it occurs later in the series.
                     */
                    if (lastAppointment == null ||
                        lastAppointment.getEndTime().compareTo(appointment.getEndTime()) < 0) {
                        lastAppointment = appointment;
                        multiCustFlag = "";
                    }
                }
            } else {
                /* The appointment was not modified at all; make sure that
                 * lastApppointment is updated to refer to this appointment
                 * if it occurs later in the series.
                 */
                if (lastAppointment == null ||
                    lastAppointment.getEndTime().compareTo(appointment.getEndTime()) < 0) {
                    lastAppointment = appointment;
                    multiCustFlag = "";
                }
            }
        }
        
        /* Is there a lastAppointment? If so then there must be at least one
         * remaining appointment in this series.
         */
        if (lastAppointment != null) {
            if (LOG.isDebugEnabled()) {
                LOG.debug("customers and appointments remain in series, updating {}",
                          series);
            }
 
            if(series.getCustomers().size() >1 && "PREVIOUS_APPOINTMENT".equals(multiCustFlag)){
               	series.setSeriesEndTime(lastAppointment.getEndTime());
               }
            if (!keepCustomerInSeries) {
               // if(removeAppointmentSeries)removeCustomerFromAppointmentSeries(series, customerId);
            }

            if(series.getCustomers().size() ==1 ){
           	series.setSeriesEndTime(lastAppointment.getEndTime());
           }

            appointmentSeriesDAO.update(series,
                                        personManagerService.getSystemUpdatePerson());
        } else {
            if (LOG.isDebugEnabled()) {
                LOG.debug("series {} is effectively empty, deleting", series);
            }
            //---GSSP 381 Capture Lesson Appointment by cancelled customer changes
            series.setSeriesEndTime(new Date());
            if(checkStartDateWithCurrentDate(series.getSeriesStartTime()))
            {
            	series.setSeriesEndTime(series.getSeriesStartTime());	
            }
            appointmentSeriesDAO.update(series,
                                        personManagerService.getSystemUpdatePerson());
        }
        if (LOG.isDebugEnabled()) {
            LOG.debug("exit");
        }
    }
    
    private boolean checkStartDateWithCurrentDate(Date startDate) {
    	
    	boolean flag= false;
    	try {
			String currentDate =new SimpleDateFormat("dd-MMM-yyyy").format(new Date());
			Date currentDt  = new SimpleDateFormat("dd-MMM-yyyy").parse(currentDate);
			
			String srtDate =new SimpleDateFormat("dd-MMM-yyyy").format(startDate);
			Date startDt  = new SimpleDateFormat("dd-MMM-yyyy").parse(srtDate);
 
            if(startDt.equals(currentDt) || startDt.after(currentDt)){
            	flag = true;
            }
			
		} catch (ParseException e) {
			LOG.error("AppointmentSeriesService.format : ParseException error");
		}
		return flag;
 
}
    /**
     * Given an appointment and a customer id, this method will remove any
     * matching customer in the appointment.
     * 
     * @param appointment an Appointment instance to examine
     * @param customerId long identifier of customer to remove
     */
    private void removeCustomerFromAppointment(Appointment appointment,
                                               long customerId) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("removing customer with id {} from {}", customerId,
                      appointment);
        }
        if (appointment.getCustomers() == null) {
            LOG.warn("exiting, appointment {} has null customer set",
                     appointment);
            return;
        }
        Iterator<Customer> it = appointment.getCustomers().iterator();
        while(it.hasNext()) {
            Customer customer = it.next();
            if (customer == null) {
                continue;
            }
            if (customer.getCustomerId().longValue() == customerId) {
                if (LOG.isDebugEnabled()) {
                    LOG.debug("found matching customer for id {}, removing",
                              customerId);
                }
                it.remove();
            }
        }
        if (LOG.isDebugEnabled()) {
            LOG.debug("exiting, appointment = {}", appointment);
        }
    }
    
    /**
     * Given an appointment series and a customer id, this method will remove
     * any matching customer in the appointment series.
     * 
     * @param series an AppointmentSeries instance to examine
     * @param customerId long identifier of customer to remove
     */
    private void removeCustomerFromAppointmentSeries(AppointmentSeries appointmentSeries,
                                                     long customerId) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("removing customer with id {} from {}", customerId,
                    appointmentSeries);
        }
        if (appointmentSeries.getCustomers() == null) {
            LOG.warn("exiting, appointment series {} has null customer set",
                     appointmentSeries);
            return;
        }
        Iterator<Customer> it = appointmentSeries.getCustomers().iterator();
        while(it.hasNext()) {
            Customer customer = it.next();
            if (customer == null) {
                continue;
            }
            if (customer.getCustomerId().longValue() == customerId) {
                if (LOG.isDebugEnabled()) {
                    LOG.debug("found matching customer for id {}, removing",
                              customerId);
                }
                it.remove();
            }
        }
        if (LOG.isDebugEnabled()) {
            LOG.debug("exiting, appointmentSeries = {}", appointmentSeries);
        }
    }
    
    /**
     * Helper to determine if customer id is in the appointment provided.
     * 
     * @param appointment Appointment to examine
     * @param customerId long identifier of customer
     * @return true if the appointment contains a customer with matching id,
     *         false otherwise
     */
    private boolean appointmentContainsCustomer(Appointment appointment,
                                                long customerId) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("examining appointment {} customers for id {}",
                      appointment, customerId);
        }
        boolean appointmentContainsCustomer = false;
        if (appointment != null && appointment.getCustomers() != null &&
            !appointment.getCustomers().isEmpty()) {
            for (Customer customer: appointment.getCustomers()) {
                if (customer != null &&
                    customer.getCustomerId().longValue() == customerId) {
                    /* Found a match!
                     */
                    appointmentContainsCustomer = true;
                    break;
                }
            }
        }
        if (LOG.isDebugEnabled()) {
            LOG.debug("returning {}", appointmentContainsCustomer);
        }
        return appointmentContainsCustomer;
    }
}
