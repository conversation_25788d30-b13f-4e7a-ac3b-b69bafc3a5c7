/**
 * @Title: ValidationServiceImpl.java
 * @Package com.guitarcenter.scheduler.service.impl
 * @Description: 
 * Copyright: Copyright (c) 2013 
 * Company:
 * 
 * <AUTHOR>
 * @date Oct 4, 2013 4:12:24 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.service.impl;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.guitarcenter.scheduler.common.util.CalendarUtil;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.enums.SplitRoom;
import com.guitarcenter.scheduler.service.ActivityService;
import com.guitarcenter.scheduler.service.AppointmentService;
import com.guitarcenter.scheduler.service.CustomerService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.service.LocationProfileService;
import com.guitarcenter.scheduler.service.OnetimeService;
import com.guitarcenter.scheduler.service.ProfileTimeOffService;
import com.guitarcenter.scheduler.service.RoomService;
import com.guitarcenter.scheduler.service.TimeoffService;
import com.guitarcenter.scheduler.service.ValidationService;

/**
 * @ClassName: ValidationServiceImpl
 * @Description: 
 * <AUTHOR>
 * @date Oct 4, 2013 4:12:24 PM
 *
 */
@Service(value="validationService")
public class ValidationServiceImpl implements ValidationService {

	@SuppressWarnings("unused")
    private static final Logger LOG = LoggerFactory.getLogger(ValidationServiceImpl.class);
	@Autowired
	InstructorService instructorService;
	@Autowired
	RoomService roomService;
	@Autowired
	AppointmentService appointmentService;
	@Autowired
	LocationProfileService locationProfileService;
	@Autowired
	ActivityService activityService;
	@Autowired
	CustomerService customerService;
	@Autowired
	TimeoffService timeoffService;
	@Autowired
	OnetimeService onetimeService;
	
	@Autowired
	ProfileTimeOffService profileTimeOffService;
	
	/**
	 * <p>Title: checkInstructorByTime</p>
	 * <p>Description: </p>
	 * @param instructorId
	 * @param startDate
	 * @param endDate
	 * @param startTime
	 * @param endTime
	 * @return
	 * @see com.guitarcenter.scheduler.service.ValidationService#checkInstructorByTime(boolean, long, java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	 */
	@Override
	public boolean checkInstructorByTime(long instructorId,
			String startDate, String endDate, String startTime, String endTime) {
		//GCSS-670
		return instructorService.checkInstructorAvailabilityByTime(instructorId, startDate, endDate, startTime, endTime);
	}

	/**
	 * <p>Title: checkInstructorByAppointmentTime</p>
	 * <p>Description: </p>
	 * @param recurring
	 * @param instructorId
	 * @param startDate
	 * @param endDate
	 * @param startTime
	 * @param endTime
	 * @return
	 * @see com.guitarcenter.scheduler.service.ValidationService#checkInstructorByAppointmentTime(boolean, long, java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	 */
	@Override
	public boolean checkInstructorByAppointmentTime(boolean recurring,
			long instructorId, String startDate, String endDate,
			String startTime, String endTime) {
		return instructorService.checkInstructorByAppointmentTime(recurring, instructorId, startDate, endDate, startTime, endTime);
	}

	/**
	 * <p>Title: checkRoomByActivity</p>
	 * <p>Description: </p>
	 * @param roomId
	 * @param activityId
	 * @return
	 * @see com.guitarcenter.scheduler.service.ValidationService#checkRoomByActivity(long, long)
	 */
	@Override
	public boolean checkRoomByActivity(long roomId, long activityId) {
		return roomService.checkRoomByActivity(roomId, activityId);
	}

	/**
	 * <p>Title: checkRoomByAppointmentTime</p>
	 * <p>Description: </p>
	 * @param recurring
	 * @param roomId
	 * @param startDate
	 * @param endDate
	 * @param startTime
	 * @param endTime
	 * @return
	 * @see com.guitarcenter.scheduler.service.ValidationService#checkRoomByAppointmentTime(boolean, long, java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	 */
	@Override
	public boolean checkRoomByAppointmentTime(boolean recurring, long roomId,
			String startDate, String endDate, String startTime, String endTime, long profileId) {
		return roomService.checkRoomByAppointmentTime(recurring, roomId, startDate, endDate, startTime, endTime, profileId);
	}

	/**
	  * <p>Title: checkRoomName</p>
	  * <p>Description: </p>
	  * @param profileId
	  * @param name
	  * @return
	  * @see com.guitarcenter.scheduler.service.ValidationService#checkRoomName(long, java.lang.String)
	  */
	@Override
	public boolean checkRoomName(long profileId, String name) {
		List<Room> roomList = roomService.loadRoomList(profileId);
		for(Room r: roomList){
			if(name.equals(r.getProfileRoomName())){
				return false;
			}
		}
		return true;
	}

	/**
	  * <p>Title: checkSplitByRoomId</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @return
	  * @see com.guitarcenter.scheduler.service.ValidationService#checkSplitByRoomId(long)
	  */
	@Override
	public boolean checkSplitByRoomId(long roomId) {
		return roomService.checkSplitByRoomId(roomId);
	}

	/**
	  * <p>Title: checkRoomScheduleByRoomId</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @return
	  * @see com.guitarcenter.scheduler.service.ValidationService#checkRoomScheduleByRoomId(long)
	  */
	@Override
	public boolean checkRoomScheduleByRoomId(long roomId, long profileId) {
		boolean check = appointmentService.checkAppointmentByRoom(roomId, profileId);
		Room room = roomService.getRoom(roomId);
		boolean checkSplit = true;
		if(check && null == room.getParentRoom()){
			checkSplit = checkSplitRoomScheduleByParentRoomId(roomId, profileId);
		}
		return check && checkSplit;
	}
	
	@Override
	public boolean checkSplitRoomScheduleByParentRoomId(long roomId, long profileId) {
		List<Room> splitRoomList = roomService.getSplitRoomsByParentId(roomId);
		for(Room r : splitRoomList){
			if(!appointmentService.checkAppointmentByRoom(r.getRoomId(), profileId))
				return false;
		}
		return true;
	}

	/**
	  * <p>Title: checkRoomActivities</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @param activityIds
	  * @return
	  * @see com.guitarcenter.scheduler.service.ValidationService#checkRoomActivities(long, java.util.List)
	  */
	@Override
	public boolean checkRoomActivities(long roomId, List<Long> activityIds, long profileId) {
		for(Long activityId : activityIds){
			 if(!appointmentService.checkAppointmentByRoomActivity(roomId, activityId, profileId)){
				 return false;
			 }
		}
		return true;
	}

	/**
	  * <p>Title: checkLocationProfileByTime</p>
	  * <p>Description: </p>
	  * @param profileId
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @return
	  * @see com.guitarcenter.scheduler.service.ValidationService#checkLocationProfileByTime(boolean, long, java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	  */
	@Override
	public boolean checkLocationProfileByTime(long profileId, String startDate, String endDate, String startTime, String endTime) {
		return locationProfileService.checkLocationProfileByTime(profileId, startDate, endDate, startTime, endTime);
	}

	/**
	  * <p>Title: checkActivityByAppointmentTime</p>
	  * <p>Description: </p>
	  * @param recurring
	  * @param activityId
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @return
	  * @see com.guitarcenter.scheduler.service.ValidationService#checkActivityByAppointmentTime(boolean, long, java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	 */
	@Override
	public boolean checkActivityByAppointmentTime(boolean recurring, long activityId,
			String startDate, String endDate, String startTime, String endTime, long profileId) {
		return activityService.checkActivityByAppointmentTime(recurring, activityId, startDate, endDate, startTime, endTime, profileId);
	}

	/**
	  * <p>Title: checkAppointmentByProfileId</p>
	  * <p>Description: </p>
	  * @param profileId
	  * @return
	  * @see com.guitarcenter.scheduler.service.ValidationService#checkAppointmentByProfileId(long)
	  */
	@Override
	public boolean checkAppointmentByProfileId(long profileId) {
		return appointmentService.checkAppointmentByProfile(profileId);
	}

	/**
	  * <p>Title: checkStartTime</p>
	  * <p>Description: </p>
	  * @param startDate
	  * @param startTime
	  * @return
	  * @see com.guitarcenter.scheduler.service.ValidationService#checkStartTime(java.lang.String, java.lang.String)
	  */
	@Override
	public boolean checkStartTime(String startDate, String startTime) {
		return appointmentService.checkStartTime(startDate, startTime);
	}

	/**
	  * <p>Title: checkRoomScheduleByRoomId</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @return
	  * @see com.guitarcenter.scheduler.service.ValidationService#checkRoomScheduleByRoomId(long)
	  */
	@Override
	public boolean checkRoomScheduleByRoomId(long roomId) {
		boolean check = appointmentService.checkAppointmentByRoom(roomId);
		Room room = roomService.getRoom(roomId);
		if(check && null == room.getParentRoom()){
			List<Room> splitRoomList = roomService.getSplitRoomsByParentId(roomId);
			for(Room r : splitRoomList){
				if(!appointmentService.checkAppointmentByRoom(r.getRoomId()))
					return false;
			}
		}
		return check;
	}
	
	/**
	  * <p>Title: checkUpdateRoomByAppointmentTime</p>
	  * <p>Description: </p>
	  * @param recurring
	  * @param sourceRoomId
	  * @param targetRoomId
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @param profileId
	  * @return
	  * @see com.guitarcenter.scheduler.service.ValidationService#checkUpdateRoomByAppointmentTime(boolean, long, long, java.lang.String, java.lang.String, java.lang.String, java.lang.String, long)
	  */
	@Override
	public boolean checkUpdateRoomByAppointmentTime(boolean recurring,
			long sourceRoomId, long targetRoomId, String startDate,
			String endDate, String startTime, String endTime, long profileId, String excludeAppointmentIdParam) {
		Room sourceRoom = roomService.getRoom(sourceRoomId);
		Room targetRoom = roomService.getRoom(targetRoomId);
		if(sourceRoom.getParentRoom() != null && targetRoom.getParentRoom() == null && sourceRoom.getParentRoom().getRoomId() == targetRoomId){
			return checkExcludeRoomByAppointmentTime(recurring, targetRoomId, sourceRoomId, startDate, endDate, startTime, endTime, profileId, excludeAppointmentIdParam);
		} else if(sourceRoom.getParentRoom() != null && targetRoom.getParentRoom() != null && sourceRoomId != targetRoomId && sourceRoom.getParentRoom().getRoomId().equals(targetRoom.getParentRoom().getRoomId())){
			return checkExcludeRoomByAppointmentTime(recurring, targetRoom.getParentRoom().getRoomId(), sourceRoomId, startDate, endDate, startTime, endTime, profileId, excludeAppointmentIdParam);
		} else{
			return checkRoomByAppointmentTime(recurring, targetRoom, startDate, endDate, startTime, endTime, profileId, excludeAppointmentIdParam);
		}
	}
	
	private boolean checkExcludeRoomByAppointmentTime(boolean recurring, long parentRoomId, long splitRoomId, String startDate, String endDate, String startTime, String endTime, long profileId, String excludeAppointmentIdParam){
		boolean check = roomService.checkUpdateRoomByAppointmentTime(recurring, parentRoomId, startDate, endDate, startTime, endTime, profileId, excludeAppointmentIdParam);
		if(check){
			List<Room> splitRoomList = roomService.getSplitRoomsByParentId(parentRoomId);
			for(Room r : splitRoomList){
				if(splitRoomId != r.getRoomId()){
					if(!roomService.checkUpdateRoomByAppointmentTime(recurring, r.getRoomId(), startDate, endDate, startTime, endTime, profileId, excludeAppointmentIdParam))
						return false;
				}
			}
		}
		return check;
	}
	
	private boolean checkRoomByAppointmentTime(boolean recurring, Room room, String startDate, String endDate, String startTime, String endTime, long profileId, String excludeAppointmentIdParam) {
		long roomId = room.getRoomId();
		if(null == room.getParentRoom()){
			boolean check1 = roomService.checkUpdateRoomByAppointmentTime(recurring, roomId, startDate, endDate, startTime, endTime, profileId, excludeAppointmentIdParam);
			if(SplitRoom.N.equals(room.getIsSplitRoom())){
				return check1;
			}else{
				List<Room> splitRoomList = roomService.getSplitRoomsByParentId(roomId);
				boolean check2 = checkSplitRoom(recurring, splitRoomList, startDate, endDate, startTime, endTime, profileId, excludeAppointmentIdParam);
				return check1 && check2;
			}
		}else{
			boolean check1 = roomService.checkUpdateRoomByAppointmentTime(recurring, roomId, startDate, endDate, startTime, endTime, profileId, excludeAppointmentIdParam);
			boolean check2 = roomService.checkUpdateRoomByAppointmentTime(recurring, room.getParentRoom().getRoomId(), startDate, endDate, startTime, endTime, profileId, excludeAppointmentIdParam);
			return check1 && check2;
		}
	}
	
	private boolean checkSplitRoom(boolean recurring, List<Room> roomList, String startDate, String endDate, String startTime, String endTime, long profileId, String excludeAppointmentIdParam){
		for(Room room : roomList){
			long roomId = room.getRoomId();
			if(!roomService.checkUpdateRoomByAppointmentTime(recurring, roomId, startDate, endDate, startTime, endTime, profileId, excludeAppointmentIdParam))
				return false;
		}
		return true;
	}

	@Override
	public Boolean checkRoomupdateByTemplateId(Long templateId){
		List<Room> list = roomService.findByTemplateId(templateId);
		for (Room room : list) {
			Boolean result = checkRoomScheduleByRoomId(room.getRoomId());
			if(!result){
				return false;
			}
		}
		return true;
	}

	@Override
	public boolean checkUpdateInstructorByAppointmentTime(boolean recurring,
			long instructorId, String startDate, String endDate,
			String startTime, String endTime, String excludeAppointmentIdParam) {
		return instructorService.checkUpdateInstructorByAppointmentTime(recurring, instructorId, startDate, endDate, startTime, endTime, excludeAppointmentIdParam);
	}

	/**
	  * <p>Title: checkRoomActivities</p>
	  * <p>Description: </p>
	  * @param roomTemplateId
	  * @param activityIds
	  * @return
	  * @see com.guitarcenter.scheduler.service.ValidationService#checkRoomActivities(long, java.util.List)
	  */
	@Override
	public boolean checkRoomActivities(long roomTemplateId,
			List<Long> activityIds) {
		for(Long activityId : activityIds){
			 if(!appointmentService.checkAppointmentByRoomTemplateIdActivity(roomTemplateId, activityId)){
				 return false;
			 }
		}
		return true;
	}

	/**
	  * <p>Title: checkInstructorByProfileActivityId</p>
	  * <p>Description: </p>
	  * @param instructorId
	  * @param activityId
	  * @param profileId
	  * @return
	  * @see com.guitarcenter.scheduler.service.ValidationService#checkInstructorByProfileActivityId(long, long, long)
	  */
	@Override
	public boolean checkInstructorByProfileActivityId(long instructorId,
			long activityId, long profileId) {
		return instructorService.checkInstructorByProfileActivityId(instructorId, activityId, profileId);
	}

	/**
	  * <p>Title: checkCustomerByAppointmentTime</p>
	  * <p>Description: </p>
	  * @param recurring
	  * @param customerIds
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @param profileId
	  * @return
	  * @see com.guitarcenter.scheduler.service.ValidationService#checkCustomerByAppointmentTime(boolean, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, long)
	  */
	@Override
	public boolean checkCustomerByAppointmentTime(boolean recurring,
			List<Long> customerIds, String startDate, String endDate,
			String startTime, String endTime, long profileId) {
//		String customerIdParam = SystemUtil.getIdParamStringByIdList(customerId);
		for(long customerId : customerIds){
			if(!customerService.checkCustomerByAppointmentTime(recurring, customerId, startDate, endDate, startTime, endTime, profileId)){
				return false;
			}
		}
		return true;
	}
	
	/**
	  * <p>Title: checkUpdateCustomerByAppointmentTime</p>
	  * <p>Description: </p>
	  * @param recurring
	  * @param customerIds
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @param profileId
	  * @param excludeAppointmentIdParam
	  * @return
	  * @see com.guitarcenter.scheduler.service.ValidationService#checkUpdateCustomerByAppointmentTime(boolean, java.util.List, java.lang.String, java.lang.String, java.lang.String, java.lang.String, long, java.lang.String)
	  */
	@Override
	public boolean checkUpdateCustomerByAppointmentTime(boolean recurring,
			List<Long> customerIds, String startDate, String endDate,
			String startTime, String endTime, long profileId,
			String excludeAppointmentIdParam) {
//		String customerIdParam = SystemUtil.getIdParamStringByIdList(customerId);
		for(long customerId : customerIds){
			if(!customerService.checkUpdateCustomerByAppointmentTime(recurring, customerId, startDate, endDate, startTime, endTime, profileId, excludeAppointmentIdParam)){
				return false;
			}
		}
		return true;
	}
	
	/**
	  * <p>Title: checkTimeoffByTime</p>
	  * <p>Description: </p>
	  * @param recurring
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @param instructorId
	  * @return
	  * @see com.guitarcenter.scheduler.service.ValidationService#checkTimeoffByTime(boolean, java.lang.String, java.lang.String, java.lang.String, java.lang.String, long)
	 */
	@Override
	public boolean checkTimeoffByTime(boolean recurring, String startDate, String endDate, String startTime, String endTime, long instructorId){
		if(recurring && !startDate.equals(endDate)){
			return timeoffService.checkTimeoffByRecurringTime(startDate, endDate, startTime, endTime, instructorId);
		}else{
			return timeoffService.checkTimeoffByTime(startDate, startTime, endTime, instructorId);
		}
	}
	
	/** --GSSP-334	Req 3
	  * <p>Title: checkProfileTimeoffByTime</p>
	  * <p>Description: </p>
	  * @param recurring
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @param instructorId
	  * @return
	  * @see com.guitarcenter.scheduler.service.ValidationService#checkTimeoffByTime(boolean, java.lang.String, java.lang.String, java.lang.String, java.lang.String, long)
	 */
	@Override
	public boolean checkProfileTimeoffByTime(long profileId, String startDate, String startTime, String endTime)throws Exception{
 
			return profileTimeOffService.checkProfileTimeoffByTime(profileId,startDate, startTime, endTime);
		 
	}
	

	/**
	  * <p>Title: checkInstructorByAppointmentTime</p>
	  * <p>Description: </p>
	  * @param instructorId
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @return
	  * @see com.guitarcenter.scheduler.service.ValidationService#checkInstructorByAppointmentTime(long, java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	 */
	@Override
	public boolean checkInstructorByAppointmentTime(long instructorId,
			String startDate, String endDate, String startTime, String endTime) {
		return instructorService.checkInstructorByAppointmentTime(instructorId, startDate, endDate, startTime, endTime);
	}

	/**
	 * <p>Title: checkTimeoffOnetime</p>
	 * <p>Description: </p>
	 * @param startDate
	 * @param startTime
	 * @param endTime
	 * @param instructorId
	 * @return
	 * @see com.guitarcenter.scheduler.service.ValidationService#checkTimeoffOnetime(java.lang.String, java.lang.String, java.lang.String, long)
	 */
	@Override
	public boolean checkTimeoffOnetime(String startDate, String startTime,String endDate,
			String endTime, long instructorId) {
		return onetimeService.checkOnetimeByTime(startDate, startTime,endDate, endTime, instructorId);
	}
	
	/**
	 * check one time is valid in given time
	 * <p>Title: checkOnetime</p>
	 * <p>Description: </p>
	 * @param recurring
	 * @param startDate
	 * @param endDate
	 * @param startTime
	 * @param endTime
	 * @param instructorId
	 * @return
	 * @see com.guitarcenter.scheduler.service.ValidationService#checkOnetime(boolean, java.lang.String, java.lang.String, java.lang.String, java.lang.String, long)
	 */
	@Override
	public boolean checkOnetime(boolean recurring, String startDate, String endDate, String startTime,
			String endTime, long instructorId) {
		if(recurring && !startDate.equals(endDate)){
			List<String> startDateList = CalendarUtil.getPeriodWeekDate(startDate, endDate);
			for(String date : startDateList){
				//GCSS-684
				boolean check = onetimeService.getOnetimeByTime(date, startTime, endTime, instructorId).size()==0?false:true;
				if(!check){
					return false;
				}
			}
			return true;
		}else{
			//GCSS-684
			return onetimeService.getOnetimeByTime(startDate, startTime, endTime, instructorId).size()==0?false:true;
		}
	}

	/**
	 * <p>Title: checkProfileAvailabilityByTime</p>
	 * <p>Description: </p>
	 * @param startDate
	 * @param startTime
	 * @param endTime
	 * @param profileId
	 * @return
	 * @see com.guitarcenter.scheduler.service.ValidationService#checkProfileAvailabilityByTime(java.lang.String, java.lang.String, java.lang.String, long)
	 */
	@Override
	public boolean checkProfileAvailabilityByTime(String startDate,
			String startTime, String endTime, long profileId) {
		return onetimeService.checkOnetimeAvailabilityByProfileId(startDate, startTime, endTime, profileId);
	}

	/**
	 * <p>Title: checkAvailabilityAndOntime</p>
	 * <p>Description: </p>
	 * @param instructorId
	 * @param pStartDate
	 * @param pEndDate
	 * @return
	 * @see com.guitarcenter.scheduler.service.ValidationService#checkAvailabilityAndOntime(long, java.util.Date,java.util.Date)
	 */
	@Override
	public boolean checkAvailabilityAndOntime(long instructorId,Date pStartDate, Date pEndDate) {
		return instructorService.checkAvailabilityAndOntime(instructorId, pStartDate, pEndDate);
	}

	@Override
	public boolean validateStartEndDiff(String startDate, String endDate) {
		//264 start
				boolean result=true;
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateTimeUtil.DATE_PATTERN_SLASH);
				//convert String to LocalDate
				LocalDate localStartDate = LocalDate.parse(startDate, formatter);
				LocalDate localEndDate = LocalDate.parse(endDate, formatter);
				long numberOfDays = ChronoUnit.DAYS.between(localStartDate, localEndDate);
				//GSSP-336 :: Corrected to 11 to 12 months :: Adjusted the validation to 365 to 367(Although 366 works fine for safer side update to 367 which don't impact of creating appointments)
				if(numberOfDays>=367)
				{
					result=false;
				}
				
				return result;
	}

	//GSSP-268 Issue fix 2: Start time can't be later than the end date of the series. start
	@Override
	public boolean validateStartTime(String startDate, Date endDate, Appointment a) { //ProdIssue added an additional parameter of the type appointment
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateTimeUtil.DATE_PATTERN_SLASH);
		//convert String and Date to LocalDate
				LocalDate localStartDate = LocalDate.parse(startDate, formatter);
				LocalDate localSeriesreferenceDate = a.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		if(endDate==null)
		{
			
			LocalDate localDummyReferenceEndDate=localSeriesreferenceDate.plusYears(1);
			if(localStartDate.isBefore(localDummyReferenceEndDate))
			{
				return true;
			}
			
				return false;
			
			
			
		}
		else
		{
		
		//convert String and Date to LocalDate
		LocalDate localEndDate = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		if(localStartDate.isBefore(localEndDate))
		{
			return true;
		}
		}
		return false;
	}//Issue fix 2: Start time can't be later than the end date of the series. end
	
}
