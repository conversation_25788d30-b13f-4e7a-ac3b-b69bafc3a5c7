package com.guitarcenter.scheduler.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.guitarcenter.scheduler.dao.ProfileLookupDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.ProfileLookupCriterion;
import com.guitarcenter.scheduler.model.ProfileLookup;
import com.guitarcenter.scheduler.service.ProfileLookupService;


@Service("ProfileLookUpSerive")
public class ProfileLookupServiceimpl implements ProfileLookupService {
	
	
	@Autowired
	@Qualifier("profileLookupDAO")
	private ProfileLookupDAO			profileLookupDAO;
	

	private static final Logger	LOG	= LoggerFactory.getLogger(ProfileLookupServiceimpl.class);
	
	@Override
	public Map<String,List<String>> getProfileLookUpValues()
	{
		Map<String, List<String>> profileLookupMap  = new HashMap<String, List<String>>();
		
		List<String> studioAvailabilityList = new ArrayList<String>();
		
		List<String> serviceList = new ArrayList<String>();
		
		List<String> activityList = new ArrayList<String>();
		
		List<ProfileLookup> profileLookUpList = new ArrayList<ProfileLookup>();
		
		Criterion<ProfileLookup, ProfileLookup> criterion = ProfileLookupCriterion.fetchAll();
		profileLookUpList = profileLookupDAO.search(criterion);
		
		for(ProfileLookup  profileLookup  : profileLookUpList)
		{
			
			if(! profileLookupMap.containsKey("studioTimings"))
			{	
						if(null != profileLookup.getMondayStartTime() && null != profileLookup.getMondayEndTime()){
							studioAvailabilityList.add(0, profileLookup.getMondayStartTime().toString());
							studioAvailabilityList.add(1, profileLookup.getMondayEndTime().toString());
						}else{
							studioAvailabilityList.add(0, null);
							studioAvailabilityList.add(1,null);
						}
						  if( null != profileLookup.getTuesdayStartTime() && null != profileLookup.getTuesdayEndTime()){
						    studioAvailabilityList.add(2, profileLookup.getTuesdayStartTime().toString());
							studioAvailabilityList.add(3, profileLookup.getTuesdayEndTime().toString());
					  }else{
						  studioAvailabilityList.add(2,null);
							studioAvailabilityList.add(3, null);
					  }
					   if(null != profileLookup.getWednesdayStartTime() && null != profileLookup.getWednesdayEndTime()){
						   studioAvailabilityList.add(4, profileLookup.getWednesdayStartTime().toString());
							studioAvailabilityList.add(5, profileLookup.getWednesdayEndTime().toString());
					   }else{
						   studioAvailabilityList.add(4, null);
							studioAvailabilityList.add(5, null);
					   }
					   if(null != profileLookup.getThursdayStartTime() && null != profileLookup.getThursdayEndTime()){
						   studioAvailabilityList.add(6, profileLookup.getThursdayStartTime().toString());
							studioAvailabilityList.add(7, profileLookup.getThursdayEndTime().toString());
					   }else{
						   studioAvailabilityList.add(6, null);
							studioAvailabilityList.add(7, null); 
					   }
					   if(null != profileLookup.getFridayStartTime() && null != profileLookup.getFridayEndTime()){
						   studioAvailabilityList.add(8, profileLookup.getFridayStartTime().toString());
							studioAvailabilityList.add(9, profileLookup.getFridayEndTime().toString());
					   }else{
						   studioAvailabilityList.add(8, null);
							studioAvailabilityList.add(9, null);
					   }
					   if(null != profileLookup.getSaturdayStartTime() && null != profileLookup.getSaturdayEndTime()){
						   studioAvailabilityList.add(10, profileLookup.getSaturdayStartTime().toString());
							studioAvailabilityList.add(11, profileLookup.getSaturdayEndTime().toString());
					   }else{
						   studioAvailabilityList.add(10, null);
							studioAvailabilityList.add(11, null);
					   }
					   if(null != profileLookup.getSundayStartTime() && null != profileLookup.getSundayEndTime() ){
						   studioAvailabilityList.add(12, profileLookup.getSundayStartTime().toString());
							studioAvailabilityList.add(13, profileLookup.getSundayEndTime().toString());
					   }else{
						   studioAvailabilityList.add(12, null);
							studioAvailabilityList.add(13, null);
					   }
					
		
				profileLookupMap.put("studioTimings", studioAvailabilityList);
			}	
				
			if(null !=  profileLookup.getServiceId())
			{
				serviceList.add(profileLookup.getServiceId().toString());
			}
			
			if(null !=  profileLookup.getActivityId())
			{
				activityList.add(profileLookup.getActivityId().toString());
			}
				
		}
		
		if(null != serviceList && serviceList.size() > 0)
			profileLookupMap.put("serviceList", serviceList);
		
		
		if(null != activityList && activityList.size() > 0)
			profileLookupMap.put("activityList", activityList);
		
	
		return profileLookupMap;
	}
	
	
	

	

}

