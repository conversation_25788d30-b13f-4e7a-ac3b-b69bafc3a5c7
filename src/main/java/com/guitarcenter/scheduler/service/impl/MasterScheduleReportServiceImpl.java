package com.guitarcenter.scheduler.service.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.AppointmentSeriesDAO;
import com.guitarcenter.scheduler.dto.UpdateAppointmentDTO;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.AppointmentSeries;
import com.guitarcenter.scheduler.model.AppointmentTransactions;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.enums.Canceled;
import com.guitarcenter.scheduler.service.AppointmentService;
import com.guitarcenter.scheduler.service.CancelHoldAppointmentsService;
import com.guitarcenter.scheduler.service.CustomerService;
import com.guitarcenter.scheduler.service.FileTransferScheduleReportService;
import com.guitarcenter.scheduler.service.MasterScheduleReportService;
import com.guitarcenter.scheduler.service.PersonManagerService;
import com.guitarcenter.scheduler.service.ReportService;


@Service("masterScheduleReportService")
public class MasterScheduleReportServiceImpl implements MasterScheduleReportService{

	
	@Autowired
	@Qualifier("appointmentDAO")
	private AppointmentDAO			mAppointmentDAO;
	
	@Autowired
	private AppointmentSeriesDAO 	appointmentSeriesDAO;
	
	
	@Autowired
	private CustomerService customerService;
	
	@Autowired
	private AppointmentService appointmentService;
	
	@Autowired
    private PersonManagerService personManagerService;

	public AppointmentDAO getAppointmentDAO() {
		return mAppointmentDAO;
	}
    
	public void setAppointmentDAO(AppointmentDAO pAppointmentDAO) {
		mAppointmentDAO = pAppointmentDAO;
	}
 
	@Autowired
	private FileTransferScheduleReportService fileTransferScheduleReportService;
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public String  getMasterScheduleReport(String startDate,String endDate) {
			 
		//Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
		//Date queryEndDate = DateTimeUtil.handleEndDate(endDate);
   //System.out.println("GENERATE_MASTER_SCHEDULE_REPORT_MAPPING ..........gg");
   List<Map<String, Object>> report =  fileTransferScheduleReportService.generateEmployeeMasterScheduleReportJob(startDate, endDate);
		  return "success";

	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public  Map<String, Object> appointmentStatusUpdateService(UpdateAppointmentDTO updateAppointmentDTO){
			 
		Map<String, Object> map = new HashMap<String, Object>();	
		List<AppointmentTransactions> appointmentTransactions =  mAppointmentDAO.findHoldAppointmentIdByTransactionId(updateAppointmentDTO.getOrderId());
					//Here from CustomerId(Customer External Id) get the Customer object and to update Appointmetn_customer and customer_appointmetnSeries
			Customer customer = customerService.getCustomerByExternalId(1L, updateAppointmentDTO.getCustomerId());
		
			if((null != appointmentTransactions && appointmentTransactions.isEmpty()) || null == customer  ) {
	
				map.put("status", true);
				map.put("message", "Invalid Input, please check and try again.!");
				return map;
			}
				
				
			Appointment  dbapt = null;
			 AppointmentSeries  dbAptSer = null;
			 boolean isAptUpdated = false;
			List<Long> aapSeries = new ArrayList<Long>();
			for(AppointmentTransactions a : appointmentTransactions){
				dbapt = appointmentService.getAppointmentWithCustomer(a.getAppointmentId());
				dbAptSer= dbapt.getAppointmentSeries();
				
				Set<Customer> lstCust = new HashSet<Customer>();
				lstCust.add(customer);
	 
				if("H".equals(dbapt.getCanceled().toString()) && dbapt.getStartTime().after(new Date())) {
							dbapt.setCanceled(Canceled.N);
							dbapt.setVersion(dbapt.getVersion()+1);
							Person pt = new Person();
							pt.setPersonId(1l);
							dbapt.setCustomers(lstCust);
							dbapt.setUpdatedBy(pt);
							mAppointmentDAO.saveOrUpdate(dbapt);
							isAptUpdated = true;
				
					if(!aapSeries.contains(dbAptSer.getAppointmentSeriesId())){
						if(dbAptSer != null && isAptUpdated){
 
							  dbAptSer.setVersion(dbAptSer.getVersion()+1);
								pt.setPersonId(1l);
								dbAptSer.setCustomers(lstCust);
								dbAptSer.setUpdatedBy(pt);
								appointmentSeriesDAO.saveOrUpdate(dbAptSer);
					} 	
					}
				}
				aapSeries.add(dbAptSer.getAppointmentSeriesId());
			}
			

			   
			  if(!isAptUpdated) {
				    map.put("status", true);
					map.put("message", "no valid appoitments to update!");
					return map;
			  }
			  
			    
				map.put("status", true);
				map.put("message", "Update Appointment Success!");
				return map;
			  
			 // return null;

	}

	
 
 
 
}
