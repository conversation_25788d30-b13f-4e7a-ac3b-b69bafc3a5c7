package com.guitarcenter.scheduler.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.InstructorDAO;
import com.guitarcenter.scheduler.dao.InstoreAvailabilityDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.InstructorCriterion;
import com.guitarcenter.scheduler.dto.InstoreAvailableDTO;
import com.guitarcenter.scheduler.dto.OnLineAvailableDTO;
import com.guitarcenter.scheduler.dto.OnetimeDTO;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Onetime;
import com.guitarcenter.scheduler.model.InstoreAvailability;
import com.guitarcenter.scheduler.model.InstoreAvailability;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.service.InstoreAvailabilityService;
 

@Service("instoreAvailabilityService")
public class InstoreAvailabilityServiceImpl implements InstoreAvailabilityService {
	
	@SuppressWarnings("unused")
    private static final Logger LOG = LoggerFactory.getLogger(InstoreAvailabilityServiceImpl.class);
	
	@Autowired
	private InstoreAvailabilityDAO instoreAvailabilityDAO;
 
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public List<InstoreAvailability> getInstoreAvailabilityByInstructorId(long instructorId) {
		List<InstoreAvailability> instoreAvailabilities = instoreAvailabilityDAO.getInstoreAvailabilityByInstructorId(instructorId);
		//instoreAvailabilityDAO.get
		
		return instoreAvailabilities;
	}
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public List<InstoreAvailableDTO> getInstoreAvlFormatByInstructorId(long instructorId){
		List<InstoreAvailability> instoreAvailabilities = instoreAvailabilityDAO.getInstoreAvailabilityByInstructorId(instructorId);
		List<InstoreAvailableDTO>  instoreDtoList = new ArrayList<InstoreAvailableDTO>();
 
		for (InstoreAvailability dto : instoreAvailabilities) {
			InstoreAvailableDTO onetimeDto = onetimeDateFormatToString(dto);
			instoreDtoList.add(onetimeDto);
		}
		return instoreDtoList;
	}
	//HOURS_PATTERN
	private InstoreAvailableDTO onetimeDateFormatToString(InstoreAvailability onetime){
		String fromDate = new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN).format(onetime.getStartTime());
		String fromTime = new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN).format(onetime.getStartTime());
		String toTime = new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN).format(onetime.getEndTime());
		InstoreAvailableDTO onetimeDto = new InstoreAvailableDTO();
		onetimeDto.setStartDate(fromDate);
		onetimeDto.setFromTime(fromTime);
		onetimeDto.setToTime(toTime);
		onetimeDto.setWeekDay(onetime.getDay());
		onetimeDto.setInstructorId(onetime.getInstructor().getInstructorId());
		onetimeDto.setInstoreAvailabilityId(onetime.getInstoreAvailabilityId());
		onetimeDto.setOnetimeStartToEnd(getDayStr(onetime.getDay()) +": "+"" +fromTime+" - "+toTime);
		return onetimeDto;
	}
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public List<InstoreAvailableDTO> getInstoreAvailabilityDtoByInstructorId(long instructorId){
		List<InstoreAvailability> instoreAvailabilities = instoreAvailabilityDAO.getInstoreAvailabilityByInstructorId(instructorId);
		List<InstoreAvailableDTO>  onLineDtoList = new ArrayList<InstoreAvailableDTO>();
 
		for (InstoreAvailability dto : instoreAvailabilities) {
			InstoreAvailableDTO onetimeDto = onetimeDateToString(dto);
			onLineDtoList.add(onetimeDto);
		}
		return onLineDtoList;
	}
	
	private InstoreAvailableDTO onetimeDateToString(InstoreAvailability onetime){
		String fromDate = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_DATE).format(onetime.getStartTime());
		String fromTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_TIME).format(onetime.getStartTime());
		String toTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_TIME).format(onetime.getEndTime());
		InstoreAvailableDTO onetimeDto = new InstoreAvailableDTO();
		onetimeDto.setStartDate(fromDate);
		onetimeDto.setFromTime(fromTime);
		onetimeDto.setToTime(toTime);
 
		onetimeDto.setInstructorId(onetime.getInstructor().getInstructorId());
		onetimeDto.setInstoreAvailabilityId(onetime.getInstoreAvailabilityId());
		onetimeDto.setOnetimeStartToEnd(getDayStr(onetime.getDay()) +": "+"" +fromTime+" - "+toTime);
		return onetimeDto;
	}
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public long saveInstoreAvailablity(InstoreAvailability instoreAvailability) {
		return instoreAvailabilityDAO.save(instoreAvailability, instoreAvailability.getUpdatedBy());
	}
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public long saveOrUpdate(InstoreAvailability instoreAvailability,Long personId) {
		Person pUpdatedBy = new Person();
		pUpdatedBy.setPersonId(personId);
		instoreAvailabilityDAO.saveOrUpdate(instoreAvailability, pUpdatedBy);
		
		return 1l;
	}
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED)
	public Boolean deleteInstoreTime(long instoreTimeId) {
	
		InstoreAvailability t = new InstoreAvailability();
		t.setInstoreAvailabilityId(instoreTimeId);
		instoreAvailabilityDAO.delete(t);
		 
		return true;
	}
	
	private String getDayStr(String num){
		Map<String,String> mb = new HashMap<String,String>();
		String output = "";
		mb.put("0", "Sun");
		mb.put("1", "Mon");
		mb.put("2", "Tue");
		mb.put("3", "Wed");
		mb.put("4", "Thu");
		mb.put("5", "Fri");
		mb.put("6", "Sat");
		output = mb.get(num);
		return output;
	 
	}
}

