/**
 * @Title: TimeoffServiceImpl.java
 * @Package com.guitarcenter.scheduler.service.impl
 * Copyright: Copyright (c) 2014 
 * Company:
 * 
 * <AUTHOR>
 * @date Mar 10, 2014 4:52:52 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.service.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.CalendarUtil;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.common.util.TimeoffUtil;
import com.guitarcenter.scheduler.dao.ProfileTimeoffDAO;
import com.guitarcenter.scheduler.dao.TimeoffDAO;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.dto.ProfileTimeOffDTO;
import com.guitarcenter.scheduler.dto.TimeoffDateDTO;
import com.guitarcenter.scheduler.model.ProfileTimeoff;
import com.guitarcenter.scheduler.model.Timeoff;
import com.guitarcenter.scheduler.service.TimeoffService;

/**
 * @ClassName: TimeoffServiceImpl
 * <AUTHOR>
 * @date Mar 10, 2014 4:52:52 PM
 *
 */
@Service(value="timeoffService")
public class TimeoffServiceImpl implements TimeoffService {

	@SuppressWarnings("unused")
    private static final Logger LOG = LoggerFactory.getLogger(TimeoffServiceImpl.class);
	
	@Autowired
	@Qualifier("timeoffDAO")
	private TimeoffDAO timeoffDAO;
	
	@Autowired
	@Qualifier("profileTimeoffDAO")
	private ProfileTimeoffDAO profileTimeoffDAO;

	/**
	  * <p>Title: getTimeoffByInstructorId</p>
	  * <p>Description: </p>
	  * @param instructorId
	  * @return
	  * @see com.guitarcenter.scheduler.service.TimeoffService#getTimeoffByInstructorId(long)
	  */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public List<Timeoff> getTimeoffByInstructorId(long instructorId) {
		return timeoffDAO.getTimeoffByInstructorId(instructorId);
	}
	
	@Override
	public Timeoff getTimeoffByTimeoffId(long timeoffId) {
		return timeoffDAO.get(timeoffId, DAOHelper.FETCH_INSTRUCTOR);
	}
	
	/**
	  * <p>Title: saveTimeoff</p>
	  * <p>Description: </p>
	  * @param timeoff
	  * @return
	  * @see com.guitarcenter.scheduler.service.TimeoffService#saveTimeoff(com.guitarcenter.scheduler.model.Timeoff)
	  */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public long saveTimeoff(Timeoff timeoff) {
		return timeoffDAO.save(timeoff, timeoff.getUpdatedBy());
	}

	/**
	  * deleteTimeoff
	  * @Title: deleteTimeoff
	  * @param @param timeoff
	  * @return void
	  * @throws
	 */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public void deleteTimeoff(Timeoff timeoff) {
		timeoffDAO.delete(timeoff);
	}
	
	/**
	  * <p>Title: getDisplayTimeoffByInstructorId</p>
	  * <p>Description: </p>
	  * @param instructorId
	  * @return
	  * @see com.guitarcenter.scheduler.service.TimeoffService#getDisplayTimeoffByInstructorId(long)
	  */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public List<Timeoff> getDisplayTimeoffByInstructorId(long instructorId) {
		List<Timeoff> list = timeoffDAO.getDisplayTimeoffByInstructorId(instructorId);
		List<Timeoff> l = new ArrayList<Timeoff>();
		for(int i = 0; i < list.size(); i++){
			l.add(list.get(i));
			if(l.size()==7){
				break;
			}
		}
		return l;
	}

	/**
	  * <p>Title: checkTimeoffByRecurringTime</p>
	  * <p>Description: </p>
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @param instructorId
	  * @return
	  * @see com.guitarcenter.scheduler.service.TimeoffService#checkTimeoffByRecurringTime(java.lang.String, java.lang.String, java.lang.String, java.lang.String, long)
	 */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public boolean checkTimeoffByRecurringTime(String startDate,
			String endDate, String startTime, String endTime, long instructorId) {
		List<String> startDateList = CalendarUtil.getPeriodWeekDate(startDate, endDate);
		boolean flag = timeoffDAO.checkTimeoffByRecurringTime(startDateList, startTime, endTime, instructorId);
		return flag;
	}

	/**
	  * <p>Title: checkTimeoffByTime</p>
	  * <p>Description: </p>
	  * @param startDate
	  * @param startTime
	  * @param endTime
	  * @param instructorId
	  * @return
	  * @see com.guitarcenter.scheduler.service.TimeoffService#checkTimeoffByTime(java.lang.String, java.lang.String, java.lang.String, long)
	 */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public boolean checkTimeoffByTime(String startDate, String startTime,
			String endTime, long instructorId) {
		boolean flag = timeoffDAO.checkTimeoffByTime(startDate, startTime, endTime, instructorId);
		return flag;
	}

	/**
	  * GCSS-525
	  * <p>Title: getTimeoffByDateInstructorId</p>
	  * <p>Description: </p>
	  * @param instructorId
	  * @param date
	  * @return
	  * @see com.guitarcenter.scheduler.service.TimeoffService#getTimeoffByDateInstructorId(long, java.lang.String)
	  */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public List<Timeoff> getTimeoffByDateInstructorId(long instructorId,
			String date) {
		List<Timeoff> list = timeoffDAO.getTimeoffByDateInstructorId(instructorId, date);
		List<Timeoff> l = new ArrayList<Timeoff>();
		for(Timeoff timeoff: list){
			Timeoff t = new Timeoff();
			t.setTimeoffId(timeoff.getTimeoffId());
			t.setInstructor(timeoff.getInstructor());
			Date start = TimeoffUtil.getTimeoffDayTime(timeoff.getStartTime(), date, SystemUtil.APPOINTMENT_START_TIME);
			Date end = TimeoffUtil.getTimeoffDayTime(timeoff.getEndTime(), date, SystemUtil.APPOINTMENT_END_TIME);
			t.setStartTime(start);
			t.setEndTime(end);
			l.add(t);
		}
		return l;
	}

	/**
	  * getTimeoffDateDTOByAvailabilityTime
	  * 
	  *
	  * @Title: getTimeoffDateDTOByAvailabilityTime
	  * @Description: 
	  * @param @param date
	  * @param @param locationId
	  * @param @return
	  * @return List<TimeOffDTO>
	  * @throws
	 */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public List<TimeoffDateDTO> getTimeoffDateDTOByAvailabilityTime(String date,
			long locationId) {
		return timeoffDAO.getTimeoffDateDTOByAvailabilityTime(date, locationId);
	}
//----Changes for GSSP-321 for get the profileTimeOffDetails from DB -------- profileTimeOffDetailsInsAVL
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public Map<String, ProfileTimeOffDTO> profileTimeOffDetails(Long profileId,Date startDate ) {
		List<ProfileTimeoff> profileTimeoffList = null;
		String stringDate = null;
		Map<String, ProfileTimeOffDTO> profileTimeOffMap = new HashMap<String, ProfileTimeOffDTO>();  
		try {
			
			 Calendar c = Calendar.getInstance(); 
			 c.setTime(startDate); 
			 c.add(Calendar.DATE, 7);
			 Date endDate = c.getTime();
			 profileTimeoffList = profileTimeoffDAO.getTimeoffByProfileId(profileId,startDate, endDate);
			 DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");  
			
			for(ProfileTimeoff pt :profileTimeoffList){
				ProfileTimeOffDTO profileTimeOffDTO = new ProfileTimeOffDTO();
				profileTimeOffDTO.setProfileId(pt.getProfileId()); 
				profileTimeOffDTO.setStartTime(pt.getStartTime()); 
				profileTimeOffDTO.setEndTime(pt.getEndTime()); 
				profileTimeOffDTO.setProfiletimeoffId(pt.getProfiletimeoffId()); 
				stringDate = dateFormat.format(pt.getStartTime());
				profileTimeOffMap.put(stringDate, profileTimeOffDTO);
			}
			
		} catch (Exception e) {
			LOG.error("Error during fetching of DateFormat ", e);
		}
		return profileTimeOffMap;
		 
	}
	
	//----Changes for GSSP-321 for get the profileTimeOffDetails from DB -------- 
		@Override
		@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
		public Map<String, ProfileTimeOffDTO> profileTimeOffDetailsInsAVL(Long profileId,Date startDate ) {
			List<ProfileTimeoff> profileTimeoffList = null;
			String stringDate = null;
			Map<String, ProfileTimeOffDTO> profileTimeOffMap = new HashMap<String, ProfileTimeOffDTO>();  
			try {
				
				 Calendar c = Calendar.getInstance(); 
				 c.setTime(startDate); 
				 c.add(Calendar.DATE, 7);
				 Date endDate = c.getTime();
				 profileTimeoffList = profileTimeoffDAO.getTimeoffByProfileIdInsAVL(profileId,startDate, endDate);
				 DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");  
				
				for(ProfileTimeoff pt :profileTimeoffList){
					ProfileTimeOffDTO profileTimeOffDTO = new ProfileTimeOffDTO();
					profileTimeOffDTO.setProfileId(pt.getProfileId()); 
					profileTimeOffDTO.setStartTime(pt.getStartTime()); 
					profileTimeOffDTO.setEndTime(pt.getEndTime()); 
					profileTimeOffDTO.setProfiletimeoffId(pt.getProfiletimeoffId()); 
					stringDate = dateFormat.format(pt.getStartTime());
					profileTimeOffMap.put(stringDate, profileTimeOffDTO);
				}
				
			} catch (Exception e) {
				LOG.error("Error during fetching of DateFormat ", e);
			}
			return profileTimeOffMap;
			 
		}
		
}
