package com.guitarcenter.scheduler.service.impl;

import org.springframework.stereotype.Service;

import com.guitarcenter.scheduler.service.PasswordGeneratorService;

import java.security.SecureRandom;

@Service
public class PasswordGeneratorServiceImpl implements PasswordGeneratorService {

	private static final String UPPER = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String LOWER = "abcdefghijklmnopqrstuvwxyz";
    private static final String DIGITS = "0123456789";
    private static final SecureRandom random = new SecureRandom();

    @Override
    public String generateRandomPassword(int length) {
    StringBuilder password = new StringBuilder();
    boolean hasUpper = false;
    boolean hasLower = false;
    boolean hasDigit = false;
 
    // Ensure at least one character from each category
    password.append(UPPER.charAt(random.nextInt(UPPER.length())));
    password.append(LOWER.charAt(random.nextInt(LOWER.length())));
    password.append(DIGITS.charAt(random.nextInt(DIGITS.length())));
 
    // Generate the remaining characters
    for (int i = 3; i < length; i++) {
        int type = random.nextInt(3);
        if (type == 0) {
            password.append(UPPER.charAt(random.nextInt(UPPER.length())));
            hasUpper = true;
        } else if (type == 1) {
            password.append(LOWER.charAt(random.nextInt(LOWER.length())));
            hasLower = true;
        } else {
            password.append(DIGITS.charAt(random.nextInt(DIGITS.length())));
            hasDigit = true;
        }
    }
 
    // If all required character types are not present, replace a character
    if (!hasUpper) {
        replaceCharacter(password, UPPER);
    }
    if (!hasLower) {
        replaceCharacter(password, LOWER);
    }
    if (!hasDigit) {
        replaceCharacter(password, DIGITS);
    }
 
    return password.toString();
    }

    private  void replaceCharacter(StringBuilder password, String characters) {
        int index = random.nextInt(password.length());
        password.setCharAt(index, characters.charAt(random.nextInt(characters.length())));
    }
}

