package com.guitarcenter.scheduler.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.dao.PersonPersonalDetailsDao;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.PersonPersonalDetailsCriterion;
import com.guitarcenter.scheduler.integration.dto.InstructorPersonalDetailsDTO;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.PersonPersonalDetails;
import com.guitarcenter.scheduler.service.InstructorPersonalDetailsService;

//New class created for GSSP-275
@Service("instructorPersonalDetailsService")
public class PersonPersonalDetailsServiceImpl implements InstructorPersonalDetailsService, AppConstants{

	@Autowired
	private PersonPersonalDetailsDao pDao;
	
	@Override
	@Transactional
	public List<PersonPersonalDetails> getListByPersonId(long personId) {
		
		Criterion<PersonPersonalDetails, PersonPersonalDetails> criterion = PersonPersonalDetailsCriterion.findByPersonId(personId);
		return pDao.search(criterion);
		
	}

	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	@Override
	public void createPersonPersonalDetails(InstructorPersonalDetailsDTO instructorDto, Instructor instructor, Person pUpdatedBy) {

		PersonPersonalDetails pObj=new PersonPersonalDetails();
    	pObj.setPersonId(instructor.getPerson().getPersonId());
    	pObj.setVersion(1);
    	pObj.setUpdated(new Date());
    	pObj.setSite(instructor.getSite());
    	pObj.setUpdatedBy(pUpdatedBy);
    	pObj.setPersonalEmail(instructorDto.getEmail());
    	pObj.setExternalSource(instructorDto.getExternalSource());
		pDao.save(pObj,pUpdatedBy);
		
	}

	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	@Override	
	public void updatePersonPersonalDetails(InstructorPersonalDetailsDTO instructorDto, Instructor instructor, Person pUpdatedBy) {

		PersonPersonalDetails pObj=null;
		if(getListByPersonId(instructor.getPerson().getPersonId())!=null)
		{
			pObj=getListByPersonId(instructor.getPerson().getPersonId()).get(0);
		}
		
    	pObj.setPersonalEmail(instructorDto.getEmail());
    	pObj.setExternalSource(instructorDto.getExternalSource());
    	
    	pDao.update(pObj,pUpdatedBy.getPersonId());
		
		
		
	}

}
