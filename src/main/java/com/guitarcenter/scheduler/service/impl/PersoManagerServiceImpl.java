package com.guitarcenter.scheduler.service.impl;

import jakarta.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.PersonDAO;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.service.PersonManagerService;

@Service("personManagerService")
public class PersoManagerServiceImpl implements PersonManagerService {
    Logger log = LoggerFactory.getLogger(PersoManagerServiceImpl.class);
    
    private static final String SYSTEM_UPDATE_ERROR_MESSAGE =
        "A valid System Update person record must be present for correct " +
        "functioning of application";
    
	@Resource
	private PersonDAO personDAO;
	
	@Transactional
	@Override
	public Person findPersonByExtlId(String extId) {
		
		Person example = new Person();
		example.setExternalId(extId);
		Person person =  personDAO.get(example);
		
		return person;
	}
	
	/**
     * Returns the Person entity used to indicate a record was updated by the
     * system and not an interactive user.
     * 
     * Note: Runtime exception is explicitly called out to remind developers
     * that this method must return a suitable Person or the system will not
     * function correctly.
     *  
     * @return a Person record representing internal system updates
     * @throws IllegalStateException if a suitable Person record is not found.
     */
    public Person getSystemUpdatePerson()
        throws IllegalStateException
    {
        if (log.isDebugEnabled()) {
            log.debug("looking for system update record");
        }
        Person systemUpdate = null;
        try {
            systemUpdate = personDAO.get(SYSTEM_UPDATE_PERSON_ID,
                                         DAOHelper.FETCH_NONE);
        } catch (Throwable t) {
            log.error("Caught an exception getting System Update record", t);
            throw new IllegalStateException(SYSTEM_UPDATE_ERROR_MESSAGE, t);
        }
        if (systemUpdate == null) {
            log.error("system update record cannot be null");
            throw new IllegalStateException(SYSTEM_UPDATE_ERROR_MESSAGE);
        }
        if (log.isDebugEnabled()) {
            log.debug("returning {}", systemUpdate);
        }
        return systemUpdate;
    }
}
