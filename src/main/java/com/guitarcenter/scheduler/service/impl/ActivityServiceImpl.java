/**
 * @Title: ActivityServiceImpl.java
 * @Package com.guitarcenter.scheduler.service.impl
 * @Description: Copyright: Copyright (c) 2013 Company:
 * 
 * <AUTHOR>
 * @date Aug 22, 2013 7:43:54 PM
 * @version V1.0
 */

package com.guitarcenter.scheduler.service.impl;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.ActivityDAO;
import com.guitarcenter.scheduler.dao.ProfileActivityDAO;
import com.guitarcenter.scheduler.dao.criterion.ActivityCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.ProfileActivityCriterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileActivity;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.service.ActivityService;

/**
 * @ClassName: ActivityServiceImpl
 * <AUTHOR>
 * @date Aug 22, 2013 7:43:55 PM
 * 
 */
@Service("activityService")
public class ActivityServiceImpl implements ActivityService {

	private static final Logger	LOG	= LoggerFactory.getLogger(ActivityServiceImpl.class);

	@Autowired
	@Qualifier("activityDAO")
	private ActivityDAO			activityDAO;
	
	@Autowired
	@Qualifier("profileActivityDAO")
	private ProfileActivityDAO profileActivityDAO;

	/*
	 * <p>Title: getActivityByActivityId</p> <p>Description: </p>
	 * 
	 * @param activityI
	 * 
	 * @return
	 * 
	 * @see
	 * com.guitarcenter.scheduler.service.ActivityService#getActivityByActivityId
	 * (long)
	 */
	@Override
	public Activity getActivityByActivityId(long activityId) {
		return activityDAO.get(activityId, DAOHelper.FETCH_SERVICE);
	}



	@Override
	public List<Activity> loadActivitiesByService(long serviceId) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("ActivityService.loadActivitiesByService start");
		}
		List<Activity> list = new LinkedList<Activity>();
		try {
			Criterion<Activity, Activity> criterion = ActivityCriterion.findByService(serviceId);
			list = activityDAO.search(criterion,DAOHelper.FETCH_SERVICE);
		} catch (Exception e) {
			LOG.error("Caught an {} from ActivityService.loadActivitiesByService", e);
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("Get ActivityList: {} from ActivityService.loadActivitiesByService", list);
		}
		return list;
	}



	@Override
	public List<ActivityDTO> loadActivityDTOsByService(long serviceId) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("ActivityService.loadActivitiesByService start");
		}
		List<ActivityDTO> dtos = new LinkedList<ActivityDTO>();
		try {
			List<Activity> list = this.loadActivitiesByService(serviceId);
			if (null != list && !list.isEmpty()) {
				for (Activity a : list) {
					dtos.add(new ActivityDTO(a.getActivityId(), a.getActivityName()));
				}
			}
		} catch (Exception e) {
			LOG.error("Caught an {} from ActivityService.loadActivitiesByService", e);
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("Get ActivityList: {} from ActivityService.loadActivitiesByService", dtos);
		}
		return dtos;
	}



	@Transactional
	@Override
	public List<Activity> findBySite(Site site) {
		Activity example = new Activity();
		example.setSite(site);
		List<Activity> list = activityDAO.search(example);
		return list;
	}



	@Override
	public List<Activity> findBySite(long siteId) {
		Criterion<Activity, Activity> criterion = ActivityCriterion.findBySiteId(siteId);
		List<Activity> list = activityDAO.search(criterion, DAOHelper.FETCH_SERVICE);
		return list;
	}



	@Transactional
	@Override
	public void craeteActivity(Activity act, Person p) {
		activityDAO.save(act, p);
	}



	@Override
	public Map<String,List<Activity>> loadActivityByProfileId(Long profileId) {
		final Criterion<ProfileActivity, ProfileActivity> criterion = ProfileActivityCriterion.findByProfileId(profileId);
		final List<ProfileActivity> list = profileActivityDAO.search(criterion, DAOHelper.FETCH_ACTIVITY);
		final List<Activity> result = new ArrayList<Activity>();
		final List<Activity> non_result = new ArrayList<Activity>();
		for (final ProfileActivity profileActivity : list) {
			if(Enabled.Y.equals(profileActivity.getEnabled())){
				result.add(profileActivity.getActivity());
			}else {
				non_result.add(profileActivity.getActivity());
			}
		}
		final ConcurrentMap<String,List<Activity>> map = new ConcurrentHashMap<String, List<Activity>>();
		map.put("result", result);
		map.put("non_result", non_result);
		return map;
	}

	
	@Override
	public List<ProfileActivity> loadProfileActivityByProfileId(Long profileId) {
		Criterion<ProfileActivity, ProfileActivity> criterion = ProfileActivityCriterion.findByProfileId(profileId);
		List<ProfileActivity> list = profileActivityDAO.search(criterion, DAOHelper.FETCH_ACTIVITY);
		return list;
	}


	@Override
	public List<Activity> queryActivityByDimName(String activityName) {
		Criterion<Activity, Activity> criterion = ActivityCriterion.findByActivityName(activityName, Enabled.Y);
		return activityDAO.search(criterion);
	}
	
	@Transactional
	@Override
	public ProfileActivity getProfileActivity(Long profileActivityId){
		return profileActivityDAO.get(profileActivityId,DAOHelper.FETCH_ACTIVITY);
	}
	
	//update profile activity
	@Transactional
	@Override
	public void updateProfileActivity(ProfileActivity profileActivity,Long updatedById){
		profileActivityDAO.update(profileActivity, updatedById);
	}
	
	
	//deleteCentralizeActivity
	@Transactional
	@Override
	public void deleteCentralizeActivity(Activity activity){
		activityDAO.delete(activity);
	}
	
	//update centralizedActivity
	@Transactional
	@Override
	public void updateActivity(Long siteId,Activity activity,Person updatedBy,Boolean globalChange){
		
		//if disable activity && globalChange,disable profileActivity
		if(globalChange){
			//profileActivities in activity
			Criterion<ProfileActivity, ProfileActivity>  profileActivityCriterion = ProfileActivityCriterion.findByActivityIdAndSiteIdAndEnabled(activity.getActivityId(), siteId, (Enabled.Y.equals(activity.getEnabled()))?Enabled.N:Enabled.Y);
			List<ProfileActivity> profileActivities = profileActivityDAO.search(profileActivityCriterion);
			//disable profileActivity
			for (ProfileActivity profileActivity : profileActivities) {
					profileActivity.setEnabled(activity.getEnabled());
					profileActivityDAO.update(profileActivity, updatedBy);
				}
		}
		activityDAO.update(activity, updatedBy);
	}



	/**
	 * 
	  * <p>Title: checkActivityByAppointmentTime</p>
	  * <p>Description: </p>
	  * @param recurring
	  * @param activityId
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @return
	  * @see com.guitarcenter.scheduler.service.ActivityService#checkActivityByAppointmentTime(boolean, long, java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	 */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public boolean checkActivityByAppointmentTime(boolean recurring, long activityId,
			String startDate, String endDate, String startTime, String endTime, long profileId) {
		if(recurring && !startDate.equals(endDate)){
			return activityDAO.getActivityByAppointmentRecurringTime(activityId, startDate, endDate, startTime, endTime, profileId)==null?false:true;
		}
		return activityDAO.getActivityByAppointmentTime(activityId, startDate, startTime, endTime, profileId)==null?false:true;
	}



	@Override
	public List<ProfileActivity> loadProfileActivityByProfileIdAndServiceId(
			Long profileId, Long serviceId) {
		Criterion<ProfileActivity, ProfileActivity> profileCriterion = ProfileActivityCriterion.findByProfileIdAndServiceIds(profileId, serviceId);
		List<ProfileActivity> profileActivities = profileActivityDAO.search(profileCriterion);
		return profileActivities;
	}



	@Override
	@Transactional
	public Boolean hasSameName(String createName) {
		Activity activity = new Activity();
		activity.setActivityName(createName);
		List<Activity> list = activityDAO.search(activity);
		return (list == null || list.isEmpty()) ? false : true;
	}



	/**
	  * <p>Title: getActivityDTOByProfileRoom</p>
	  * <p>Description: </p>
	  * @param profileId
	  * @param roomId
	  * @return
	  * @see com.guitarcenter.scheduler.service.ActivityService#getActivityDTOByProfileRoom(long, long)
	  */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public List<ActivityDTO> getActivityDTOByProfileRoom(long profileId,
			long roomId) {
		return activityDAO.getActivityDTOByProfileRoom(profileId, roomId);
	}
	
	@Transactional
	@Override
	public List<ActivityDTO> loadByProfileAndServiceAndInstructor(
			long profileId, long serviceId, long instructorId) {
		List<Activity> list = new LinkedList<Activity>();
		List<ActivityDTO> dtos = new LinkedList<ActivityDTO>();
		try {
			Criterion<Activity, Activity> criterion = ActivityCriterion.findByProfileIdAndServiceIddAndInstructorId(profileId, serviceId, instructorId);
			list = activityDAO.search(criterion);
		} catch (Exception e) {
			LOG.error("Caught exception {} when loading activity list ByProfileAndServiceAndInstructor", e);
		}
		for(Activity a : list) {
			dtos.add(new ActivityDTO(a.getActivityId(), a.getActivityName()));
		}
		return dtos;
	}
	
	@Transactional
	@Override
	public List<ActivityDTO> loadByProfileAndServiceAndRoom(long profileId,
			long serviceId, long roomId) {
		List<Activity> list = new LinkedList<Activity>();
		List<ActivityDTO> dtos = new LinkedList<ActivityDTO>();
		try {
			Criterion<Activity, Activity> criterion = ActivityCriterion.findByProfileIdAndServiceIddAndRoomId(profileId, serviceId, roomId);
			list = activityDAO.search(criterion);
		} catch (Exception e) {
			LOG.error("Caught exception {} when loading activity list ByProfileAndServiceAndRoom", e);
		}
		for(Activity a : list) {
			dtos.add(new ActivityDTO(a.getActivityId(), a.getActivityName()));
		}
		return dtos;
	}
	
	/**
	 * For gcss-578,query the activity list that the assosiated service is enabled
	 */
	@Transactional
	@Override
	public List<ProfileActivity> findActivityOfEnabledServiceAndProfile(long pProfileId) {
		Criterion<ProfileActivity, ProfileActivity> criterion = ProfileActivityCriterion.findAcitivitiesEnabledServiceAndByProfile(pProfileId);
		List<ProfileActivity> list = new LinkedList<ProfileActivity>();
		try {
			list = profileActivityDAO.search(criterion);
			if(LOG.isDebugEnabled()) {
				LOG.debug("Returning ProfileActivity list {} by profileId {} ", list, pProfileId);
			}
		} catch (Exception e) {
			LOG.error("Caught exception {} when loading ProfileActivity list by enbaled service and profile", e);
		}
		return list;
	}

}
