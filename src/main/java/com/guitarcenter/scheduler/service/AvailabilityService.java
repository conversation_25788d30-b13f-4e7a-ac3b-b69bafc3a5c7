package com.guitarcenter.scheduler.service;

import java.util.Set;

import org.springframework.ui.ModelMap;

import com.guitarcenter.scheduler.dto.StudioHourDTO;
import com.guitarcenter.scheduler.model.Availability;

public interface AvailabilityService {

	public Availability findByProfileId(Long profileId);
	//public Map<String, String> mergeMap(Availability availability);
	public Set<StudioHourDTO> mergeMap(Availability availability);
	public void update(Availability avalAvailability,Long personId);
	
	public ModelMap getMap(Long profileId,ModelMap map);
	Availability getAvailability(Long id);
	
	public Availability loadCommonTimeRangeByProfileAndInstructor(Long profileId, Long instructorId);
}
