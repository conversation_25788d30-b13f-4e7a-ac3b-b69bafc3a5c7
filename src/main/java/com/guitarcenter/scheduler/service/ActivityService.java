/**
 * @Title: ActivityService.java
 * @Package com.guitarcenter.scheduler.service
 * Copyright: Copyright (c) 2013 
 * Company:
 * 
 * <AUTHOR>
 * @date Aug 22, 2013 7:43:29 PM
 * @version V1.0
 */

package com.guitarcenter.scheduler.service;

import java.util.List;
import java.util.Map;

import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileActivity;
import com.guitarcenter.scheduler.model.Site;

/**
 * @ClassName: ActivityService
 * <AUTHOR>
 * @date Aug 22, 2013 7:43:29 PM
 *
 */
public interface ActivityService {
	public Activity getActivityByActivityId(long activityId);
	
	public List<Activity> loadActivitiesByService(long serviceId);
	
	public List<Activity> findBySite(Site site);
	
	public List<Activity> findBySite(long siteId);
	
	public void craeteActivity(Activity act , Person p);
	
	public List<ActivityDTO> loadActivityDTOsByService(long serviceId);
	
	public Map<String, List<Activity>> loadActivityByProfileId(Long profileId);
	
	public List<Activity> queryActivityByDimName(String activityName);

	public List<ProfileActivity> loadProfileActivityByProfileId(Long profileId);

	public ProfileActivity getProfileActivity(Long profileActivityId);

	public boolean checkActivityByAppointmentTime(boolean recurring, long activityId, String startDate, String endDate, String startTime, String endTime, long profileId);

	public void updateActivity(Long siteId, Activity activity, Person updatedBy, Boolean boolean1);

	public void deleteCentralizeActivity(Activity activity);

	public void updateProfileActivity(ProfileActivity profileActivity, Long updatedById);
	
	public List<ProfileActivity> loadProfileActivityByProfileIdAndServiceId(Long profileId,Long serviceId);

	public Boolean hasSameName(String createName);
	
	public List<ActivityDTO> getActivityDTOByProfileRoom(long profileId, long roomId);
	
	public List<ActivityDTO> loadByProfileAndServiceAndInstructor(long profileId, long serviceId, long instructorId);
	
	public List<ActivityDTO> loadByProfileAndServiceAndRoom(long profileId, long serviceId, long roomId);
	
	/**
	 * For gcss-578,query activity list that the assosisated service is enabled
	 * 
	 * @param pProfileId
	 * @return
	 */
	public List<ProfileActivity> findActivityOfEnabledServiceAndProfile(long pProfileId);
	
}
