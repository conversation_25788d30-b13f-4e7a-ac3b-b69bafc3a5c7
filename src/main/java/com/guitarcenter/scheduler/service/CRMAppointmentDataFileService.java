package com.guitarcenter.scheduler.service;

import java.util.Map;
import com.guitarcenter.scheduler.dao.criterion.dto.CRMAppointmentDataFileDTO;
import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;


public interface CRMAppointmentDataFileService {

	public Map<String,CRMAppointmentDataFileDTO> scheduedTask(String dateInString); 
	public void encryptAndTransferCRMApppointmentData(Map<String, CRMAppointmentDataFileDTO> cRMAppointmentDataFileMap) throws IntegrationServiceException; 

}