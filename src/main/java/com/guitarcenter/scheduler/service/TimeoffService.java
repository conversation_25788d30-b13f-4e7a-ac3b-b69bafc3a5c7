/**
 * @Title: Timeoff.java
 * @Package com.guitarcenter.scheduler.service
 * @Description: 
 * Copyright: Copyright (c) 2014 
 * Company:
 * 
 * <AUTHOR>
 * @date Mar 10, 2014 4:48:06 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.service;


import java.util.Date;
import java.util.List;
import java.util.Map;

import com.guitarcenter.scheduler.dto.ProfileTimeOffDTO;
import com.guitarcenter.scheduler.dto.TimeoffDateDTO;
import com.guitarcenter.scheduler.model.Timeoff;

/**
 * @ClassName: Timeoff
 * @Description: 
 * <AUTHOR>
 * @date Mar 10, 2014 4:48:06 PM
 *
 */
public interface TimeoffService {
	public List<Timeoff> getTimeoffByInstructorId(long instructorId);
	public long saveTimeoff(Timeoff timeoff);
	public void deleteTimeoff(Timeoff timeoff);
	public Timeoff getTimeoffByTimeoffId(long timeoffId);
	public List<Timeoff> getDisplayTimeoffByInstructorId(long instructorId);
	public boolean checkTimeoffByTime(String startDate, String startTime, String endTime, long instructorId);
	public boolean checkTimeoffByRecurringTime(String startDate, String endDate, String startTime, String endTime, long instructorId);
	public List<Timeoff> getTimeoffByDateInstructorId(long instructorId, String date);
	public List<TimeoffDateDTO> getTimeoffDateDTOByAvailabilityTime(String date, long locationId);
	public Map<String, ProfileTimeOffDTO> profileTimeOffDetails(Long profileId,Date sdate );
	public Map<String, ProfileTimeOffDTO> profileTimeOffDetailsInsAVL(Long profileId,Date sdate );
	
	
}
