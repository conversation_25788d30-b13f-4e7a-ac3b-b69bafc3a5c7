package com.guitarcenter.scheduler.service;

import java.util.List;

import com.guitarcenter.scheduler.integration.dto.InstructorPersonalDetailsDTO;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.PersonPersonalDetails;

/**
 * <AUTHOR>
 *
 */
 //New Interface created for GSSP-275
public interface InstructorPersonalDetailsService {

	public List<PersonPersonalDetails> getListByPersonId(long personId);
	
	public void createPersonPersonalDetails(InstructorPersonalDetailsDTO update, Instructor iObj, Person pUpdatedBy);
	
	public void updatePersonPersonalDetails(InstructorPersonalDetailsDTO update, Instructor iObj, Person pUpdatedBy);


}
