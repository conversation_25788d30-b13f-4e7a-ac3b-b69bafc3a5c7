package com.guitarcenter.scheduler.service;

import java.io.IOException;
import java.util.List;

import org.apache.solr.client.solrj.SolrServerException;

import com.guitarcenter.scheduler.dto.LocationDTO;
import com.guitarcenter.scheduler.dto.SearchDTO;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.Site;

public interface LocationManagerService {
	
	@Deprecated
    public Location findByExternalId(String externalId);
	
	/**
     * Return a collection of Location records for the site id provided that
     * have a matching external id value.
     * 
     * Note: this is returning a collection simply because there is not a
     * database constraint on the external identifiers; it is possible that
     * multiple customers *could* be updated from a single external record in
     * the future.
     * 
     * @param siteId identifier for the site that results must be restricted to
     * @param externalId String containing the external id to match
     * @return List of Location instances that match the external id in the site
     *         provided
     */
    public List<Location> findByExternalId(long siteId, String externalId);
	
	public Location findById(long locationId);
	
	public List<SearchDTO> findLocationsByCriteria(String criteria);
	
	public List<Location> locationsInSite(Site site);
    
    /**
     * Create or updates an Scheduler Location record with values taken from the
     * supplied LocationDTO record. Any exceptions are passed up to caller for
     * decision and handling.
     * 
     * The method will retrieve an existing location record from persistent
     * storage that matches the external id of the update record.
     * 
     * @param update a LoctionDTO instance containing values to update
     * @throws IOException 
     * @throws SolrServerException 
     */
    public void updateFromExternal(LocationDTO update) throws SolrServerException, IOException;
}
