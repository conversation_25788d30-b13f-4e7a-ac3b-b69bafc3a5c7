package com.guitarcenter.scheduler.service;

import com.guitarcenter.scheduler.dao.criterion.dto.AppointmentBookDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.CustomerAppointmentDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.ExportAppointmentDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorLessonLinkDTO;
import com.guitarcenter.scheduler.dto.*;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.AppointmentCancelReason;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.webservice.dto.CustomerAppointmentDetailsResultDTO;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface AppointmentService {

	public void createAppointment(Appointment appointment,CreateAppointmentDTO dto);

	public List<Appointment> loadAppointmentList(long sizeId, long profileId, Date startTime, Date endTime, String services, String instructors, String rooms, String activities);

	public Appointment getAppointment(long appointmentId);
	
	public Appointment getAppointmentWithCustomer(long appointmentId);

	public Map<String, Object> loadByWeek(Long customerId, long sizeId, long profileId, Date startTime, Date endTime, String services, String instructors, String rooms, String activitys, String date);

	public Map<String, Object> getDeliveryMonthDateParam(String date);

	public Map<String, Object> loadByMonth(Long customerId, long siteId, long profileId, String date, Date startTime, Date endTime, String services, String instructors, String rooms, String activitys);

	public List<Appointment> loadByCustomer(long customerId, Date startTime, Date endTime, long profileId);

	public Appointment getClosestScheduledAppByActivity(long activityId);

	public void updateAppointment(CreateAppointmentDTO dto, Appointment appointment, Person person, Appointment appObj);

	public boolean checkAppointmentByRoom(long roomId, long profileId);

	public boolean checkAppointmentByRoomActivity(long roomId, long activity, long profileId);

	public boolean checkAppointmentByProfile(long profileId);

	public boolean checkStartTime(String startDate, String startTime);

	public boolean checkAppointmentByRoom(long roomId);

	public Boolean hasByProfileIdAndActivityId(Long profileId, long activityId);

	public List<Appointment> loadAppointmentListBySeries(CreateAppointmentDTO dto, Appointment db);
	
	public List<Appointment> loadActiveAppointmentListBySeries(CreateAppointmentDTO dto, Appointment db);
	//For GSSP-173, fix for Discontinuous Appointments
	public List<Appointment> loadAllAppointmentListBySeries(Appointment db);

	public boolean checkAppointmentByRoomTemplateIdActivity(long roomTemplateId, long activityId);

	public List<StartTimeDTO> generateStartTimeListByProfileAndDate(Long profileId, Long instructorId, Date startDate);

	public List<Appointment> loadAppointmentListBySeriesAndStartTime(CreateAppointmentDTO dto, Appointment db);

	//Including Cancel Reason for GSSP-250
	public boolean cancelAppointment(Long appointmentId, String cancelType,long cancelReason, Person updateBy, String activityID,long serviceID);

	public List<CalendarViewUnavailableHourDTO> getRoomViewUnavailableHourList(Long profileId, Date date);

	public List<InstructorViewUnavailableHourDTOWrapper> getInstructorViewUnavailableHourList(Long profileId, Long[] instructorIds, Date date);

	public List<WeekViewUnavailableHourDTOWrapper> getWeekViewUnavailableHourDTOMap(Long profileId, String date);

	public List<InstructorViewUnavailableHourDTOWrapper> getInstructorViewUnavailableHourAndTimeoffList(Long pLocationId, Long profileId, Long[] instructorIds, Date date);

	public List<ExportAppointmentDTO> getExportLessionCSVData(long pLocationId, Date pStartTime, Date pEndTime);

	public List<WeekViewUnavailableHourDTOWrapper> getWeekViewUnavailableHourDTOMapByOneInstructor(Long pLocationId, Long profileId, Long[] instructorIds,String date);

	//method to update single appointment-phase 2-UPDATE LESSON SERVICE
	public void updateLessonServiceAppointment(Appointment app,Long personID);


	//Code change added for GSSP-188
	public List<StartTimeDTO> getStartTimeByInstructor(Long profileId, Long instructorId, Date startDate) ;

	//Added for GSSP-210
	public  void updateAppointmentLog(Appointment app,Long personId);



	//Changes for GSSP-250
	public List<AppointmentCancelReason> getcancelReason(String isRecurring) ;
	//Changes for GSSP-278
	/*	public List<AppointmentCancelReason> getCancelReasonCode(long cancelreason);*/
	public String findInstructorName(CreateAppointmentDTO dto);

	//Changes maded for GSSP-363   -------------------------
	public List<InstructorLessonLinkDTO> getInstructorLessonViewList(List<String> person_Id,Date startDate,Date endDate);

	//Changes maded for GSSP-368   -------------------------
	public List<AppointmentBookDTO> getAppointmentBookByLocationAndDate(Long pLocationId, Date startTime, Date endTime);

	//LES-624
	List<CustomerAppointmentDTO> getAppointmentBookByCustomerIdAndDate(Long customerId, Date startTime, Date endTime);

	public List<CustomerAppointmentDetailsResultDTO> loadByCustomerIdAndDate(String customerId, Date startTime, Date endTime);





}


