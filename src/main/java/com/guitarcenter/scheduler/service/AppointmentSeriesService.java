package com.guitarcenter.scheduler.service;

import org.springframework.stereotype.Service;

import com.guitarcenter.scheduler.model.AppointmentSeries;
import com.guitarcenter.scheduler.model.Person;

@Service
public interface AppointmentSeriesService {

	public AppointmentSeries createAppointmentSeries(AppointmentSeries appointmentSeries, Person person);
	
	public AppointmentSeries getAppointmentSeries(AppointmentSeries series);
	
	/**
     * When a customer is cancelled, all current and future appointments for that
     * customer must be removed. This is a hard-delete, there is no value in
     * keeping a cancelled customers future appointments. If the customer ever
     * becomes active again the operational procedure will be to reschedule
     * appointments manually - no need to recover deleted appointments.
     * 
     * @param customerId Identifier of the customer being cancelled
     */
    public void deleteCancelledCustomerAppointments(long customerId);
}
