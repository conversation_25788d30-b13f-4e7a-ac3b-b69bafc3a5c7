package com.guitarcenter.scheduler.service;

import java.util.List;
import java.util.Map;

import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceResponseDTO;

public interface GetInstructorFullAvailabiltyService {

	//public List<InstructorAVLServiceResponseDTO> getInstructorFullAvailabilitySlots(); 
	
	public List<InstructorAVLServiceResponseDTO> getInstructorFullAvailabilitySlots(List<Instructor> batch); 
	
	public List<InstructorAVLServiceResponseDTO> getDisabledInstructorsFullAvailabiltySlots();
	
	public List<Instructor> getFullInstructorsAffectRecent();
	
	public String getEnvironmentName();
	
	public Map<String,String> getPropertiesDataForElasticLoader();
 
}