package com.guitarcenter.scheduler.service;

import java.util.List;

import com.guitarcenter.scheduler.dto.InstoreAvailableDTO;
import com.guitarcenter.scheduler.dto.OnLineAvailableDTO;
import com.guitarcenter.scheduler.model.InstoreAvailability;

public interface InstoreAvailabilityService {

	public List<InstoreAvailability> getInstoreAvailabilityByInstructorId(long instructorId);
	public List<InstoreAvailableDTO> getInstoreAvailabilityDtoByInstructorId(long instructorId);
	public List<InstoreAvailableDTO> getInstoreAvlFormatByInstructorId(long instructorId);

	public long saveInstoreAvailablity(InstoreAvailability onlineAvailability);

	public long saveOrUpdate(InstoreAvailability onlineAvailability,Long personId);
	public Boolean deleteInstoreTime(long oneTimeId);
	
}
