package com.guitarcenter.scheduler.service.thread;

import java.util.Map;

import com.guitarcenter.scheduler.service.MailSenderService;

public class EmailThread implements Runnable {

	private Map<String, Object> dataMap;

	private String template;

	private MailSenderService mailSenderService;

	public EmailThread(Map<String, Object> dataMap, String template, MailSenderService mailSenderService) {
		this.dataMap = dataMap;
		this.template = template;
		this.mailSenderService = mailSenderService;
	}

	@Override
	public void run() {
		mailSenderService.sendMail(this.dataMap, this.template);
	}

}
