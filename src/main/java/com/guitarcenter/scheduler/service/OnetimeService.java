/**
 * @Title: OnetimeService.java
 * @Package com.guitarcenter.scheduler.service
 * @Description: 
 * Copyright: Copyright (c) 2014 
 * Company:
 * 
 * <AUTHOR>
 * @date May 29, 2014 2:06:02 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.service;

import java.util.List;

import com.guitarcenter.scheduler.model.Onetime;

/**
 * 
 * @ClassName: OnetimeService
 * @Description: 
 * <AUTHOR>
 * @date May 30, 2014 3:02:58 PM
 *
 */
public interface OnetimeService {
	public List<Onetime> getOnetimeByInstructorId(long instructorId);
	public long saveOnetime(Onetime onetime);
	public Onetime getOnetimeByOnetimeId(long onetimeId);
	public boolean checkOnetimeAvailabilityByProfileId(String startDate, String startTime, String endTime, long profileId);
	public boolean checkOnetimeByTime(String startDate, String startTime, String endDate,String endTime, long instructorId);
	public List<Onetime> getDisplayOnetimeByInstructorId(long instructorId);
	public Boolean deleteOneTime(Long oneTimeId);
	public List<Onetime> getOnetimeByTime(String startDate, String startTime, String endTime, long instructorId);
}
