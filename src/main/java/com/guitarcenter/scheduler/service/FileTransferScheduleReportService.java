package com.guitarcenter.scheduler.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.guitarcenter.scheduler.dto.AvailabilityValueDTO2;
import com.guitarcenter.scheduler.dto.FinalFreeSlotsAndMaxMinDates;

public interface FileTransferScheduleReportService {


    public String REPORT_DATE_KEY = "date1";
    public String INSTRUCTOR_INFO_KEY = "instructorInfo";
    public String REPORT_DTO_LIST_KEY = "appDTOList";
    public String EMPLOYEE_LIST_KEY = "employeeDTOList";
    public String CANCELLED_REASON = "cancelledReason";

   
    
    public List<Map<String, Object>> generateEmployeeMasterScheduleReportJob(String startDate, String endDate);
    public   FinalFreeSlotsAndMaxMinDates getInstructorAvailabilitySlots(AvailabilityValueDTO2 availabilityValueDTO2,String staDate); 
	
 
}