/**
 * serviceService.java
 */
package com.guitarcenter.scheduler.service;

import java.util.List;

import com.guitarcenter.scheduler.dto.ServiceDTO;
import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileService;
import com.guitarcenter.scheduler.model.Service;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.enums.Enabled;

/**
 * @ClassName: ServiceService
 * @Description: TODO
 * @date Sep 12, 2013 11:00:05 AM
 * 
 */
public interface ServiceService {
    /**
     * use site to load the service list.
     * @param siteId
     * @return List<Service>
     */
	List<Service> getServiceListBySiteId(long siteId);

	/**
	 * use site to load the service list.
	 * @param siteID
	 * @return List<Service>
	 */
	List<Service> findServiceBySite(long siteID);

	/**
	 * give a service-id,get this service.
	 * @param serviceId
	 * @return Service
	 */
	Service getServiceById(long serviceId);

	/**
	 * create service.
	 * @param service
	 * @param updatedBy
	 */
	void createService(Service service, long updatedBy);
	
	/**
	 * use site to load the serviceDTO list.
	 * @param siteId
	 * @return List<ServiceDTO>
	 */
	List<ServiceDTO> getServiceDTOListBySite(long siteId);

	/**
	 * give a profile-service-id ,get this profile-service.
	 * @param profileServiceId
	 * @return ProfileService
	 */
	ProfileService getProfileService(long profileServiceId);
	
	/**
	 * give a service-id,get this service.
	 * @param serviceId
	 * @return Service
	 */
	Service getCentralizeService(long serviceId);

	/**
	 * delete centralize service.<p>delete activities under it</p>
	 * <p>check if their's some profile-service/profile-activity use this 
	 *    service/activity</p>
	 * <p>check if their's some instructor use this service/activity</p>
	 * <p>check if their's some room-template use this service/activity</p>
	 * @param service
	 */
    void removeCentralizedService(Service service);

	/**
	 * update profile service,disable profile service should disable profile 
	 * activities under it.
	 * @param profileService will updated profile service
	 * @param updatedById  @param activityIds  @param profileId @param site
	 * @param location
	 * @param enabled if this profile service is enabled(Enabled.Y,Enabled.N).
	 */
	 void updateService(ProfileService profileService, long updatedById,
			List<Long> activityIds, long profileId, Site site,
			Location location, Enabled enabled);
	
	/**
	 * create and update a service,can not have same name.
	 * 
	 * @param name create and update name of service
	 * @return <code>true</code> if some service has the same name
	 *         <code>false</code> no service has the same name.
	 */
	 Boolean hasSameServiceName(String name);
	 
	 /**
	  * update centralize service,if checked global change,the enable and disable
	  * will impact the profile service's active.
	  * disable service,the activities under it will be disabled too.
	  * global change service:
	  * <p>global disable:profile service will disable,profile activities under these 
	  * profile service will disable,check appointment</p>
	  * <p>global enable: profile service under it will enable</p>
	  *  @param serviceDTO 
	  *  @param updatedBy
	  */
	 void updateCentralizeService(ServiceDTO serviceDTO, Person updatedBy);
	 
	 public List<ServiceDTO> getServiceDTOByProfileRoom(long profileId, long roomId);
	 
	 public List<ServiceDTO> loadByProfileAndInstructorAndEnabled(long profileId, long instructorId, Enabled enabled);
	 
	 public List<ServiceDTO> loadByProfileAndRoomAndEnabled(long profileId, long roomId, Enabled enabled);
	 //GSSP-211 CHANGES
	 public List<Location> findLocation();
	 
	 public boolean createDualInstructor(String externalId, String locationId, Instructor instructor,Person person) throws IntegrationServiceException;
}
