package com.guitarcenter.scheduler.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.guitarcenter.scheduler.dao.criterion.dto.AppointmentHistoryDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorAppointmentStatusListDTO;
import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.dto.InstructorReportPagingDTO;


public interface ReportService {

    public String REPORT_DATE_KEY = "date1";
    public String INSTRUCTOR_INFO_KEY = "instructorInfo";
    public String REPORT_DTO_LIST_KEY = "appDTOList";
    public String EMPLOYEE_LIST_KEY = "employeeDTOList";
    public String CANCELLED_REASON = "cancelledReason";

    public List<Map<String, Object>> generateInstructorScheduleReport(long locationId, Date startDate, Date endDate);

    public List<Map<String, Object>> generateRehearsalBookingReport(long profileId, Date startDate, Date endDate);

    public List<Map<String, Object>> generateRehearsalScheduleReport(long profileId, Date startDate, Date endDate);

    public List<Map<String, Object>> generateMasterScheduleReport(long profileId, Date startDate, Date endDate);
    
    public List<Map<String, Object>> findCancelledAppointmentReport(Long pProfileId, Date pStartTime, Date pEndTime, boolean justLessonActivities);
  //Added for GSSP-185
    public List<InstructorReportPagingDTO> generateActiveStudentsReport(long locationId, Date startDate, Date endDate, String activityType, boolean isFromPage,String instructorName);
  //Added for GSSP-203
    public List<InstructorReportPagingDTO> generateStudentCheckInReport(long locationId, Date startDate, Date endDate, boolean isFromPage);
    
    //Added for GSSP-205
    public List<InstructorReportPagingDTO> generateInActiveStudentReport(String locationId,String externalId, boolean isFromPage);
    //Added for GSSP-213
    public List<InstructorReportPagingDTO> generateProfileDetailsReport(Date startDate, Date endDate);
  //Added for GSSP-210
    public List<AppointmentHistoryDTO> generateAppointmentHistoryReport(Date startDate, Date endDate, String externalId);
    /**
     * Method used to generate instructor pdf report 
     * 
     * @param locationId
     * @param startDate
     * @param endDate
     * @param isFromPage a flag to indicate generate PDF or JSP page report
     * @return
     */
    public List<InstructorReportPagingDTO> generateInstructorReportPDF(long locationId, Date startDate, Date endDate, boolean isFromPage);
    
    public  List<InstructorReportPagingDTO> generateInstructorAppointmentStatusReport(long locationId, Date startDate, Date endDate);
    
	/** Added for NewInsAptReport _ June 2015 Enhancement */
    //GSSP-190,added a field-InstructreName
    public List<InstructorReportPagingDTO> generateInstructorOpenAppointmentsReportPDF(long locationId, Date startDate, Date endDate, String inputExternalId, boolean isFromPage,String instructorName);
    
    //For GSSP-170
    public List<InstructorReportPagingDTO> generateConflictAppointmentsReportByInstructor(Long pProfileId, Date startDate, Date endDate);
    public List<InstructorReportPagingDTO> generateConflictAppointmentsReportByRoom(Long pProfileId, Date startDate, Date endDate);
    
    //For GSSP-161
    public List<InstructorReportPagingDTO> generateInstructorOutsideAppointmentsReportPDF(Long pProfileId, String startTime, String endTime, String inputExternalId, String dayType);
    
    //Added for GSSP-185
    public List<ActivityDTO> findLessonTypes(Long locationId) ;    
    
    //Changes added for GSSP-238
    public void generateConflictReportBatch() throws Exception;

}
