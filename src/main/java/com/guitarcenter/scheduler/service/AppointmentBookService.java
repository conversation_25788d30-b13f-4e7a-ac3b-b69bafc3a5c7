package com.guitarcenter.scheduler.service;

import com.guitarcenter.scheduler.dao.criterion.dto.AppointmentBookDTO;
import com.guitarcenter.scheduler.dto.BookedAppointmentQueryDTO;

import java.util.List;

public interface AppointmentBookService {

    public List<AppointmentBookDTO> getBookedAppointmentsList(BookedAppointmentQueryDTO queryModel);

    public AppointmentBookDTO reSubmitAppointmentBook(AppointmentBookDTO dto);

}
