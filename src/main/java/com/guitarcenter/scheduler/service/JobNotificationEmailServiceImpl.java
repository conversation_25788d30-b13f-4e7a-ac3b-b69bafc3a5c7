package com.guitarcenter.scheduler.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.criterion.AppointmentCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dto.InstructorWeeklySuccessDTO;
import com.guitarcenter.scheduler.model.Appointment;



//Newly added class for - for GSSP-172 Notification for Scheduler Batch Job Success and Failure
@Service("jobNotificationEmailService")
public class JobNotificationEmailServiceImpl implements JobNotificationEmailService,AppConstants{
 
	@Autowired
	private AppointmentDAO	appointmentDAO;
	
	
	
	public MailSenderService getMailSenderService() {
		return mailSenderService;
	}

	public void setMailSenderService(MailSenderService mailSenderService) {
		this.mailSenderService = mailSenderService;
	}


	@Autowired
	private MailSenderService mailSenderService;
	
	
	
	
	public void sendEmailForJobNotification(String jobType)
	{
			
			Map<String, Object> dataMap = renderDataForJobSuccessNotification(jobType,getEnvironment());
			mailSenderService.sendMail(dataMap, EMAIL_TEMPLATE_FOR_APPT);
	}
	
	public void sendEmailforJobFailureNotification(String jobType,String exception)
	{
			
			Map<String, Object> dataMap = renderDataForFailureNotification(jobType,exception,getEnvironment());
			mailSenderService.sendMail(dataMap, EMAIL_TEMPLATE_FOR_APPT);
	}
	
	private Map<String, Object> renderDataForJobSuccessNotification(String jobType,String environment) {
		Map<String, Object> dataMap = renderBasicData(jobType);

		//Changes made for GSSP-197 - Added Environment Variable
		dataMap.put(AppConstants.SUBJECT, "Guitar Center Studios Job Notification in " + environment);
		dataMap.put(AppConstants.FROM, "<EMAIL>");
		//Miscellaneous task
		if(environment.equalsIgnoreCase("Production"))
		{	
			dataMap.put(AppConstants.EMAIL_TYPE_TO, JOB_NOTIFICATION_EMAIL_SEND_LIST);
		}
		else
		{
			dataMap.put(AppConstants.EMAIL_TYPE_TO, JOB_NOTIFICATION_EMAIL_SEND_LIST_QA);			
		}
		dataMap.put("jobName", jobType);
		dataMap.put("successStatus", true);
		
		return dataMap;
	}
	//GSSp-267 Job notification 
	
		public void sendEmailForJobNotificationforInstructorReport(String jobType, List<InstructorWeeklySuccessDTO> sucessMap)
		
		{
			
				//--GSSP-314 changes for Instructore scheduleder Email job 
				Map<String, Object> dataMap = renderDataForJobSuccessNotificationforInstructorWeeklyreport(jobType,getEnvironment(),sucessMap);
				
				dataMap.put("successMap", sucessMap);
				mailSenderService.sendMail(dataMap, EMAIL_TEMPLATE_FOR_INS);
		}
	
		//--GSSP-314 new overloaded method for Instructor scheduleder Email job Handling Empty records on template
		private Map<String, Object> renderDataForJobSuccessNotificationforInstructorWeeklyreport(String jobType,String environment, List<InstructorWeeklySuccessDTO> sucessMap) {
			Map<String, Object> dataMap = renderBasicData(jobType);

			//Changes made for GSSP-197 - Added Environment Variable
			dataMap.put(AppConstants.SUBJECT, "Guitar Center Studios Job Notification in " + environment);
			dataMap.put(AppConstants.FROM, "<EMAIL>");
			
			// Miscellaneous task
			if(environment.equalsIgnoreCase("Production"))
			{	
				dataMap.put(AppConstants.EMAIL_TYPE_TO, JOB_NOTIFICATION_EMAIL_SEND_LIST);
			}
			else
			{
				dataMap.put(AppConstants.EMAIL_TYPE_TO, JOB_NOTIFICATION_EMAIL_SEND_LIST_QA);			
			}
			dataMap.put("jobName", jobType);
			dataMap.put("successStatus", true);
			if(null !=sucessMap){
				if(sucessMap.size()==0 ||sucessMap.isEmpty()){
					dataMap.put("successEmptyStatus", true);	
				}else{
					dataMap.put("successEmptyStatus", false);
				}
			}else{
				dataMap.put("successEmptyStatus", false);
			}
			
			return dataMap;
		}
		
	
		
		private Map<String, Object> renderDataForJobSuccessNotificationforInstructorWeeklyreport(String jobType,String environment) {
			Map<String, Object> dataMap = renderBasicData(jobType);

			//Changes made for GSSP-197 - Added Environment Variable
			dataMap.put(AppConstants.SUBJECT, "Guitar Center Studios Job Notification in " + environment);
			dataMap.put(AppConstants.FROM, "<EMAIL>");
			
			// Miscellaneous task
			if(environment.equalsIgnoreCase("Production"))
			{	
				dataMap.put(AppConstants.EMAIL_TYPE_TO, JOB_NOTIFICATION_EMAIL_SEND_LIST);
			}
			else
			{
				dataMap.put(AppConstants.EMAIL_TYPE_TO, JOB_NOTIFICATION_EMAIL_SEND_LIST_QA);			
			}
			dataMap.put("jobName", jobType);
			dataMap.put("successStatus", true);
			
			return dataMap;
		}
		
	
	
	private Map<String, Object> renderDataForFailureNotification(String jobType,String exception,String environment) {
		Map<String, Object> dataMap = renderBasicData(jobType);
		
		//Changes made for GSSP-197 - Added Environment Variable
		dataMap.put(AppConstants.SUBJECT, "Guitar Center Studios Job Notification in " + environment);
		dataMap.put(AppConstants.FROM, "<EMAIL>");
		// Miscellaneous task
		if(environment.equalsIgnoreCase("Production"))
		{	
			dataMap.put(AppConstants.EMAIL_TYPE_TO, JOB_NOTIFICATION_EMAIL_SEND_LIST);
		}
		else
		{
			dataMap.put(AppConstants.EMAIL_TYPE_TO, JOB_NOTIFICATION_EMAIL_SEND_LIST_QA);			
		}
		dataMap.put("successStatus", false);
		dataMap.put("jobName", jobType);
		dataMap.put("exception", exception);
		
		return dataMap;
	}
	
	
	private Map<String, Object> renderBasicData(String jobType) {
		Map<String, Object> dataMap = new HashMap<String, Object>();
		dataMap.put("jobType", jobType);
		return dataMap;
	}
	
	
	
	public String getEnvironment()
	{
		
		List<String> environemntList = null;
		Criterion<Appointment, String> criterion = AppointmentCriterion.findEnvironment();
		environemntList =  appointmentDAO.search(criterion);
		
		if(null != environemntList && environemntList.size() > 0)
			return environemntList.get(0);
		
		else
			return "";
	}

	// GSSP-268 lack Appointment job issue start
	@Override
	public void sendEmailForLackAppointmentWithData(String jobType,
			Map<String,String> appointmentsFailedToUpdate) {
		Map<String, Object> dataMap = renderDataForAppointmentsFailedToUpdate(jobType,getEnvironment());
		dataMap.put("appointmentsFailedToUpdate", appointmentsFailedToUpdate);
		mailSenderService.sendMail(dataMap, EMAIL_TEMPLATE_FOR_LACK_APPOINMENT_FAILS);
		
	}
	
	@Override
	public void sendEmailForCRMAppointmentDataFile(String jobType,String data){
		Map<String, Object> dataMap = renderDataForAppointmentsFailedToUpdate(jobType,getEnvironment());
		 dataMap.put("CRMAppointmentDataFile", "CRMAppointmentDataFile details");
		mailSenderService.sendMail(dataMap, EMAIL_TEMPLATE_FOR_APPT);
		
	}

	private Map<String, Object> renderDataForAppointmentsFailedToUpdate(String jobType, String environment) {
		Map<String, Object> dataMap = renderBasicData(jobType);

		//Changes made for GSSP-197 - Added Environment Variable
		dataMap.put(AppConstants.SUBJECT, "Guitar Center Studios Job Notification in " + environment);
		dataMap.put(AppConstants.FROM, "<EMAIL>");
		// Miscellaneous task
		if(environment.equalsIgnoreCase("Production"))
		{	
			dataMap.put(AppConstants.EMAIL_TYPE_TO, JOB_NOTIFICATION_EMAIL_SEND_LIST);
		}
		else
		{
			dataMap.put(AppConstants.EMAIL_TYPE_TO, JOB_NOTIFICATION_EMAIL_SEND_LIST_QA);			
		}
		dataMap.put("jobName", jobType);
		dataMap.put("successStatus", true);
		
		return dataMap;
	} //lack Appointment job issue end
	

}
