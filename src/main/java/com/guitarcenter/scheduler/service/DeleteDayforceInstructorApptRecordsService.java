package com.guitarcenter.scheduler.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.guitarcenter.scheduler.dao.criterion.dto.AppointmentHistoryDTO;
import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.dto.InstructorReportPagingDTO;
import com.guitarcenter.scheduler.dto.UpdateAppointmentDTO;

public interface DeleteDayforceInstructorApptRecordsService {

	public String  getDeleteDayforceInstructorApptRecords(String startDate,String endDate); 
	public  Map<String, Object> appointmentStatusUpdateService(UpdateAppointmentDTO updateAppointmentDTO);
	
 }