package com.guitarcenter.scheduler.service;

import java.util.List;
import java.util.Map;

import com.guitarcenter.scheduler.dto.InstructorWeeklySuccessDTO;

//Newly added interface for - for GSSP-172 Notification for Scheduler Batch Job Success and Failure
public interface JobNotificationEmailService {
	
	
	String EMAIL_TEMPLATE_FOR_APPT = "jobNotificationEmailTemplate.ftl";
	String EMAIL_TEMPLATE_FOR_LACK_APPOINMENT_FAILS="JobNotificationEmailTemplateForLackAppFails.ftl";

	//GSSP-267 Job notification 

	String EMAIL_TEMPLATE_FOR_INS = "jobNotificationEmailTemplateforInstructor.ftl";
	
	//Changes made for GSSP-197 - Added Environment Variable
	void sendEmailForJobNotification(String jobType);
	
	//Changes made for GSSP-197 - Added Environment Variable
	void sendEmailforJobFailureNotification(String jobType,String exception);
	//GSSP-267 Job notification 

	void sendEmailForJobNotificationforInstructorReport(String jobType, List<InstructorWeeklySuccessDTO> sucessMap);

	void sendEmailForLackAppointmentWithData(String jobType, Map<String,String> appointmentsFailedToUpdate);//GSSP-268 lack Appointment job issue
	
	void sendEmailForCRMAppointmentDataFile(String jobType, String data);
}