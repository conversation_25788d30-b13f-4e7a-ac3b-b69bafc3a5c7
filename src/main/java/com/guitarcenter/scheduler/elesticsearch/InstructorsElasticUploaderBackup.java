/*
 * package com.guitarcenter.scheduler.elesticsearch;
 * 
 * import java.io.File; import java.io.IOException; import java.io.PrintStream;
 * import java.security.KeyManagementException; import
 * java.security.KeyStoreException; import
 * java.security.NoSuchAlgorithmException; import
 * java.security.cert.CertificateException; import java.sql.Connection; import
 * java.text.SimpleDateFormat; import java.util.Date; import java.util.HashMap;
 * import java.util.Map;
 * 
 * import org.apache.commons.io.FileUtils; import org.apache.http.HttpHost;
 * import org.apache.http.HttpRequestInterceptor; import
 * org.elasticsearch.action.bulk.BulkRequest; import
 * org.elasticsearch.action.bulk.BulkResponse; import
 * org.elasticsearch.action.get.GetRequest; import
 * org.elasticsearch.action.index.IndexRequest; import
 * org.elasticsearch.action.update.UpdateRequest; import
 * org.elasticsearch.client.RequestOptions; import
 * org.elasticsearch.client.RestClient; import
 * org.elasticsearch.client.RestHighLevelClient; import org.slf4j.Logger; import
 * org.slf4j.LoggerFactory;
 * 
 * import com.amazonaws.auth.AWS4Signer; import
 * com.amazonaws.auth.AWSCredentialsProvider; import
 * com.amazonaws.auth.DefaultAWSCredentialsProviderChain; import
 * com.guitarcenter.scheduler.elesticsearch.AWSRequestSigningApacheInterceptor;
 * 
 * 
 * 
 * public class InstructorsElasticUploaderBackup {
 * 
 * 
 * private static final Logger LOGGER =
 * LoggerFactory.getLogger(InstructorsElasticUploaderBackup.class);
 * 
 * 
 * 
 * 
 * 
 * private static String serviceName = "es"; private static String region =
 * "us-west-2"; private static String aesEndpoint =
 * "https://search-gc-es-487-prod-rhanh3ea25k5getrxi3c3ltqba.us-west-2.es.amazonaws.com";
 * // e.g. https://search-mydomain.uswest-1.es.amazonaws.com
 * 
 * static final AWSCredentialsProvider credentialsProvider = new
 * DefaultAWSCredentialsProviderChain();
 * 
 * public static void main(String[] args) throws IOException,
 * KeyManagementException, NoSuchAlgorithmException, KeyStoreException,
 * CertificateException { SimpleDateFormat sdf = new
 * SimpleDateFormat("yyyy-MM-dd'T'HH-mm");
 * 
 * String logFileLocation=
 * "/data/lessons/gc-seller-view/log/gcss_instructoravailablity-output-qa-"+sdf.
 * format(new Date())+".log";
 * 
 * 
 * System.out.println("My Test"); LOGGER.info("message");
 * 
 * try { //Note: Removed Char Set. FileUtils.writeStringToFile(new
 * File(logFileLocation), "", true);
 * 
 * // FileUtils.writeStringToFile(new File(logFileLocation), "",
 * Charset.defaultCharset(), true); PrintStream fileOut = new
 * PrintStream(logFileLocation);
 * 
 * System.setOut(fileOut); System.out.println(fileOut);
 * System.out.println("My Test1"); } catch(Exception e) {
 * 
 * }
 * 
 * System.out.println("InstructorsElasticUploader:main() - Enter");
 * System.setProperty("aws.accessKeyId","********************");
 * System.setProperty("aws.secretKey","4oXHLJdKA3xwKvzR4cDvHRYtf5yzmJ0sA1zPf8a8"
 * ); RestHighLevelClient client = esClient(serviceName, region);
 * 
 * Connection dbConn = null;
 * 
 * try { // dbConn = DBConnectionService.getGCOloDBConnection();
 * 
 * // Statement stmt = dbConn.createStatement(); // stmt.setFetchSize(20000);
 * 
 * // String addon="";
 * 
 * // if("hourly".equalsIgnoreCase(mode)) { // addon =
 * " and c.updated > systimestamp - numtodsinterval(" + updatedTimeFrame +
 * ",'MINUTE')"; // }
 * 
 * 
 * String sql =
 * "select i.instructor_id, p_i.first_name  || ' ' || p_i.last_name as Instructor_Name, p_i.first_name, p_i.last_name, i.external_id from "
 * + " gcstudio.instructor i, " + " gcstudio.person p_i where " +
 * " p_i.person_id=i.person_id";
 * 
 * 
 * 
 * //
 * System.out.println("InstructorsElasticUploader:main() - Executing SQL --> " +
 * sql); // ResultSet resultSet = stmt.executeQuery(sql);
 * 
 * BulkRequest bulkRequest = new BulkRequest();
 * 
 * GetRequest getRequest = null;
 * 
 * // int count = 0;
 * 
 * 
 * while (resultSet.next()) { count++;
 * 
 * 
 * System.out.println("My Test2");
 * 
 * Map<String,Object> storeDetailsMap = new HashMap<>();
 * 
 * 
 * storeDetailsMap.put("id",resultSet.getString("instructor_id"));
 * storeDetailsMap.put("firstname",resultSet.getString("first_name"));
 * storeDetailsMap.put("lastname",resultSet.getString("last_name"));
 * storeDetailsMap.put("name",resultSet.getString("Instructor_Name"));
 * storeDetailsMap.put("externalid",resultSet.getString("external_id"));
 * 
 * 
 * // String gcssreseultSet = "GCSS_InstructorAvaialbity_test";
 * 
 * final String gcssreseultSet = "{" + "  \"geodata\": [" + "    {" +
 * "      \"id\": \"3\"," + "      \"name\": \"Julie Sherman\"," +
 * "      \"gender\" : \"female\"," +
 * "      \"latitude\" : \"37.33774833333334\"," +
 * "      \"longitude\" : \"-121.88670166666667\"" + "    }," + "    {" +
 * "      \"id\": \"3\"," + "      \"name\": \"Johnny Depp\"," +
 * "      \"gender\" : \"male\"," + "      \"latitude\" : \"37.336453\"," +
 * "      \"longitude\" : \"-121.884985\"" + "    }" + "  ]" + "}";
 * 
 * IndexRequest indexRequest = new
 * IndexRequest("lpp_instructor_master_qa").id(gcssreseultSet)
 * .source(storeDetailsMap);
 * 
 * bulkRequest.add(new UpdateRequest("lpp_instructor_master_qa", gcssreseultSet)
 * .doc(storeDetailsMap).upsert(indexRequest));
 * 
 * if(count%1000==0) { //
 * System.out.println("InstructorsElasticUploader:main() - "
 * +count+" Docs Uploaded");
 * 
 * BulkResponse bulkResponse = client.bulk(bulkRequest, RequestOptions.DEFAULT);
 * 
 * System.out.println(bulkResponse.status());
 * 
 * // bulkRequest = new BulkRequest();
 * 
 * }
 * 
 * //
 * System.out.println("InstructorsElasticUploader:main() - Count --> "+count);
 * // }
 * 
 * if(null!=bulkRequest.requests()) { System.out.
 * println("InstructorsElasticUploader:main() - Uploading the Pending Ones --> "
 * + bulkRequest.requests().size()); BulkResponse bulkResponseresponse =
 * client.bulk(bulkRequest, RequestOptions.DEFAULT);
 * 
 * System.out.println(bulkResponseresponse.status());
 * System.out.println(bulkResponseresponse); }
 * 
 * // System.out.
 * println("InstructorsElasticUploader:main() - Total Rows Retrieved --> " +
 * count);
 * 
 * } catch(Exception e) { e.printStackTrace(); } finally { try { client.close();
 * // client.
 * 
 * // dbConn.close(); } catch(Exception e) { e.printStackTrace(); } }
 * 
 * 
 * 
 * System.out.println("InstructorsElasticUploader:main() - Exit"); }
 * 
 * public static RestHighLevelClient esClient(String serviceName, String region)
 * { AWS4Signer signer = new AWS4Signer(); signer.setServiceName(serviceName);
 * signer.setRegionName(region); HttpRequestInterceptor interceptor = new
 * AWSRequestSigningApacheInterceptor(serviceName, signer, credentialsProvider);
 * return new
 * RestHighLevelClient(RestClient.builder(HttpHost.create(aesEndpoint)).
 * setHttpClientConfigCallback(hacb -> hacb.addInterceptorLast(interceptor))); }
 * }
 */