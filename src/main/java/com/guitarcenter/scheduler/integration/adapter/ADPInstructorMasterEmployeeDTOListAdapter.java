package com.guitarcenter.scheduler.integration.adapter;

import java.util.Scanner;

import org.apache.commons.lang3.StringUtils;

import com.guitarcenter.scheduler.integration.dto.InstructorPersonalDetailsDTO;


//New class created for GSSP-275
public class ADPInstructorMasterEmployeeDTOListAdapter<T, V>
    extends ADPDTOListAdapter<T, V> {
    
    /**
     * Defines the system property key for an override that if true will prevent
     * the authentication id of ADPInstructorMasterEmployeeDTOListAdapter 8.5 being updated from EDW file.
     * 
     * Used in DEV and QA environments to avoid resetting auth_id that has been
     * manually mapped to a specific value.
     */
   
    
    public ADPInstructorMasterEmployeeDTOListAdapter(T externalData) {
        super(externalData);
    }
    
    /**
     * Returns an EmployeeDTO built from the values in the supplied parser.
     * 
     * @return instance of EmployeeDTO or null
     */
    @SuppressWarnings("unchecked")
    @Override
    protected V handleLine(Scanner parser) {
        /* The file has been split into fields, but not all fields are relevant
         * to scheduler. Build a DTO using the known fields and let the business
         * logic handle validation.
         */
    	InstructorPersonalDetailsDTO instructorPersonalDetailsDTO = new InstructorPersonalDetailsDTO();
    	instructorPersonalDetailsDTO.setExternalSource(ADP_SOURCE_VALUE);
        
        
        String value = parser.next();
       
        if (StringUtils.isNotBlank(value)) {
        	instructorPersonalDetailsDTO.setExternalId(value.trim());
        	
        }
        
        
        value = parser.next();        
        if (StringUtils.isNotBlank(value)) {
        	instructorPersonalDetailsDTO.setFirstName(value.trim());
        	
        }
        
       
        value = parser.next();       
        if (StringUtils.isNotBlank(value)) {
        	instructorPersonalDetailsDTO.setLastName(value.trim());
        }
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
        	instructorPersonalDetailsDTO.setLocation(value.trim());
        }
        
        
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
        	instructorPersonalDetailsDTO.setLocationcode(value.trim());
        }
        
        
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
        	instructorPersonalDetailsDTO.setJobCode(value.trim());
        }
        
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
        	instructorPersonalDetailsDTO.setJobTitle(value.trim());
        }
        value = parser.next();     
        if (StringUtils.isNotBlank(value)) {
        	instructorPersonalDetailsDTO.setEmail(value.trim());
        	
        }
        
       
        if (StringUtils.isNotBlank(value)) 
            {
        	instructorPersonalDetailsDTO.setRehire(value.trim());
        }
        
       
        parser.nextLine();
        
        return (V) instructorPersonalDetailsDTO;
    }

	

}
