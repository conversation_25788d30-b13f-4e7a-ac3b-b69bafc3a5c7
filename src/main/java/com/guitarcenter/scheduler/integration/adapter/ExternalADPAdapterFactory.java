package com.guitarcenter.scheduler.integration.adapter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.integration.dto.InstructorPersonalDetailsDTO;


/**
 * Implements a factory for selecting external data adapter implementations
 *.
 *
 * <AUTHOR>
 * @version $Id:$
 */
//New class created for GSSP-275
public class ExternalADPAdapterFactory {
    private static Logger log = LoggerFactory.getLogger(ExternalADPAdapterFactory.class);
    
    private ExternalADPAdapterFactory() {
        if (log.isDebugEnabled()) {
            log.debug("instance created");
        }
    }
    
    /**
     * Factory method to return an initialised ExternalAdapter that takes an
     *
     *  
     * @param externalData
     * @return an adapter of type V that can extract information from an object
     *         of type T
     */
    public static <T, V> ExternalADPAdapter<T, V> getExternalAdapter(T externalData, Class<V> type) {
        if (log.isDebugEnabled()) {
            log.debug("looking for an adapter for {} that returns {}",
                      externalData, type);
        }
        ExternalADPAdapter<T, V> adapter = null;
         if (externalData instanceof Readable) {
            
             if (type == InstructorPersonalDetailsDTO.class) {
                adapter =  new ADPInstructorMasterEmployeeDTOListAdapter<T, V>(externalData);
               
            }
        }
        if (adapter == null) {
            adapter = new DefaultExternalADPAdapter<T, V>();
        }
        if (log.isDebugEnabled()) {
            log.debug("returning adapter {}", adapter);
        }
        return adapter;
    }

	
}
