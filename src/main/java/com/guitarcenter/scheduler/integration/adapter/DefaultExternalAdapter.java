package com.guitarcenter.scheduler.integration.adapter;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;

/**
 * Implements a default instance of ExternalAdapter that always returns a
 * correctly cast null for getEntity.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

public class DefaultExternalAdapter<T, V> implements ExternalAdapter<T, V>
{
    Logger log = LoggerFactory.getLogger(this.getClass());
    
    /**
     * The default constructor is protected to prevent construction outside of
     * factory methods.
     */
    protected DefaultExternalAdapter() {
        if (log.isDebugEnabled()) {
            log.debug("creating a DefaultExternalAdapter");
        }
    }
    
    /**
     * The default external adapter always returns a null cast for the expected
     * object type.
     * 
     * @return null of type V
     * @throws IntegrationServiceException
     */
    public V getEntity()
        throws IntegrationServiceException
    {
        if (log.isDebugEnabled()) {
            log.debug("returning null");
        }
        return null;
    }
    
    /**
     * The default external adapter always returns a null cast for the expected
     * object type.
     * 
     * @return null of type List<V>
     * @throws IntegrationServiceException
     */
    public List<V> getEntities()
        throws IntegrationServiceException
    {
        if (log.isDebugEnabled()) {
            log.debug("returning null");
        }
        return null;
    }
}
