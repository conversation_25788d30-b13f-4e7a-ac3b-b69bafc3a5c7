package com.guitarcenter.scheduler.integration.adapter;

import java.util.List;

import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;

/**
 * Defines the interface expected for external/internal data object adapters.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

public interface ExternalAdapter<T, V> {
    /**
     * The contract is that given the adapter was created/assigned an external
     * object representation of type T, the adapter will return an instance of
     * internal object of type V. There are no guarantees that the return object
     * will be non-null, or that the return object is a persistent or ready to
     * persist.
     * 
     * @return an internal representation object of type V
     * @throws IntegrationServiceException
     */
    public V getEntity() throws IntegrationServiceException;
    
    /**
     * Given an external object representation of type T, the adapter will
     * return a List of V instances, where V is an internal scheduler type.
     * There are no guarantees that the V instances in List will be persistent
     * or ready to persist.
     * 
     * @return a List of internal representation objects of type V
     * @throws IntegrationServiceException
     */
    public List<V> getEntities() throws IntegrationServiceException;

}
