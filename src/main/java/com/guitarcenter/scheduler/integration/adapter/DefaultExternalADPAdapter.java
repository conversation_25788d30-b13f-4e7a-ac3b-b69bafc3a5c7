package com.guitarcenter.scheduler.integration.adapter;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;

/**
 * Implements a default instance of ExternalAdapter that always returns a
 * correctly cast null for getEntity.
 *
 * <AUTHOR> @version $Id:$
 */
//New class created for GSSP-275
public class DefaultExternalADPAdapter<T, V> implements ExternalADPAdapter<T, V>
{
    Logger log = LoggerFactory.getLogger(this.getClass());
    
    /**
     * The default constructor is protected to prevent construction outside of
     * factory methods.
     */
    protected DefaultExternalADPAdapter() {
        if (log.isDebugEnabled()) {
            log.debug("creating a DefaultExternalAdapter");
        }
    }
    
    /**
     * The default external adapter always returns a null cast for the expected
     * object type.
     * 
     * @return null of type V
     * @throws IntegrationServiceException
     */
    public V getEntity()
        throws IntegrationServiceException
    {
        if (log.isDebugEnabled()) {
            log.debug("returning null");
        }
        return null;
    }
    
    /**
     * The default external adapter always returns a null cast for the expected
     * object type.
     * 
     * @return null of type List<V>
     * @throws IntegrationServiceException
     */
    public List<V> getEntities()
        throws IntegrationServiceException
    {
        if (log.isDebugEnabled()) {
            log.debug("returning null");
        }
        return null;
    }
}
