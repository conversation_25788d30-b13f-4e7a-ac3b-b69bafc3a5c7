package com.guitarcenter.scheduler.integration.adapter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.dto.CustomerDTO;
import com.guitarcenter.scheduler.dto.LocationDTO;
import com.guitarcenter.scheduler.integration.dto.EmployeeDTO;
import com.guitarcenter.scheduler.integration.generated.SyncCustomer;

/**
 * Implements a factory for selecting external data adapter implementations
 * based on a supplied generic object.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

public class ExternalAdapterFactory {
    private static Logger log = LoggerFactory.getLogger(ExternalAdapterFactory.class);
    
    private ExternalAdapterFactory() {
        if (log.isDebugEnabled()) {
            log.debug("instance created");
        }
    }
    
    /**
     * Factory method to return an initialised ExternalAdapter that takes an
     * externally provided object of type T and returns a Scheduler object of
     * type V.
     *  
     * @param externalData
     * @return an adapter of type V that can extract information from an object
     *         of type T
     */
    public static <T, V> ExternalAdapter<T, V> getExternalAdapter(T externalData, Class<V> type) {
        if (log.isDebugEnabled()) {
            log.debug("looking for an adapter for {} that returns {}",
                      externalData, type);
        }
        ExternalAdapter<T, V> adapter = null;
        if (externalData instanceof SyncCustomer) {
            /* Caller is looking for an adapter that can take an object of type
             * SyncCustomer and return an object of type V.
             */
            if (type == CustomerDTO.class) {
                /* Caller wants to convert/extract SyncCustomer data as a GCSS
                 * CustomerDTO object.
                 */
                adapter = new SyncCustomerCustomerDTOAdapter<T, V>(externalData);
            }
        } else if (externalData instanceof Readable) {
            /* Some kind of EDW file; pick an adapter 
             */
            if (type == LocationDTO.class) {
                adapter = new EDWStudioMasterLocationDTOListAdapter<T, V>(externalData);
            } else if (type == EmployeeDTO.class) {
                adapter = new EDWEmployeeMasterEmployeeDTOListAdapter<T, V>(externalData);
            }
        }
        /* Factory cannot determine the correct adapter for T, V combination;
         * return an instance of DefaultExternalAdapter.
         */
        if (adapter == null) {
            adapter = new DefaultExternalAdapter<T, V>();
        }
        if (log.isDebugEnabled()) {
            log.debug("returning adapter {}", adapter);
        }
        return adapter;
    }
}
