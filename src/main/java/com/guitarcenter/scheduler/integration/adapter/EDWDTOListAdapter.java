package com.guitarcenter.scheduler.integration.adapter;

import java.util.LinkedList;
import java.util.List;
import java.util.Scanner;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;

/**
 * Abstract class to parses an EDW file and returns a List of DTO values.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 * @param <W>
 */

public abstract class EDWDTOListAdapter<T, V>
    implements ExternalAdapter<T, V>
{
    Logger log = LoggerFactory.getLogger(EDWDTOListAdapter.class);

    public static final String EDW_FILE_DELIMITER = "~";
    
    public static final String EDW_SOURCE_VALUE = "EDW";
    
    /**
     * A reference to the external data source
     */
    private T externalData;
    
    protected EDWDTOListAdapter(T externalData) {
        if (log.isDebugEnabled()) {
            log.debug("created EDWDTOAdapter for {}",
                      externalData);
        }
        this.externalData = externalData;
    }
    
    /**
     * The EDW files contain multiple records, so this method will always throw
     * an exception.
     * 
     * @return an entity of type V extracted from external data
     * @throws IntegrationServiceException
     */
    @Override
    public V getEntity()
        throws IntegrationServiceException
    {
        log.warn("getEntity is unsupported for EDW files");
        throw new IntegrationServiceException("Unsupported operation");
    }

    /**
     * Returns a list of DTO records of type V.
     * 
     * @return List of records
     */
    @Override
    public List<V> getEntities()
        throws IntegrationServiceException
    {
        if (log.isDebugEnabled()) {
            log.debug("about to extract Location records from {}", externalData);
        }
        List<V> list = new LinkedList<V>();
        if (externalData instanceof Readable) {
        	Scanner parser = null;
        	try{
            parser = new Scanner((Readable)externalData);
            parser.useDelimiter(EDW_FILE_DELIMITER);
            while (parser.hasNextLine()) {
                V dto = handleLine(parser);
                if (log.isDebugEnabled()) {
                    log.debug("handleLine returned {}", dto);
                }
                if (dto != null) {
                    list.add(dto);
                }
            }
          }finally {
        	    if(parser!=null)
        	    	parser.close();
        	}
        }
        if (log.isDebugEnabled()) {
            log.debug("returning {}", list);
        }
        return list;
    }
    
    /**
     * Transform the values in the scanner supplied into a DTO and return the
     * result.
     * 
     * @param parser a Scanner instance that is initiated and has a line ready
     *               for handling.
     * @return a DTO of the results
     */
    protected abstract V handleLine(Scanner parser);
    
    /**
     * Helper to skip the provided number of field in the scanner.
     * 
     * @param parser the Scanner instance being processed
     * @param skip integer count of the number of fields to skip
     */
    protected void skipFields(Scanner parser, int skip) {
        if (log.isDebugEnabled()) {
            log.debug("attempting to skip {} fields in {}", skip, parser);
        }
        if (parser == null) {
            return;
        }
        if (skip <= 0) {
            return;
        }
        int i = 0;
        while (parser.hasNext() && i++ < skip) {
            parser.next();
        }
        if (log.isDebugEnabled()) {
            log.debug("skipped {} fields; returning", i - 1);
        }
    }
}
