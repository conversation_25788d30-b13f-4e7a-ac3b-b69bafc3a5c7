package com.guitarcenter.scheduler.integration.service;

/**
 * Defines the API for an employee integration service.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

public interface EmployeeIntegrationService {
    /**
     * Process the supplied Readable object and update/create employee records.
     * 
     * @param input Readable to read for data
     */
    public void processEmployees(Readable input) throws IntegrationServiceException;
    
    /**
     * Performs an automatic update of the employee records based on a file
     * retrieved from an external source.
     * 
     * @throws IntegrationServiceException for caught exceptions
     */
    public void scheduledUpdate() throws IntegrationServiceException;
}
