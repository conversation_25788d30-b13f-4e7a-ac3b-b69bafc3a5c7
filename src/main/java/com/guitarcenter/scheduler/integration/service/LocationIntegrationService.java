package com.guitarcenter.scheduler.integration.service;

/**
 * Defines the API for a location integration service.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

public interface LocationIntegrationService {
    /**
     * Process the supplied Readable object and update/create location records.
     * 
     * @param input Readable to read for data
     * @throws IntegrationServiceException for caught exceptions
     */
    public void processLocations(Readable input) throws IntegrationServiceException;
    
    /**
     * Performs an automatic update of the location records based on a file
     * retrieved from an external source.
     * 
     * @throws IntegrationServiceException for caught exceptions
     */
    public void scheduledUpdate() throws IntegrationServiceException;
}
