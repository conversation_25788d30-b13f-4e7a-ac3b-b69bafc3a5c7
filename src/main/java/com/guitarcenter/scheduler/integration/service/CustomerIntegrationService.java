package com.guitarcenter.scheduler.integration.service;

/**
 * CustomerIntegrationService defines the methods that will be exposed to
 * external interfaces to support creation and update of Customer records from
 * outside the scheduler application.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

public interface CustomerIntegrationService {
    public <T> void process(T externalCustomer) throws IntegrationServiceException;
}
