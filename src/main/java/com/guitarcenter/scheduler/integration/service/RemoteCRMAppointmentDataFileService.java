package com.guitarcenter.scheduler.integration.service;

import java.io.FileNotFoundException;
import java.util.Map;

import com.guitarcenter.scheduler.dao.criterion.dto.CRMAppointmentDataFileDTO;

/**
 * Defines a service that can transfer file to a remote server.
 **/

public interface RemoteCRMAppointmentDataFileService {
    
    public void encryptAndUpLoadtoRemote(Map<String, CRMAppointmentDataFileDTO> cRMAppointmentDataFileList) throws IntegrationServiceException;
    /**
     * File transfers to remote path.
     * 
     * @param filePath a String that identifies the path ,where to place the our secure file,
     * 		  If the file file already exists override the same ; can be anything that is meaningful to the
     *        implementation. No error or validation checking occurs by the
     *        method, but the remote server may raise an exception if the
     *        filePath is invalid.
     * @return void
     * @throws IntegrationServiceException on any caught exception
     */
}
