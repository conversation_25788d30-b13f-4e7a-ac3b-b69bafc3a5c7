package com.guitarcenter.scheduler.integration.service;

/**
 * Defines the base exception type for integration services.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

public class IntegrationServiceException extends Exception {
    
    private static final long serialVersionUID = -574727936035755002L;

    /**
     * Declare a constructor that takes a String message.
     * 
     * @param message String containing the message to assign to the exception
     */
    public IntegrationServiceException(String message) {
        super(message);
    }
    
    /**
     * Declare a constructor that takes a String message and a throwable to
     * chain.
     * 
     * @param message String containing the message to assign to the exception
     * @param root root-cause Throwable instance
     */
    public IntegrationServiceException(String message, Throwable root) {
        super(message, root);
    }
}
