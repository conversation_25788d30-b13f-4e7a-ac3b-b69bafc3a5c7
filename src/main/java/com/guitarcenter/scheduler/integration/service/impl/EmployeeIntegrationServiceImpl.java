package com.guitarcenter.scheduler.integration.service.impl;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.guitarcenter.scheduler.integration.adapter.ExternalAdapterFactory;
import com.guitarcenter.scheduler.integration.dto.EmployeeDTO;
import com.guitarcenter.scheduler.integration.service.EmployeeIntegrationService;
import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;
import com.guitarcenter.scheduler.integration.service.RemoteDataIntegrationService;
import com.guitarcenter.scheduler.model.Employee;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.service.EmployeeService;
import com.guitarcenter.scheduler.service.InstructorService;

/**
 * Implements an EmployeeIntegrationService for external updates.
 *
 * <AUTHOR> Emes <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

@Service("employeeIntegrationService")
public class EmployeeIntegrationServiceImpl implements EmployeeIntegrationService
{
    Logger log = LoggerFactory.getLogger(EmployeeIntegrationServiceImpl.class);
    
    /**
     * Defines the full path to a remote "trigger" file that contains the
     * missing piece of the full employee path.
     */
    @Value("${edw.employeeTriggerPath}")
    private String employeeTriggerPath;
    
    /**
     * Defines the template for a full path to a remote file that will be
     * retrieved and processed for updated Employee/Instructor information. This
     * value will be concatenated with the <em>contents</em> of the file in
     * employeeTriggerPath to produce a final file name.
     */
    @Value("${edw.employeeFilePathPrefix}")
    private String employeeFilePathPrefix;
    
    @Autowired
    private InstructorService instructorService;
    
    @Autowired
    private EmployeeService employeeService;
    
    @Autowired
    private RemoteDataIntegrationService remoteDataIntegrationService;
    
    /**
     * Process the supplied Readable object and update/create instructor and
     * staff records.
     * 
     * @param input Readable to read for data
     */
    @Override
    public void processEmployees(Readable input)
        throws IntegrationServiceException
    {
        if (log.isDebugEnabled()) {
            log.debug("about to process input {} for records", input);
        }
        try {
            List<EmployeeDTO> externalEmployees =
                    ExternalAdapterFactory.getExternalAdapter(input, EmployeeDTO.class).getEntities();
            for (EmployeeDTO externalEmployee: externalEmployees) {
                if (isViableEmployeeUpdate(externalEmployee)) {
                    if (isInstructorUpdate(externalEmployee)) {
                         instructorService.updateFromExternal(externalEmployee);
                    } else {
                    	//Changes made for GSSP-209
                    	if(isDMRVPUpdate(externalEmployee))
                    	{
                    		employeeService.updateFromAuthIdForRVPDM(externalEmployee);
                    		
                    	}
                    	else
                    	{	
	                    	employeeService.updateFromExternal(externalEmployee);
	                        
                    	}    
                    }
                } else {
                    if (log.isInfoEnabled()) {
                        log.info("The external record " + externalEmployee +
                                 " does not contain the details necessary for " +
                                 "an update");
                    }
                }
            }
        } catch (Throwable t) {
            throw new IntegrationServiceException("Caught throwable while processing external employee data", t);
        }
        if (log.isDebugEnabled()) {
            log.debug("processing complete");
        }
    }

    /**
     * Helper method to determine if the supplied LocationDTO has information
     * necessary to create or update a Location instance.
     * 
     * @param employee an EmployeeDTO to examine
     * @return true if the EmployeeDTO is suitable for transforming to
     *         persistent object
     */
    private boolean isViableEmployeeUpdate(EmployeeDTO employee) {
        if (log.isDebugEnabled()) {
            log.debug("examining {} for viability", employee);
        }
        /* Viable employee is not null and has a pair of non-blank site and
         * external id fields.
         */
        boolean viable = employee != null &&
            StringUtils.isNotBlank(employee.getSite()) &&
            StringUtils.isNotBlank(employee.getExternalId());
        if (log.isDebugEnabled()) {
            log.debug("employee {} is {} update record", employee,
                      viable ? "a viable" : "an unviable");
        }
        return viable;
    }
    
    /**
     * Helper to determine if the supplied employee is an instructor.
     * 
     * @param employee EmployeeDTO to test
     * @return true if employee is an instructor
     */
    private boolean isInstructorUpdate(EmployeeDTO employee) {
        boolean isInstructor =
            Instructor.isInstructorJobCode(employee.getJobCode());
        if (log.isDebugEnabled()) {
            log.debug("employee {} is {} an instructor", employee,
                      isInstructor ? "" : "not");
        }
        return isInstructor;
    }
    
    //Changes made for GSSP-209
    /**
     * Helper to determine if the supplied employee is an DM or RVP.
     * 
     * @param employee EmployeeDTO to test
     * @return true if employee is an DM or RVP
     */
    private boolean isDMRVPUpdate(EmployeeDTO employee) {
        boolean isDMRVP =
            Employee.isRVP_DMJobCode(employee.getJobCode());
        if (log.isDebugEnabled()) {
            log.debug("employee {} is {} an instructor", employee,
            		isDMRVP ? "" : "not");
        }
        return isDMRVP;
    }
    
    /**
     * Performs an automatic update of the employee records based on a file
     * retrieved from an external source.
     * 
     * @throws IntegrationServiceException for caught exceptions
     */
    public void scheduledUpdate() throws IntegrationServiceException {
        if (log.isDebugEnabled()) {
            log.debug("performing scheduled update of location information");
        }
        try {
            /* First, look for trigger file in the remote location, and get the
             * contents
             */
            String triggerValue = 
                IOUtils.toString(remoteDataIntegrationService.getRemoteData(employeeTriggerPath));
            if (log.isDebugEnabled()) {
                log.debug("employee trigger file {} has content: {}",
                          employeeTriggerPath, triggerValue);
            }
            if (StringUtils.isNotBlank(triggerValue)) {
                String remoteFile = new StringBuilder(employeeFilePathPrefix)
                    .append(triggerValue.trim())
                    .toString();
                if (log.isDebugEnabled()) {
                    log.debug("attempting to retrieve and process {}", remoteFile);
                }
                processEmployees(new InputStreamReader(remoteDataIntegrationService.getRemoteData(remoteFile)));
            }
        } catch (IOException ioe) {
            throw new IntegrationServiceException("Caught an IOException retrieving trigger content", ioe);
        }
    }
}
