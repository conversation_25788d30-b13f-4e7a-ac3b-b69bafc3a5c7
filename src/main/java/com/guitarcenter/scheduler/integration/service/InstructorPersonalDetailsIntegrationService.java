package com.guitarcenter.scheduler.integration.service;

/**
 * Defines the API for an employee integration service.
 *
 * <AUTHOR> 
 * @version $Id:$
 */
//New class created for GSSP-275
public interface InstructorPersonalDetailsIntegrationService {
    /**
     * Process the supplied Readable object and update/create instructor records.
     * 
     * @param input Readable to read for data
     */
    public void processEmployees(Readable input) throws IntegrationServiceException;
    
    /**
     * Performs an automatic update of the instructor records based on a file
     * retrieved from an external source.
     * 
     * @throws IntegrationServiceException for caught exceptions
     */
    public void scheduledUpdate() throws IntegrationServiceException;
}
