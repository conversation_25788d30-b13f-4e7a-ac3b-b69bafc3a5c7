package com.guitarcenter.scheduler.integration.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.security.NoSuchProviderException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.openpgp.PGPException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.dao.criterion.dto.CRMAppointmentDataFileDTO;
import com.guitarcenter.scheduler.dao.util.PGPUtils;
import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;
import com.guitarcenter.scheduler.integration.service.RemoteCRMAppointmentDataFileService;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;

/**
 * Implements a service that can connect to a remote server using SSH private
 * key exchange and transfer secure PGP/GPG file to Remote path.
 * 
 * defined for each working environment. At GuitarCenter the key and username
 * will be different for each of DEV, QA and PROD environments.Also encrypt the 
 * stream of data to PGP format file .
 */
@Component
public class RemoteCRMAppointmentDataFileServiceImpl implements RemoteCRMAppointmentDataFileService
{
    Logger log = LoggerFactory.getLogger(RemoteCRMAppointmentDataFileServiceImpl.class);
    
    private static final String SFTP_TYPE = "sftp";
 
    //The below variable value mapped at crm.properties file configuration
    @Value("${crm.publicKeyFile}")
	private String publicKeyFilePath;
 
	@Value("${crm.encrypt}")
	private String encryptFilePath;
	
	@Value("${crm.fileExtn}")
	private String fileExtn;

	@Value("${crm.sftpPGPdir}")
	private String sftpPGPdir;
 
    @Value("${crm.hostname}")
    private String hostname;

    @Value("${crm.username}")
    private String username;

    @Value("${crm.privateKeyPath}")
    private String privateKeyPath;

    @Value("${crm.knownHostsPath}")
    private String knownHostsPath;
    
 
    /**
     * Configure JSch before making any connections.
     */
    static {
        /* This is the default, but specified here just in case that changes
         * in the future.
         */
        JSch.setConfig("StrictHostKeyChecking", "yes");
    }
    
    /**
     * The map of data is encrypted to file in GPG format ,
     * and placed in the Application server ,and transfer 
     * to Remote server by get connection and transfer 
     * to Remote path using the SSH connectivity.
     * 
     * @param Map of data that use to create for PGP secure data file .
     * @return void.
     * @throws IntegrationServiceException on any caught exception
     */
  
    @Override
    public  void encryptAndUpLoadtoRemote(Map<String, CRMAppointmentDataFileDTO> cRMAppointmentDataFileMap) throws IntegrationServiceException {

    	String encryptFilePathExtn = null;
 	  try {
 		  

		 		String pattern = "yyyyMMdd";
		 		//CRM-338 ::Before one-date changed as per job trigger time changed to 12:30 AM PST.
				//Need to get all the date changed yestarday and till time job triggered
				Calendar cal = Calendar.getInstance();
				cal.setTime(new Date());
				cal.add(Calendar.DATE, -1);
				String currentDate =new SimpleDateFormat(pattern).format(cal.getTime());
 		 		encryptFilePathExtn =  AppConstants.APPLICATION_DATA_FILE_NAME+currentDate+"."+fileExtn;
 		 
    			FileInputStream keyFile = new FileInputStream(publicKeyFilePath);
    			FileOutputStream encryptFile = new FileOutputStream(encryptFilePath+encryptFilePathExtn);
    			PGPUtils.encryptFile(encryptFile, cRMAppointmentDataFileMap, PGPUtils.readPublicKey(keyFile), false,false);
 
    			keyFile.close();
    			encryptFile.close();
 
    		} catch (NoSuchProviderException e) {
    			 
    			log.warn("CRM NoSuchProviderException  "+e);
    		} catch (IOException e) {
    			 
    			log.warn("CRM IOException  "+e);
    		} catch (PGPException e) {
    			 
    			log.warn("CRM PGPException is  "+e);
    		}
     		catch (Exception e) {
    			 
     			log.warn("CRM Exception    "+e);
    			return;
    		}
  
		ChannelSftp sftp = null;
      try {
          sftp = getChannel();
          try {

  			  sftp.cd(sftpPGPdir);;
              File f1 = new File(encryptFilePath+encryptFilePathExtn);
              sftp.put(new FileInputStream(f1), f1.getName(), ChannelSftp.OVERWRITE);

          } catch (SftpException | FileNotFoundException sftpe) {
  
        	  log.warn("Caught a JSchException setting up CRM transfer", sftpe);
          }
		} finally {
			/*
			 * Release any channels and sessions that may be in use
			 */
			if (sftp != null) {
				sftp.disconnect();
				try {
					sftp.getSession().disconnect();
				} catch (JSchException jse) {
					log.warn("Caught a JSchException setting up CRM transfer", jse);
				}
			}
			 
		}
 
    }

    /**
     * Validate that the environment is suitable for remote SSH transfer
     * 
     * @return true if all required parameters are present, false otherwise
     */
    private boolean isValidEnvironment() {
        if (log.isDebugEnabled()) {
            log.debug("validating environment: hostname = {}, username = {}, " +
                      "privateKeyPath = {}, knownHostsPath = {}", hostname,
                      username, privateKeyPath, knownHostsPath);
        }
        boolean isValid = StringUtils.isNotBlank(hostname) &&
            StringUtils.isNotBlank(username) &&
            StringUtils.isNotBlank(privateKeyPath) &&
            StringUtils.isNotBlank(knownHostsPath) &&
            new File(privateKeyPath).canRead() &&
            new File(knownHostsPath).canRead();
        if (log.isDebugEnabled()) {
            log.debug("returning {}", isValid);
        }
        return isValid;
    }
    
    /**
     * Returns an SFTP channel ready for file retrieval
     * 
     * @return ChannelSftp for use by caller
     * @throws IntegrationServiceException if the channel could not be setup for
     *         any reason
     */
    private ChannelSftp getChannel()
        throws IntegrationServiceException
    {
        if (log.isDebugEnabled()) {
            log.debug("trying to setup channel for SFTP");
        }
        ChannelSftp channel = null;
        if (isValidEnvironment()) {
            try {
                JSch jsch = new JSch();
                jsch.addIdentity(privateKeyPath);
                jsch.setKnownHosts(knownHostsPath);
                Session session = jsch.getSession(username, hostname);
                session.connect();
                channel = (ChannelSftp) session.openChannel(SFTP_TYPE);
                channel.connect();
            } catch (JSchException jse) {
                log.warn("Caught a JSchException setting up CRM transfer", jse);
                throw new IntegrationServiceException("SSH setup issue", jse);
            }
        } else {
            throw new IntegrationServiceException("CRM integration properties are invalid");
        }
        if (log.isDebugEnabled()) {
            log.debug("returning {}", channel);
        }
        return channel;
    }

    
}
