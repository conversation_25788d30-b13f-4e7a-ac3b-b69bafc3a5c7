package com.guitarcenter.scheduler.integration.service;

import java.io.InputStream;

/**
 * Defines a service that can retrieve a remote data resource.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

public interface RemoteDataIntegrationService {

    /**
     * Connects to a remote data source and returns an InputStream of the
     * contents of the file named.
     * 
     * @param filePath a String that identifies the resource to stream from the
     *        remote server; can be anything that is meaningful to the
     *        implementation. No error or validation checking occurs by the
     *        method, but the remote server may raise an exception if the
     *        filePath is invalid.
     * @return instance of InputStream linked to the contents of filePath
     * @throws IntegrationServiceException on any caught exception
     */
    public InputStream getRemoteData(String filePath) throws IntegrationServiceException;
}
