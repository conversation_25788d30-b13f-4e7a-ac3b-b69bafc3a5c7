package com.guitarcenter.scheduler.integration.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dto.CustomerDTO;
import com.guitarcenter.scheduler.integration.adapter.ExternalAdapterFactory;
import com.guitarcenter.scheduler.integration.service.CustomerIntegrationService;
import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;
import com.guitarcenter.scheduler.service.CustomerService;

/**
 * Implements an integration service for customer records coming from an
 * external source.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

@Component
public class CustomerIntegrationServiceImpl implements
        CustomerIntegrationService
{
    Logger log = LoggerFactory.getLogger(CustomerIntegrationServiceImpl.class);
    
    @Autowired
    private CustomerService customerService;
    
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    public <T> void process(T externalCustomer)
        throws IntegrationServiceException
    {
        if (log.isDebugEnabled()) {
            log.debug("process external record: " + externalCustomer);
        }
        CustomerDTO fromExt = null;
        try {
            fromExt = ExternalAdapterFactory.getExternalAdapter(externalCustomer, CustomerDTO.class).getEntity();
            log.error("Info after reading create CustomerIntegrationServiceImpl  fromExt ==>  "+ fromExt);
            if (isViableCustomerUpdate(fromExt)) {
                customerService.updateFromExternal(fromExt);
            } else {
                /* Log the failing update but process no further
                 */
                if (log.isInfoEnabled()) {
                    log.info("The external record " + externalCustomer +
                             " does not contain the details necessary for an " +
                             "update");
                }
            }
        } catch (Throwable t) {
            StringBuilder buf = new StringBuilder("Problem while processing external customer record");
            if (fromExt != null) {
                buf.append(" with GC ID ")
                    .append(fromExt.getExternalId())
                    .append(" [")
                    .append(" ")
                    .append(fromExt.getFirstName())
                    .append(" ")
                    .append(fromExt.getLastName())
                    .append("] code ")
                    .append(fromExt.getCustomerStatus());
            } else {
                buf.append(" - didn't convert to CustomerDTO; original object is ")
                    .append(externalCustomer);
            }
            throw new IntegrationServiceException(buf.toString(), t);
        }
    }
    
    /**
     * Helper function to determine if the supplied CustomerDTO has a
     * minimum level of detail in order to be a viable update.
     * 
     * @param customer the CustomerDTO record to examine
     * @return true if the supplied customer record is viable
     */
    private boolean isViableCustomerUpdate(CustomerDTO customer) {
        if (log.isDebugEnabled()) {
            log.debug("examining {} for viability as an update or new record",
                      customer);
        }
        /* Customer must be non-null and have non-blank fields for external id
         * and site, and a valid first or last name.
         */
        boolean viable = customer != null &&
            StringUtils.isNotBlank(customer.getExternalId()) &&
            StringUtils.isNotBlank(customer.getSite()) &&
            (StringUtils.isNotBlank(customer.getFirstName()) ||
             StringUtils.isNotBlank(customer.getLastName()));
        if (log.isDebugEnabled()) {
            log.debug("customer {} is {} update record", customer,
                      viable ? "a viable" : "an unviable");
        }
        return viable;
    }
    
       
}
