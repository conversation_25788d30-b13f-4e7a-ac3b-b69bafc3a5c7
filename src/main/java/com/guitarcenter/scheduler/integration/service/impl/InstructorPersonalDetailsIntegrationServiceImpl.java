package com.guitarcenter.scheduler.integration.service.impl;

import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.integration.adapter.ExternalADPAdapterFactory;
import com.guitarcenter.scheduler.integration.dto.InstructorPersonalDetailsDTO;
import com.guitarcenter.scheduler.integration.service.InstructorPersonalDetailsIntegrationService;
import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;
import com.guitarcenter.scheduler.integration.service.RemoteADPDataIntegrationService;
import com.guitarcenter.scheduler.service.InstructorService;

/**
 * Implements an InstructorPersonalDetailsIntegrationService for external updates.
 *
 
 * @version $Id:$
 */
//New class created for GSSP-275
@Service("instructorPersonalDetailsIntegrationService")
public class InstructorPersonalDetailsIntegrationServiceImpl implements InstructorPersonalDetailsIntegrationService
{
    Logger log = LoggerFactory.getLogger(InstructorPersonalDetailsIntegrationServiceImpl.class);
    
    /**
     * Defines the full path to a remote "trigger" file that contains the
     * missing piece of the full adp server path.
     */
    @Value("${adp.employeeTriggerPath}")
    private String employeeTriggerPath;
    
    /**
     * Defines the template for a full path to a remote file that will be
     * retrieved and processed for updated Instructor personal email information. This
     * value will be concatenated with the <em>contents</em> of the file in
     * employeeTriggerPath to produce a final file name.
     */
    @Value("${adp.employeeFilePathPrefix}")
    private String employeeFilePathPrefix;
    
    @Autowired
    private InstructorService instructorService;
    
  
    
    @Autowired
    private RemoteADPDataIntegrationService remoteADPDataIntegrationService;
    
    /**
     * Process the supplied Readable object and update/create instructor and
     * staff records.
     * 
     * @param input Readable to read for data
     */
    @SuppressWarnings("resource")
	@Override
    public void processEmployees(Readable input)
        throws IntegrationServiceException
    {
        if (log.isDebugEnabled()) {
            log.debug("about to process input {} for records", input);
        }
        try {
          List<InstructorPersonalDetailsDTO> externalInstructors =
                    ExternalADPAdapterFactory.getExternalAdapter(input, InstructorPersonalDetailsDTO.class).getEntities();
       
            for (InstructorPersonalDetailsDTO externalInstructoremp: externalInstructors) {
                                
                         instructorService.isInstructorExist(externalInstructoremp);
                                  
                    if (log.isInfoEnabled()) {
                        log.info("The external record " + externalInstructoremp +
                                 " does not contain the details necessary for " +
                                 "an update");
                    }                
            }
        } catch (Exception t) {
        	
           throw new IntegrationServiceException("Caught throwable while processing external employee data", t);
        
        }
        if (log.isDebugEnabled()) {
            log.debug("processing complete");
        }
    }

        

   
    /**
     * Performs an automatic update of the person personal details records based on a file
     * retrieved from an external source.
     * @param ext 
     * 
     * @throws IntegrationServiceException for caught exceptions
     */
  
    public void scheduledUpdate() throws IntegrationServiceException {
        if (log.isDebugEnabled()) {
            log.debug("performing scheduled update of location information");
        }
        /* First, look for trigger file in the remote location, and get the
		 * contents
		 */
		if (log.isDebugEnabled()) {
		    log.debug("employee trigger file {} has content: {}",
		    		employeeTriggerPath);
		}
		
		 String fileFilter =employeeTriggerPath + new SimpleDateFormat("MMddyy").format(new Date())+"*." + AppConstants.EXTENSTION;
		 String remoteFile = fileFilter;
		 
		    if (log.isDebugEnabled()) {
		        log.debug("attempting to retrieve and process {}", remoteFile);
		    }
		    processEmployees(new InputStreamReader(remoteADPDataIntegrationService.getRemoteData(remoteFile)));
		 
    }
}
