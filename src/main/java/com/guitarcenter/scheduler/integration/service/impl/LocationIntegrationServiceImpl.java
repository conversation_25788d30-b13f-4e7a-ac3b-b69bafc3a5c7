package com.guitarcenter.scheduler.integration.service.impl;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.guitarcenter.scheduler.dto.LocationDTO;
import com.guitarcenter.scheduler.integration.adapter.ExternalAdapterFactory;
import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;
import com.guitarcenter.scheduler.integration.service.LocationIntegrationService;
import com.guitarcenter.scheduler.integration.service.RemoteDataIntegrationService;
import com.guitarcenter.scheduler.service.LocationManagerService;

/**
 * Implements LocationIntegrationService to update location records from an
 * external source.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

@Service("locationIntegrationService")
public class LocationIntegrationServiceImpl implements LocationIntegrationService
{
    Logger log = LoggerFactory.getLogger(LocationIntegrationServiceImpl.class);
    
    /**
     * Defines the full path to a remote "trigger" file that contains the
     * missing piece of the full location path.
     */
    @Value("${edw.locationTriggerPath}")
    private String locationTriggerPath;
    
    /**
     * Defines the template for a full path to a remote file that will be
     * retrieved and processed for updated Location information. This value will
     * be concatenated with the <em>contents</em> of the file in
     * locationTriggerPath to produce a final file name.
     */
    @Value("${edw.locationFilePathPrefix}")
    private String locationFilePathPrefix;
    
    @Autowired
    private LocationManagerService locationManagerService;
    
    @Autowired
    private RemoteDataIntegrationService remoteDataIntegrationService;
    
    /**
     * Process the supplied Readable object and update/create location records.
     * 
     * @param input Readable to read for data
     */
    @Override
    public void processLocations(Readable input)
        throws IntegrationServiceException
    {
        if (log.isDebugEnabled()) {
            log.debug("about to process input {} for records", input);
        }
        try {
            List<LocationDTO> externalLocations =
                    ExternalAdapterFactory.getExternalAdapter(input, LocationDTO.class).getEntities();
            for (LocationDTO externalLocation: externalLocations) {
                if (isViableLocationUpdate(externalLocation)) {
                    locationManagerService.updateFromExternal(externalLocation);
                } else {
                    if (log.isInfoEnabled()) {
                        log.info("The external record " + externalLocation +
                                 " does not contain the details necessary for " +
                                 "an update");
                    }
                }
            }
        } catch (Throwable t) {
            throw new IntegrationServiceException("Caught throwable while processing external location data", t);
        }
        if (log.isDebugEnabled()) {
            log.debug("processing complete");
        }
    }

    /**
     * Helper method to determine if the supplied LocationDTO has information
     * necessary to create or update a Location instance.
     * 
     * @param location a LocationDTO to examine
     * @return true if the LocationDTO is suitable for transforming to
     *         persistent object
     */
    private boolean isViableLocationUpdate(LocationDTO location) {
        if (log.isDebugEnabled()) {
            log.debug("examining {} for viability", location);
        }
        /* Viable location is not null and has non-blank site, external_id,
         * phone and fax fields.
         */
        boolean viable = location != null &&
            StringUtils.isNotBlank(location.getSite()) &&
            StringUtils.isNotBlank(location.getExternalId());
        if (log.isDebugEnabled()) {
            log.debug("location {} is {} update record", location,
                      viable ? "a viable" : "an unviable");
        }
        return viable;
    }
    
    /**
     * Performs an automatic update of the location records based on a file
     * retrieved from an external source.
     * 
     * @throws IntegrationServiceException for caught exceptions
     */
    public void scheduledUpdate() throws IntegrationServiceException {
        if (log.isDebugEnabled()) {
            log.debug("performing scheduled update of location information");
        }
        try {
            /* First, look for trigger file in the remote location, and get the
             * contents
             */
            String triggerValue = 
                IOUtils.toString(remoteDataIntegrationService.getRemoteData(locationTriggerPath));
            if (log.isDebugEnabled()) {
                log.debug("location trigger file {} has content: {}",
                          locationTriggerPath, triggerValue);
            }
            if (StringUtils.isNotBlank(triggerValue)) {
                String remoteFile = new StringBuilder(locationFilePathPrefix)
                    .append(triggerValue.trim())
                    .toString();
                if (log.isDebugEnabled()) {
                    log.debug("attempting to retrieve and process {}", remoteFile);
                }
                processLocations(new InputStreamReader(remoteDataIntegrationService.getRemoteData(remoteFile)));
            }
        } catch (IOException ioe) {
            throw new IntegrationServiceException("Caught an IOException retrieving trigger content", ioe);
        }
    }
}
