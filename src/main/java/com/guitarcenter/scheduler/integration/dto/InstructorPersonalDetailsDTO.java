package com.guitarcenter.scheduler.integration.dto;

/**
 * Represents a studio InstructorPersonalDetailsDTO as described by an external system.
 *
 * <AUTHOR>
 * @version $Id:$
 */
//New class created for GSSP-275
public class InstructorPersonalDetailsDTO {
	
	private String externalSource;
    private String externalId;
    private String firstName;
    private String lastName;
    private String Locationcode;
    private String Location ;
    private String jobCode;
    private String jobTitle;
    private String email;
    private String Rehire;
    
    public String getExternalSource() {
        return externalSource;
    }
    public void setExternalSource(String externalSource) {
        this.externalSource = externalSource;
    }
	public String getExternalId() {
		return externalId;
	}
	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}
	public String getFirstName() {
		return firstName;
	}
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}
	public String getLastName() {
		return lastName;
	}
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}
	public String getLocationcode() {
		return Locationcode;
	}
	public void setLocationcode(String locationcode) {
		Locationcode = locationcode;
	}
	public String getLocation() {
		return Location;
	}
	public void setLocation(String location) {
		Location = location;
	}
	public String getJobCode() {
		return jobCode;
	}
	public void setJobCode(String jobCode) {
		this.jobCode = jobCode;
	}
	public String getJobTitle() {
		return jobTitle;
	}
	public void setJobTitle(String jobTitle) {
		this.jobTitle = jobTitle;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getRehire() {
		return Rehire;
	}
	public void setRehire(String rehire) {
		Rehire = rehire;
	}
	
    
    
}