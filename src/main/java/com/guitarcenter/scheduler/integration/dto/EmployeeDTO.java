package com.guitarcenter.scheduler.integration.dto;

/**
 * Represents a studio employee as described by an external system.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

public class EmployeeDTO {
    private String externalSource;
    private String site;
    private String studio;
    private String externalId;
    private String firstName;
    private String lastName;
    private String email;
    private int jobCode;
    private String status;
    private String authId;
    
    public String getExternalSource() {
        return externalSource;
    }
    public void setExternalSource(String externalSource) {
        this.externalSource = externalSource;
    }
    public String getSite() {
        return site;
    }
    public void setSite(String site) {
        this.site = site;
    }
    public String getStudio() {
        return studio;
    }
    public void setStudio(String studio) {
        this.studio = studio;
    }
    public String getExternalId() {
        return externalId;
    }
    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }
    public String getFirstName() {
        return firstName;
    }
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }
    public String getLastName() {
        return lastName;
    }
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }
    public String getEmail() {
        return email;
    }
    public void setEmail(String email) {
        this.email = email;
    }
    public int getJobCode() {
        return jobCode;
    }
    public void setJobCode(int jobCode) {
        this.jobCode = jobCode;
    }
    public String getStatus() {
        return status;
    }
    public void setStatus(String status) {
        this.status = status;
    }
    public String getAuthId() {
        return authId;
    }
    public void setAuthId(String authId) {
        this.authId = authId;
    }
}
