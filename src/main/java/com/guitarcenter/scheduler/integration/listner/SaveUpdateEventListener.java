/*
 * package com.guitarcenter.scheduler.integration.listner;
 * 
 * 
 * 
 * import java.io.BufferedReader; import java.io.IOException; import
 * java.io.InputStreamReader; import java.net.HttpURLConnection; import
 * java.net.MalformedURLException; import java.net.URL; import
 * java.text.SimpleDateFormat; import java.util.Date;
 * 
 * 
 * import org.hibernate.event.spi.PostInsertEvent; import
 * org.hibernate.event.spi.PostInsertEventListener; import
 * org.hibernate.event.spi.PostUpdateEvent; import
 * org.hibernate.event.spi.PostUpdateEventListener; import
 * org.springframework.beans.factory.annotation.Autowired;
 * 
 * import com.fasterxml.jackson.core.JsonProcessingException; import
 * com.guitarcenter.scheduler.elesticsearch.
 * InstructorAvailablityElasticUploader; import
 * com.guitarcenter.scheduler.model.Appointment; import
 * com.guitarcenter.scheduler.model.Availability; import
 * com.guitarcenter.scheduler.service.InstructorService; import
 * com.guitarcenter.scheduler.webservice.InstructorAvailabilityService; import
 * com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceDTO; import
 * com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceResponseDTO;
 * 
 * 
 * public class SaveUpdateEventListener {
 * 
 * 
 *//**
	 * 
	 *//*
		 * private static final long serialVersionUID = 1L;
		 * 
		 * 
		 * @Autowired private InstructorService instructorService;
		 * 
		 * @Override public void onPostInsert(PostInsertEvent event) { // TODO
		 * Auto-generated method stub Object obj = event.getEntity();
		 * InstructorAVLServiceDTO input = new InstructorAVLServiceDTO(); if (obj
		 * instanceof Appointment || obj instanceof Availability) { Appointment
		 * appointment = (Appointment) obj;
		 * 
		 * String patterns = "yyyy-MM-dd"; SimpleDateFormat simpleDateFormats = new
		 * SimpleDateFormat(patterns); String startDateStr =
		 * simpleDateFormats.format(new Date()); input.setStartDate(startDateStr);
		 * System.out.println("onPost Save 1 ----------------------------------->"
		 * +appointment); String instrorId =
		 * appointment.getInstructor().getInstructorId()+"";
		 * 
		 * InstructorAvailabilityService ins = new InstructorAvailabilityService();
		 * InstructorAVLServiceDTO instructorServiceDTO = new InstructorAVLServiceDTO();
		 * instructorServiceDTO.setInstructorId(instrorId);
		 * instructorServiceDTO.setStartDate(startDateStr); try {
		 * InstructorAVLServiceResponseDTO output =
		 * ins.getInstructorAvailability(instructorServiceDTO);
		 * System.out.println(output); } catch (JsonProcessingException e) { // TODO
		 * Auto-generated catch block e.printStackTrace(); }
		 * checkInstructorAvailabiltyApi(instrorId,startDateStr); } }
		 * 
		 * @Override public void onPostUpdate(PostUpdateEvent event) { // TODO
		 * Auto-generated method stub Object obj = event.getEntity();
		 * InstructorAVLServiceDTO input = new InstructorAVLServiceDTO(); if (obj
		 * instanceof Appointment || obj instanceof Availability) { Appointment
		 * appointment = (Appointment) obj;
		 * System.out.println("onPostUpdate 2 ----------------------------------->"
		 * +appointment); String patterns = "yyyy-MM-dd"; SimpleDateFormat
		 * simpleDateFormats = new SimpleDateFormat(patterns); String startDateStr =
		 * simpleDateFormats.format(new Date()); input.setStartDate(startDateStr);
		 * 
		 * String instrorId = appointment.getInstructor().getInstructorId()+"";
		 * 
		 * InstructorAvailabilityService ins = new InstructorAvailabilityService();
		 * InstructorAVLServiceDTO instructorServiceDTO = new InstructorAVLServiceDTO();
		 * instructorServiceDTO.setInstructorId(instrorId);
		 * instructorServiceDTO.setStartDate(startDateStr); try {
		 * InstructorAVLServiceResponseDTO output =
		 * ins.getInstructorAvailability(instructorServiceDTO);
		 * System.out.println(output); } catch (JsonProcessingException e) { // TODO
		 * Auto-generated catch block e.printStackTrace(); }
		 * checkInstructorAvailabiltyApi(instrorId,startDateStr); }
		 * 
		 * }
		 * 
		 * private void checkInstructorAvailabiltyApi(String instructorId,String
		 * startDateStr){
		 * 
		 * try{ URL url = new
		 * URL("http://localhost:8082/scheduler/InstructorAvailabilty?instructorId="+
		 * instructorId+"&startDate="+startDateStr+""); HttpURLConnection conn =
		 * (HttpURLConnection) url.openConnection(); conn.setRequestMethod("POST");
		 * conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
		 * 
		 * System.out.println("Output Generate as   "); if (conn.getResponseCode() !=
		 * 200) { throw new RuntimeException("Failed : HTTP error code : " +
		 * conn.getResponseCode()); }
		 * 
		 * BufferedReader br = new BufferedReader(new InputStreamReader(
		 * (conn.getInputStream())));
		 * 
		 * String output; while ((output = br.readLine()) != null) {
		 * System.out.println(output); InstructorAvailablityElasticUploader iaeu = new
		 * InstructorAvailablityElasticUploader(); iaeu.elasticInsertOrUpdate(output);
		 * 
		 * }
		 * 
		 * conn.disconnect();
		 * 
		 * } catch (MalformedURLException e) {
		 * 
		 * e.printStackTrace();
		 * 
		 * } catch (IOException e) {
		 * 
		 * e.printStackTrace();
		 * 
		 * } catch (Exception e) {
		 * 
		 * e.printStackTrace();
		 * 
		 * } } }
		 */