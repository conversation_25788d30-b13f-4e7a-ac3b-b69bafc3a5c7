<?xml version="1.0" encoding="UTF-8"?>
<!--

Revision 47:

R.Rice: 01/14/2010

Removed <Description> from <Taxonomy> element. We will not ever send a text description of an entire taxonomy.
Added version attribute on <Taxonomy> element. Consuming systems can skip the taxonomy if version number is unchanged.
Removed legacyID attribute on <Node> element.
Changed "min occurs" from 1 to 0 on <Description> element (within <Descriptions>).
Added <RelatedNodes> container element and move repeatable <RelatedNode> element inside it.
Changed sequence of elements within <Node>. Was <RelatedNode><Names><Descriptions><Keywords><Attributes>. Changed to <Names><Descriptions><Keywords><Attributes><RelatedNodes>
Removed @requiredFlag from <Attribute> element.
Added/updated several annotations.

==============================================================================================


Revision 46:

Removed the spaces and "camel-cased" the values in the Taxonomy.Node.RelatedNode.RelationType
enumeration.

==============================================================================================
	
Revision 45:

1. Made the RelatedNode element unbounded in the TaxonomyNode.
2. Restructured the Attributes element to enclose a repeating element named Attribute. Product Attributes 
   (i.e. "String Guage") are associated to taxonomy nodes (i.e "Guitar Strings"). Each attribute has a name,
   a sequence, a usage indicator, a required/optional flag, and a set of preset values.

-->
<xs:schema xmlns:taxonomy="http://www.guitarcenter.com/schemas/public/CDM/Taxonomy" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.guitarcenter.com/schemas/public/CDM/Taxonomy" elementFormDefault="qualified" attributeFormDefault="unqualified" version="3.0">
	<xs:element name="Taxonomy" type="taxonomy:TaxonomyType"/>
	<xs:complexType name="TaxonomyType">
		<xs:complexContent>
			<xs:extension base="taxonomy:TaxonomyIdentifierType">
				<xs:sequence>
					<xs:element name="Node" type="taxonomy:TaxonomyNodeType" maxOccurs="unbounded">
						<xs:annotation>
							<xs:documentation>A member of the taxonomy. A category, in other words.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="RecordID" type="xs:string" minOccurs="0"/>
					<xs:element name="DCT" type="xs:string" minOccurs="0"/>
					<xs:element name="DCTName" type="xs:string" minOccurs="0"/>
					<xs:element name="Path" type="xs:string" minOccurs="0"/>
					<xs:element name="GroupType" type="xs:string" minOccurs="0"/>
					<xs:element name="ProductName_UI" type="xs:string" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="TaxonomyIdentifierType">
		<xs:attribute name="name" type="xs:string" use="required">
			<xs:annotation>
				<xs:documentation>The name of the taxonomy (i.e., "Musician's Friend DCSC").</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="version" type="xs:string" use="required">
			<xs:annotation>
				<xs:documentation>The version of the taxonomy. Consists of a 4-digit year and a 2-digit version  number (i.e. "201101").</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="taxonomyID" type="xs:string" use="required">
			<xs:annotation>
				<xs:documentation>The unique identifier for the taxonomy (i.e. "MFDCSC").</xs:documentation>
			</xs:annotation>
		</xs:attribute>

	</xs:complexType>
	<xs:element name="Taxonomies">
		<xs:annotation>
			<xs:documentation>The outermost container element. Contains multiple "taxonomy" elements.</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="taxonomy:Taxonomy" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="TaxonomyNodeType">
		<xs:sequence>
			<xs:element name="Names" minOccurs="0">
				<xs:annotation>
					<xs:documentation>A category can have various names, qualified by site ID. By name we mean what the customer might see while browsing categories on the site (i.e. guitars, percussion, recording, etc.)</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Name" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation>Each name is qualified by a channelID. The first Name element will always have channelID=0, representing the "enterprise" or standard name for the taxonomy node. If a channel is not represented, it should inherit the "enterprise" Name.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:simpleContent>
									<xs:extension base="xs:string">
										<xs:attribute name="channelID" type="xs:int">
											<xs:annotation>
												<xs:documentation>Name is a "channel qualified" element. There can be multiple names, each with a unique channelID. The default channel is "0" for Enterprise.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:simpleContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ShortNames" minOccurs="0">
				<xs:annotation>
					<xs:documentation>A category can have various short names, qualified by site ID. By name we mean what the customer might see while browsing categories on the site (i.e. guitars, percussion, recording, etc.)</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="ShortName" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation>Each short name is qualified by a channelID. The first Name element will always have channelID=0, representing the "enterprise" or standard name for the taxonomy node. If a channel is not represented, it should inherit the "enterprise" Name.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:simpleContent>
									<xs:extension base="xs:string">
										<xs:attribute name="channelID" type="xs:int">
											<xs:annotation>
												<xs:documentation>Name is a "channel qualified" element. There can be multiple names, each with a unique channelID. The default channel is "0" for Enterprise.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:simpleContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Descriptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Container element for all channel-qualified category descriptions.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Description" minOccurs="0" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation>A text description of the taxonomy node.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:simpleContent>
									<xs:extension base="xs:string">
										<xs:attribute name="channelID" type="xs:int">
											<xs:annotation>
												<xs:documentation>Description is a "channel qualified" element. There can be multiple descriptions, each with a unique channelID. The default channel is "0" for Enterprise.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:simpleContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Keywords" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>A comma delimited list of terms associated with the taxonomy node.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Attributes" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Attribute" minOccurs="0" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation>Product Attributes (i.e. "String Guage") are associated to taxonomy nodes (i.e "Guitar Strings"). Each attribute has a name, a sequence, a usage indicator, a required/optional flag, and a set of preset values.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence minOccurs="0">
									<xs:element name="Value" type="xs:string" minOccurs="0" maxOccurs="unbounded">
										<xs:annotation>
											<xs:documentation>Most attributes in Heiler have an enumeration of preset values. In those cases, the values are represented here.</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="attributeName" type="xs:string">
									<xs:annotation>
										<xs:documentation>The name of the product attribute.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="sequence" type="xs:string">
									<xs:annotation>
										<xs:documentation>The order in which a list of attributes should appear when displayed in a customer-facing user interface (i.e., faceted navigation, product comparison tables).</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="usage">
									<xs:annotation>
										<xs:documentation>Options are: browse, compare, both, neither.</xs:documentation>
									</xs:annotation>
									<xs:simpleType>
										<xs:restriction base="xs:string">
											<xs:enumeration value="Navigation"/>
											<xs:enumeration value="Product Comparison"/>
											<xs:enumeration value="Both"/>
											<xs:enumeration value="Do Not Publish"/>
										</xs:restriction>
									</xs:simpleType>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="RelatedNodes" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Container element for related nodes.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="RelatedNode" minOccurs="0" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation>A cross reference to another node in this taxonomy, or another node in a different taxonomy.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:simpleContent>
									<xs:extension base="xs:string">
										<xs:attribute name="relatedTaxonomyID" type="xs:string">
											<xs:annotation>
												<xs:documentation>Only applicable to the "equalTo" relationType. It identifies the taxonomy that the related node belongs to.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="relationType">
											<xs:annotation>
												<xs:documentation>Defines the type of relationship this has to the related node. Four relationTypes (Is Accessory, Has Accessory, Similar To, Used With) are used for internal relations (nodes in the same taxonomy). One relation type (Equal To) is used for external relations (nodes in a different taxonomy). When the relationType is "Equal To", the relatedTaxonomyID is required.</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:enumeration value="isAccessory"/>
													<xs:enumeration value="hasAccessory"/>
													<xs:enumeration value="similarTo"/>
													<xs:enumeration value="usedWith"/>
													<xs:enumeration value="equalTo"/>
													<xs:enumeration value="isSecondaryParent"/>
													<xs:enumeration value="hasSecondaryParent"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:attribute>
									</xs:extension>
								</xs:simpleContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="nodeID" type="xs:string">
			<xs:annotation>
				<xs:documentation>The unique identifier for the taxonomy node.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="parentNodeID" type="xs:string">
			<xs:annotation>
				<xs:documentation>The unique identifier of the parent of this taxonomy node. Establishes the hierarchical relationship of nodes.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="action">
			<xs:annotation>
				<xs:documentation>Change indicator. Lets consuming systems know if the node is new, modified in some way, scheduled for deletion, or unchanged. Options are: "create", "update", "delete", "unchanged".</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="create"/>
					<xs:enumeration value="update"/>
					<xs:enumeration value="delete"/>
					<xs:enumeration value="unchanged"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="activeFlag" type="xs:boolean">
			<xs:annotation>
				<xs:documentation>Only "active" taxonomy nodes should be displayed in a customer-facing user interface. </xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
</xs:schema>
