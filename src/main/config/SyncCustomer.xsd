<?xml version="1.0" encoding="utf-8"?>

<!-- edited with XMLSpy v2013 rel. 2 sp2 (x64) (http://www.altova.com) by <PERSON><PERSON><PERSON> (Guitar Center, Inc.) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	 xmlns:ns1="http://www.guitarcenter.com/schemas/public/CDM"
	 xmlns:ns2="http://www.guitarcenter.com/schemas/public/CDM/Customer"
	 targetNamespace="http://www.guitarcenter.com/schemas/public/CDM/Customer"
	 elementFormDefault="unqualified"
	 attributeFormDefault="unqualified">
	<xs:import namespace="http://www.guitarcenter.com/schemas/public/CDM" schemaLocation="GCCommonBaseCDM.xsd"/>
	<xs:element name="SyncCustomer">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="CustomerIDS">
					<xs:complexType>
						<xs:sequence>
							<xs:element ref="ns1:ID" maxOccurs="unbounded"/>
							<xs:element ref="ns1:EnterpriseCustomerID" minOccurs="0"/>
							<xs:element ref="ns1:FederalTaxID" minOccurs="0"/>
						</xs:sequence>
					</xs:complexType>
				</xs:element> 
				<xs:element ref="ns1:CreationDateTime" minOccurs="0"/>
				<xs:element ref="ns1:LastModificationDateTime" minOccurs="0"/>
				<xs:element name="CustomerDetails">
					<xs:complexType>
						<xs:sequence>
							<xs:element ref="ns1:Name"/>
							<xs:element ref="ns1:PrefferedName" minOccurs="0"/>
							<xs:element ref="ns1:DateOfBirth" minOccurs="0"/>
							<xs:element ref="ns1:Gender" minOccurs="0"/>
							<xs:element ref="ns1:JobTitle" minOccurs="0"/>
							<xs:element ref="ns1:Responsibility" minOccurs="0"/>
							<xs:element ref="ns1:DepartmentName" minOccurs="0"/>
							<xs:element ref="ns1:CommunicationPreference" minOccurs="0"/>
							<xs:element ref="ns1:BarCodeID" minOccurs="0"/>
							<xs:element ref="ns1:ParentName" minOccurs="0"/>
							<xs:element ref="ns1:ContractDay" minOccurs="0"/>
						</xs:sequence>
					</xs:complexType>
				</xs:element> 
				<xs:element name="AssociatedStores" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="StoreLocations" minOccurs="0" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element ref="ns1:Location"/>
										<xs:element ref="ns1:Address" minOccurs="0"/>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="ContactDetails" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element ref="ns1:TelephoneNumber" minOccurs="0" maxOccurs="unbounded"/>
							<xs:element ref="ns1:EMailAddress" minOccurs="0" maxOccurs="unbounded"/>
							<xs:element ref="ns1:Address" minOccurs="0" maxOccurs="unbounded"/>
							<xs:element ref="ns1:ContactName"/>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="PurchaseDetails" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="GCStudio" type="ns1:LessonPurchaseType" minOccurs="0"/>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element ref="ns2:User_Type" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="User_Type" type="xs:string"/>
</xs:schema>