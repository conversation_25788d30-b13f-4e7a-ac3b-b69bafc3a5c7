<?xml version="1.0" encoding="utf-8"?>
<!-- edited with XMLSpy v2013 rel. 2 sp2 (x64) (http://www.altova.com) by <PERSON><PERSON> (Guitar Center, Inc.) -->
<xs:schema xmlns:ns="http://www.guitarcenter.com/schemas/public/CDM/Customer" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.guitarcenter.com/schemas/public/CDM/Customer" elementFormDefault="unqualified" attributeFormDefault="unqualified">
	<xs:complexType name="CustomerIDType">
		<xs:sequence>
			<xs:sequence minOccurs="0" maxOccurs="unbounded">
				<xs:element name="ID" type="xs:string"/>
				<xs:element name="CustomerIDType" type="xs:string"/>
				<xs:element name="CustomerIDSourceSystem" type="xs:string"/>
			</xs:sequence>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AddressType">
		<xs:sequence>
			<xs:element name="Type" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="AddressLine1" minOccurs="0"/>
			<xs:element name="AddressLine2" minOccurs="0"/>
			<xs:element name="AddressLine3" minOccurs="0"/>
			<xs:element name="AddressLine4" minOccurs="0"/>
			<xs:element name="City" type="xs:string" minOccurs="0"/>
			<xs:element name="State" type="xs:string" minOccurs="0"/>
			<xs:element name="ZipCode" type="xs:string" minOccurs="0"/>
			<xs:element name="Country" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ContactType">
		<xs:sequence>
			<xs:element name="ContactName" type="xs:string" minOccurs="0"/>
			<xs:element name="ContactPurpose" type="xs:string" minOccurs="0"/>
			<xs:element name="Email" type="ns:EmailType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Phone" type="ns:PhoneType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="EmailType">
		<xs:sequence>
			<xs:element name="Type" type="xs:string" minOccurs="0"/>
			<xs:element name="Email" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PhoneType">
		<xs:sequence>
			<xs:element name="Type" type="xs:string" minOccurs="0"/>
			<xs:element name="Phone" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PersonalDetailsType">
		<xs:sequence>
			<xs:element name="Title" type="xs:string" minOccurs="0"/>
			<xs:element name="Name" minOccurs="0"/>
			<xs:element name="PreferredName" type="xs:string" minOccurs="0"/>
			<xs:element name="Gender" type="xs:string" minOccurs="0"/>
			<xs:element name="BirthDate" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PurchaseDetailsType">
		<xs:sequence>
			<xs:element name="GCStudio" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CustomerType" type="xs:string" minOccurs="0"/> 	<!-- Values like - Regular, Guest, Prospect etc. -->
						<xs:element name="CustomerStatus" type="xs:string" minOccurs="0"/>	<!-- Values like - Active, Cancel etc. -->
						<xs:element name="BillingStatus" type="xs:string" minOccurs="0"/>		<!-- Values like - Auto Billing, Payment Pending, Payment Cancel etc. -->
						<xs:element name="PrimaryStudio" type="xs:string" minOccurs="0"/>
						<xs:element name="LessonStudio" type="xs:string" minOccurs="0"/>
						<xs:element name="LessonInstrument" type="xs:string" minOccurs="0"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="GCPOS" type="xs:string" minOccurs="0"/>
			<xs:element name="GCCOM" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
