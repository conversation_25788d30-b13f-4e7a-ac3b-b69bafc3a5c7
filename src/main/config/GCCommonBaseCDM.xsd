<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2013 rel. 2 sp2 (x64) (http://www.altova.com) by <PERSON><PERSON><PERSON> (Guitar Center, Inc.) -->
<xs:schema xmlns:cdm="http://www.guitarcenter.com/schemas/public/CDM" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:media="http://www.guitarcenter.com/schemas/public/CDM/Media" targetNamespace="http://www.guitarcenter.com/schemas/public/CDM" elementFormDefault="qualified" attributeFormDefault="unqualified" version="3.0">
	<xs:annotation>
		<xs:documentation> The *IdentifierType types are used as the basis for the standard messages
			and are avaialble to be used for cross-reference between elements where full details are
			not needed. </xs:documentation>
	</xs:annotation>
	<xs:annotation>
		<xs:documentation>These are types that are used repeatedly throughout all the related
			XSDs</xs:documentation>
	</xs:annotation>
	<xs:import namespace="http://www.guitarcenter.com/schemas/public/CDM/Media" schemaLocation="CDMMedia.xsd"/>
	<xs:complexType name="RelatedProductsType">
		<xs:sequence>
			<xs:element name="ProductID" type="xs:string">
				<xs:annotation>
					<xs:documentation>The enterprise product ID of the related
							product.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="relationshipType">
			<xs:annotation>
				<xs:documentation>Recommended Accessory Product-specific
										Accessory Direct replacement model Gift item
					 </xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="RecommendedAccessory"/>
					<xs:enumeration value="Product-specificAccessory"/>
					<xs:enumeration value="DirectReplacementModel"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="ChannelAssociationType">
		<xs:sequence>
			<xs:element name="Web" minOccurs="0" maxOccurs="unbounded">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="UpsellSKU" type="cdm:ItemIDType" minOccurs="0" maxOccurs="unbounded"/>
						<xs:element name="WebOKFlag" type="xs:boolean" minOccurs="0"/>
					</xs:sequence>
					<xs:attribute name="channelID" type="xs:string"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="Retail" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="StorePlacement" type="xs:string"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CollectionType">
		<xs:annotation>
			<xs:documentation>A collection is a special container type that will have a flat list of
                skus with override pricing. This will be used to drive promotional pricing from
                Heiler to downstream systems and allows the setting of arbitrary sale prices on
                individual skus.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="CollectionName" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>A name for the collection. Mostly for human
                                consumption, since it will have a machine-generated
                                identifier.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ComponentItem" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>A member of this collection. It will be a SKU of any
                                kind--item, serial, or kit, but NEVER a product.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="ItemID" type="cdm:ItemIdentifierType">
							<xs:annotation>
								<xs:documentation>The item ID.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="OverridePrice" type="xs:decimal">
							<xs:annotation>
								<xs:documentation>The sale price for the item. This will
                                            override the current standard reference price for the
                                            duration of the collection's
                                            lifespan.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="startDate" type="xs:dateTime">
						<xs:annotation>
							<xs:documentation>The first date when this collection will be in use.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="endDate" type="xs:dateTime">
						<xs:annotation>
							<xs:documentation>The date after which this collection will no longer be used.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="actionType">
						<xs:simpleType>
							<xs:restriction base="xs:string">
								<xs:enumeration value="add"/>
								<xs:enumeration value="modify"/>
								<xs:enumeration value="delete"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
					<!-- Added by Rajesh for CDM change . Adds startDate, endDate, and actionType attributes to the component item level.-->
					<!-- End of Change by Rajesh for CDM-->
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="startDate" type="xs:dateTime">
			<xs:annotation>
				<xs:documentation>The first date when this collection will be in use.
                        </xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="endDate" type="xs:dateTime">
			<xs:annotation>
				<xs:documentation>The date after which this collection will no longer be
                            used.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="channelID" type="xs:string"/>
		<xs:attribute name="actionType">
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="add"/>
					<xs:enumeration value="modify"/>
					<xs:enumeration value="delete"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="offerID" type="xs:string"/>
		<!-- Added by Rajesh for CDM change . Adds ActionType and OfferID.-->
		<!-- End of Change by Rajesh for CDM change for ActionType and OfferID-->
	</xs:complexType>
	<xs:complexType name="StyleType">
		<xs:sequence>
			<xs:element name="Class" type="xs:string" minOccurs="0"/>
			<xs:element name="Color" type="xs:string" minOccurs="0"/>
			<xs:element name="FullSKU" type="cdm:ItemIDType" minOccurs="0"/>
			<xs:element name="ItemName" type="xs:string" minOccurs="0"/>
			<xs:element name="Media" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Images" minOccurs="0">
							<xs:complexType>
								<xs:sequence>
									<xs:element name="Image" type="cdm:MediaType" minOccurs="0" maxOccurs="unbounded"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="Videos" minOccurs="0">
							<xs:complexType>
								<xs:sequence>
									<xs:element name="Video" type="cdm:MediaType" minOccurs="0" maxOccurs="unbounded"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="AudioFiles" minOccurs="0">
							<xs:complexType>
								<xs:sequence>
									<xs:element name="Audio" type="cdm:MediaType" minOccurs="0" maxOccurs="unbounded"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="PhysicalCharacteristics" type="cdm:PhysicalCharacteristicsType" minOccurs="0"/>
			<xs:element name="Pricing" type="cdm:PricingInfoType" minOccurs="0"/>
			<xs:element name="Serialization" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="SerialItem" type="cdm:SerialType" minOccurs="0" maxOccurs="unbounded"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ShippingInformation" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="ImportTariffCode" type="xs:string" minOccurs="0"/>
						<xs:element name="HTCCode" type="xs:string" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Harmonized Tariff Code</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="NumberOfCartons" type="xs:string" minOccurs="0"/>
						<xs:element name="OversizeCode" type="xs:string" minOccurs="0"/>
						<xs:element name="RestrictShipmentCode" type="xs:string" minOccurs="0"/>
						<xs:element name="ShippingCost" type="xs:string" minOccurs="0"/>
						<xs:element name="ShippingMethod" type="xs:string" minOccurs="0"/>
						<xs:element name="ShippingWeight" type="xs:string" minOccurs="0"/>
						<xs:element name="SpecificShipper" type="xs:string" minOccurs="0"/>
						<xs:element name="Cartons" minOccurs="0">
							<xs:complexType>
								<xs:sequence>
									<xs:element name="Carton" type="cdm:PackagingType" minOccurs="0" maxOccurs="unbounded"/>
								</xs:sequence>
								<xs:attribute name="numberOfCartons" type="xs:string"/>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="alwaysDaggerFlag" type="xs:boolean"/>
					<xs:attribute name="directImportFlag" type="xs:boolean"/>
					<xs:attribute name="excludePostageChargesFlag" type="xs:boolean"/>
					<xs:attribute name="noFreeShippingFlag" type="xs:boolean"/>
					<xs:attribute name="promoFreeShippingFlag" type="xs:boolean"/>
					<xs:attribute name="neverDaggerFlag" type="xs:boolean"/>
					<xs:attribute name="noTruckFeeFlag" type="xs:boolean"/>
					<xs:attribute name="shipAloneFlag" type="xs:boolean"/>
					<xs:attribute name="shippableCartonFlag" type="xs:boolean"/>
					<xs:attribute name="specificShipperFlag" type="xs:boolean"/>
					<xs:attribute name="uspsFlag" type="xs:boolean"/>
					<xs:attribute name="warehouseDontShipFlag" type="xs:boolean"/>
					<xs:attribute name="forceTruckFlag" type="xs:boolean"/>
				</xs:complexType>
			</xs:element> 
			<xs:element name="StockingCharacteristics" type="cdm:InventoryType" minOccurs="0"/>
			<xs:element name="StockingStatus" nillable="true" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="ST"/>
						<xs:enumeration value="BTD"/>
						<xs:enumeration value="PS"/>
						<xs:enumeration value="SPO"/>
						<xs:enumeration value="HC"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Stories" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Story" type="cdm:StoryType" minOccurs="0" maxOccurs="unbounded"/>
					</xs:sequence>
					<xs:attribute name="multiChannel" type="xs:boolean"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="ItemAttribute" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Repeating element to contain key-value pairs of
                                attributes that describe the product and can be used to drive where
                                the product is displayed on the site.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="xs:string">
							<xs:attribute name="attributeName" type="xs:string" use="required"/>
							<xs:attribute name="rank" type="xs:int"/>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="RelatedItems" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Products that are in some way connected to the current
                                product. Relationship types may include the following: Recommended
                                Accessory Product-specific Accessory Direct replacement model Gift
                                item </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="ItemID" type="xs:string">
							<xs:annotation>
								<xs:documentation>The enterprise product ID of the related
                                            product.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="relationshipType">
						<xs:annotation>
							<xs:documentation>Recommended Accessory Product-specific
                                        Accessory Direct replacement model Gift item
                                    </xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:string">
								<xs:enumeration value="SupplementaryItem"/>
								<xs:enumeration value="Service"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
					<xs:attribute name="quantity" type="xs:int"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="GCItemData" type="cdm:GCItemDataType" minOccurs="0"/>
			<xs:element name="GCItemResponseData" type="cdm:GCItemResponseDataType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="enterpriseItemID" type="xs:string"/>
		<xs:attribute name="webOK" type="xs:boolean">
			<xs:annotation>
				<xs:documentation>Flag used to denote if an Item is sellable on the web regardless of distribution channel.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="mfItemStatus" type="xs:string"/>
		<xs:attribute name="isInternationalFlag" type="xs:boolean"/>
		<xs:attribute name="availableToCanadaFlag" type="xs:boolean"/>
		<xs:attribute name="kitOnlyFlag" type="xs:string"/>
	</xs:complexType>
	<xs:complexType name="SerialType">
		<xs:sequence>
			<xs:element name="SerialName" minOccurs="0">
				<xs:annotation>
					<xs:documentation>It's doubtful whether this is really necessary, esp. if serials inherit from Item.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="xs:string">
							<xs:attribute name="channelID" type="xs:string"/>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="VendorPricing" type="cdm:PricingType" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Serialized items need to be mapped to a vendor. The pricing structure included here is the same as that for the parent item, although the serialized item's vendor may differ from that of the parent item and therefore will have its own data.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="StandardReferencePrice" type="xs:decimal" minOccurs="0">
				<xs:annotation>
					<xs:documentation>A serialized item will have its own standard reference price.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SerialCopy" type="cdm:CopyType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>A serialized item may have its own copy. Not all of the child elements will be populated.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SerialMediaAsset" type="media:MediaAssetType" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>A serialized item will have one or more of its own media assets.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="serialColor" type="xs:string"/>
		<xs:attribute name="serialNumber" type="xs:string" use="required">
			<xs:annotation>
				<xs:documentation>A serial item is a specific instance of a SKU. It inherits the SKU ID from its parent, but it has a serial number and, in the case of guitars, a weight.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="manufacturerSerialNumber" type="xs:string">
			<xs:annotation>
				<xs:documentation>This will be the serial number supplied by the manufacturer. When available, it will be displayed on the web.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="serialWeight" type="xs:decimal">
			<xs:annotation>
				<xs:documentation>This applies specifically to guitars. It is the weight, in pounds, of this specific instance of a guitar SKU.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="enterpriseSKU" type="xs:string"/>
		<xs:attribute name="itemID" type="xs:string"/>
		<xs:attribute name="itemAvailable" type="xs:boolean"/>
		<!-- Attribute  manufacturerSerialNumber added by Rajesh (Task 1092) -->
		<!--End of  Attribute  manufacturerSerialNumber added by Rajesh (Task 1092) -->
	</xs:complexType>
	<xs:element name="SiteVisibilityChanged" type="xs:boolean"/>
	<xs:complexType name="GCItemDataType">
		<xs:sequence>
			<xs:element name="ActivePickSite" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Y"/>
						<xs:enumeration value="N"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ArthurAllocationFlag" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Y"/>
						<xs:enumeration value="N"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="BuyCode" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="S"/>
						<xs:enumeration value="C"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Conveyable" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Y"/>
						<xs:enumeration value="N"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="MinSellPrice" type="xs:decimal" minOccurs="0"/>
			<xs:element name="DCLocation" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="DCNumberOfBoxes" type="xs:string" minOccurs="0"/>
			<xs:element name="ExcludePOSDownload" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Y"/>
						<xs:enumeration value="N"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ForecastImportFlag" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Y"/>
						<xs:enumeration value="N"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="GCSKUType" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="R"/>
						<xs:enumeration value="Y"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="HoldOrder" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Y"/>
						<xs:enumeration value="N"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="HumidityControl" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Y"/>
						<xs:enumeration value="N"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="InventoryFlowMethod" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="1"/>
						<xs:enumeration value="2"/>
						<xs:enumeration value="3"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="MPOverrideFlag" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Y"/>
						<xs:enumeration value="N"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="MultiTagCode" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Y"/>
						<xs:enumeration value="N"/>
						<xs:enumeration value="X"/>
						<xs:enumeration value="Z"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="OrderWindow" type="xs:integer" minOccurs="0"/>
			<xs:element name="PlatinumItem" type="xs:string" minOccurs="0">
				<!--			<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Y"/>
						<xs:enumeration value="N"/>
					</xs:restriction>
				</xs:simpleType> -->
			</xs:element>
			<xs:element name="PreventDCDownload" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Y"/>
						<xs:enumeration value="N"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ProprietaryEndDate" type="xs:string" minOccurs="0"/>
			<xs:element name="ProprietaryMerchCode" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="EM"/>
						<xs:enumeration value="LB"/>
						<xs:enumeration value="NP"/>
						<xs:enumeration value="PB"/>
						<xs:enumeration value="PC"/>
						<xs:enumeration value="SB"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ProprietaryStartDate" type="xs:string" minOccurs="0"/>
			<xs:element name="EnterpriseSKUType" type="xs:string" minOccurs="0"/>
			<xs:element name="GCSKUType_2" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="01"/>
						<xs:enumeration value="02"/>
						<xs:enumeration value="03"/>
						<xs:enumeration value="04"/>
						<xs:enumeration value="12"/>
						<xs:enumeration value="21"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="SPOFlag" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Y"/>
						<xs:enumeration value="N"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="SecurityReqd" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Y"/>
						<xs:enumeration value="N"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="SendToPOS" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Y"/>
						<xs:enumeration value="N"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="SerialCode" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Y"/>
						<xs:enumeration value="N"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="SetCode" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="0"/>
						<xs:enumeration value="1"/>
						<xs:enumeration value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ShipPoint" minOccurs="0" maxOccurs="unbounded">
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="xs:string">
							<xs:attribute name="QuantumShipPt" type="xs:boolean"/>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="SpaceConstraint" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Y"/>
						<xs:enumeration value="N"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="UPCType" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="A"/>
						<xs:enumeration value="B"/>
						<xs:enumeration value="C"/>
						<xs:enumeration value="E"/>
						<xs:enumeration value="I"/>
						<xs:enumeration value="N"/>
						<xs:enumeration value="P"/>
						<xs:enumeration value="R"/>
						<xs:enumeration value="X"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="VendorSplitRatio" type="xs:string" minOccurs="0"/>
			<xs:element name="GCImportFlag" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="10"/>
						<xs:enumeration value="20"/>
						<xs:enumeration value="30"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="GCItemDescription" type="xs:string" minOccurs="0"/>
			<xs:element name="GCBuyer" type="xs:string" minOccurs="0"/>
			<xs:element name="MFReadyForExport" type="xs:boolean" minOccurs="0"/>
			<xs:element name="GCReadyForExport" type="xs:boolean" minOccurs="0"/>
			<xs:element name="CasePack" type="xs:string" minOccurs="0"/>
			<xs:element name="InnerPack" type="xs:string" minOccurs="0"/>
			<xs:element name="GCVendor" minOccurs="0" maxOccurs="unbounded">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="GCVendorLeadTime" type="xs:string" minOccurs="0"/>
						<xs:element name="GCCasePack" type="xs:string" minOccurs="0"/>
						<xs:element name="GCInnerPack" type="xs:string" minOccurs="0"/>
						<xs:element name="GCPartNumber" type="xs:string" minOccurs="0"/>
					</xs:sequence>
					<xs:attribute name="isPrimaryFlag" type="xs:boolean"/>
					<xs:attribute name="VendorID" type="xs:string"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="GCItemStatus" type="xs:string" minOccurs="0"/>
			<xs:element name="FeatherSKU" type="xs:string" minOccurs="0"/>
			<xs:element name="MSRP" type="cdm:GCCost" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="GCVendorCost" minOccurs="0" maxOccurs="unbounded">
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="cdm:GCCost">
							<xs:attribute name="GCVendorID" type="xs:string"/>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="GCVendorCommission" minOccurs="0" maxOccurs="unbounded">
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="cdm:GCCost">
							<xs:attribute name="GCVendorID" type="xs:string"/>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="ProductDimension" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Weight" type="xs:decimal" minOccurs="0"/>
						<xs:element name="Cube" type="xs:decimal" minOccurs="0"/>
						<xs:element name="Length" type="xs:decimal" minOccurs="0"/>
						<xs:element name="Width" type="xs:decimal" minOccurs="0"/>
						<xs:element name="Height" type="xs:decimal" minOccurs="0"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="StdCaseDimension" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Length" type="xs:decimal" minOccurs="0"/>
						<xs:element name="Width" type="xs:decimal" minOccurs="0"/>
						<xs:element name="Height" type="xs:decimal" minOccurs="0"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GiftCertificateType">
		<xs:annotation>
			<xs:documentation>Contains the Gift Cert Info</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="GiftCertificateID" type="xs:string"/>
			<xs:element name="originalAmount" type="xs:decimal"/>
			<xs:element name="remainingAmount" type="xs:decimal"/>
			<xs:element name="expirationDate" type="xs:dateTime" minOccurs="0"/>
			<xs:element name="SiteId" type="xs:string"/>
			<xs:element name="GiftCertificateDetails" type="cdm:GiftCertificateDetailsType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GiftCertificateDetailsType">
		<xs:annotation>
			<xs:documentation>Contains the Gift Cert Info</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="RecipientEmail" type="xs:string" minOccurs="0"/>
			<xs:element name="RecipientName" type="xs:string" minOccurs="0"/>
			<xs:element name="SenderName" type="xs:string" minOccurs="0"/>
			<xs:element name="Message" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GCItemResponseDataType">
		<xs:sequence>
			<xs:element name="GCSKU" type="xs:string"/>
			<xs:element name="GCItemStatus" type="xs:string" minOccurs="0"/>
			<xs:element name="GLP" type="xs:string" minOccurs="0"/>
			<xs:element name="GLPStartDate" type="xs:date" minOccurs="0"/>
			<xs:element name="GLPEndDate" type="xs:date" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GCCost">
		<xs:sequence>
			<xs:element name="Cost" type="xs:string"/>
			<xs:element name="StartDate" type="xs:string" minOccurs="0"/>
			<xs:element name="EndDate" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PackagingType">
		<xs:sequence>
			<xs:element name="Height" type="xs:string" minOccurs="0"/>
			<xs:element name="Length" type="xs:string" minOccurs="0"/>
			<xs:element name="Weight" type="xs:string" minOccurs="0"/>
			<xs:element name="Width" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:attributeGroup name="changeAttributes">
		<xs:attribute name="type" use="required">
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="create"/>
					<xs:enumeration value="update"/>
					<xs:enumeration value="delete"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="highPriority" type="xs:boolean"/>
	</xs:attributeGroup>
	<xs:complexType name="StoryType">
		<xs:sequence>
			<xs:element name="CallToAction" type="xs:string" minOccurs="0"/>
			<xs:element name="DetailedDescription" type="xs:string" minOccurs="0"/>
			<xs:element name="FeatureBullets" type="xs:string" minOccurs="0"/>
			<xs:element name="Heading" type="xs:string" minOccurs="0"/>
			<xs:element name="ShortDescription" type="xs:string" minOccurs="0"/>
			<xs:element name="TechnicalSpecifications" type="xs:string" minOccurs="0"/>
			<xs:element name="ValueAdded" type="xs:string" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="siteID" type="xs:string"/>
		<xs:attribute name="channelID" type="xs:string"/>
	</xs:complexType>
	<xs:complexType name="DurationType">
		<xs:sequence>
			<xs:element name="StartDate" type="xs:date" nillable="true" minOccurs="0"/>
			<xs:element name="EndDate" type="xs:date" nillable="true" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GiftType">
		<xs:sequence>
			<xs:element name="GiftSKU" type="cdm:ItemIDType" minOccurs="0"/>
			<xs:element name="Quantity" type="xs:string" minOccurs="0"/>
			<xs:element name="Validity" type="cdm:DurationType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="siteID" type="xs:string"/>
		<xs:attribute name="channelID" type="xs:string"/>
		<xs:attribute name="promoCode" type="xs:string"/>
	</xs:complexType>
	<xs:complexType name="InventoryType">
		<xs:sequence>
			<xs:element name="CaseDimensions" type="xs:string" minOccurs="0"/>
			<xs:element name="FourWeekDemand" type="xs:string" minOccurs="0"/>
			<xs:element name="LeadTime" type="xs:string" minOccurs="0"/>
			<xs:element name="LocationInWarehouse" type="xs:string" minOccurs="0"/>
			<xs:element name="MACSStatus" type="xs:string" minOccurs="0"/>
			<xs:element name="MinimumOrder" type="xs:string" minOccurs="0"/>
			<xs:element name="PalletQuantity" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of items it takes to fill a palette.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PlannerCode" type="xs:string" minOccurs="0"/>
			<xs:element name="QuantiyInCase" type="xs:string" minOccurs="0"/>
			<xs:element name="QuantityHowManyOfSKU" type="xs:string" minOccurs="0"/>
			<xs:element name="QuantityOnHand" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>QuantityOnHand  is calculated as items physically available in the warehouse  minus the items on order.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UnitOfMeasure" type="xs:string" minOccurs="0"/>
			<xs:element name="BackOrder" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="ExpectedDate" type="xs:dateTime" minOccurs="0"/>
						<xs:element name="QuantityExpected" type="xs:string" minOccurs="0"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="PreOrder" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="ExpectedDate" type="xs:dateTime" minOccurs="0"/>
						<xs:element name="QuantityExpected" type="xs:string" minOccurs="0"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ReplenishmentLeadTime" type="xs:string" minOccurs="0"/>
			<xs:element name="InnerCarton" minOccurs="0">
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="cdm:PackagingType">
							<xs:attribute name="numberOfProducts" type="xs:string"/>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="MasterPack" minOccurs="0">
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="cdm:PackagingType">
							<xs:attribute name="numberOfProducts" type="xs:string"/>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="conveyableFlag" type="xs:boolean"/>
		<xs:attribute name="inStockFlag" type="xs:boolean"/>
		<xs:attribute name="warehouseID" type="xs:string"/>
	</xs:complexType>
	<xs:complexType name="KittingCharacteristics">
		<xs:sequence>
			<xs:element name="Component" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>In each group there are components. A component may be fixed or it may be variable. If it is variable, we give the customer a choice of items as they are assembling their kit. In other words, you get a guitar and your choice of either a strap or a gig bag, plus your choice of a cable or a pack of picks. </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="ComponentSKU" maxOccurs="unbounded">
							<xs:complexType>
								<xs:annotation>
									<xs:documentation>A sku that is a fixed member of this kit.</xs:documentation>
								</xs:annotation>
								<xs:sequence>
									<xs:element name="SiteComponentSKU" maxOccurs="unbounded">
										<xs:complexType>
											<xs:attribute name="channelID" type="xs:anySimpleType"/>
										</xs:complexType>
									</xs:element>
									<xs:element name="ItemIdentifier" type="xs:string" minOccurs="0"/>
								</xs:sequence>
								<xs:attribute name="quantity" type="xs:int"/>
								<xs:attribute name="sequence" type="xs:int"/>
								<xs:attribute name="componentType">
									<xs:simpleType>
										<xs:restriction base="xs:string">
											<xs:enumeration value="static"/>
											<xs:enumeration value="variable"/>
										</xs:restriction>
									</xs:simpleType>
								</xs:attribute>
								<xs:attribute name="FixedVariableFlag" type="xs:string"/>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="sequence" type="xs:int">
						<xs:annotation>
							<xs:documentation>The order in which the components of a kit are presented on the product detail page matters. This indicates what order the components should be shown in.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="componentID" type="xs:string"/>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="kitOnlyFlag" type="xs:boolean"/>
		<xs:attribute name="kitType">
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="static"/>
					<xs:enumeration value="variable"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="MeasurementsType">
		<xs:sequence>
			<xs:element name="BillableWeight" type="xs:string" minOccurs="0"/>
			<xs:element name="Depth" type="xs:string" minOccurs="0"/>
			<xs:element name="DimensionalWeight" type="xs:string" minOccurs="0"/>
			<xs:element name="Height" type="xs:string" minOccurs="0"/>
			<xs:element name="Length" type="xs:string" minOccurs="0"/>
			<xs:element name="Size" type="xs:string" minOccurs="0"/>
			<xs:element name="Weight" type="xs:string" minOccurs="0"/>
			<xs:element name="Width" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="MediaFileType">
		<xs:sequence>
			<xs:element name="Description" type="xs:string" minOccurs="0"/>
			<xs:element name="FileExtension" type="xs:string" minOccurs="0"/>
			<xs:element name="FileURL" type="xs:string" minOccurs="0"/>
			<xs:element name="Size" type="xs:string" minOccurs="0"/>
			<xs:element name="Title" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PhysicalCharacteristicsType">
		<xs:sequence>
			<xs:element name="NumberOfBoxes" type="xs:string" minOccurs="0"/>
			<xs:element name="StyleCode1" type="xs:string" minOccurs="0"/>
			<xs:element name="StyleCode2" type="xs:string" minOccurs="0"/>
			<xs:element name="StyleDescription1" type="xs:string" minOccurs="0"/>
			<xs:element name="StyleDescription2" type="xs:string" minOccurs="0"/>
			<xs:element name="WoodGenus" type="xs:string" minOccurs="0"/>
			<xs:element name="Measurements" type="cdm:MeasurementsType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="conditionCode" type="xs:string"/>
		<xs:attribute name="hazardousMaterialFlag" type="xs:boolean"/>
		<xs:attribute name="multiboxFlag" type="xs:boolean"/>
	</xs:complexType>
	<xs:complexType name="PricingInfoType">
		<xs:sequence>
			<xs:element name="Cost" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Cost captures Item price when purchased from the vendor.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="xs:decimal">
							<xs:attribute name="vendorID" type="xs:string"/>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="CostPrice" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Calculated Cost Price (Price/Discount tab - Purchase price)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GiftValue" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>GiftValue translates to the MSRP of an item that is a gift.  In other words, it's what we would charge for an item that we currently give away as a gift if we were to sell it.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="InventoryCost" type="xs:decimal" minOccurs="0"/>
			<xs:element name="ListPrice" type="xs:decimal" minOccurs="0"/>
			<xs:element name="MiscellaneusCharges" type="xs:decimal" minOccurs="0"/>
			<xs:element name="MSRP" type="xs:decimal" minOccurs="0"/>
			<xs:element name="Notes" type="xs:string" minOccurs="0"/>
			<xs:element name="OriginalSalePrice" type="xs:decimal" minOccurs="0"/>
			<xs:element name="TaxCode" type="xs:string" minOccurs="0"/>
			<xs:element name="MAP" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="AllowedMAPPrice" type="xs:decimal" minOccurs="0"/>
						<xs:element name="MAPPrice" type="xs:decimal" minOccurs="0"/>
					</xs:sequence>
					<xs:attribute name="catalagueMAPPriceFlag" type="xs:boolean"/>
					<xs:attribute name="webMAPPriceFlag" type="xs:boolean"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="SalePrice" minOccurs="0" maxOccurs="unbounded">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Price" type="xs:decimal" nillable="true" minOccurs="0"/>
						<xs:element name="StandardPrice" type="xs:decimal" nillable="true" minOccurs="0"/>
						<xs:element name="Validity" type="cdm:DurationType" minOccurs="0"/>
					</xs:sequence>
					<xs:attribute name="salesChannel">
						<xs:simpleType>
							<xs:restriction base="xs:string">
								<xs:enumeration value="Web"/>
								<xs:enumeration value="Retail"/>
								<xs:enumeration value="Catalogue"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
					<xs:attribute name="channelID" type="xs:string"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="WarrantyPriceRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>This is only for item messages where the item is a
                                warranty. What is expressed here is the minimum price (of another
                                item) that this warranty would be offered at.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MinPrice" type="xs:decimal"/>
						<xs:element name="MaxPrice" type="xs:decimal"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="catalogueCallForPriceFlag" type="xs:boolean"/>
		<xs:attribute name="displayListPriceFlag" type="xs:boolean"/>
		<xs:attribute name="excludeDiscountFlag" type="xs:boolean"/>
		<xs:attribute name="webCallForPriceFlag" type="xs:boolean"/>
		<xs:attribute name="webEmailForPriceFlag" type="xs:boolean"/>
	</xs:complexType>
	<xs:complexType name="PromotionType">
		<xs:sequence>
			<xs:element name="Description" type="xs:string" minOccurs="0"/>
			<xs:element name="FreeShippingThreshold" type="xs:string" minOccurs="0"/>
			<xs:element name="PromotionCode" type="xs:string" minOccurs="0"/>
			<xs:element name="Status" type="xs:string" minOccurs="0"/>
			<xs:element name="Validity" type="cdm:DurationType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="promotionID" type="xs:string"/>
		<xs:attribute name="siteID" type="xs:string"/>
		<xs:attribute name="channelID" type="xs:string"/>
	</xs:complexType>
	<xs:complexType name="RebateType">
		<xs:sequence>
			<xs:element name="Amount" type="xs:decimal" minOccurs="0"/>
			<xs:element name="Description" type="xs:string" minOccurs="0"/>
			<xs:element name="DocumentURL" type="xs:string" minOccurs="0"/>
			<xs:element name="Title" type="xs:string" minOccurs="0"/>
			<xs:element name="Validity" type="cdm:DurationType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="rebateID" type="xs:string"/>
		<xs:attribute name="siteID" type="xs:string"/>
		<xs:attribute name="channelID" type="xs:string"/>
	</xs:complexType>
	<xs:complexType name="SearchType">
		<xs:sequence>
			<xs:element name="SearchDescription" type="xs:string" minOccurs="0"/>
			<xs:element name="SearchKeywords" type="xs:string" minOccurs="0"/>
			<xs:element name="SearchTitle" type="xs:string" minOccurs="0"/>
			<xs:element name="SearchURL" type="xs:string" minOccurs="0"/>
			<xs:element name="SEOURL" type="xs:string" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="siteID" type="xs:string"/>
		<xs:attribute name="channelID" type="xs:string"/>
	</xs:complexType>
	<xs:complexType name="WarrantyType">
		<xs:sequence>
			<xs:element name="DocumentURL" type="xs:string" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="warrantyOK" type="xs:boolean"/>
	</xs:complexType>
	<xs:complexType name="ProductStatusType">
		<xs:sequence>
			<xs:element name="CatalogStatusFlag" type="xs:boolean" minOccurs="0"/>
			<xs:element name="ClearanceFlag" type="xs:boolean" minOccurs="0"/>
			<xs:element name="Dagger1Flag" type="xs:boolean" minOccurs="0"/>
			<xs:element name="Dagger2Flag" type="xs:boolean" minOccurs="0"/>
			<xs:element name="GCWebOnlyFlag" type="xs:boolean" minOccurs="0"/>
			<xs:element name="GibsonCustomFlag" type="xs:boolean" minOccurs="0"/>
			<xs:element name="HasGiftFlag" type="xs:boolean" minOccurs="0"/>
			<xs:element name="InStockNLAFlag" type="xs:boolean" minOccurs="0"/>
			<xs:element name="NewProductFlag" type="xs:boolean" minOccurs="0"/>
			<xs:element name="NoMarkInStockNLAFlag" type="xs:boolean" minOccurs="0"/>
			<xs:element name="OnSaleFlag" type="xs:boolean" minOccurs="0"/>
			<xs:element name="PreOrderFlag" type="xs:boolean" minOccurs="0"/>
			<xs:element name="RefurbishedFlag" type="xs:boolean" minOccurs="0"/>
			<xs:element name="TruckShipFlag" type="xs:boolean" minOccurs="0"/>
			<xs:element name="WebOKFlag" type="xs:boolean" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="VendorType">
		<xs:sequence>
			<xs:element name="Vendor" minOccurs="0" maxOccurs="unbounded">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Priority" type="xs:string" minOccurs="0"/>
						<xs:element name="VendorName" type="xs:string" minOccurs="0"/>
					</xs:sequence>
					<xs:attribute name="isPrimaryFlag" type="xs:boolean"/>
					<xs:attribute name="vendorID" type="xs:string"/>
					<xs:attribute name="APVendorID" type="xs:string"/>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="BrandType">
		<xs:sequence>
			<xs:element name="BrandLogoURL" type="xs:string" minOccurs="0"/>
			<xs:element name="BrandName" type="xs:string" minOccurs="0"/>
			<xs:element name="ManufacturerInfoURL" type="xs:string" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="brandID" type="xs:string"/>
	</xs:complexType>
	<xs:complexType name="PricingType">
		<xs:sequence>
			<xs:element name="MAP" type="xs:decimal" minOccurs="0"/>
			<xs:element name="MSRP" type="xs:decimal" minOccurs="0"/>
			<xs:element name="Cost" type="xs:decimal" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="ID" type="xs:string"/>
		<xs:attribute name="isPrimary" type="xs:boolean"/>
	</xs:complexType>
	<xs:complexType name="SKUType">
		<xs:sequence>
			<xs:element name="BaseConditionSKU" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>In the majority of cases, BaseConditionSKU contains a single-character condition code after the 1st chars, which is missing from BaseSKU.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BaseSKU" type="cdm:ItemIDType" minOccurs="0"/>
			<xs:element name="EnterpriseSKU" type="xs:string" minOccurs="0"/>
			<xs:element name="FullSKU" type="cdm:ItemIDType" minOccurs="0"/>
			<xs:element name="HowManyOfSKU" type="cdm:ItemIDType" minOccurs="0"/>
			<xs:element name="OriginalSKU" type="cdm:ItemIDType" minOccurs="0"/>
			<xs:element name="SubstituteItemID" type="xs:string" minOccurs="0"/>
			<xs:element name="SubstituteSKU" type="cdm:ItemIDType" minOccurs="0"/>
			<xs:element name="SiteSKU" minOccurs="0" maxOccurs="unbounded">
				<xs:complexType>
					<xs:attribute name="siteID" type="xs:string"/>
					<xs:attribute name="channelID" type="xs:string"/>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ShippingInformationType">
		<xs:sequence>
			<xs:element name="ImportTariffCode" type="xs:string" minOccurs="0"/>
			<xs:element name="HTCCode" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Harmonized Tariff Code</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NumberOfCartons" type="xs:string" minOccurs="0"/>
			<xs:element name="OversizeCode" type="xs:string" minOccurs="0"/>
			<xs:element name="RestrictShipmentCode" type="xs:string" minOccurs="0"/>
			<xs:element name="ShippingCost" type="xs:string" minOccurs="0"/>
			<xs:element name="ShippingMethod" type="xs:string" minOccurs="0"/>
			<xs:element name="ShippingWeight" type="xs:string" minOccurs="0"/>
			<xs:element name="SpecificShipper" type="xs:string" minOccurs="0"/>
			<xs:element name="DropShip" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="LeadTime" type="xs:string" minOccurs="0"/>
					</xs:sequence>
					<xs:attribute name="itemFlag" type="xs:boolean"/>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="alwaysDaggerFlag" type="xs:boolean"/>
		<xs:attribute name="directImportFlag" type="xs:boolean"/>
		<xs:attribute name="excludePostageChargesFlag" type="xs:boolean"/>
		<xs:attribute name="noFreeShippingFlag" type="xs:boolean"/>
		<xs:attribute name="promoFreeShippingFlag" type="xs:boolean"/>
		<xs:attribute name="neverDaggerFlag" type="xs:boolean"/>
		<xs:attribute name="noTruckFeeFlag" type="xs:boolean"/>
		<xs:attribute name="shipAloneFlag" type="xs:boolean"/>
		<xs:attribute name="shippableCartonFlag" type="xs:boolean"/>
		<xs:attribute name="specificShipperFlag" type="xs:boolean"/>
		<xs:attribute name="uspsFlag" type="xs:boolean"/>
		<xs:attribute name="warehouseDontShipFlag" type="xs:boolean"/>
	</xs:complexType>
	<xs:complexType name="ItemIDType">
		<xs:attribute name="itemID" type="xs:string"/>
	</xs:complexType>
	<xs:complexType name="ItemIdentifierType">
		<xs:attribute name="enterpriseSKU" type="xs:string" use="required"/>
	</xs:complexType>
	<xs:complexType name="ProductIdentifierType">
		<xs:annotation>
			<xs:documentation>This corresponds to the Product's Enterprise SKU</xs:documentation>
		</xs:annotation>
		<xs:attribute name="enterpriseId" type="xs:string" use="required"/>
	</xs:complexType>
	<xs:complexType name="SerialIdentifierType">
		<xs:attribute name="enterpriseSKU" type="xs:string" use="required"/>
	</xs:complexType>
	<xs:complexType name="BrandIdentifierType">
		<xs:attribute name="brandId" type="xs:string" use="required"/>
	</xs:complexType>
	<xs:complexType name="MediaIdentifierType">
		<xs:attribute name="MediaID" type="xs:string" use="required"/>
	</xs:complexType>
	<xs:complexType name="CollectionIdentifierType">
		<xs:attribute name="collectionID" type="xs:string" use="required"/>
	</xs:complexType>
	<xs:complexType name="CategoryType">
		<xs:sequence>
			<xs:element name="CategoryCode" type="xs:string" minOccurs="0"/>
			<xs:element name="CategoryTreeString" type="xs:string" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="channelID" type="xs:string"/>
	</xs:complexType>
	<xs:complexType name="KitIdentifierType">
		<xs:attribute name="kidID" type="xs:string" use="required"/>
	</xs:complexType>
	<xs:simpleType name="stringList">
		<xs:list itemType="xs:string"/>
	</xs:simpleType>
	<xs:complexType name="CopyType">
		<xs:sequence>
			<xs:element name="Headline" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation> The main display title that is used to identify the product
						in the catalog or on the web. </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BulletCopyFeatures" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation> Bulleted list of product features </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BulletCopySpecifications" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation> Technical specifications for the product in bullet form
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Copy100" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>100-character maximum description of the product
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Copy250" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>250-character maximum description of the
						product</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Copy500" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>500-character maximum description of the
						product.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CopyUnlimited" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Unlimited-length description of the
						product.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ValueAddCopy" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description of features that add value to the
						product.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CallToAction" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>A statement that summarizes why the shopper should buy this
						product.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="channelID" type="cdm:ChannelIdentifierType"/>
	</xs:complexType>
	<xs:simpleType name="ChannelIdentifierType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="0"/>
			<xs:enumeration value="51"/>
			<xs:enumeration value="52"/>
			<xs:enumeration value="5"/>
			<xs:enumeration value="53"/>
			<xs:enumeration value="54"/>
			<xs:enumeration value="55"/>
			<xs:enumeration value="56"/>
			<xs:enumeration value="57"/>
			<xs:enumeration value="7"/>
			<xs:enumeration value="58"/>
			<xs:enumeration value="59"/>
			<xs:enumeration value="60"/>
			<xs:enumeration value="61"/>
			<xs:enumeration value="62"/>
			<xs:enumeration value="63"/>
			<xs:enumeration value="1"/>
			<xs:enumeration value="64"/>
			<xs:enumeration value="65"/>
			<xs:enumeration value="66"/>
			<xs:enumeration value="67"/>
			<xs:enumeration value="68"/>
			<xs:enumeration value="8"/>
			<xs:enumeration value="69"/>
			<xs:enumeration value="70"/>
			<xs:enumeration value="71"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="divisionID">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MFI"/>
			<xs:enumeration value="GCR"/>
			<xs:enumeration value="GC.com"/>
			<xs:enumeration value="MAC"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="CountryOfOriginType">
		<xs:attribute name="countryCode" type="xs:string"/>
	</xs:complexType>
	<xs:complexType name="MediaType">
		<xs:sequence>
			<xs:element name="CreateDate" type="xs:string" minOccurs="0"/>
			<xs:element name="ColorSpace" type="xs:string" minOccurs="0"/>
			<xs:element name="Description" type="xs:string" minOccurs="0"/>
			<xs:element name="Height" type="xs:string" minOccurs="0"/>
			<xs:element name="LastModifiedDate" type="xs:string" minOccurs="0"/>
			<xs:element name="Resolution" type="xs:string" minOccurs="0"/>
			<xs:element name="URL" type="xs:string" minOccurs="0"/>
			<xs:element name="Width" type="xs:string" minOccurs="0"/>
			<xs:element name="mediaRank" type="xs:string" minOccurs="0"/>
			<xs:element name="mediaID" type="xs:string" minOccurs="0"/>
			<xs:element name="imediaType" type="xs:string" minOccurs="0"/>
			<xs:element name="action" type="xs:string" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="imageContext">
			<xs:annotation>
				<xs:documentation>Certain images are not associated with a particular style of a product, but serve as the default image on the product detail page, showing a composite of several styles of the product. This attribute provides
a mechanism way to associate a composite image with the base product.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="Brand"/>
					<xs:enumeration value="Category"/>
					<xs:enumeration value="Product"/>
					<xs:enumeration value="Style"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="mainImageFlag" type="xs:boolean"/>
		<xs:attribute name="channelID" type="xs:string"/>
	</xs:complexType>
	<xs:complexType name="BaseInfoType">
		<xs:sequence>
			<xs:element name="Buyer" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Employee for creating the item.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Category" minOccurs="0" maxOccurs="unbounded">
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="cdm:CategoryType">
							<xs:attribute name="Status" use="required">
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:enumeration value="Active"/>
										<xs:enumeration value="Inactive"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Taxonomies" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Taxonomy" minOccurs="0" maxOccurs="unbounded">
							<xs:complexType>
								<xs:sequence>
									<xs:element name="TaxonomyNode" minOccurs="0" maxOccurs="unbounded">
										<xs:complexType>
											<xs:attribute name="nodeID" type="xs:string"/>
											<xs:attribute name="status" type="xs:string"/>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="taxonomyID" type="xs:string"/>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="CountryOfOrigin" type="cdm:CountryOfOriginType" minOccurs="0"/>
			<xs:element name="Language" type="xs:string" minOccurs="0"/>
			<xs:element name="DamageDescription" type="xs:string" minOccurs="0"/>
			<xs:element name="ModelNumber" type="xs:string" minOccurs="0"/>
			<xs:element name="Notes" type="xs:string" minOccurs="0"/>
			<xs:element name="ProductName" type="xs:string" minOccurs="0"/>
			<xs:element name="ReleaseDate" type="xs:string" nillable="true" minOccurs="0"/>
			<xs:element name="PromoStatus" type="xs:string" minOccurs="0"/>
			<xs:element name="UPC" minOccurs="0" maxOccurs="unbounded">
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="xs:string">
							<xs:attribute name="isPrimaryFlag" type="xs:boolean"/>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Brand" type="cdm:BrandType" minOccurs="0"/>
			<xs:element name="SKU" type="cdm:SKUType" minOccurs="0"/>
			<xs:element name="Vendors" type="cdm:VendorType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="activeStatusCode" type="xs:string">
			<xs:annotation>
				<xs:documentation>This is a high level status that DAX calculates based off of mfItemStatus and another field called service.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="enterpriseProductID" type="xs:string">
			<xs:annotation>
				<xs:documentation>Products and Items are separate entities in Heiler (PIM), and they each need their own primary key. This attribute denotes the enterprise ProductID.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="productType" type="xs:string"/>
		<xs:attribute name="partNumber" type="xs:string"/>
		<xs:attribute name="isParentFlag" type="xs:boolean"/>
		<xs:attribute name="isUsedGearFlag" type="xs:boolean"/>
		<xs:attribute name="itemID" type="xs:string"/>
		<xs:attribute name="productStatus" type="xs:string"/>
		<xs:attribute name="returnableFlag" type="xs:boolean"/>
		<xs:attribute name="serialControlFlag" type="xs:boolean">
			<xs:annotation>
				<xs:documentation>This field is a code that indicates if the item is serialized or not.  

Valid values:

false = No, the item is not serialized (true for all items at this point in time) 

true = Yes, the item is serialized

</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="PartyIdAny">
		<xs:simpleContent>
			<xs:extension base="xs:string"/>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="PartyId">
		<xs:simpleContent>
			<xs:extension base="cdm:PartyIdAny"/>
		</xs:simpleContent>
	</xs:complexType>
	<!-- Document Identifier type and substitutions-->
	<xs:element name="AccountID" type="cdm:PartyId"/>
	<xs:element name="JobTitle" type="xs:string"/>
	<xs:element name="Responsibility" type="xs:string"/>
	<xs:element name="DepartmentName" type="xs:string"/>
	<xs:element name="CreationDateTime" type="xs:dateTime"/>
	<xs:element name="LastModificationDateTime" type="xs:string"/>
	<xs:element name="DateOfBirth" type="xs:string"/>
	<xs:element name="BarCodeID" type="xs:string" />
	<xs:element name="ParentName" type="xs:string"/>
	<xs:element name="ContractDay" type="xs:string"/>
	<xs:element name="EnterpriseCustomerID" type="cdm:PartyId"/>
	<xs:element name="Code" type="xs:string"/>
	<xs:element name="FederalTaxID" type="cdm:PartyId">
		<xs:annotation>
			<xs:documentation source="References the federal TAX ID">References the federal TAX ID</xs:documentation>
		</xs:annotation>
	</xs:element>
	<xs:element name="EMailAddress" type="cdm:EmailType"/>
	<xs:element name="PostalCode" type="xs:string">
		<xs:annotation>
			<xs:documentation>Address Paostal Code</xs:documentation>
		</xs:annotation>
	</xs:element>
	<xs:element name="Name" type="xs:string"/>
	<xs:element name="PrefferedName" type="xs:string"/>
	<xs:element name="Gender" type="xs:string"/>
	<xs:element name="ID">
		<xs:complexType>
			<xs:simpleContent>
				<xs:extension base="xs:string">
					<xs:attribute name="Source" type="xs:anySimpleType"/>
					<xs:attribute name="Type" type="xs:anySimpleType"/>
				</xs:extension>
			</xs:simpleContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="TelephoneNumber" type="cdm:PhoneType"/>
	<xs:element name="City" type="xs:string"/>
	<xs:element name="Type" type="xs:string"/>
	<xs:element name="State" type="xs:string"/>
	<xs:element name="CountryCode" type="xs:string"/>
	<xs:element name="ContactName" type="xs:string"/>
	<xs:element name="Latitude" type="xs:string"/>
	<xs:element name="Longitude" type="xs:string"/>
	<xs:element name="Location" type="cdm:LocationType"/>
	<xs:element name="CommunicationPreference" type="xs:string"/>
	<xs:element name="Address" type="cdm:AddressType"/>
	<xs:complexType name="EmailType">
		<xs:sequence>
			<xs:element name="Type" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Email" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LessonInstrumentType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" minOccurs="0"/>
			<xs:element name="Description" type="xs:string" minOccurs="0"/>
			<xs:element name="ID" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AddressType">
		<xs:sequence>
			<xs:element name="AddressType" type="xs:string" maxOccurs="unbounded"/>
			<xs:element name="AddressLine1" type="xs:string"/>
			<xs:element name="AddressLine2" type="xs:string"/>
			<xs:element name="AddressLine3" type="xs:string" minOccurs="0"/>
			<xs:element name="AddressLine4" type="xs:string" minOccurs="0"/>
			<xs:element ref="cdm:City"/>
			<xs:element ref="cdm:State"/>
			<xs:element ref="cdm:CountryCode"/>
			<xs:element ref="cdm:PostalCode"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TaxExemptionCode"/>
	<xs:complexType name="PhoneType">
		<xs:sequence>
			<xs:element name="Type" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Phone" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LocationType">
		<xs:sequence>
			<xs:element ref="cdm:ID"/>
			<xs:element ref="cdm:Name" minOccurs="0"/>
			<xs:element name="Coordinates" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element ref="cdm:Latitude"/>
						<xs:element ref="cdm:Longitude"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element ref="cdm:Type" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LessonPurchaseType">
		<xs:sequence>
			<xs:element name="CustomerType" type="xs:string" minOccurs="0"/>
			<!-- Values like - Regular, Guest, Prospect etc. -->
			<xs:element name="CustomerStatus" type="xs:string" minOccurs="0"/>
			<!-- Values like - Active, Cancel etc. -->
			<xs:element name="BillingStatus" minOccurs="0">
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="xs:string">
							<xs:attribute name="ID" type="xs:anySimpleType"/>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<!-- Values like - Auto Billing, Payment Pending, Payment Cancel etc. -->
			<!-- POS-808 Adding LessonCount and LastBookedDate -->
			<xs:element name="LessonStudio" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="LessonInstrument" type="cdm:LessonInstrumentType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="PrimaryStudio" type="xs:string"/>
			<xs:element name="LessonCount" type="xs:string" minOccurs="0"/>
			<xs:element name="LastBookedDate" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>

	
</xs:schema>
