<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:media="http://www.guitarcenter.com/schemas/public/CDM/Media" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cdm="http://www.guitarcenter.com/schemas/public/CDM" targetNamespace="http://www.guitarcenter.com/schemas/public/CDM/Media" elementFormDefault="qualified" attributeFormDefault="unqualified" version="3.45">
	<xs:annotation>
		<xs:documentation>
=============================================================================================
Revision 45:

1. Moved base type definitions to cdm-media.xsd.
2. Standalone schema now references base complex type difinitions in cdm-media.xsd via include statement.

=============================================================================================
Revision 44:

Changed Added "Interview" enumeration.

=============================================================================================

Revision 43:

Changed Derivative/Type nillable = "true".

=============================================================================================

Revision 42:

Added "Item Image", "Product Image", and "Manufacturer's Warranty" in the General Purpose
enumeration.

=============================================================================================

Revision 41:

Renamed Purpose in the Derivative node to Derivative purpose and restricted its enumeration
to Jumbo, Large, Regular, Thumbnail, and Tiny. Also removed those same values from the Pupose
enumeration under MediaAsset. Also renamed Purpose under MediaAsset to GeneralPurpose.

==============================================================================================

Revision 40:

Reverted "Product Image (Large)" to "Large", "Product Image (Regular)" to "Regular", 
"Product Image (Thumbnail)" to "Thumbnail", and "Product Image (Tiny)" to "Tiny" in the 
"Derivative" enumeration.

==============================================================================================

Revision 39:

1.	Remove "Language" element from within Derivative. There is a language element in Media
	Asset, and the language will always be the same for the master and all derivatives. If 
	there is an alternate language version, it will always be a totally separate master asset.
2.	The "Identifier" element within Deriviative should be renamed "URL", for consistency with 
	the "URL" element within Media Asset (for the master copy).
3.	We need to add a "Format" element within Media Asset (for the format of the master file). 
4.	I think "RunTime" should be renamed to "Duration" and moved from the Derivative element 
	to the Media Asset element. When the asset has a duration (videos, sound clips) it will 
	always be the same for the master and all service copies. If there is a version with a 
	different duration, it will be captured as a totally separate master asset.
5. 	add a "Purpose" element within Media Asset and duplicate the same value across all 
	derivatives when applicable

==============================================================================================

Revision 38:

Added a Type child to the MediaAsset element
Added "Category Reference Image" and "Brand Reference Image" to the DerivativePurpose enumeration

==============================================================================================
	
Revision 37:

Modified the DerivativeSubtype to be DerivativePurpose and added enumeration elements per Ron.

==============================================================================================	

Revision 36:

Added text file types to DerivativeSubtype element. Interim solution until we come up with 
something better.

==============================================================================================
	
Revision 35:

Added the element DerivativeSubtype to capture derivative image types: Large, Regular, Thumbnail,
and Tiny
	
==============================================================================================
Revision 34:

1. Moved the Derivatives grouping element into the MediaAsset type
2. Made the MediaAsset type repeating within the MediaBundle

==============================================================================================
Revision 33:

1. Renamed MediaType to MediaAssetType
2. Created a simple type named ChannelIdentifierType
3. Created a simple type named CategoryIdentifierType
4. Created a simple type named BrandIdentifierType
5. Created a simple type named ItemIdentifierType
6. Created a simple type named ProductIdentifierType
7. Deleted the Image, DerivedImage, GenericBinary and PDF types
8. Replaced the three types enumerated above with a Derivative type. This type is designed to 
   serve as a generic container for any media type that derives from the master media asset.
   It is loosely patterned after the DCMI standard.
9. Created a MediaBundle container element that is required to contain a MediaAsset element as
   well as an optional Derivatives container element.
10. Created a Derivatives container element which will contain a repeating Derivative type as
    described in 8, above.</xs:documentation>
	</xs:annotation>
	<xs:import namespace="http://www.guitarcenter.com/schemas/public/CDM" schemaLocation="GCCommonBaseCDM.xsd"/>
	<xs:complexType name="MediaAssetType">
					<xs:sequence>
					<xs:element name="Title" type="xs:string" minOccurs="0"/>
					<xs:element name="Description" type="xs:string" minOccurs="0"/>
					<xs:element name="Type" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Media asset type, based on the DCMI Type Vocabulary</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:string">
								<xs:enumeration value="Collection"/>
								<xs:enumeration value="Dataset"/>
								<xs:enumeration value="Event"/>
								<xs:enumeration value="Image"/>
								<xs:enumeration value="InteractiveResource"/>
								<xs:enumeration value="MovingImage"/>
								<xs:enumeration value="PhysicalObject"/>
								<xs:enumeration value="Service"/>
								<xs:enumeration value="Software"/>
								<xs:enumeration value="Sound"/>
								<xs:enumeration value="StillImage"/>
								<xs:enumeration value="Text"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:element>
					<xs:element name="Keywords" type="media:stringList" minOccurs="0"/>
					<xs:element name="Format" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>File type</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="URL" type="xs:string" minOccurs="0"/>
					<xs:element name="Language" type="xs:string" minOccurs="0"/>
					<xs:element name="Duration" type="xs:string" minOccurs="0"/>
					<xs:element name="DistributionChannels" minOccurs="0">
						<xs:complexType>
							<xs:sequence>
								<xs:element name="Channel" type="cdm:ChannelIdentifierType" maxOccurs="unbounded"/>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
					<xs:element name="AssociatedCategories" minOccurs="0">
						<xs:complexType>
							<xs:sequence>
								<xs:element name="Category" type="media:CategoryIdentifierType" maxOccurs="unbounded">
									<xs:annotation>
										<xs:documentation>Category ID that this relates to</xs:documentation>
									</xs:annotation>
								</xs:element>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
					<xs:element name="AssociatedBrands" minOccurs="0">
						<xs:complexType>
							<xs:sequence>
								<xs:element name="Brand" type="media:BrandIdentifierType" maxOccurs="unbounded"/>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
					<xs:element name="AssociatedItems" minOccurs="0">
						<xs:complexType>
							<xs:sequence>
								<xs:element name="Item" type="media:ItemIdentifierType" maxOccurs="unbounded"/>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
					<xs:element name="AssociatedProducts" minOccurs="0">
						<xs:complexType>
							<xs:sequence>
								<xs:element name="Product" type="media:ProductIdentifierType" maxOccurs="unbounded"/>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
					<xs:element name="GeneralPurpose" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Used for capturing the subtype of the master media asset (both for image size descriptors and file purpose descriptor). 
							Note that "Large," "Regular," "Thumbnail," and "Tiny" refer to image sizes.</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:string">
								<xs:enumeration value="Buying Guide"/>
								<xs:enumeration value="Comparison Chart"/>
								<xs:enumeration value="Event"/>
								<xs:enumeration value="Factory Tour"/>
								<xs:enumeration value="How-To"/>
								<xs:enumeration value="Product (Demonstration)"/>
								<xs:enumeration value="Product (Owner's Manual)"/>
								<xs:enumeration value="Product (Spec Sheet)"/>
								<xs:enumeration value="Rebate"/>
								<xs:enumeration value="Review"/>
								<xs:enumeration value="Category Reference Image"/>
								<xs:enumeration value="Brand Reference Image"/>
								<xs:enumeration value="Item Image"/>
								<xs:enumeration value="Product Image"/>
								<xs:enumeration value="Manufacturer's Warranty"/>
								<xs:enumeration value="Interview"/>
								<xs:enumeration value="Undefined"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:element>
					<xs:element name="Derivatives" minOccurs="0">
						<xs:complexType>
							<xs:sequence minOccurs="0">
								<xs:element ref="media:Derivative" minOccurs="0" maxOccurs="unbounded">
									<xs:annotation>
										<xs:documentation>Generic type designed to accommodate all types of media. Loosely based on DCMI.</xs:documentation>
									</xs:annotation>
								</xs:element>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
				<xs:attribute name="createdDate" type="xs:dateTime" use="required"/>
				<xs:attribute name="lastModifiedDate" type="xs:dateTime" use="required"/>
				<xs:attribute name="rank" type="xs:int"/>
				<xs:attribute name="startDate" type="xs:date"/>
				<xs:attribute name="endDate" type="xs:date"/>
				<xs:attribute name="action">
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:enumeration value="create"/>
							<xs:enumeration value="update"/>
							<xs:enumeration value="delete"/>
							<xs:enumeration value="unchanged"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:attribute>
				<xs:attribute name="MediaID" type="xs:string" use="required"/>
			
	</xs:complexType>
	<xs:complexType name="Derivative">
		<xs:sequence>
			<xs:element name="Type" nillable="true">
				<xs:annotation>
					<xs:documentation>Media asset type, based on the DCMI Type Vocabulary</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Collection"/>
						<xs:enumeration value="Dataset"/>
						<xs:enumeration value="Event"/>
						<xs:enumeration value="Image"/>
						<xs:enumeration value="InteractiveResource"/>
						<xs:enumeration value="MovingImage"/>
						<xs:enumeration value="PhysicalObject"/>
						<xs:enumeration value="Service"/>
						<xs:enumeration value="Software"/>
						<xs:enumeration value="Sound"/>
						<xs:enumeration value="StillImage"/>
						<xs:enumeration value="Text"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Format" type="xs:string">
				<xs:annotation>
					<xs:documentation>File type</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="URL" type="xs:string">
				<xs:annotation>
					<xs:documentation>URL for the file -- standard DCMI nomenclature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DerivativePurpose" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Used for capturing the subtype of the master media asset (both for image size descriptors and file purpose descriptor). 
						Note that "Jumbo", "Large," "Regular," "Thumbnail," and "Tiny" refer to image sizes.</xs:documentation>
				</xs:annotation>
				<!--				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Jumbo"/>
						<xs:enumeration value="Large"/>
						<xs:enumeration value="Regular"/>
						<xs:enumeration value="Thumbnail"/>
						<xs:enumeration value="Tiny"/>
						<xs:enumeration value="Header"/>
						<xs:enumeration value="BrandLogo"/>
						<xs:enumeration value="Small"/>
						<xs:enumeration value="VideoThumbImage"/>
						<xs:enumeration value="VideoPreviewImageSmall"/>
						<xs:enumeration value="VideoPreviewImageLarge"/>
						<xs:enumeration value="VideoPreviewImageHomepage"/>
						<xs:enumeration value="SmallVideo"/>
						<xs:enumeration value="LargeVideo"/>
						<xs:enumeration value="Copy"/>
					</xs:restriction>
				</xs:simpleType>
-->
			</xs:element>
			<xs:element name="PhysicalCharacteristics" minOccurs="0">
				<xs:complexType>
					<xs:sequence minOccurs="0">
						<xs:element name="Dimensions" minOccurs="0">
							<xs:complexType>
								<xs:sequence minOccurs="0">
									<xs:element name="Width" type="xs:string" minOccurs="0"/>
									<xs:element name="Height" type="xs:string" minOccurs="0"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="Resolution" type="xs:string" minOccurs="0"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="derivativeID" type="xs:string"/>
	</xs:complexType>
	<xs:element name="MediaBundle">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="media:MediaAsset" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:simpleType name="stringList">
		<xs:list itemType="xs:string"/>
	</xs:simpleType>
	<xs:element name="MediaAsset" type="media:MediaAssetType"/>
	<xs:element name="Derivative" type="media:Derivative"/>
	<xs:simpleType name="BrandIdentifierType">
		<xs:restriction base="xs:string"/>
	</xs:simpleType>
	<xs:simpleType name="CategoryIdentifierType">
		<xs:restriction base="xs:string"/>
	</xs:simpleType>
	<xs:simpleType name="ItemIdentifierType">
		<xs:restriction base="xs:string"/>
	</xs:simpleType>
	<xs:simpleType name="ProductIdentifierType">
		<xs:restriction base="xs:string"/>
	</xs:simpleType>
	<xs:complexType name="PDFType">
		<xs:complexContent>
			<xs:extension base="media:MediaAssetType">
				<xs:attribute name="documentType">
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:enumeration value="rebate"/>
							<xs:enumeration value="manual"/>
							<xs:enumeration value="buying guide"/>
							<xs:enumeration value="spec sheet"/>
							<xs:enumeration value="other"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:attribute>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="GenericBinaryType">
		<xs:complexContent>
			<xs:extension base="media:MediaAssetType">
				<xs:sequence>
					<xs:element name="FileType" type="xs:string"/>
					<xs:element name="Attribute" minOccurs="0" maxOccurs="unbounded">
						<xs:annotation>
							<xs:documentation>This is sort of a catch-all element designed to accommodate the fact that a media asset may have particular characteristics we may want to message such as run-time of file size or frame rate that would be too restrictive to define as individual elements.
</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:simpleContent>
								<xs:extension base="xs:string">
									<xs:attribute name="Name" type="xs:string"/>
								</xs:extension>
							</xs:simpleContent>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
</xs:schema>
