--
-- Add indices to tables where needed
--

-- Index external_id for data migration
drop index appointment_external_id_idx;
create index appointment_external_id_idx on appointment (external_id);

-- Indices to improve data migration performance
drop index stg_rejected_record_key_idx;
create index stg_rejected_record_key_idx on stg_rejected_record (key);
drop index stg_rej_record_table_name_idx;
create index stg_rej_record_table_name_idx on stg_rejected_record (table_name);
drop index stg_appts_appointmentid_idx;
create index stg_appts_appointmentid_idx on stg_appointments (appointmentid);
drop index stg_appts_userid_idx;
create index stg_appts_userid_idx on stg_appointments (userid);
drop index stg_etl_status_job_id_idx;
create index stg_etl_status_job_id_idx on stg_etl_status (job_id);