--
-- Provides a minimally usable instance of GC Studios Scheduler schema
--
-- At this point none of this is fixed, but it is expected that this file will
-- be used to populate the real systems at some point; use a different file
-- for DEV work.

-- Force "system update" person_id to be 1 and use it consistently in this SQL
-- instead of sequence values.
insert into person (person_id, version, updated, updated_by,
                    first_name, last_name)
  values(1, 0, systimestamp, 1, 'System', 'Update');
insert into site (site_id, version, updated, updated_by, name, url, external_id)
  values(1, 0, systimestamp, 1, 'GC Studios Scheduling',
         'https://scheduler.guitarcenter.com/scheduler', 'GCS');

-- all the following updates rely on the fact that 1 matches
-- the 'system' person record that was just inserted!

-- create roles for the system
insert into role (role_id, version, updated, updated_by, site_id, role_name)
  values (role_id_seq.nextval, 0, systimestamp, 1, 1,
          'Site Admin');
insert into role (role_id, version, updated, updated_by, site_id, role_name)
  values (role_id_seq.nextval, 0, systimestamp, 1, 1,
          'Studio Manager');
insert into role (role_id, version, updated, updated_by, site_id, role_name)
  values (role_id_seq.nextval, 0, systimestamp, 1, 1, 
          'Studio Lead');
insert into role (role_id, version, updated, updated_by, site_id, role_name)
  values (role_id_seq.nextval, 0, systimestamp, 1, 1,
          'Studio Associate');

-- create Rehearsal room type and sizes
insert into room_type (room_type_id, version, updated, updated_by, site_id,
                       room_type, can_split_room, enabled)
  values (room_type_id_seq.nextval, 0, systimestamp, 1, 1,
          'Rehearsal', 'Y', 'Y');
insert into room_size (room_size_id, version, updated, updated_by, site_id,
                       room_type_id, room_size_name)
  values(room_size_id_seq.nextval, 0, systimestamp, 1, 1,
         room_type_id_seq.currval, 'Large');
insert into room_size (room_size_id, version, updated, updated_by, site_id,
                       room_type_id, room_size_name)
  values(room_size_id_seq.nextval, 0, systimestamp, 1, 1,
         room_type_id_seq.currval, 'Medium');
insert into room_size (room_size_id, version, updated, updated_by, site_id,
                       room_type_id, room_size_name)
  values(room_size_id_seq.nextval, 0, systimestamp, 1, 1,
         room_type_id_seq.currval, 'Small');

-- create Lesson room type
insert into room_type (room_type_id, version, updated, updated_by, site_id,
                       room_type, can_split_room, enabled)
  values (room_type_id_seq.nextval, 0, systimestamp, 1, 1,
          'Lesson', 'N', 'Y');

-- create standardised room numbering
insert into room_number (room_number_id, version, updated, updated_by, site_id,
                         room_number)
  values (room_number_id_seq.nextval, 0, systimestamp, 1, 1,
          'Room 1');
insert into room_number (room_number_id, version, updated, updated_by, site_id,
                         room_number)
  values (room_number_id_seq.nextval, 0, systimestamp, 1, 1,
          'Room 2');
insert into room_number (room_number_id, version, updated, updated_by, site_id,
                         room_number)
  values (room_number_id_seq.nextval, 0, systimestamp, 1, 1,
          'Room 3');
insert into room_number (room_number_id, version, updated, updated_by, site_id,
                         room_number)
  values (room_number_id_seq.nextval, 0, systimestamp, 1, 1,
          'Room 4');
insert into room_number (room_number_id, version, updated, updated_by, site_id,
                         room_number)
  values (room_number_id_seq.nextval, 0, systimestamp, 1, 1,
          'Room 5');
insert into room_number (room_number_id, version, updated, updated_by, site_id,
                         room_number)
  values (room_number_id_seq.nextval, 0, systimestamp, 1, 1,
          'Room 6');
insert into room_number (room_number_id, version, updated, updated_by, site_id,
                         room_number)
  values (room_number_id_seq.nextval, 0, systimestamp, 1, 1,
          'Room 7');
insert into room_number (room_number_id, version, updated, updated_by, site_id,
                         room_number)
  values (room_number_id_seq.nextval, 0, systimestamp, 1, 1,
          'Room 8');
insert into room_number (room_number_id, version, updated, updated_by, site_id,
                         room_number)
  values (room_number_id_seq.nextval, 0, systimestamp, 1, 1,
          'Room 9');
insert into room_number (room_number_id, version, updated, updated_by, site_id,
                         room_number)
  values (room_number_id_seq.nextval, 0, systimestamp, 1, 1,
          'Room 10');
insert into room_number (room_number_id, version, updated, updated_by, site_id,
                         room_number)
  values (room_number_id_seq.nextval, 0, systimestamp, 1, 1,
          'Room 11');
insert into room_number (room_number_id, version, updated, updated_by, site_id,
                         room_number)
  values (room_number_id_seq.nextval, 0, systimestamp, 1, 1,
          'Room 12');

-- Add some Rehersal services and activities
--  THIS IS PRELIMINARY!
insert into service (service_id, version, updated, updated_by, site_id,
                     service_name, enabled, requires_instructor,
                     allow_band_name)
  values (service_id_seq.nextval, 0, systimestamp, 1, 1,
          'Rehearsal', 'Y', 'N', 'Y');
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, requires_instructor,
                     enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1,
          'Rehearsal', service_id_seq.currval, 1, NULL, 120, 'N', 'Y');
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, requires_instructor,
                     enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1,
          'Solo Rehearsal', service_id_seq.currval, 1, 1, 60, 'N', 'Y');
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, requires_instructor,
                     enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1,
          'Clinic', service_id_seq.currval, 0, NULL, 120, 'N', 'Y');

--lesson activities
-- Add some Lesson services and activities
--  THIS IS PRELIMINARY!
insert into service (service_id, version, updated, updated_by, site_id,
                     service_name, enabled, requires_instructor,
                     allow_band_name)
  values (service_id_seq.nextval, 0, systimestamp, 1, 1,
          'Lesson', 'Y', 'R', 'Y');
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, requires_instructor,
                     enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1,
          'Group Class', service_id_seq.currval, 1, NULL, 60, 'R', 'Y');
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, requires_instructor,
                     enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1,
          'Summer Camp', service_id_seq.currval, 4, NULL, 60, 'R', 'Y');
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, requires_instructor,
                     enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1,
          'Rockshow', service_id_seq.currval, 2, NULL, 60, 'R', 'Y');
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, requires_instructor,
                     enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1,
          'Open Office', service_id_seq.currval, 0, NULL, 60, 'R', 'Y');
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, requires_instructor,
                     enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1,
          'Guitar Lesson', service_id_seq.currval, 1, 1, 30, 'R', 'Y');
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, requires_instructor,
                     enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1,
          'Drum Lesson', service_id_seq.currval, 1, 1, 30, 'R', 'Y');
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, requires_instructor,
                     enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1,
          'Bass Lesson', service_id_seq.currval, 1, 1, 30, 'R', 'Y');
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, requires_instructor,
                     enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1,
          'Keys/Piano Lesson', service_id_seq.currval, 1, 1, 30, 'R', 'Y');
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, requires_instructor,
                     enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1,
          'Vocal Lesson', service_id_seq.currval, 1, 1, 30, 'R', 'Y');
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, requires_instructor,
                     enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1,
          'DJ Lesson', service_id_seq.currval, 1, 1, 30, 'R', 'Y');
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, requires_instructor,
                     enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1,
          'Horn/Brass Lesson', service_id_seq.currval, 1, 1, 30, 'R', 'Y');
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, requires_instructor,
                     enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1,
          'Logic Lesson', service_id_seq.currval, 1, 1, 30, 'R', 'Y');
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, requires_instructor,
                     enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1,
          'Protools Lesson', service_id_seq.currval, 1, 1, 30, 'R', 'Y');
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, requires_instructor,
                     enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1,
          'Sparring Partner', service_id_seq.currval, 1, 1, 30, 'R', 'Y');
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, requires_instructor,
                     enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1,
          'Strings Lesson', service_id_seq.currval, 1, 1, 30, 'R', 'Y');
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, requires_instructor,
                     enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1,
          'Woodwind Lesson', service_id_seq.currval, 1, 1, 30, 'R', 'Y');
          
-- Add some customer status records
insert into customer_status (customer_status_id, version, updated, updated_by,
                             site_id, external_id, status_name)
  values(customer_status_id_seq.nextval, 0, systimestamp, 1, 1, 'A',
         'Auto Billing Customer');
insert into customer_status (customer_status_id, version, updated, updated_by,
                             site_id, external_id, status_name)
  values(customer_status_id_seq.nextval, 0, systimestamp, 1, 1, 'R',
        'Rehearsal Customer');
insert into customer_status (customer_status_id, version, updated, updated_by,
                             site_id, external_id, status_name)
  values(customer_status_id_seq.nextval, 0, systimestamp, 1, 1, 'C', 'Cancel');
insert into customer_status (customer_status_id, version, updated, updated_by,
                             site_id, external_id, status_name)
  values(customer_status_id_seq.nextval, 0, systimestamp, 1, 1, 'G', 'Guest');
-- Note the empty (not null) value of external_id for Prospect
-- this will be the only reference value that needs to match as a blank
insert into customer_status (customer_status_id, version, updated, updated_by,
                             site_id, external_id, status_name)
  values(customer_status_id_seq.nextval, 0, systimestamp, 1, 1, '', 'Prospect');
insert into customer_status (customer_status_id, version, updated, updated_by,
                             site_id, external_id, status_name)
  values(customer_status_id_seq.nextval, 0, systimestamp, 1, 1, 'M',
         'Master Account');
insert into customer_status (customer_status_id, version, updated, updated_by,
                             site_id, external_id, status_name)
  values(customer_status_id_seq.nextval, 0, systimestamp, 1, 1, 'CP',
         'Cancel Pending');