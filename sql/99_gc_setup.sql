--
-- Prepare database for GC DEV and QA environments
--
-- Note: best to be executed AFTER an EDW employee import has happened.
--       NOT FOR PRODUCTION

-- Reset any existing employee using the testing authentication ids
update person
  set auth_id = external_id
 where auth_id in ('111111', '111112', '111113', '111114', '111115', '111116',
                   '111117', '111118', '111119', '222222', '333333', '444444',
                   '555555', '666666');

-- Override employees to allow for login using test AD accounts

-- <PERSON> - 199 Studio Associate
update person
  set auth_id = '111111'
 where person_id =
  (select person_id from employee where external_id = '071140');

-- <PERSON>-<PERSON> - 109 Studio Associate
update person
  set auth_id = '111112'
 where person_id =
  (select person_id from employee where external_id = '079331');

-- <PERSON> - 199 Studio Associate
update person
  set auth_id = '111113'
 where person_id =
  (select person_id from employee where external_id = '075467');

-- <PERSON> - 109 Studio Lead
update person
  set auth_id = '111114'
 where person_id =
  (select person_id from employee where external_id = '076932');

-- <PERSON> - 199 Studio Lead
update person
  set auth_id = '111115'
 where person_id =
  (select person_id from employee where external_id = '070016');

-- James Tagliere - 199 Studio Lead
update person
  set auth_id = '111116'
 where person_id =
  (select person_id from employee where external_id = '069735');

-- Anton Jemiolo - 199 Studio Lead
update person
  set auth_id = '111117'
 where person_id = (select person_id from employee where external_id = '066672');

-- Brian Palacios - 199 Studio Lead
update person
  set auth_id = '111118'
 where person_id =
  (select person_id from employee where external_id = '059793');

-- Carlos Salcedo - 199 Studio Lead
update person
  set auth_id = '111119'
 where person_id =
  (select person_id from employee where external_id = '022316');

-- Leo Soderman - 109 Studio Manager
update person
  set auth_id = '555555'
 where person_id =
  (select person_id from employee where external_id = '073683');

-- Jeremy Boilek - 199 Studio Manager
update person
  set auth_id = '444444'
 where person_id = (select person_id from employee where external_id = '032281');
insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person),
         (select site_id from site where external_id = 'GCS'),
         (select person_id from employee where external_id = '032281'),
         (select role_id from role where role_name = 'Studio Manager'), null);


-- These accounts are not typically part of the EDW update since they do not
-- have the correct company code. Add manually.

-- Rick Wallis - add as a Site Admin
insert into person (person_id, version, updated, updated_by,
                    first_name, last_name, email, phone, external_id, auth_id)
  values(person_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 'Rick', 'Wallis',
         '<EMAIL>', '************', '010369', '333333');
insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name='Site Admin'), null);

-- Don Rodrigues - add as a Site Admin
insert into person (person_id, version, updated, updated_by,
                    first_name, last_name, email, phone, external_id, auth_id)
  values(person_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 'Don', 'Rodrigues',
         '<EMAIL>', '************', '000021', '222222');
insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name='Site Admin'), null);

-- Frank Joseph - add as a Site Admin
insert into person (person_id, version, updated, updated_by,
                    first_name, last_name, email, phone, external_id, auth_id)
  values(person_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 'Frank', 'Joseph',
         '<EMAIL>', '************', '005487', '666666');
insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name='Site Admin'), null);