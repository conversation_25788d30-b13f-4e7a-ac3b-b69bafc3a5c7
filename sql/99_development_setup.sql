--
-- Add records to support testing in a DEV environment
--
DROP
  TABLE gcss_patches CASCADE CONSTRAINTS ;
  
create table gcss_patches (description varchar2(512 byte) not null,
                           applied timestamp with local time zone default systimestamp not null)
  logging;
comment on table gcss_patches is 'Record of patches applied to schema';
comment on column gcss_patches.Description is 'Description of patch applied';
comment on column gcss_patches.applied is 'Timestamp of patch application';

--location dummy
insert into location (location_id, version, updated, updated_by, site_id,location_name, address_1, address_2, city, state, zip,country, phone, fax)
values(location_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 1, 'Matthew''s test studio','123 Main St', 'Suite 1', 'Beverly Hills', 'CA', '90210', 'USA','************', '************');
	
-- Add some login accounts
insert into person (person_id, version, updated, updated_by,
                    first_name, last_name, email, phone)
  values(person_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 'Site', 'Admin',
	 '<EMAIL>', '************');
insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name='Site Admin'),
         location_id_seq.currval);
insert into person (person_id, version, updated, updated_by,
                    first_name, last_name, email, phone)
  values(person_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 'Studio', 'Manager',
	 '<EMAIL>', '************');
insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name='Studio Manager'),
         location_id_seq.currval);
insert into person (person_id, version, updated, updated_by,
                    first_name, last_name, email, phone)
  values(person_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 'Studio', 'Lead',
	 '<EMAIL>', '************');
insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name='Studio Lead'),
         location_id_seq.currval);
insert into person (person_id, version, updated, updated_by,
                    first_name, last_name, email, phone)
  values(person_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 'Studio', 'Associate',
	 '<EMAIL>', '************');
insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name='Studio Associate'),
         location_id_seq.currval);
         
      
         
         
-----------------------------------------FAKE DATE PROVIED BY OFFSHORE------------------------------------------         
-- Add test data to simulate ldap login function, 1001 is admin, 1002 is manager


update person set auth_id='1003' 
where person_id in
      (select person_id from person_role  
        where role_id in(select role_id from role where role_name='Studio Lead')
      );
update person set auth_id='1001' 
where person_id in
      (select person_id from person_role  
        where role_id in(select role_id from role where role_name='Site Admin')
      );
update person set auth_id='1002'
where person_id in
      (select person_id from person_role  
        where role_id in(select role_id from role where role_name='Studio Manager')
      );
update person set auth_id='1004' where person_id in
	  (select person_id from person_role
	  	where role_id in(select role_id from role where role_name='Studio Associate')
	  );
update location set external_id=1 where location_id=(select location_id from location where location_name = 'Matthew''s test studio');


--Add instructor dummy 
insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'Holly', 'Instructor1','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Matthew''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp, (select min(person_id) from person), 'Parker', 'Instructor2','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Matthew''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'Pelvic', 'Instructor3','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Matthew''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp, (select min(person_id) from person), 'Jody', 'Instructor4','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Matthew''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'April', 'Instructor5','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Matthew''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp, (select min(person_id) from person), 'Crystal', 'Instructor6','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Matthew''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'Alec', 'Instructor7','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Matthew''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'Shero', 'Instructor8','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Matthew''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');


--Customer dummy
insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'Claude', 'Customer1','<EMAIL>', '************');

insert into CUSTOMER_STATUS (CUSTOMER_STATUS_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,STATUS_NAME)
values (CUSTOMER_STATUS_ID_seq.nextval,0,sysdate,(select min(person_id) from person),1,null,'status');

insert into CUSTOMER (CUSTOMER_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,CUSTOMER_STATUS_ID,EXTERNAL_ID)
values (CUSTOMER_ID_seq.nextval,0,sysdate,1,(select min(person_id) from person),person_id_seq.currval,CUSTOMER_STATUS_ID_seq.currval,null);

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'Keith', 'Customer2','<EMAIL>', '************');

insert into CUSTOMER_STATUS (CUSTOMER_STATUS_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,STATUS_NAME)
values (CUSTOMER_STATUS_ID_seq.nextval,0,sysdate,(select min(person_id) from person),1,null,'status');

insert into CUSTOMER (CUSTOMER_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,CUSTOMER_STATUS_ID,EXTERNAL_ID)
values (CUSTOMER_ID_seq.nextval,0,sysdate,1,(select min(person_id) from person),person_id_seq.currval,CUSTOMER_STATUS_ID_seq.currval,null);

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'Jody', 'Customer3','<EMAIL>', '************');

insert into CUSTOMER_STATUS (CUSTOMER_STATUS_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,STATUS_NAME)
values (CUSTOMER_STATUS_ID_seq.nextval,0,sysdate,(select min(person_id) from person),1,null,'status');

insert into CUSTOMER (CUSTOMER_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,CUSTOMER_STATUS_ID,EXTERNAL_ID)
values (CUSTOMER_ID_seq.nextval,0,sysdate,1,(select min(person_id) from person),person_id_seq.currval,CUSTOMER_STATUS_ID_seq.currval,null);

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'Ritchie', 'Customer4','<EMAIL>', '************');

insert into CUSTOMER_STATUS (CUSTOMER_STATUS_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,STATUS_NAME)
values (CUSTOMER_STATUS_ID_seq.nextval,0,sysdate,(select min(person_id) from person),1,null,'status');

insert into CUSTOMER (CUSTOMER_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,CUSTOMER_STATUS_ID,EXTERNAL_ID)
values (CUSTOMER_ID_seq.nextval,0,sysdate,1,(select min(person_id) from person),person_id_seq.currval,CUSTOMER_STATUS_ID_seq.currval,null);

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'Hilton', 'Customer5','<EMAIL>', '************');

insert into CUSTOMER_STATUS (CUSTOMER_STATUS_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,STATUS_NAME)
values (CUSTOMER_STATUS_ID_seq.nextval,0,sysdate,(select min(person_id) from person),1,null,'status');

insert into CUSTOMER (CUSTOMER_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,CUSTOMER_STATUS_ID,EXTERNAL_ID)
values (CUSTOMER_ID_seq.nextval,0,sysdate,1,(select min(person_id) from person),person_id_seq.currval,CUSTOMER_STATUS_ID_seq.currval,null);

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'Joan', 'Customer6','<EMAIL>', '************');

insert into CUSTOMER_STATUS (CUSTOMER_STATUS_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,STATUS_NAME)
values (CUSTOMER_STATUS_ID_seq.nextval,0,sysdate,(select min(person_id) from person),1,null,'status');

insert into CUSTOMER (CUSTOMER_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,CUSTOMER_STATUS_ID,EXTERNAL_ID)
values (CUSTOMER_ID_seq.nextval,0,sysdate,1,(select min(person_id) from person),person_id_seq.currval,CUSTOMER_STATUS_ID_seq.currval,null);

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'Billy', 'Customer7','<EMAIL>', '************');

insert into CUSTOMER_STATUS (CUSTOMER_STATUS_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,EXTERNAL_ID,STATUS_NAME)
values (CUSTOMER_STATUS_ID_seq.nextval,0,sysdate,(select min(person_id) from person),1,null,'status');

insert into CUSTOMER (CUSTOMER_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,CUSTOMER_STATUS_ID,EXTERNAL_ID)
values (CUSTOMER_ID_seq.nextval,0,sysdate,1,(select min(person_id) from person),person_id_seq.currval,CUSTOMER_STATUS_ID_seq.currval,null);

--Add another location data
insert into location (location_id, version, updated, updated_by, site_id,location_name, address_1, address_2, city, state, zip,country, phone, fax)
values(location_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 1, 'Offshore''s test studio','123 Main St', 'Suite 1', 'Beverly Hills', 'CA', '90210', 'CHN','************', '************');

insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1,
         (select person_id from person where email = '<EMAIL>'),
         (select role_id from role where role_name='Site Admin'),
         location_id_seq.currval);

update location set external_id=2 where location_id=(select location_id from location where location_name = 'Offshore''s test studio');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'Offshore', 'Instructor1','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Offshore''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp, (select min(person_id) from person), 'Offshore', 'Instructor2','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Offshore''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'Offshore', 'Instructor3','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Offshore''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp, (select min(person_id) from person), 'Offshore', 'Instructor4','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Offshore''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'Offshore', 'Instructor5','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Offshore''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp, (select min(person_id) from person), 'Offshore', 'Instructor6','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Offshore''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'Offshore', 'Instructor7','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Offshore''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

--Add another location data
insert into location (location_id, version, updated, updated_by, site_id,location_name, address_1, address_2, city, state, zip,country, phone, fax)
values(location_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 1, 'Onsite''s test studio','123 Main St', 'Suite 1', 'Beverly Hills', 'CA', '90210', 'CHN','************', '************');

insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1,
         (select person_id from person where email = '<EMAIL>'),
         (select role_id from role where role_name='Site Admin'),
         location_id_seq.currval);

update location set external_id=3 where location_id=(select location_id from location where location_name = 'Onsite''s test studio');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'Onsite', 'Instructor1','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Onsite''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp, (select min(person_id) from person), 'Onsite', 'Instructor2','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Onsite''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'Onsite', 'Instructor3','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Onsite''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp, (select min(person_id) from person), 'Onsite', 'Instructor4','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Onsite''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'Onsite', 'Instructor5','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Onsite''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp, (select min(person_id) from person), 'Onsite', 'Instructor6','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Onsite''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'Onsite', 'Instructor7','<EMAIL>', '************');

insert into AVAILABILITY (AVAILABILITY_ID,VERSION,UPDATED,UPDATED_BY,SITE_ID,MONDAY_START_TIME,MONDAY_END_TIME,TUESDAY_START_TIME,TUESDAY_END_TIME,WEDNESDAY_START_TIME,WEDNESDAY_END_TIME,THURSDAY_START_TIME,THURSDAY_END_TIME,FRIDAY_START_TIME,FRIDAY_END_TIME,SATURDAY_START_TIME,SATURDAY_END_TIME,SUNDAY_START_TIME,SUNDAY_END_TIME,EXTERNAL_ID) 
values (availability_id_seq.nextval,0,systimestamp,(select min(person_id) from person),1,TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/1970 09.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),TO_TIMESTAMP_TZ('08/01/2099 21.00.00.000000000 +00:00','MM/DD/YYYY HH24.MI.SS.FF TZH:TZM'),null);

insert into INSTRUCTOR (INSTRUCTOR_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,STATUS,PERSON_ID,LOCATION_ID,AVAILABILITY_ID,EXTERNAL_ID,ENABLED)
values (INSTRUCTOR_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),'status',person_id_seq.currval,(select location_id from location where location_name = 'Onsite''s test studio'),AVAILABILITY_ID_seq.currval,null,'Y');

-- add instructor and activity relationship
insert into instructor_activities(activity_id,instructor_id) 
select activity_id,instructor_id from (select a.activity_id, i.instructor_id, rownum n from activity a, instructor i where a.requires_instructor in ('R','O')) t where mod(t.n,3) = 1;

-- add employees
insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'location 1', 'employee 1','<EMAIL>', '************');

insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID)
values (EMPLOYEE_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),person_id_seq.currval,'status',(select location_id from location where location_name = 'Matthew''s test studio'),null);

insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name='Studio Associate'),
         (select location_id from location where location_name = 'Matthew''s test studio'));

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'location 1', 'employee 2','<EMAIL>', '************');

insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID)
values (EMPLOYEE_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),person_id_seq.currval,'status',(select location_id from location where location_name = 'Matthew''s test studio'),null);

insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name='Studio Associate'),
         (select location_id from location where location_name = 'Matthew''s test studio'));

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'location 1', 'employee 3','<EMAIL>', '************');

insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID)
values (EMPLOYEE_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),person_id_seq.currval,'status',(select location_id from location where location_name = 'Matthew''s test studio'),null);

insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name='Studio Associate'),
         (select location_id from location where location_name = 'Matthew''s test studio'));

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'location 1', 'employee 4','<EMAIL>', '************');

insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID)
values (EMPLOYEE_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),person_id_seq.currval,'status',(select location_id from location where location_name = 'Matthew''s test studio'),null);

insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name='Studio Associate'),
         (select location_id from location where location_name = 'Matthew''s test studio'));

--- location 2

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'location 2', 'employee 1','<EMAIL>', '************');

insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID)
values (EMPLOYEE_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),person_id_seq.currval,'status',(select location_id from location where location_name = 'Offshore''s test studio'),null);

insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name='Studio Associate'),
         (select location_id from location where location_name = 'Offshore''s test studio'));

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'location 2', 'employee 2','<EMAIL>', '************');

insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID)
values (EMPLOYEE_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),person_id_seq.currval,'status',(select location_id from location where location_name = 'Offshore''s test studio'),null);

insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name='Studio Associate'),
         (select location_id from location where location_name = 'Offshore''s test studio'));

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'location 2', 'employee 3','<EMAIL>', '************');

insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID)
values (EMPLOYEE_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),person_id_seq.currval,'status',(select location_id from location where location_name = 'Offshore''s test studio'),null);

insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name='Studio Associate'),
         (select location_id from location where location_name = 'Offshore''s test studio'));

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'location 2', 'employee 4','<EMAIL>', '************');

insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID)
values (EMPLOYEE_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),person_id_seq.currval,'status',(select location_id from location where location_name = 'Offshore''s test studio'),null);

insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name='Studio Associate'),
         (select location_id from location where location_name = 'Offshore''s test studio'));
         
 -- location 3

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'location 3', 'employee 1','<EMAIL>', '************');

insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID)
values (EMPLOYEE_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),person_id_seq.currval,'status',(select location_id from location where location_name = 'Onsite''s test studio'),null);

insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name='Studio Associate'),
         (select location_id from location where location_name = 'Onsite''s test studio'));

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'location 3', 'employee 2','<EMAIL>', '************');

insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID)
values (EMPLOYEE_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),person_id_seq.currval,'status',(select location_id from location where location_name = 'Onsite''s test studio'),null);

insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name='Studio Associate'),
         (select location_id from location where location_name = 'Onsite''s test studio'));

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'location 3', 'employee 3','<EMAIL>', '************');

insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID)
values (EMPLOYEE_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),person_id_seq.currval,'status',(select location_id from location where location_name = 'Onsite''s test studio'),null);

insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name='Studio Associate'),
         (select location_id from location where location_name = 'Onsite''s test studio'));

insert into person (person_id, version, updated, updated_by,first_name, last_name, email, phone)
values(person_id_seq.nextval, 0, systimestamp,(select min(person_id) from person), 'location 3', 'employee 4','<EMAIL>', '************');

insert into EMPLOYEE (EMPLOYEE_ID,VERSION,UPDATED,SITE_ID,UPDATED_BY,PERSON_ID,STATUS,LOCATION_ID,EXTERNAL_ID)
values (EMPLOYEE_ID_seq.nextval,0,systimestamp,1,(select min(person_id) from person),person_id_seq.currval,'status',(select location_id from location where location_name = 'Onsite''s test studio'),null);

insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name='Studio Associate'),
         (select location_id from location where location_name = 'Onsite''s test studio'));