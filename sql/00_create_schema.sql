-- Initial setup of GC Studios Scheduler
--
-- Based on:- Generated by Oracle SQL Developer Data Modeler 3.3.0.747
--   at:        2013-07-26 19:27:56 PDT
--   site:      Oracle Database 11g
--   type:      Oracle Database 11g

drop SEQUENCE activity_id_seq;

DROP SEQUENCE appointment_id_seq;

DROP SEQUENCE appointment_series_id_seq;

DROP SEQUENCE availability_id_seq;

DROP SEQUENCE customer_id_seq;

DROP SEQUENCE customer_status_id_seq;

DROP SEQUENCE employee_id_seq;

DROP SEQUENCE instructor_id_seq;

DROP SEQUENCE instrument_id_seq;

DROP SEQUENCE location_id_seq;

DROP SEQUENCE location_profile_id_seq;

DROP SEQUENCE profile_activity_id_seq;

DROP SEQUENCE profile_service_id_seq;

DROP SEQUENCE person_id_seq;

DROP SEQUENCE person_role_id_seq;

DROP SEQUENCE role_id_seq;

DROP SEQUENCE room_id_seq;

DROP SEQUENCE room_number_id_seq;

DROP SEQUENCE room_size_id_seq;

DROP SEQUENCE room_template_id_seq;

DROP SEQUENCE room_type_id_seq;

DROP SEQUENCE service_id_seq;

DROP SEQUENCE site_id_seq;

DROP
  TABLE activity CASCADE CONSTRAINTS ;

DROP
  TABLE appointment CASCADE CONSTRAINTS ;

DROP
  TABLE appointment_customers CASCADE CONSTRAINTS ;

DROP
  TABLE appointment_series CASCADE CONSTRAINTS ;

DROP
  TABLE availability CASCADE CONSTRAINTS ;

DROP
  TABLE customer CASCADE CONSTRAINTS ;

DROP
  TABLE customer_appointment_series CASCADE CONSTRAINTS ;

DROP
  TABLE customer_status CASCADE CONSTRAINTS ;

DROP
  TABLE employee CASCADE CONSTRAINTS ;

DROP
  TABLE instructor CASCADE CONSTRAINTS ;
  
DROP
  TABLE instructor_activities CASCADE CONSTRAINTS ;

DROP
  TABLE instrument CASCADE CONSTRAINTS ;
  
DROP
  TABLE customer_instrument CASCADE CONSTRAINTS ;

DROP
  TABLE location CASCADE CONSTRAINTS ;

DROP
  TABLE location_profile CASCADE CONSTRAINTS ;

DROP
  TABLE person CASCADE CONSTRAINTS ;

DROP
  TABLE person_role CASCADE CONSTRAINTS ;
  
DROP
  TABLE profile_activity CASCADE CONSTRAINTS ;

DROP
  TABLE profile_service CASCADE CONSTRAINTS ;

DROP
  TABLE role CASCADE CONSTRAINTS ;

DROP
  TABLE room CASCADE CONSTRAINTS ;

DROP
  TABLE room_activities CASCADE CONSTRAINTS ;

DROP
  TABLE room_number CASCADE CONSTRAINTS ;

DROP
  TABLE room_services CASCADE CONSTRAINTS ;

DROP
  TABLE room_size CASCADE CONSTRAINTS ;

DROP
  TABLE room_template CASCADE CONSTRAINTS ;

DROP
  TABLE room_template_activities CASCADE CONSTRAINTS ;

DROP
  TABLE room_template_services CASCADE CONSTRAINTS ;

DROP
  TABLE room_type CASCADE CONSTRAINTS ;

DROP
  TABLE service CASCADE CONSTRAINTS ;

DROP
  TABLE site CASCADE CONSTRAINTS ;

CREATE SEQUENCE activity_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE appointment_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE appointment_series_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE availability_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE customer_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE customer_status_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE employee_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE instructor_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE instrument_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE location_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE location_profile_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE profile_activity_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE profile_service_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

-- MEmes: start the person_id sequence at 10; there have been problems where
-- different Oracle instances assign different values to person_id for "system
-- update" user even though the same SQL is being executed. Solve by making
-- first sequence value 10 and hard-coding system update id in Java and
-- 01_populate_reference_values.sql
CREATE SEQUENCE person_id_seq START WITH 10 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 10 CACHE 20 ;

CREATE SEQUENCE person_role_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE role_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE room_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE room_number_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE room_size_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE room_template_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE room_type_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE service_id_seq START WITH 0 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE SEQUENCE site_id_seq START WITH 1 INCREMENT BY 1 MAXVALUE
  9999999999999999999999999999 MINVALUE 0 CACHE 20 ;

CREATE
  TABLE activity
  (
    activity_id         NUMBER NOT NULL ,
    version             NUMBER NOT NULL ,
    updated             TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    activity_name       VARCHAR2 (256 CHAR) NOT NULL ,
    updated_by          NUMBER NOT NULL ,
    site_id             NUMBER NOT NULL ,
    service_id          NUMBER NOT NULL ,
    minimum_attendees   NUMBER NOT NULL ,
    maximum_attendees   NUMBER ,
    minimum_duration    NUMBER NOT NULL ,
    maximum_duration    NUMBER ,
    requires_instructor CHAR (1) ,
    enabled             CHAR (1) ,
    external_id         VARCHAR2 (256 CHAR)
  )
  LOGGING ;
COMMENT ON TABLE activity
IS
  'Represents activities that are known to the system' ;
  ALTER TABLE activity ADD CONSTRAINT activity_PK PRIMARY KEY
  (
    activity_id
  )
  ;

CREATE
  TABLE appointment
  (
    appointment_id        NUMBER NOT NULL ,
    version               NUMBER NOT NULL ,
    updated               TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    updated_by            NUMBER NOT NULL ,
    site_id               NUMBER NOT NULL ,
    external_id           VARCHAR2 (256 CHAR) ,
    appointment_series_id NUMBER NOT NULL ,
    activity_id           NUMBER NOT NULL ,
    profile_id            NUMBER NOT NULL ,
    instructor_id         NUMBER ,
    room_id               NUMBER NOT NULL ,
    start_time            TIMESTAMP WITH TIME ZONE NOT NULL ,
    end_time              TIMESTAMP WITH TIME ZONE NOT NULL ,
    create_time           TIMESTAMP WITH TIME ZONE DEFAULT systimestamp NOT NULL ,
    canceled              CHAR (1) ,
    band_name             VARCHAR2 (256 CHAR) ,
    note                  VARCHAR2 (256 CHAR)
  )
  LOGGING ;
COMMENT ON TABLE appointment
IS
  'Represents appointments in the scheduler' ;
  ALTER TABLE appointment ADD CONSTRAINT appointment_PK PRIMARY KEY
  (
    appointment_id
  )
  ;

CREATE
  TABLE appointment_customers
  (
    customer_id    NUMBER NOT NULL ,
    appointment_id NUMBER NOT NULL
  )
  LOGGING ;
COMMENT ON TABLE appointment_customers
IS
  'Mapping table to associate appointments with customers' ;
  ALTER TABLE appointment_customers ADD CONSTRAINT appointment_customers_PK
  PRIMARY KEY
  (
    customer_id, appointment_id
  )
  ;

CREATE
  TABLE appointment_series
  (
    appointment_series_id NUMBER NOT NULL ,
    version               NUMBER NOT NULL ,
    updated               TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    updated_by            NUMBER NOT NULL ,
    site_id               NUMBER NOT NULL ,
    profile_id            NUMBER NOT NULL ,
    external_id           VARCHAR2 (256 CHAR) ,
    is_recurring          CHAR (1) ,
    series_start_time     TIMESTAMP WITH TIME ZONE NOT NULL ,
    series_end_time       TIMESTAMP WITH TIME ZONE ,
    activity_id           NUMBER NOT NULL ,
    band_name             VARCHAR2 (256 CHAR) ,
    note                  VARCHAR2 (256 CHAR)
  )
  LOGGING ;
COMMENT ON TABLE appointment_series
IS
  'Maps appointments to a series of appointments' ;
  ALTER TABLE appointment_series ADD CONSTRAINT appointment_series_PK PRIMARY
  KEY
  (
    appointment_series_id
  )
  ;

CREATE
  TABLE availability
  (
    availability_id      NUMBER NOT NULL ,
    version              NUMBER NOT NULL ,
    updated              TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    updated_by           NUMBER NOT NULL ,
    site_id              NUMBER NOT NULL ,
    monday_start_time    TIMESTAMP WITH TIME ZONE ,
    monday_end_time      TIMESTAMP WITH TIME ZONE ,
    tuesday_start_time   TIMESTAMP WITH TIME ZONE ,
    tuesday_end_time     TIMESTAMP WITH TIME ZONE ,
    wednesday_start_time TIMESTAMP WITH TIME ZONE ,
    wednesday_end_time   TIMESTAMP WITH TIME ZONE ,
    thursday_start_time  TIMESTAMP WITH TIME ZONE ,
    thursday_end_time    TIMESTAMP WITH TIME ZONE ,
    friday_start_time    TIMESTAMP WITH TIME ZONE ,
    friday_end_time      TIMESTAMP WITH TIME ZONE ,
    saturday_start_time  TIMESTAMP WITH TIME ZONE ,
    saturday_end_time    TIMESTAMP WITH TIME ZONE ,
    sunday_start_time    TIMESTAMP WITH TIME ZONE ,
    sunday_end_time      TIMESTAMP WITH TIME ZONE ,
    external_id          VARCHAR2 (256 CHAR)
  )
  LOGGING ;
COMMENT ON TABLE availability
IS
  'Provides availability for related entities' ;
  ALTER TABLE availability ADD CONSTRAINT availability_PK PRIMARY KEY
  (
    availability_id
  )
  ;

CREATE
  TABLE customer
  (
    customer_id        NUMBER NOT NULL ,
    version            NUMBER NOT NULL ,
    updated            TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    site_id            NUMBER NOT NULL ,
    updated_by         NUMBER NOT NULL ,
    person_id          NUMBER NOT NULL ,
    customer_status_id NUMBER NOT NULL ,
    external_id        VARCHAR2 (256 CHAR) ,
    external_source    VARCHAR2 (256 CHAR),
    badge_number	   VARCHAR2 (256 CHAR)
  )
  LOGGING ;
COMMENT ON TABLE customer
IS
  'Represents a customer' ;
  ALTER TABLE customer ADD CONSTRAINT customer_PK PRIMARY KEY
  (
    customer_id
  )
  ;

CREATE
  TABLE customer_appointment_series
  (
    customer_id           NUMBER NOT NULL ,
    appointment_series_id NUMBER NOT NULL
  )
  LOGGING ;
COMMENT ON TABLE customer_appointment_series
IS
  'Maps a series of appointments to customers' ;
  ALTER TABLE customer_appointment_series ADD CONSTRAINT
  customer_appt_series_PK PRIMARY KEY
  (
    customer_id, appointment_series_id
  )
  ;

CREATE
  TABLE customer_status
  (
    customer_status_id NUMBER NOT NULL ,
    version            NUMBER NOT NULL ,
    updated            TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    updated_by         NUMBER NOT NULL ,
    site_id            NUMBER NOT NULL ,
    external_id        VARCHAR2 (256 CHAR) ,
    status_name        VARCHAR2 (256 CHAR) NOT NULL
  )
  LOGGING ;
COMMENT ON TABLE customer_status
IS
  'A customer has status in POS; this table provides a lookup between the stat code provided and a display value'
  ;
  ALTER TABLE customer_status ADD CONSTRAINT customer_status_PK PRIMARY KEY
  (
    customer_status_id
  )
  ;

CREATE
  TABLE employee
  (
    employee_id NUMBER NOT NULL ,
    version     NUMBER NOT NULL ,
    updated     TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    site_id     NUMBER NOT NULL ,
    updated_by  NUMBER NOT NULL ,
    person_id   NUMBER NOT NULL ,
    status      VARCHAR2 (256 CHAR) ,
    enterprise_status VARCHAR2 (256 CHAR) ,
    location_id NUMBER NOT NULL ,
    external_id VARCHAR2 (256 CHAR) ,
    external_source VARCHAR2 (256 CHAR)
  )
  LOGGING ;
COMMENT ON TABLE employee
IS
  'Represents an employee of a studio' ;
  ALTER TABLE employee ADD CONSTRAINT employee_PK PRIMARY KEY
  (
    employee_id
  )
  ;

CREATE
  TABLE instructor
  (
    instructor_id   NUMBER NOT NULL ,
    version         NUMBER NOT NULL ,
    updated         TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    site_id         NUMBER NOT NULL ,
    updated_by      NUMBER NOT NULL ,
    status          VARCHAR2 (256 CHAR) ,
    enabled         CHAR (1) ,
    person_id       NUMBER NOT NULL ,
    location_id     NUMBER NOT NULL ,
    availability_id NUMBER NOT NULL ,
    external_id     VARCHAR2 (256 CHAR) ,
    external_source VARCHAR2 (256 CHAR)
  )
  LOGGING ;
COMMENT ON TABLE instructor
IS
  'Represents an employee of a studio' ;
  ALTER TABLE instructor ADD CONSTRAINT instructor_PK PRIMARY KEY
  (
    instructor_id
  )
  ;
  
CREATE
  TABLE instructor_activities
  (
    instructor_id   NUMBER NOT NULL ,
    activity_id     NUMBER NOT NULL
  )
  LOGGING ;
COMMENT ON TABLE instructor_activities
IS
  'Represents an instructor has activities' ;
  ALTER TABLE instructor_activities ADD CONSTRAINT instructor_activities_PK PRIMARY KEY
  (
    instructor_id, activity_id
  )
  ;

CREATE
  TABLE instrument
  (
    instrument_id   NUMBER NOT NULL ,
    version         NUMBER NOT NULL ,
    updated         TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    external_id     VARCHAR2 (256 CHAR) ,
    instrument_name VARCHAR2 (256 CHAR) ,
    updated_by      NUMBER NOT NULL ,
    site_id         NUMBER NOT NULL
  )
  LOGGING ;
COMMENT ON TABLE instrument
IS
  'Provides a lookup of instruments for a site' ;
  ALTER TABLE instrument ADD CONSTRAINT instrument_PK PRIMARY KEY
  (
    instrument_id
  )
  ;

CREATE
  TABLE customer_instrument
  (
    customer_id           NUMBER NOT NULL ,
    instrument_id         NUMBER NOT NULL
  )
  LOGGING ;
COMMENT ON TABLE customer_instrument
IS
  'Maps multiple instruments per customer' ;
  ALTER TABLE customer_instrument ADD CONSTRAINT
  customer_instrument_PK PRIMARY KEY
  (
    customer_id, instrument_id
  )
  ;  

CREATE
  TABLE location
  (
    location_id   NUMBER NOT NULL ,
    external_id   VARCHAR2 (256 CHAR) ,
    external_source VARCHAR2 (256 CHAR) ,
    version       NUMBER NOT NULL ,
    updated       TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    site_id       NUMBER NOT NULL ,
    updated_by    NUMBER NOT NULL ,
    location_name VARCHAR2 (256 CHAR) NOT NULL ,
    profile_id    NUMBER ,
    address_1     VARCHAR2 (256 CHAR) NOT NULL ,
    address_2     VARCHAR2 (256 CHAR) ,
    city          VARCHAR2 (256 CHAR) NOT NULL ,
    state         VARCHAR2 (256 CHAR) NOT NULL ,
    zip           VARCHAR2 (256 CHAR) NOT NULL ,
    country       VARCHAR2 (256 CHAR) NOT NULL ,
    phone         VARCHAR2 (256 CHAR) ,
    fax           VARCHAR2 (256 CHAR)
  )
  LOGGING ;
COMMENT ON TABLE location
IS
  'Represents a Location, as supplied by EDW ' ;
  ALTER TABLE location ADD CONSTRAINT location_PK PRIMARY KEY
  (
    location_id
  )
  ;

CREATE
  TABLE location_profile
  (
    profile_id      NUMBER NOT NULL ,
    external_id     VARCHAR2 (256 CHAR) ,
    version         NUMBER NOT NULL ,
    updated         TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    updated_by      NUMBER NOT NULL ,
    site_id         NUMBER NOT NULL ,
    enabled         CHAR (1) ,
    tz              VARCHAR2 (256 CHAR) NOT NULL ,
    availability_id NUMBER NOT NULL
  )
  LOGGING ;
COMMENT ON TABLE location_profile
IS
  'Represents a Location Profile' ;
  COMMENT ON COLUMN location_profile.profile_id
IS
  'Unique identifier of a profile' ;
  COMMENT ON COLUMN location_profile.tz
IS
  'The time zone of the location; not needed for phase 1 but will be needed for integration with external calendars later'
  ;
  ALTER TABLE location_profile ADD CONSTRAINT location_profile_PK PRIMARY KEY
  (
    profile_id
  )
  ;

CREATE
  TABLE person
  (
    person_id   NUMBER NOT NULL ,
    version     NUMBER NOT NULL ,
    updated     TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    updated_by  NUMBER NOT NULL ,
    external_id VARCHAR2 (256 CHAR) ,
    first_name  VARCHAR2 (256 CHAR) ,
    last_name   VARCHAR2 (256 CHAR) ,
    email       VARCHAR2 (256 CHAR) ,
    phone       VARCHAR2 (256 CHAR) ,
    auth_id     VARCHAR2 (256 CHAR)
  )
  LOGGING ;
COMMENT ON TABLE person
IS
  'Represents an individual person in the system; the person may also be a customer, employee or instructor'
  ;
  COMMENT ON COLUMN person.person_id
IS
  'Unique identifier of a person in scheduler' ;
  COMMENT ON COLUMN person.version
IS
  'ORM version number' ;
  COMMENT ON COLUMN person.updated
IS
  'Timestamp of modification' ;
  COMMENT ON COLUMN person.auth_id
IS
  'Identifaction string in external authentication system' ;
  ALTER TABLE person ADD CONSTRAINT person_PK PRIMARY KEY
  (
    person_id
  )
  ;

CREATE
  TABLE person_role
  (
    person_role_id NUMBER NOT NULL ,
    version        NUMBER NOT NULL ,
    updated        TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    updated_by     NUMBER NOT NULL ,
    site_id        NUMBER NOT NULL ,
    person_id      NUMBER NOT NULL ,
    role_id        NUMBER NOT NULL ,
    location_id    NUMBER
  )
  LOGGING ;
COMMENT ON TABLE person_role
IS
  'Most role associations will be to a specific location, but Site Admin role is not restricted by location. Allow NULL in location_id for this reason'
  ;
  ALTER TABLE person_role ADD CONSTRAINT person_role_PK PRIMARY KEY
  (
    person_role_id
  )
  ;
  ALTER TABLE person_role ADD CONSTRAINT person_role_UN UNIQUE
  (
    person_id , role_id , location_id
  )
  ;

CREATE
  TABLE profile_activity
  (
  	profile_activity_id NUMBER NOT NULL ,
  	version             NUMBER NOT NULL ,
    updated             TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    updated_by          NUMBER NOT NULL ,
    site_id             NUMBER NOT NULL ,
    external_id         VARCHAR2 (256 CHAR) ,
  	profile_id          NUMBER NOT NULL ,
    activity_id         NUMBER NOT NULL ,
    enabled             CHAR (1)
  )
  LOGGING ;
COMMENT ON TABLE profile_activity
IS
  'Mapping of activities that are available at a profile' ;
  ALTER TABLE profile_activity ADD CONSTRAINT profile_activity_PK PRIMARY
  KEY
  (
    profile_activity_id
  )
  ;
  ALTER TABLE profile_activity ADD CONSTRAINT profile_activity_UN UNIQUE
  (
    profile_id , activity_id
  )
  ;

CREATE
  TABLE profile_service
  (
  	profile_service_id NUMBER NOT NULL ,
  	version            NUMBER NOT NULL ,
    updated            TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    updated_by         NUMBER NOT NULL ,
    site_id            NUMBER NOT NULL ,
    external_id        VARCHAR2 (256 CHAR) ,
  	profile_id         NUMBER NOT NULL ,
    service_id         NUMBER NOT NULL ,
    enabled            CHAR (1)
  )
  LOGGING ;
COMMENT ON TABLE profile_service
IS
  'Mapping of services that are available at a profile' ;
  ALTER TABLE profile_service ADD CONSTRAINT profile_service_PK PRIMARY KEY
  (
    profile_service_id
  )
  ;
  ALTER TABLE profile_service ADD CONSTRAINT profile_service_UN UNIQUE
  (
    profile_id , service_id
  )
  ;

CREATE
  TABLE role
  (
    role_id     NUMBER NOT NULL ,
    version     NUMBER NOT NULL ,
    updated     TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    updated_by  NUMBER NOT NULL ,
    site_id     NUMBER NOT NULL ,
    role_name   VARCHAR2 (256 CHAR) NOT NULL ,
    description VARCHAR2 (256 CHAR) ,
    external_id VARCHAR2 (256 CHAR)
  )
  LOGGING ;
COMMENT ON TABLE role
IS
  'These are the high-level roles that will be available in the system' ;
  ALTER TABLE role ADD CONSTRAINT role_PK PRIMARY KEY
  (
    role_id
  )
  ;

CREATE
  TABLE room
  (
    room_id           NUMBER NOT NULL ,
    version           NUMBER NOT NULL ,
    updated           TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    updated_by        NUMBER NOT NULL ,
    site_id           NUMBER NOT NULL ,
    room_type_id      NUMBER NOT NULL ,
    room_template_id  NUMBER ,
    room_number_id    NUMBER NOT NULL ,
    room_size_id      NUMBER ,
    is_split_room     CHAR (1) ,
    parent_id         NUMBER,
    profile_room_name VARCHAR2 (256 CHAR) ,
    enabled           CHAR (1) ,
    profile_id        NUMBER NOT NULL ,
    external_id       VARCHAR2 (256 CHAR)
  )
  LOGGING ;
COMMENT ON TABLE room
IS
  'Represents a physical space or room at a location' ;
  ALTER TABLE room ADD CONSTRAINT room_PK PRIMARY KEY
  (
    room_id
  )
  ;

CREATE
  TABLE room_activities
  (
    activity_id NUMBER NOT NULL ,
    room_id     NUMBER NOT NULL
  )
  LOGGING ;
COMMENT ON TABLE room_activities
IS
  'A mapping of activities available in a room' ;
  ALTER TABLE room_activities ADD CONSTRAINT room_activities_PK PRIMARY KEY
  (
    activity_id, room_id
  )
  ;

CREATE
  TABLE room_number
  (
    room_number_id NUMBER NOT NULL ,
    version        NUMBER NOT NULL ,
    updated        TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    updated_by     NUMBER NOT NULL ,
    site_id        NUMBER NOT NULL ,
    room_number    VARCHAR2 (256 CHAR) NOT NULL ,
    external_id    VARCHAR2 (256 CHAR)
  )
  LOGGING ;
COMMENT ON TABLE room_number
IS
  'Impose a standardised naming scheme on profile rooms' ;
  ALTER TABLE room_number ADD CONSTRAINT room_number_PK PRIMARY KEY
  (
    room_number_id
  )
  ;

CREATE
  TABLE room_services
  (
    service_id NUMBER NOT NULL ,
    room_id    NUMBER NOT NULL
  )
  LOGGING ;
COMMENT ON TABLE room_services
IS
  'A mapping of services available in a room' ;
  ALTER TABLE room_services ADD CONSTRAINT room_services_PK PRIMARY KEY
  (
    service_id, room_id
  )
  ;

CREATE
  TABLE room_size
  (
    room_size_id   NUMBER NOT NULL ,
    version        NUMBER NOT NULL ,
    updated        TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    updated_by     NUMBER NOT NULL ,
    site_id        NUMBER NOT NULL ,
    room_type_id   NUMBER NOT NULL ,
    room_size_name VARCHAR2 (256 CHAR) NOT NULL ,
    external_id    VARCHAR2 (256 CHAR)
  )
  LOGGING ;
COMMENT ON TABLE room_size
IS
  'Implement a standard set of room sizes for profiles' ;
  ALTER TABLE room_size ADD CONSTRAINT room_size_PK PRIMARY KEY
  (
    room_size_id
  )
  ;

CREATE
  TABLE room_template
  (
    room_template_id   NUMBER NOT NULL ,
    version            NUMBER NOT NULL ,
    updated            TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    updated_by         NUMBER NOT NULL ,
    site_id            NUMBER NOT NULL ,
    room_template_name VARCHAR2 (256 CHAR) NOT NULL ,
    room_type_id       NUMBER NOT NULL ,
    is_split_room      CHAR (1) ,
    enabled            CHAR (1) ,
    room_size_id       NUMBER ,
    external_id        VARCHAR2 (256 CHAR)
  )
  LOGGING ;
COMMENT ON TABLE room_template
IS
  'Represents a template created in centralised management that can be setup and re-used by profiles'
  ;
  ALTER TABLE room_template ADD CONSTRAINT room_template_PK PRIMARY KEY
  (
    room_template_id
  )
  ;

CREATE
  TABLE room_template_activities
  (
    activity_id      NUMBER NOT NULL ,
    room_template_id NUMBER NOT NULL
  )
  LOGGING ;
COMMENT ON TABLE room_template_activities
IS
  'A mapping of activities available in a room template' ;
  ALTER TABLE room_template_activities ADD CONSTRAINT rm_template_activities_PK
  PRIMARY KEY
  (
    activity_id, room_template_id
  )
  ;

CREATE
  TABLE room_template_services
  (
    service_id       NUMBER NOT NULL ,
    room_template_id NUMBER NOT NULL
  )
  LOGGING ;
COMMENT ON TABLE room_template_services
IS
  'A mapping of services available in a room template' ;
  ALTER TABLE room_template_services ADD CONSTRAINT room_template_services_PK
  PRIMARY KEY
  (
    service_id, room_template_id
  )
  ;

CREATE
  TABLE room_type
  (
    room_type_id   NUMBER NOT NULL ,
    version        NUMBER NOT NULL ,
    updated        TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    room_type      VARCHAR2 (256 CHAR) NOT NULL ,
    updated_by     NUMBER NOT NULL ,
    site_id        NUMBER NOT NULL ,
    can_split_room CHAR (1) NOT NULL ,
    enabled        CHAR (1) ,
    external_id    VARCHAR2 (256 CHAR)
  )
  LOGGING ;
COMMENT ON TABLE room_type
IS
  'Impose a standard set of rooms for profiles' ;
  ALTER TABLE room_type ADD CONSTRAINT room_type_PK PRIMARY KEY
  (
    room_type_id
  )
  ;

CREATE
  TABLE service
  (
    service_id          NUMBER NOT NULL ,
    version             NUMBER NOT NULL ,
    updated             TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    service_name        VARCHAR2 (256 CHAR) NOT NULL ,
    updated_by          NUMBER NOT NULL ,
    site_id             NUMBER NOT NULL ,
    enabled             CHAR (1) ,
    requires_instructor CHAR (1) ,
    allow_band_name     CHAR (1) ,
    external_id         VARCHAR2 (256 CHAR)
  )
  LOGGING ;
COMMENT ON TABLE service
IS
  'Represents the services known to the system' ;
  ALTER TABLE service ADD CONSTRAINT service_PK PRIMARY KEY
  (
    service_id
  )
  ;

CREATE
  TABLE site
  (
    site_id     NUMBER NOT NULL ,
    external_id VARCHAR2 (256 CHAR) ,
    url         VARCHAR2 (256 CHAR) ,
    version     NUMBER NOT NULL ,
    updated     TIMESTAMP WITH LOCAL TIME ZONE NOT NULL ,
    updated_by  NUMBER NOT NULL ,
    name        VARCHAR2 (256 CHAR)
  )
  LOGGING ;
COMMENT ON TABLE site
IS
  'Scheduler will be multi-tenant capable, so all entities need to be associated with a site via a site_id FK.'
  ;
  COMMENT ON COLUMN site.site_id
IS
  'Unique identifier for site' ;
  COMMENT ON COLUMN site.external_id
IS
  'Identifier of site used by external system, may be null' ;
  COMMENT ON COLUMN site.url
IS
  'The URL associated with this site' ;
  COMMENT ON COLUMN site.version
IS
  'ORM version number' ;
  COMMENT ON COLUMN site.updated
IS
  'Timestamp of last update' ;
  ALTER TABLE site ADD CONSTRAINT site_PK PRIMARY KEY
  (
    site_id
  )
  ;

ALTER TABLE activity ADD CONSTRAINT activity_service_FK FOREIGN KEY (
service_id ) REFERENCES service ( service_id ) NOT DEFERRABLE ;

ALTER TABLE activity ADD CONSTRAINT activity_site_FK FOREIGN KEY ( site_id )
REFERENCES site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE activity ADD CONSTRAINT activity_updated_by_FK FOREIGN KEY (
updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE appointment ADD CONSTRAINT appointment_activity_FK FOREIGN KEY (
activity_id ) REFERENCES activity ( activity_id ) NOT DEFERRABLE ;

ALTER TABLE appointment ADD CONSTRAINT appointment_instructor_FK FOREIGN KEY (
instructor_id ) REFERENCES instructor ( instructor_id ) NOT DEFERRABLE ;

ALTER TABLE appointment ADD CONSTRAINT appointment_room_FK FOREIGN KEY (
room_id ) REFERENCES room ( room_id ) NOT DEFERRABLE ;

ALTER TABLE appointment_series ADD CONSTRAINT appointment_series_activity_FK
FOREIGN KEY ( activity_id ) REFERENCES activity ( activity_id ) NOT DEFERRABLE
;

ALTER TABLE appointment_series ADD CONSTRAINT appointment_series_site_FK
FOREIGN KEY ( site_id ) REFERENCES site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE appointment ADD CONSTRAINT appointment_site_FK FOREIGN KEY (
site_id ) REFERENCES site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE appointment ADD CONSTRAINT appointment_updated_by_FK FOREIGN KEY (
updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE appointment ADD CONSTRAINT appt_appointment_series_FK FOREIGN KEY (
appointment_series_id ) REFERENCES appointment_series ( appointment_series_id )
NOT DEFERRABLE ;

ALTER TABLE appointment_customers ADD CONSTRAINT appt_customers_appt_FK FOREIGN
KEY ( appointment_id ) REFERENCES appointment ( appointment_id ) NOT DEFERRABLE
;

ALTER TABLE appointment_customers ADD CONSTRAINT appt_customers_customer_FK
FOREIGN KEY ( customer_id ) REFERENCES customer ( customer_id ) NOT DEFERRABLE
;

ALTER TABLE appointment ADD CONSTRAINT appt_location_profile_FK FOREIGN KEY (
profile_id ) REFERENCES location_profile ( profile_id ) NOT DEFERRABLE ;

ALTER TABLE appointment_series ADD CONSTRAINT appt_series_loc_profile_FK
FOREIGN KEY ( profile_id ) REFERENCES location_profile ( profile_id ) NOT
DEFERRABLE ;

ALTER TABLE appointment_series ADD CONSTRAINT appt_series_updated_by_FK FOREIGN
KEY ( updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE availability ADD CONSTRAINT availability_site_FK FOREIGN KEY (
site_id ) REFERENCES site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE availability ADD CONSTRAINT availability_updated_by_FK FOREIGN KEY
( updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE customer_appointment_series ADD CONSTRAINT
customer_appt_appt_series_FK FOREIGN KEY ( appointment_series_id ) REFERENCES
appointment_series ( appointment_series_id ) NOT DEFERRABLE ;

ALTER TABLE customer_appointment_series ADD CONSTRAINT
customer_appt_customer_FK FOREIGN KEY ( customer_id ) REFERENCES customer (
customer_id ) NOT DEFERRABLE ;

ALTER TABLE customer ADD CONSTRAINT customer_customer_status_FK FOREIGN KEY (
customer_status_id ) REFERENCES customer_status ( customer_status_id ) NOT
DEFERRABLE ;

ALTER TABLE customer ADD CONSTRAINT customer_person_FK FOREIGN KEY ( person_id
) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE customer ADD CONSTRAINT customer_site_FK FOREIGN KEY ( site_id )
REFERENCES site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE customer ADD CONSTRAINT customer_updated_by_FK FOREIGN KEY (
updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE customer_status ADD CONSTRAINT customer_status_site_FK FOREIGN KEY
( site_id ) REFERENCES site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE customer_status ADD CONSTRAINT customer_status_updated_by_FK
FOREIGN KEY ( updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE employee ADD CONSTRAINT employee_location_FK FOREIGN KEY (
location_id ) REFERENCES location ( location_id ) NOT DEFERRABLE ;

ALTER TABLE employee ADD CONSTRAINT employee_person_FK FOREIGN KEY ( person_id
) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE employee ADD CONSTRAINT employee_updated_by_FK FOREIGN KEY (
updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE employee ADD CONSTRAINT employee_site_FK FOREIGN KEY ( site_id )
REFERENCES site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE instructor ADD CONSTRAINT instructor_availability_FK FOREIGN KEY (
availability_id ) REFERENCES availability ( availability_id ) NOT DEFERRABLE ;

ALTER TABLE instructor ADD CONSTRAINT instructor_location_FK FOREIGN KEY (
location_id ) REFERENCES location ( location_id ) NOT DEFERRABLE ;

ALTER TABLE instructor ADD CONSTRAINT instructor_person_FK FOREIGN KEY (
person_id ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE instructor ADD CONSTRAINT instructor_updated_by_FK FOREIGN KEY (
updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE instructor ADD CONSTRAINT instructor_site_FK FOREIGN KEY ( site_id )
REFERENCES site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE instructor_activities ADD CONSTRAINT instructor_instructor_FK FOREIGN KEY ( instructor_id )
REFERENCES instructor ( instructor_id ) NOT DEFERRABLE ;

ALTER TABLE instructor_activities ADD CONSTRAINT instructor_activity_FK FOREIGN KEY ( activity_id )
REFERENCES activity ( activity_id ) NOT DEFERRABLE ;

ALTER TABLE instrument ADD CONSTRAINT instrument_site_FK FOREIGN KEY ( site_id
) REFERENCES site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE instrument ADD CONSTRAINT instrument_updated_by_FK FOREIGN KEY (
updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE customer_instrument ADD CONSTRAINT
customer_inst_inst_FK FOREIGN KEY ( instrument_id ) REFERENCES
instrument ( instrument_id ) NOT DEFERRABLE ;

ALTER TABLE customer_instrument ADD CONSTRAINT
customer_inst_cus_FK FOREIGN KEY ( customer_id ) REFERENCES customer (
customer_id ) NOT DEFERRABLE ;

ALTER TABLE location_profile ADD CONSTRAINT loc_profile_availability_FK FOREIGN
KEY ( availability_id ) REFERENCES availability ( availability_id ) NOT
DEFERRABLE ;

ALTER TABLE location_profile ADD CONSTRAINT loc_profile_updated_by_FK FOREIGN
KEY ( updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE location_profile ADD CONSTRAINT location_profile_site_FK FOREIGN
KEY ( site_id ) REFERENCES site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE location ADD CONSTRAINT location_site_FK FOREIGN KEY ( site_id )
REFERENCES site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE location ADD CONSTRAINT location_updated_by_FK FOREIGN KEY (
updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE location ADD CONSTRAINT location_loc_profile_FK
FOREIGN KEY ( profile_id ) REFERENCES location_profile ( profile_id ) NOT
DEFERRABLE ;

ALTER TABLE person_role ADD CONSTRAINT person_role_location_FK FOREIGN KEY (
location_id ) REFERENCES location ( location_id ) NOT DEFERRABLE ;

ALTER TABLE person_role ADD CONSTRAINT person_role_person_FK FOREIGN KEY (
person_id ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE person_role ADD CONSTRAINT person_role_role_FK FOREIGN KEY (
role_id ) REFERENCES role ( role_id ) NOT DEFERRABLE ;

ALTER TABLE person_role ADD CONSTRAINT person_role_site_FK FOREIGN KEY (
site_id ) REFERENCES site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE person_role ADD CONSTRAINT person_role_updated_by_FK FOREIGN KEY (
updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE person ADD CONSTRAINT person_updated_by_FK FOREIGN KEY ( updated_by
) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE profile_activity ADD CONSTRAINT profile_activiy_activity_FK
FOREIGN KEY ( activity_id ) REFERENCES activity ( activity_id ) NOT DEFERRABLE
;

ALTER TABLE profile_activity ADD CONSTRAINT profile_activity_profile_FK
FOREIGN KEY ( profile_id ) REFERENCES location_profile ( profile_id ) NOT
DEFERRABLE ;

ALTER TABLE profile_activity ADD CONSTRAINT profile_activity_site_FK FOREIGN KEY (
site_id ) REFERENCES site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE profile_activity ADD CONSTRAINT profile_activity_updated_by_FK FOREIGN KEY (
updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE profile_service ADD CONSTRAINT profile_service_profile_FK FOREIGN
KEY ( profile_id ) REFERENCES location_profile ( profile_id ) NOT DEFERRABLE ;

ALTER TABLE profile_service ADD CONSTRAINT profile_service_service_FK FOREIGN
KEY ( service_id ) REFERENCES service ( service_id ) NOT DEFERRABLE ;

ALTER TABLE profile_service ADD CONSTRAINT profile_service_site_FK FOREIGN KEY (
site_id ) REFERENCES site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE profile_service ADD CONSTRAINT profile_service_updated_by_FK FOREIGN KEY (
updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE room_template_activities ADD CONSTRAINT rm_templ_acts_activity_FK
FOREIGN KEY ( activity_id ) REFERENCES activity ( activity_id ) NOT DEFERRABLE
;

ALTER TABLE room_template_activities ADD CONSTRAINT rm_templ_acts_rm_templ_FK
FOREIGN KEY ( room_template_id ) REFERENCES room_template ( room_template_id )
NOT DEFERRABLE ;

ALTER TABLE room_template_services ADD CONSTRAINT rm_templ_services_rm_templ_FK
FOREIGN KEY ( room_template_id ) REFERENCES room_template ( room_template_id )
NOT DEFERRABLE ;

ALTER TABLE room_template_services ADD CONSTRAINT rm_templ_services_service_FK
FOREIGN KEY ( service_id ) REFERENCES service ( service_id ) NOT DEFERRABLE ;

ALTER TABLE role ADD CONSTRAINT role_site_FK FOREIGN KEY ( site_id ) REFERENCES
site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE role ADD CONSTRAINT role_updated_by_FK FOREIGN KEY ( updated_by )
REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE room_activities ADD CONSTRAINT room_activities_activity_FK FOREIGN
KEY ( activity_id ) REFERENCES activity ( activity_id ) NOT DEFERRABLE ;

ALTER TABLE room_activities ADD CONSTRAINT room_activities_room_FK FOREIGN KEY
( room_id ) REFERENCES room ( room_id ) NOT DEFERRABLE ;

ALTER TABLE room_number ADD CONSTRAINT room_number_site_FK FOREIGN KEY (
site_id ) REFERENCES site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE room_number ADD CONSTRAINT room_number_updated_by_FK FOREIGN KEY (
updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE room ADD CONSTRAINT room_room_number_FK FOREIGN KEY (
room_number_id ) REFERENCES room_number ( room_number_id ) NOT DEFERRABLE ;

ALTER TABLE room ADD CONSTRAINT room_room_size_FK FOREIGN KEY ( room_size_id )
REFERENCES room_size ( room_size_id ) NOT DEFERRABLE ;

ALTER TABLE room ADD CONSTRAINT room_room_template_FK FOREIGN KEY (
room_template_id ) REFERENCES room_template ( room_template_id ) NOT DEFERRABLE
;

ALTER TABLE room ADD CONSTRAINT room_room_type_FK FOREIGN KEY ( room_type_id )
REFERENCES room_type ( room_type_id ) NOT DEFERRABLE ;

ALTER TABLE room ADD CONSTRAINT room_room_parent_FK FOREIGN KEY (
parent_id ) REFERENCES room ( room_id ) NOT DEFERRABLE ;

ALTER TABLE room_services ADD CONSTRAINT room_services_room_FK FOREIGN KEY (
room_id ) REFERENCES room ( room_id ) NOT DEFERRABLE ;

ALTER TABLE room_services ADD CONSTRAINT room_services_service_FK FOREIGN KEY (
service_id ) REFERENCES service ( service_id ) NOT DEFERRABLE ;

ALTER TABLE room ADD CONSTRAINT room_site_FK FOREIGN KEY ( site_id ) REFERENCES
site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE room ADD CONSTRAINT room_location_profile_FK FOREIGN KEY (
profile_id ) REFERENCES location_profile ( profile_id ) NOT DEFERRABLE ;

ALTER TABLE room_size ADD CONSTRAINT room_size_room_type_FK FOREIGN KEY (
room_type_id ) REFERENCES room_type ( room_type_id ) NOT DEFERRABLE ;

ALTER TABLE room_size ADD CONSTRAINT room_size_site_FK FOREIGN KEY ( site_id )
REFERENCES site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE room_size ADD CONSTRAINT room_size_updated_by_FK FOREIGN KEY (
updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE room_template ADD CONSTRAINT room_template_room_size_FK FOREIGN KEY
( room_size_id ) REFERENCES room_size ( room_size_id ) NOT DEFERRABLE ;

ALTER TABLE room_template ADD CONSTRAINT room_template_room_type_FK FOREIGN KEY
( room_type_id ) REFERENCES room_type ( room_type_id ) NOT DEFERRABLE ;

ALTER TABLE room_template ADD CONSTRAINT room_template_site_FK FOREIGN KEY (
site_id ) REFERENCES site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE room_template ADD CONSTRAINT room_template_updated_by_FK FOREIGN
KEY ( updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE room_type ADD CONSTRAINT room_type_site_FK FOREIGN KEY ( site_id )
REFERENCES site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE room_type ADD CONSTRAINT room_type_updated_by_FK FOREIGN KEY (
updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE room ADD CONSTRAINT room_updated_by_FK FOREIGN KEY ( updated_by )
REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE service ADD CONSTRAINT service_site_FK FOREIGN KEY ( site_id )
REFERENCES site ( site_id ) NOT DEFERRABLE ;

ALTER TABLE service ADD CONSTRAINT service_updated_by_FK FOREIGN KEY (
updated_by ) REFERENCES person ( person_id ) NOT DEFERRABLE ;

ALTER TABLE site ADD CONSTRAINT site_updated_by_FK FOREIGN KEY ( updated_by )
REFERENCES person ( person_id ) NOT DEFERRABLE ;


-- Oracle SQL Developer Data Modeler Summary Report: 
-- 
-- CREATE TABLE                            29
-- CREATE INDEX                             0
-- ALTER TABLE                            113
-- CREATE VIEW                              0
-- CREATE PACKAGE                           0
-- CREATE PACKAGE BODY                      0
-- CREATE PROCEDURE                         0
-- CREATE FUNCTION                          0
-- CREATE TRIGGER                           0
-- ALTER TRIGGER                            0
-- CREATE COLLECTION TYPE                   0
-- CREATE STRUCTURED TYPE                   0
-- CREATE STRUCTURED TYPE BODY              0
-- CREATE CLUSTER                           0
-- CREATE CONTEXT                           0
-- CREATE DATABASE                          0
-- CREATE DIMENSION                         0
-- CREATE DIRECTORY                         0
-- CREATE DISK GROUP                        0
-- CREATE ROLE                              0
-- CREATE ROLLBACK SEGMENT                  0
-- CREATE SEQUENCE                         21
-- CREATE MATERIALIZED VIEW                 0
-- CREATE SYNONYM                           0
-- CREATE TABLESPACE                        0
-- CREATE USER                              0
-- 
-- DROP TABLESPACE                          0
-- DROP DATABASE                            0
-- 
-- ERRORS                                   0
-- WARNINGS                                 0
