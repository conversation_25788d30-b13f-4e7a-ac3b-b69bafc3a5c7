--
-- Modify instructor with employee id 087180 to studio 818
--
-- Applicable to:-
--  Production: required to be applied once
--  QA: required to be applied as needed to confirm resolution
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to update location association for instructor 087180
--
-- $Id: 
whenever sqlerror exit sql.sqlcode rollback;

-- Update the instructor associations
update instructor
  set location_id = (select location_id from location where external_id = '818'),
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where external_id = '087180';

-- Update patch log
insert into gcss_patches(description)
  values('Update location for instructor <PERSON> $Id: 33_modify_instructor_087180_location_gcss-710.sql 2978 2014-09-30 13:54:07Z memes $');
