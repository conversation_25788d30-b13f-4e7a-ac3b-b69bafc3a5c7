--
-- Modify authentication id for <PERSON>
--
-- Applicable to:-
--  Production: required to be applied once
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to modify authentication id
--
-- $Id: 08_modify_auth_id_for_<PERSON>_<PERSON>_gcss-557.sql 2513 2014-01-27 22:09:19Z memes $

whenever sqlerror exit sql.sqlcode rollback;

-- Modify the person record for <PERSON>
update person
  set auth_id = '065728',
      updated = systimestamp,
      version = version + 1
 where email = '<EMAIL>';

-- Update patch log
insert into gcss_patches(description)
  values('Modify <PERSON> authentication id  $Id: 08_modify_auth_id_for_<PERSON>_<PERSON>_gcss-557.sql 2513 2014-01-27 22:09:19Z memes $');
