--
-- Modify instructor with employee id 081990 to studio 431
--
-- Applicable to:-
--  Production: required to be applied once
--  QA: required to be applied as needed to confirm resolution
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to update location association for instructor 081990
--
-- $Id: 
whenever sqlerror exit sql.sqlcode rollback;

-- Update the instructor associations
update instructor
  set location_id = (select location_id from location where external_id = '431'),
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where external_id = '081990';

-- Update patch log
insert into gcss_patches(description)
  values('Update location for instructor <PERSON> $Id: 35_modify_instructor_081990_location_gcss-712.sql 2980 2014-10-07 13:21:58Z memes $');
