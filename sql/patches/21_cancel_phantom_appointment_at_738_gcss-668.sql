--
-- Cancel future appointments in series 76490 so that the timeslot and room
-- becomes available for booking again.
--
-- Applicable to:-
--  Production: required to be applied once
--  QA: required to be applied as needed to confirm resolution
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to cancel phantom appointment at studio 738
--
-- $Id: 21_cancel_phantom_appointment_at_738_gcss-668.sql 2904 2014-07-09 14:32:05Z memes $

whenever sqlerror exit sql.sqlcode rollback;

-- Cancel future appointments in series 76490
update appointment
  set canceled = 'Y',
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where appointment_series_id = 76490
   and trunc(start_time) >= trunc(sysdate);

-- Update patch log
insert into gcss_patches(description)
  values('Cancel phantom appointment in Thelonius room $Id: 21_cancel_phantom_appointment_at_738_gcss-668.sql 2904 2014-07-09 14:32:05Z memes $');
