--
-- Modify instructor with employee id 078403 to studio 852
--
-- Applicable to:-
--  Production: required to be applied once
--  QA: required to be applied as needed to confirm resolution
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to update location association for instructor 078403
--
-- $Id: 
whenever sqlerror exit sql.sqlcode rollback;

-- Update the instructor associations
update instructor
  set location_id = (select location_id from location where external_id = '852'),
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where external_id = '078403';

-- Update patch log
insert into gcss_patches(description)
  values('Update location for instructor <PERSON> $Id: 30_modify_instructor_078403_location_gcss-701.sql 2973 2014-09-12 13:31:46Z memes $');
