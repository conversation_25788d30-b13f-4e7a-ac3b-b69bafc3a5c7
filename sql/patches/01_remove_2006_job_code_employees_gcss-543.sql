--
-- This SQL is used to remove *previously identified* employees that were
-- assigned Studio Associate role through job code 2006 prior to CR GCSS-543.
--
-- Applicable to:-
--  GC QA and PROD: required to be applied once
--  All others: optional
--
-- Steps:
--  1. Deploy build -rc56
--  2. Execute this script once, just in case the rules change again in the
--     future
--
-- Note:
--  As patch #1 this script will create a patch table for tracking applied
--  patches
--
-- $Id: 01_remove_2006_job_code_employees_gcss-543.sql 2496 2014-01-17 23:17:40Z memes $

whenever sqlerror continue;

-- Create patch log table since this is the first patch
create table gcss_patches (description varchar2(512 byte) not null,
                           applied timestamp with local time zone default systimestamp not null)
  logging;
comment on table gcss_patches is 'Record of patches applied to schema';
comment on column gcss_patches.Description is 'Description of patch applied';
comment on column gcss_patches.applied is 'Timestamp of patch application';

whenever sqlerror exit sql.sqlcode rollback;

-- These employees were identified from the EDW file
-- GCIDW_ENTPR_EMPMAST_DAT_20140116 and may not include employees from
-- previously processed EDW files
--
-- 006210 <PERSON>
-- 040957 <PERSON> <PERSON> <PERSON>y
-- 071643 <PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>
-- 071683 Kodi M Sells
-- 072443 <PERSON> J Gac<PERSON>e
-- 073290 <PERSON><PERSON>  <PERSON>
-- 074741 <PERSON>  <PERSON><PERSON>
-- 074984 <PERSON>  <PERSON>or
-- 075329 Predrag  <PERSON><PERSON>ic
-- 076109 <PERSON>  Ra<PERSON><PERSON>
-- 076413 <PERSON>  Henss
-- 076462 <PERSON>  <PERSON>less
-- 077030 <PERSON> <PERSON> <PERSON>
-- 077802 <PERSON> B <PERSON>
-- 078154 <PERSON> F Tomlin
-- 078293 Tom  Boisse
-- 078390 Mark E Sander
-- 078666 Julian A Chobot
-- 079013 Joel R Karner
-- 079557 Timothy  Ports
-- 079560 Courtney D Kendrick
-- 080300 Russell  Pruett
-- 080726 Alex  Ferreiro
-- 081431 Joey B Bronner
-- 081465 Hannah E Larson
-- 081736 Phillip  McNeil
-- 082327 Justin  Molaison
-- 082575 Richard A Chastain
-- 084686 Barry l Arnell
-- 086378 Thomas p Smith III

delete
  from person_role
 where person_id in (
  select p.person_id
    from employee e
      inner join person p on
        e.person_id = p.person_id
   where e.external_id in ('006210', '040957', '071643', '071683', '072443',
                           '073290', '074741', '074984', '075329', '076109',
                           '076413', '076462', '077030', '077802', '078154',
                           '078293', '078390', '078666', '079013', '079557',
                           '079560', '080300', '080726', '081431', '081465',
                           '081736', '082327', '082575', '084686', '086378')
  );
delete
  from employee
 where employee_id in (
  select employee_id
    from employee
   where external_id in ('006210', '040957', '071643', '071683', '072443',
                         '073290', '074741', '074984', '075329', '076109',
                         '076413', '076462', '077030', '077802', '078154',
                         '078293', '078390', '078666', '079013', '079557',
                         '079560', '080300', '080726', '081431', '081465',
                         '081736', '082327', '082575', '084686', '086378')
  );

-- Update patch log
insert into gcss_patches(description)
  values('Remove employees processed with job code 2006 according to EDW file of 01/16/2014 $Id: 01_remove_2006_job_code_employees_gcss-543.sql 2496 2014-01-17 23:17:40Z memes $');
