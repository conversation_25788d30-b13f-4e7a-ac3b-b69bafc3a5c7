--
-- Modify instructor with employee id 087423 to studio 337
--
-- Applicable to:-
--  Production: required to be applied once
--  QA: required to be applied as needed to confirm resolution
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to update location association for instructor 087423
--
-- $Id: 
whenever sqler<PERSON>r exit sql.sqlcode rollback;

-- Update the instructor associations
update instructor
  set location_id = (select location_id from location where external_id = '337'),
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where external_id = '087423';

-- Update patch log
insert into gcss_patches(description)
  values('Update location for instructor <PERSON> Abud $Id: 29_modify_instructor_087423_location_gcss-699.sql 2971 2014-09-03 13:16:30Z memes $');
