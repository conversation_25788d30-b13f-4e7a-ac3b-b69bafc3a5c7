--
-- Add additional index definitions to speed up common queries in GCSS
--
-- Applicable to:-
--  All: apply
--
-- Steps:-
--  1. Execute this script to create/update indices
--
-- $Id: 06_add_indices_for_performance_gcss-553.sql 2534 2014-02-05 00:58:56Z memes $

whenever sqlerror continue;

-- 1. Add indices for commonly used foreign keys - e.g. don't bother indexing on
--    updated_by because it is never used in queries
-- 2. Add indices for data fields used in queries that are not referential
--    e.g. appointment start time, series end time, etc
--
-- Activity
drop index activity_service_id_fk_idx;
create index activity_service_id_fk_idx on activity (service_id) online;
drop index activity_site_id_fk_idx;
create index activity_site_id_fk_idx on activity (site_id) online;
drop index activity_enabled_idx;
create index activity_enabled_idx on activity (enabled) online;
drop index activity_version_idx;
create index activity_version_idx on activity (version) online;


-- Appointment
drop index appt_activity_id_fk_idx;
create index appt_activity_id_fk_idx on appointment (activity_id) online;
drop index appt_appt_series_id_fk_idx;
create index appt_appt_series_id_fk_idx on appointment (appointment_series_id) online;
drop index appt_instructor_id_fk_idx;
create index appt_instructor_id_fk_idx on appointment (instructor_id) online;
drop index appt_profile_id_fk_idx;
create index appt_profile_id_fk_idx on appointment (profile_id) online;
drop index appt_room_id_fk_idx;
create index appt_room_id_fk_idx on appointment (room_id) online;
drop index appt_site_id_fk_idx;
create index appt_site_id_fk_idx on appointment (site_id) online;
drop index appt_cancelled_idx;
create index appt_cancelled_idx on appointment (canceled) online;
drop index appt_start_time_idx;
create index appt_start_time_idx on appointment (start_time) online;
drop index appt_char_start_time_idx;
create index appt_char_start_time_idx on appointment (to_char(start_time, 'MM/DD/YYYY')) online;
drop index appt_munge_start_time_idx;
create index appt_munge_start_time_idx on appointment (to_date(to_char(start_time, 'YYYY-MM-DD'), 'YYYY-MM-DD')) online;
drop index appt_end_time_idx;
create index appt_end_time_idx on appointment (end_time) online;
drop index appt_char_end_time_idx;
create index appt_char_end_time_idx on appointment (to_char(end_time, 'MM/DD/YYYY')) online;
drop index appt_munge_end_time_idx;
create index appt_munge_end_time_idx on appointment (to_date(to_char(end_time, 'YYYY-MM-DD'), 'YYYY-MM-DD')) online;
drop index appt_version_idx;
create index appt_version_idx on appointment (version) online;

-- Appointment customers
drop index appt_custs_appt_id_fk_idx;
create index appt_custs_appt_id_fk_idx on appointment_customers (appointment_id) online;
drop index appt_custs_customer_id_fk_idx;
create index appt_custs_customer_id_fk_idx on appointment_customers (customer_id) online;

-- Appointment series
drop index appt_series_activity_id_fk_idx;
create index appt_series_activity_id_fk_idx on appointment_series (activity_id) online;
drop index appt_series_profile_id_fk_idx;
create index appt_series_profile_id_fk_idx on appointment_series (profile_id) online;
drop index appt_series_site_id_fk_idx;
create index appt_series_site_id_fk_idx on appointment_series (site_id) online;
drop index appt_series_start_time_idx;
create index appt_series_start_time_idx on appointment_series (series_start_time) online;
drop index appt_series_end_time_idx;
create index appt_series_end_time_idx on appointment_series (series_end_time) online;
drop index appt_series_version_idx;
create index appt_series_version_idx on appointment_series (version) online;


-- Availability
drop index availability_site_id_fk_idx;
create index availability_site_id_fk_idx on availability (site_id) online;
drop index availability_version_idx;
create index availability_version_idx on availability (version) online;


-- Customer
drop index customer_cust_status_id_fk_idx;
create index customer_cust_status_id_fk_idx on customer (customer_status_id) online;
drop index customer_person_id_fk_idx;
create index customer_person_id_fk_idx on customer (person_id) online;
drop index customer_site_id_fk_idx;
create index customer_site_id_fk_idx on customer (site_id) online;
drop index customer_version_idx;
create index customer_version_idx on customer (version) online;

-- Customer appointment series
drop index cas_appt_series_id_fk_idx;
create index cas_appt_series_id_fk_idx on customer_appointment_series (appointment_series_id) online;
drop index cas_customer_id_fk_idx;
create index cas_customer_id_fk_idx on customer_appointment_series (customer_id) online;

-- Customer instrument
drop index cust_inst_customer_id_fk_idx;
create index cust_inst_customer_id_fk_idx on customer_instrument (customer_id) online;
drop index cust_inst_instrument_id_fk_idx;
create index cust_inst_instrument_id_fk_idx on customer_instrument (instrument_id) online;

-- Customer status
drop index cust_status_site_id_fk_idx;
create index cust_status_site_id_fk_idx on customer_status (site_id) online;
drop index cust_status_version_idx;
create index cust_status_version_idx on customer_status (version) online;


-- Employee
drop index employee_location_id_fk_idx;
create index employee_location_id_fk_idx on employee (location_id) online;
drop index employee_person_id_fk_idx;
create index employee_person_id_fk_idx on employee (person_id) online;
drop index employee_site_id_fk_idx;
create index employee_site_id_fk_idx on employee (site_id) online;
drop index employee_version_idx;
create index employee_version_idx on employee (version) online;

-- Instructor
drop index inst_availability_id_fk_idx;
create index inst_availability_id_fk_idx on instructor (availability_id) online;
drop index inst_location_id_fk_idx;
create index inst_location_id_fk_idx on instructor (location_id) online;
drop index inst_person_id_fk_idx;
create index inst_person_id_fk_idx on instructor (person_id) online;
drop index inst_site_id_fk_idx;
create index inst_site_id_fk_idx on instructor (site_id) online;
drop index instructor_enabled_idx;
create index instructor_enabled_idx on instructor (enabled) online;
drop index instructor_version_idx;
create index instructor_version_idx on instructor (version) online;

-- Instructor activities
drop index inst_act_activity_id_fk_idx;
create index inst_act_activity_id_fk_idx on instructor_activities (activity_id) online;
drop index inst_act_inst_id_fk_idx;
create index inst_act_inst_id_fk_idx on instructor_activities (instructor_id) online;

-- Instrument
drop index instrument_site_id_fk_idx;
create index instrument_site_id_fk_idx on instrument (site_id) online;
drop index instrument_version_idx;
create index instrument_version_idx on instrument (version) online;

-- Location
drop index location_profile_id_fk_idx;
create index location_profile_id_fk_idx on location (profile_id) online;
drop index location_site_id_fk_idx;
create index location_site_id_fk_idx on location (site_id) online;
drop index location_external_id_idx;
create index location_external_id_idx on location (external_id) online;
drop index location_version_idx;
create index location_version_idx on location (version) online;

-- Location profile
drop index loc_profile_avail_id_fk_idx;
create index loc_profile_avail_id_fk_idx on location_profile (availability_id) online;
drop index loc_profile_site_id_fk_idx;
create index loc_profile_site_id_fk_idx on location_profile (site_id) online;
drop index loc_profile_version_idx;
create index loc_profile_version_idx on location_profile (version) online;

-- Person
drop index person_auth_id_idx;
create index person_auth_id_idx on person (lower(auth_id)) online;
drop index person_first_name_idx;
create index person_first_name_idx on person (first_name) online;
drop index person_last_name_idx;
create index person_last_name_idx on person (last_name) online;
drop index person_version_idx;
create index person_version_idx on person (version) online;

-- Person role
drop index per_role_location_id_fk_idx;
create index per_role_location_id_fk_idx on person_role (location_id) online;
drop index per_role_person_id_fk_idx;
create index per_role_person_id_fk_idx on person_role (person_id) online;
drop index per_role_role_id_fk_idx;
create index per_role_role_id_fk_idx on person_role (role_id) online;
drop index per_role_site_id_fk_idx;
create index per_role_site_id_fk_idx on person_role (site_id) online;
drop index per_role_version_idx;
create index per_role_version_idx on person_role (version) online;

-- Profile activity
drop index prof_act_activity_id_fk_idx;
create index prof_act_activity_id_fk_idx on profile_activity (activity_id) online;
drop index prof_act_profile_id_fk_idx;
create index prof_act_profile_id_fk_idx on profile_activity (profile_id) online;
drop index prof_act_site_id_fk_idx;
create index prof_act_site_id_fk_idx on profile_activity (site_id) online;
drop index prof_act_enabled_idx;
create index prof_act_enabled_idx on profile_activity (enabled) online;
drop index prof_act_version_idx;
create index prof_act_version_idx on profile_activity (version) online;

-- Profile service
drop index prof_svc_profile_id_fk_idx;
create index prof_svc_profile_id_fk_idx on profile_service (profile_id) online;
drop index prof_svc_service_id_fk_idx;
create index prof_svc_service_id_fk_idx on profile_service (service_id) online;
drop index prof_svc_site_id_fk_idx;
create index prof_svc_site_id_fk_idx on profile_service (site_id) online;
drop index prof_svc_enabled_idx;
create index prof_svc_enabled_idx on profile_service (enabled) online;
drop index prof_svc_version_idx;
create index prof_svc_version_idx on profile_service (version) online;

-- Role
drop index role_site_id_fk_idx;
create index role_site_id_fk_idx on role (site_id) online;
drop index role_version_idx;
create index role_version_idx on role (version) online;

-- Room
drop index room_parent_id_fk_idx;
create index room_parent_id_fk_idx on room (parent_id) online;
drop index room_profile_id_fk_idx;
create index room_profile_id_fk_idx on room (profile_id) online;
drop index room_room_number_id_fk_idx;
create index room_room_number_id_fk_idx on room (room_number_id) online;
drop index room_room_size_id_fk_idx;
create index room_room_size_id_fk_idx on room (room_size_id) online;
drop index room_room_templ_id_fk_idx;
create index room_room_templ_id_fk_idx on room (room_template_id) online;
drop index room_room_type_id_fk_idx;
create index room_room_type_id_fk_idx on room (room_type_id) online;
drop index room_site_id_fk_idx;
create index room_site_id_fk_idx on room (site_id) online;
drop index room_enabled_idx;
create index room_enabled_idx on room (enabled) online;
drop index room_version_idx;
create index room_version_idx on room (version) online;

-- Room activities
drop index room_act_activity_id_fk_idx;
create index room_act_activity_id_fk_idx on room_activities (activity_id) online;
drop index room_act_room_id_fk_idx;
create index room_act_room_id_fk_idx on room_activities (room_id) online;

-- Room number
drop index room_num_site_id_fk_idx;
create index room_num_site_id_fk_idx on room_number (site_id) online;
drop index room_num_version_idx;
create index room_num_version_idx on room_number (version) online;

-- Room services
drop index room_svc_room_id_fk_idx;
create index room_svc_room_id_fk_idx on room_services (room_id) online;
drop index room_svc_service_id_fk_idx;
create index room_svc_service_id_fk_idx on room_services (service_id) online;

-- Room size
drop index room_size_room_type_id_fk_idx;
create index room_size_room_type_id_fk_idx on room_size (room_type_id) online;
drop index room_size_site_id_fk_idx;
create index room_size_site_id_fk_idx on room_size (site_id) online;
drop index room_size_version_idx;
create index room_size_version_idx on room_size (version) online;

-- Room template
drop index room_templ_room_size_id_fk_idx;
create index room_templ_room_size_id_fk_idx on room_template (room_size_id) online;
drop index room_templ_room_type_id_fk_idx;
create index room_templ_room_type_id_fk_idx on room_template (room_type_id) online;
drop index room_templ_site_id_fk_idx;
create index room_templ_site_id_fk_idx on room_template (site_id) online;
drop index room_templ_enabled_idx;
create index room_templ_enabled_idx on room_template (enabled) online;
drop index room_templ_version_idx;
create index room_templ_version_idx on room_template (version) online;

-- Room template activities
drop index rta_activity_id_fk_idx;
create index rta_activity_id_fk_idx on room_template_activities (activity_id) online;
drop index rta_room_templ_id_fk_idx;
create index rta_room_templ_id_fk_idx on room_template_activities (room_template_id) online;

-- Room template services
drop index rts_room_templ_id_fk_idx;
create index rts_room_templ_id_fk_idx on room_template_services (room_template_id) online;
drop index rts_service_id_fk_idx;
create index rts_service_id_fk_idx on room_template_services (service_id) online;

-- Room type
drop index room_templ_room_type_id_fk_idx;
create index room_templ_room_type_id_fk_idx on room_template (room_type_id) online;
drop index room_type_site_id_fk_idx;
create index room_type_site_id_fk_idx on room_type (site_id) online;
drop index room_type_enabled_idx;
create index room_type_enabled_idx on room_type (enabled) online;
drop index room_type_version_idx;
create index room_type_version_idx on room_type (version) online;

-- Service
drop index service_site_id_fk_idx;
create index service_site_id_fk_idx on service (site_id) online;
drop index service_enabled_idx;
create index service_enabled_idx on service (enabled) online;
drop index service_version_idx;
create index service_version_idx on service (version) online;

-- Site
drop index site_external_id_idx;
create index site_external_id_idx on site (external_id) online;
drop index site_url_idx;
create index site_url_idx on site (url) online;
drop index site_version_idx;
create index site_version_idx on site (version) online;

-- Update patch log
insert into gcss_patches(description)
  values('Add indexes for performance $Id: 06_add_indices_for_performance_gcss-553.sql 2534 2014-02-05 00:58:56Z memes $');


