variable "env" {
  description = "Environment name"
  type        = string
  default     = "dev"
}

variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-west-2"
}

variable "STS_ASSUME_ROLE" {
  description = "The ARN of the role to assume"
  type        = string
}

variable "SESSION_NAME" {
  description = "The name of the session"
  type        = string
}

variable "LSS_CONTAINER_W_COMMIT_HASH" {
  description = "value of the commit hash"
  type        = string
}

variable "docker_tag" {
  description = "The tag for the Docker image"
  type        = string
  default     = "latest"
}

variable "vpc_id" {
  type        = string
  description = "The ID of the VPC"
}


variable "vpc_name" {
  default = "DEV-Sandbox-VPC"

}

variable "private_subnet_tags" {
  type = list(map(string))
  default = [{
    Name = "DEV-Sandbox-VPC_Public*"
  }]
}

variable "public_subnet_tags" {
  type = list(map(string))
  default = [{
    Name = "DEV-Sandbox-VPC_Public*"
  }]
}

variable "ecs_cluster_name" {
  description = "The name of the ECS cluster"
  type        = string
  default     = "lessons-scheduler-service"
}

variable "app_name" {
  description = "The name of the application"
  type        = string
  default     = "lessons-scheduler-service"
}

variable "autoscaling_max_size" {
  description = "The maximum size of the autoscaling group"
  type        = number
  default     = 2
}
variable "acm_cert_arn" {
  description = "value of the ACM certificate ARN"
  type        = string
}

variable "rds_instance_class" {
  description = "The instance class for the RDS instance"
  type        = string
  default     = "db.serverless"

}

variable "aurora_min_scaling_capacity" {
  default = 1
  type    = number
}

variable "aurora_max_scaling_capacity" {
  default = 2
  type    = number
}

variable "es_arn" {
  type        = string
  description = "The arn of the house account es"
  // will not work with cross account arns, cannot hook this up to ECOM-487
}
