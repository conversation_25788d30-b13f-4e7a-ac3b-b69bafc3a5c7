resource "aws_lb" "ecs_lb" {
  depends_on                 = [module.alb_security_group]
  name                       = "lessons-scheduler-service-pub"
  internal                   = false
  load_balancer_type         = "application"
  subnets                    = data.aws_subnets.public_subnets.ids
  security_groups            = [module.alb_security_group.security_group_id]
  enable_deletion_protection = true
  idle_timeout               = 600 # Increase to 10 minutes (in seconds) for legacy orders TODO: remove this after SMS data import is complete

  tags = {
    "Environment" = var.env
    "Name"        = "lessons-scheduler-service-application-load-balancer"
    "project"     = "LessonsSchedulerService"
  }
}

resource "aws_lb_target_group" "ecs_target_group" {
  name        = "lss-target-group"
  port        = 8080   # Changed to match container port
  protocol    = "HTTP" # Changed to HTTP since using ALB
  target_type = "ip"
  vpc_id      = data.aws_vpc.selected.id

  health_check {
    enabled             = true
    healthy_threshold   = 3
    interval            = 300
    matcher             = "200,302,401"
    path                = "/health"
    timeout             = 30
    unhealthy_threshold = 3
  }

  tags = {
    "Environment" = var.env
    "Name"        = "lessons-scheduler-service-target-group"
    "project"     = "LessonsSchedulerService"
  }
}

resource "aws_lb_target_group" "pgadmin_target_group" {
  lifecycle {
    create_before_destroy = true
  }
  name        = "lss-pgadmin-target-group"
  port        = 5050
  protocol    = "HTTP"
  target_type = "ip"
  vpc_id      = data.aws_vpc.selected.id

  health_check {
    enabled             = true
    healthy_threshold   = 5
    interval            = 300
    matcher             = "200,302,401"
    path                = "/misc/ping"
    timeout             = 30
    unhealthy_threshold = 5
  }

  tags = {
    "Environment" = var.env
    "Name"        = "pgadmin-target-group"
    "project"     = "LessonsSchedulerService"
  }
}

resource "aws_lb_listener" "pgadmin_lb_listener_5050" {
  load_balancer_arn = aws_lb.ecs_lb.arn
  port              = 5050
  protocol          = "HTTP"
  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.pgadmin_target_group.arn
  }

}

resource "aws_lb_listener" "ecs_lb_listener_80" {
  load_balancer_arn = aws_lb.ecs_lb.arn
  port              = 80
  protocol          = "HTTP"

  default_action {
    type = "redirect"
    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

resource "aws_lb_listener" "ecs_lb_listener_443" {
  load_balancer_arn = aws_lb.ecs_lb.arn
  port              = 443
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_cert_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.ecs_target_group.arn
  }
}
