terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 4.58.0"
    }
  }
  backend "s3" {
    # change the name of the bucket in each env
    bucket               = "appgroup-devops-terraform-state-guitarcenter"
    key                  = "lessons-scheduler-service/terraform.tfstate"
    region               = "us-west-2"
    encrypt              = true
    workspace_key_prefix = "atg"
    dynamodb_table       = "appgroup-devops-terraform-locking"

  }
}
provider "aws" {
  region = "us-west-2"
  assume_role {
    role_arn     = var.STS_ASSUME_ROLE
    session_name = var.SESSION_NAME
  }

  default_tags {
    tags = {
      "ManagedBy"   = "Terraform"
      "Environment" = var.env
      "costcenter"  = "Omni-Ecommerce"
      "project"     = "LessonsSchedulerService"
    }

  }

}
