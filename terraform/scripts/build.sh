#!/bin/bash

# Inspiration from https://github.com/onnimonni/terraform-ecr-docker-build-module
# Fail fast
set -e

# This is the order of arguments
existing_built_image=$1
aws_ecr_repository_url_with_tag=$2
aws_ecr_password=$3

# Check that aws is installed
which aws > /dev/null || { echo 'ERROR: aws-cli is not installed' ; exit 1; }
region="$(echo "$aws_ecr_repository_url_with_tag" | cut -d. -f4)"

# Check that docker is installed and running
which docker > /dev/null && docker ps > /dev/null || { echo 'ERROR: docker is not running' ; exit 1; }

# Tag our built image
docker tag $existing_built_image $aws_ecr_repository_url_with_tag

docker login --username AWS --password "$aws_ecr_password" "$aws_ecr_repository_url_with_tag"

# # Push image
docker push $aws_ecr_repository_url_with_tag