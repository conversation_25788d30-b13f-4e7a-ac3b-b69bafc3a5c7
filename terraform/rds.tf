# locals {
#   rds_secret_manager_value = jsondecode(data.aws_secretsmanager_secret_version.lss_rds_db.secret_string)
# }

resource "aws_db_subnet_group" "lss_db_subnet_group" {
  name       = "lessons-scheduler-service-db-subnet-group"
  subnet_ids = data.aws_subnets.private_subnets.ids
}
# Create a dedicated security group for RDS
module "rds_security_group" {
  source = "terraform-aws-modules/security-group/aws"
  name   = "lss_rds_sg_group"
  vpc_id = data.aws_vpc.selected.id

  # Allow inbound PostgreSQL traffic from ECS service
  ingress_with_source_security_group_id = [
    {
      from_port                = 5432
      to_port                  = 5432
      protocol                 = "tcp"
      source_security_group_id = module.ecs_service_sg_group.security_group_id
      description              = "PostgreSQL from ECS"
    }
  ]

  egress_rules       = ["all-all"]
  egress_cidr_blocks = ["0.0.0.0/0"]

  tags = {
    "Environment" = var.env
    "Name"        = "lessons-scheduler-service-rds-security-group"
    "project"     = "LessonsSchedulerService"
  }
}


module "lss_rds_aurora" {

  source  = "terraform-aws-modules/rds-aurora/aws"
  version = "v9.10.0"
  depends_on = [
    module.rds_security_group,
    module.ecs_service_sg_group,
    aws_secretsmanager_secret.lss_rds_db,
    # data.aws_secretsmanager_secret_version.lss_rds_db
  ]
  apply_immediately           = true
  create_cloudwatch_log_group = true
  create_monitoring_role      = true
  create_security_group       = false
  database_name               = "lessons_service"
  db_subnet_group_name        = aws_db_subnet_group.lss_db_subnet_group.name
  enable_http_endpoint        = true
  engine                      = "aurora-postgresql"
  engine_mode                 = "provisioned"
  engine_version              = "16.1"
  instance_class              = "db.serverless"
  # master_username             = local.rds_secret_manager_value["DB_USER"]
  master_username             = "tempF468F02E"
  manage_master_user_password = false
  # master_password             = local.rds_secret_manager_value["DB_PASSWORD"]
  master_password     = "tempcb4C75Bd4"
  monitoring_interval = 60
  name                = "lessons-scheduler-service-db-${var.env}"
  port                = 5432
  security_group_name = module.ecs_service_sg_group.security_group_name
  skip_final_snapshot = true
  source_region       = "us-west-2"
  storage_encrypted   = true
  vpc_id              = data.aws_vpc.selected.id
  vpc_security_group_ids = [
    module.rds_security_group.security_group_id,
    module.ecs_service_sg_group.security_group_id,
  ]

  serverlessv2_scaling_configuration = {
    min_capacity = 0.5  # minimum ACU
    max_capacity = 16.0 # maximum ACU
  }
  instances = {
    lssdb1 = {
    }
  }

  tags = {
    "Environment" = var.env
    "Name"        = "lessons-scheduler-service-rds"
    "project"     = "LessonsSchedulerService"
  }
}
