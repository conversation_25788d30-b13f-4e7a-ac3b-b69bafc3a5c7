resource "aws_ecr_repository" "lss_ecr" {
  name                 = var.app_name
  image_tag_mutability = "MUTABLE"
  force_delete         = false
}

resource "null_resource" "push" {
  triggers = {
    always_run = "${timestamp()}"
  }
  provisioner "local-exec" {
    command     = "bash ${path.module}/scripts/build.sh ${var.LSS_CONTAINER_W_COMMIT_HASH} ${aws_ecr_repository.lss_ecr.repository_url}:${var.docker_tag} ${data.aws_ecr_authorization_token.token.password}"
    interpreter = ["bash", "-c"]
  }
  depends_on = [aws_ecr_repository.lss_ecr]
}


data "external" "build_dir" {
  program = ["bash", "${path.module}/scripts/dir_md5.sh", "../"]
}
