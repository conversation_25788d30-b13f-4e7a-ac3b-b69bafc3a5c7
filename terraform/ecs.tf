
module "lesson_service_ecs_cluster_definition" {
  source       = "terraform-aws-modules/ecs/aws//modules/cluster"
  version      = "5.11.4"
  cluster_name = var.ecs_cluster_name
  fargate_capacity_providers = {
    FARGATE = {
      default_capacity_provider_strategy = {
        weight = 100
      }
    }
    FARGATE_SPOT = {
      default_capacity_provider_strategy = {
        weight = 100
      }
    }
  }
}


module "lss_ecs_service_definition" {
  depends_on = [
    aws_ecr_repository.lss_ecr,
    aws_lb_target_group.ecs_target_group,
    data.aws_subnets.private_subnets,
    data.aws_subnets.public_subnets,
    module.ecs_service_sg_group,
    aws_secretsmanager_secret.lss_pgadmin_db,
    aws_secretsmanager_secret.lss_rds_db,
    aws_secretsmanager_secret.lss_shared,
    # module.lss_rds_aurora,
    null_resource.push
  ]
  source                    = "terraform-aws-modules/ecs/aws//modules/service"
  version                   = "5.11.4"
  name                      = var.app_name
  cluster_arn               = module.lesson_service_ecs_cluster_definition.arn
  cpu                       = "1024"
  memory                    = "4096"
  create_tasks_iam_role     = false
  tasks_iam_role_arn        = aws_iam_role.ecs_task_role.arn
  create_task_exec_policy   = false
  create_task_exec_iam_role = false
  task_exec_iam_role_arn    = aws_iam_role.ecs_task_execution_role.arn
  requires_compatibilities  = ["FARGATE"]
  autoscaling_max_capacity  = var.autoscaling_max_size
  autoscaling_min_capacity  = 1
  autoscaling_policies = {
    "cpu" : {
      "policy_type" : "TargetTrackingScaling",
      "target_tracking_scaling_policy_configuration" : {
        "predefined_metric_specification" : {
          "predefined_metric_type" : "ECSServiceAverageCPUUtilization"
        },
        "target_value" : 65.0
      }
    },
    "memory" : {
      "policy_type" : "TargetTrackingScaling",
      "target_tracking_scaling_policy_configuration" : {
        "predefined_metric_specification" : {
          "predefined_metric_type" : "ECSServiceAverageMemoryUtilization"
        },
        "target_value" : 65.0
      }
    }
  }
  container_definitions = [{
    name                                   = var.app_name
    essential                              = true
    service                                = var.app_name
    image                                  = "${aws_ecr_repository.lss_ecr.repository_url}:${var.docker_tag}"
    enable_cloudwatch_logging              = true
    create_cloudwatch_log_group            = true
    cloudwatch_log_group_use_name_prefix   = true
    cloudwatch_log_group_retention_in_days = 60
    cloudwatch_log_group_name              = "/ecs/${var.app_name}"
    force_delete                           = true
    readonly_root_filesystem               = false
    port_mappings = [
      {
        name          = var.app_name
        containerPort = 8080 # var.docker_port
        protocol      = "tcp"
      }
    ]
    environment = [
      {
        name  = "spring.profiles.active"
        value = "production"
        }, {
        name  = "spring.main-allow-bean-definition-overriding"
        value = true
        }, {
        name  = "spring.main.all-circular-references"
        value = true
        }, {
        name  = "server.servlet.context-path"
        value = "/lessons-api/v1"
      },
      {
        name  = "spring.jpa.open-in-view"
        value = false
      },
      {
        name  = "spring.sql.init.mode"
        value = "never"
      },
    ]
    secrets = [
      {
        name      = "spring.security.user.name"
        valueFrom = "${aws_secretsmanager_secret.lss_shared.arn}:spring.security.user.name::"
      },
      {
        name      = "spring.security.user.password"
        valueFrom = "${aws_secretsmanager_secret.lss_shared.arn}:spring.security.user.password::"
      },
      {
        name      = "jdbc.schedulerDB.driverClassName"
        valueFrom = "${aws_secretsmanager_secret.lss_rds_db.arn}:jdbc.schedulerDB.driverClassName::"
        }, {
        name      = "jdbc.schedulerDB.url"
        valueFrom = "${aws_secretsmanager_secret.lss_rds_db.arn}:jdbc.schedulerDB.url::"
        }, {
        name      = "jdbc.schedulerDB.username"
        valueFrom = "${aws_secretsmanager_secret.lss_rds_db.arn}:jdbc.schedulerDB.username::"
        }, {
        name      = "jdbc.schedulerDB.password"
        valueFrom = "${aws_secretsmanager_secret.lss_rds_db.arn}:jdbc.schedulerDB.password::"
        }, {
        name      = "spring.datasource.driver.class-name"
        valueFrom = "${aws_secretsmanager_secret.lss_rds_db.arn}:spring.datasource.driver.class-name::"
        }, {
        name      = "spring.datasource.url"
        valueFrom = "${aws_secretsmanager_secret.lss_rds_db.arn}:spring.datasource.url::"
        }, {
        name      = "spring.datasource.username"
        valueFrom = "${aws_secretsmanager_secret.lss_rds_db.arn}:spring.datasource.username::"
      }
    ]
    ignore_task_definition_changes = false
    cpu                            = "512"
    memory_reservation             = "3072"
    tags = {
      "Environment" = var.env
      "Name"        = "LessonsSchedulerService"
      "project"     = "LessonsSchedulerService"
    }
    }, {
    name                        = "pgadmin"
    essential                   = false
    image                       = "dpage/pgadmin4"
    enable_cloudwatch_logging   = true
    create_cloudwatch_log_group = true
    # cloudwatch_log_group_use_name_prefix = false
    readonly_root_filesystem = false
    force_delete             = true
    # health_check = {
    #   command = [
    #     "CMD_SHELL",
    #     "curl -f http://localhost:5050/misc/ping"
    #   ]
    #   internal    = 240
    #   timeout     = 30
    #   retries     = 4
    #   startPeriod = 240
    # }
    environment = []
    secrets = [
      {
        name      = "PGADMIN_LISTEN_PORT"
        valueFrom = "${aws_secretsmanager_secret.lss_pgadmin_db.arn}:PGADMIN_LISTEN_PORT::"
        }, {
        name      = "PGADMIN_DEFAULT_EMAIL"
        valueFrom = "${aws_secretsmanager_secret.lss_pgadmin_db.arn}:PGADMIN_DEFAULT_EMAIL::"
        }, {
        name      = "PGADMIN_DEFAULT_PASSWORD"
        valueFrom = "${aws_secretsmanager_secret.lss_pgadmin_db.arn}:PGADMIN_DEFAULT_PASSWORD::"
      }
    ]
    cpu                = "512"
    memory_reservation = "1024"

    port_mappings = [
      {
        name          = "pgadmin"
        containerPort = 5050
        protocol      = "tcp"
      }
    ]
    tags = {
      "Environment" = var.env
      "Name"        = "pgadmin"
      "project"     = "LessonsSchedulerService"
    }
  }]

  security_group_rules = {
    lss_ingress = {
      type                     = "ingress"
      from_port                = 8080
      to_port                  = 8080
      protocol                 = "tcp"
      source_security_group_id = module.ecs_service_sg_group.security_group_id
      description              = "Ingress from LessonsSchedulerService"
    }

    pgadmin_ingress = {
      type                     = "ingress"
      from_port                = 5050
      to_port                  = 5050
      protocol                 = "tcp"
      source_security_group_id = module.ecs_service_sg_group.security_group_id
      description              = "Ingress from pgadmin"
    }

    oracle_oci_ingress = {
      type                     = "ingress"
      from_port                = 1521
      to_port                  = 1521
      protocol                 = "tcp"
      source_security_group_id = module.ecs_service_sg_group.security_group_id
      description              = "Ingress from Oracle OCI"
    }

    rds_access = {
      type                     = "ingress"
      from_port                = 5432
      to_port                  = 5432
      protocol                 = "tcp"
      source_security_group_id = module.ecs_service_sg_group.security_group_id
      description              = "Access to RDS"
    }

    all_egress = {
      type        = "egress"
      from_port   = 0
      to_port     = 0
      protocol    = "-1"
      cidr_blocks = ["0.0.0.0/0"]
      description = "Allow all outbound traffic"
    }
  }
  # create_security_group = false
  security_group_ids = [module.ecs_service_sg_group.security_group_id]
  subnet_ids         = data.aws_subnets.private_subnets.ids
  load_balancer = [{
    target_group_arn = aws_lb_target_group.ecs_target_group.arn
    container_name   = var.app_name
    container_port   = 8080
    }, {
    target_group_arn = aws_lb_target_group.pgadmin_target_group.arn
    container_name   = "pgadmin"
    container_port   = 5050
  }]

  tags = {
    "Environment" = var.env
    "Name"        = "lessons-scheduler-service"
    "project"     = "LessonsSchedulerService"
  }
}

# resource "aws_security_group_rule" "pgadmin_5050" {
#   type              = "ingress"
#   description       = "Allow pgadmin access from ALB"
#   security_group_id = module.alb_security_group.security_group_id
#   self              = true
#   protocol          = "tcp"
#   from_port         = 5050
#   to_port           = 5050
# }

# ALB Security Group
module "alb_security_group" {
  source = "terraform-aws-modules/security-group/aws"
  name   = "lss_alb_sg_group"
  vpc_id = data.aws_vpc.selected.id

  ingress_rules       = ["http-80-tcp", "https-443-tcp", ]
  ingress_cidr_blocks = ["0.0.0.0/0"]
  computed_ingress_with_cidr_blocks = [
    {
      from_port   = 5050
      to_port     = 5050
      protocol    = "tcp"
      cidr_blocks = "0.0.0.0/0"
      description = "Allow pgadmin access from ALB"
    },
  ]

  number_of_computed_ingress_with_cidr_blocks = 1


  egress_rules = ["all-all"]
  egress_with_source_security_group_id = [
    {
      from_port                = 5050
      to_port                  = 5050
      protocol                 = "tcp"
      source_security_group_id = module.ecs_service_sg_group.security_group_id
      description              = "To pgadmin"
    },
    {
      from_port                = 8080
      to_port                  = 8080
      protocol                 = "tcp"
      source_security_group_id = module.ecs_service_sg_group.security_group_id
      description              = "To ECS lss container"
    }
  ]

  tags = {
    "Environment" = var.env
    "Name"        = "lss-alb-security-group"
    "project"     = "LessonsSchedulerService"
  }
}

# ECS Service Security Group
module "ecs_service_sg_group" {
  source = "terraform-aws-modules/security-group/aws"
  name   = "lss_ecs_sg_group"
  vpc_id = data.aws_vpc.selected.id

  ingress_with_source_security_group_id = [
    {
      from_port                = 8080
      to_port                  = 8080
      protocol                 = "tcp"
      source_security_group_id = module.alb_security_group.security_group_id
      description              = "From ALB to lss"
    },
    {
      from_port                = 5050
      to_port                  = 5050
      protocol                 = "tcp"
      source_security_group_id = module.alb_security_group.security_group_id
      description              = "From ALB to pgadmin"
    }
  ]

  egress_rules       = ["all-all"]
  egress_cidr_blocks = ["0.0.0.0/0"]

  tags = {
    "Environment" = var.env
    "Name"        = "lss-ecs-security-group"
    "project"     = "LessonsSchedulerService"
  }
}
