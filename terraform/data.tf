data "aws_region" "current" {}

data "aws_caller_identity" "current" {}


data "aws_ecr_authorization_token" "token" {
}

data "aws_vpc" "selected" {
  id = var.vpc_id
  # tags = {
  #   Name = var.vpc_name
  # }
}
data "aws_subnets" "private_subnets" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.selected.id]
  }
  tags = merge(var.private_subnet_tags...)
}

data "aws_subnets" "public_subnets" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.selected.id]
  }
  tags = merge(var.public_subnet_tags...)
}


# data "aws_secretsmanager_secret" "lss_shared" {
#   name = "/lss/lessons-scheduler-service-shared"
# }

# data "aws_secretsmanager_secret_version" "lss_shared" {
#   secret_id = data.aws_secretsmanager_secret.lss_shared.id
# }

# data "aws_secretsmanager_secret" "lss_pgadmin_secrets" {
#   name = "/lss/lessons-scheduler-service-pgadmin"
# }

# data "aws_secretsmanager_secret_version" "lss_pgadmin_secrets" {
#   secret_id = data.aws_secretsmanager_secret.lss_pgadmin_secrets.id
# }

# data "aws_secretsmanager_secret" "lss_rds_db" {
#   name = "/lss/lessons-scheduler-service-rds_db"
# }

# data "aws_secretsmanager_secret_version" "lss_rds_db" {
#   secret_id = data.aws_secretsmanager_secret.lss_rds_db.id
# }

