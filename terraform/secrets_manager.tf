resource "aws_kms_key" "lss_shared" {
  description = "shared secrets the lessons service api"
}

# resource "aws_kms_key_policy" "lss_shared" {
#   key_id = aws_kms_key.lss_shared.key_id
#   policy = jsonencode({
#     Version = "2012-10-17"
#     Statement = [
#       {
#         Sid    = "Allow role access to KMS operations"
#         Effect = "Allow"
#         Principal = {
#           AWS = "*"
#         }
#         Condition = {
#           StringEquals = {
#             "aws:PrincipalOrgID" = data.aws_caller_identity.current.account_id
#           }
#         }
#         Action = [
#           "kms:Decrypt",
#           "kms:DescribeKey",
#           "kms:GenerateDataKey",
#           "kms:ListAliases",
#           "kms:Encrypt"
#         ]
#         Resource = "*"
#       }
#     ]
#   })
# }
resource "aws_kms_alias" "lss_shared" {
  name          = "alias/lessons-scheduler-service-shared_secrets"
  target_key_id = aws_kms_key.lss_shared.key_id
}


resource "aws_secretsmanager_secret" "lss_shared" {
  name        = "/lss/lessons-scheduler-service-shared"
  description = "shared secrets the lessons service api"
  kms_key_id  = aws_kms_key.lss_shared.key_id
}
resource "aws_kms_key" "lss_rds_db" {
  description = "shared secrets the subscription management api"
}

# resource "aws_kms_key_policy" "lss_rds_db" {
#   key_id = aws_kms_key.lss_rds_db.key_id
#   policy = jsonencode({
#     Version = "2012-10-17"
#     Statement = [
#       {
#         Sid    = "Allow role access to KMS operations"
#         Effect = "Allow"
#         Principal = {
#           AWS = "*"
#         }
#         Condition = {
#           StringEquals = {
#             "aws:PrincipalOrgID" = data.aws_caller_identity.current.account_id
#           }
#         }
#         Action = [
#           "kms:Decrypt",
#           "kms:DescribeKey",
#           "kms:GenerateDataKey",
#           "kms:ListAliases",
#           "kms:Encrypt"
#         ]
#         Resource = "*"
#       }
#     ]
#   })
# }

resource "aws_kms_alias" "lss_rds_db" {
  name          = "alias/lessons-scheduler-service-rds_db"
  target_key_id = aws_kms_key.lss_rds_db.key_id
}

resource "aws_secretsmanager_secret" "lss_rds_db" {
  name        = "/lss/lessons-scheduler-service-rds_db"
  description = "shared secrets the lessons-scheduler-service rds instance"
  kms_key_id  = aws_kms_key.lss_rds_db.key_id
}

resource "aws_kms_key" "lss_pgadmin_db" {
  description = "shared secrets the subscription management api"
}

# resource "aws_kms_key_policy" "lss_pgadmin_db" {
#   key_id = aws_kms_key.lss_pgadmin_db.key_id
#   policy = jsonencode({
#     Version = "2012-10-17"
#     Statement = [
#       {
#         Sid    = "Allow role access to KMS operations"
#         Effect = "Allow"
#         Principal = {
#           AWS = "*"
#         }
#         Condition = {
#           StringEquals = {
#             "aws:PrincipalOrgID" = data.aws_caller_identity.current.account_id
#           }
#         }
#         Action = [
#           "kms:Decrypt",
#           "kms:DescribeKey",
#           "kms:GenerateDataKey",
#           "kms:ListAliases",
#           "kms:Encrypt"
#         ]
#         Resource = "*"
#       }
#     ]
#   })
# }

resource "aws_kms_alias" "lss_pgadmin_db" {
  name          = "alias/lessons-scheduler-service-pgadmin_db"
  target_key_id = aws_kms_key.lss_pgadmin_db.key_id
}
resource "aws_secretsmanager_secret" "lss_pgadmin_db" {
  name        = "/lss/lessons-scheduler-service-pgadmin"
  description = "shared secrets the lessons-scheduler-service pgadmin instance"
  kms_key_id  = aws_kms_key.lss_pgadmin_db.key_id
}
