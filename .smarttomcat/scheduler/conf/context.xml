<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!-- The contents of this file will be loaded for each web application -->
<Context>

    <!-- Default set of monitored resources. If one of these changes, the    -->
    <!-- web application will be reloaded.                                   -->
    <WatchedResource>WEB-INF/web.xml</WatchedResource>
    <WatchedResource>WEB-INF/tomcat-web.xml</WatchedResource>
    <WatchedResource>${catalina.base}/conf/web.xml</WatchedResource>

    <!-- Uncomment this to enable session persistence across Tomcat restarts -->
    <!--
    <Manager pathname="SESSIONS.ser" />
    -->


    <Environment name="solrURL" value="ecm-app-001-lq.domestic.guitarcenter.com:2181,ecm-app-002-lq.domestic.guitarcenter.com:2181,ecm-app-003-lq.domestic.guitarcenter.com:2181" type="java.lang.String" />

    <Environment name="emailHeaderImgUrl" type="java.lang.String"
                 value="https://media.guitarcenter.com/is/image/MMGS7/Lessons-Email-Header-135?hei=135"
                 overrides="false" />

    <Resource name="mail/session" auth="Container"
              type="javax.mail.Session" mail.debug="false"
              mail.smtp.auth="false"
              mail.smtp.host="phxcas.guitarcenter.com"
              mail.smtp.port="25"/>

    <Resource name="jdbc/schedulerDB" auth="container"
              factory="org.apache.tomcat.jdbc.pool.DataSourceFactory"
              type="javax.sql.DataSource"
              driverClassName="oracle.jdbc.OracleDriver"
              defaultRowPrefetch="100" testWhileIdle="true"
              validationQuery="select 1 from dual"
              defaultTransactionIsolation="READ_COMMITTED"
              url="*******************************************************************************"
              username="gcstudio" password="MGU86#nJA4ZbK4H"  initSQL="ALTER SESSION SET CURRENT_SCHEMA = gcstudio"/>


</Context>
