# Use Eclipse Temurin 17 as the base image for building the application
FROM eclipse-temurin:17-jdk-jammy AS build
# Install Maven in the build stage
ENV AWS_REGION=us-west-2
RUN apt-get update && \
  apt-get upgrade -y && \
  apt-get install -y maven
# Set the working directory
WORKDIR /app
# Copy the Maven configuration and resolve dependencies
COPY pom.xml .
RUN mvn dependency:purge-local-repository -DreResolve=false
RUN mvn dependency:resolve
# Copy the rest of the application source code
COPY src /app/src
# Build the application and package it (skipping tests)
RUN mvn clean package -DskipTests

# Use Eclipse Temurin 17 as the base image for running the application
FROM eclipse-temurin:17-jre-jammy
ENV AWS_REGION=us-west-2
# Set the working directory in the runtime container
WORKDIR /app
# Copy the built jar from the build stage to the runtime stage
COPY --from=build /app/target/scheduler-3.2.1.jar /app/scheduler-3.2.1.jar
EXPOSE 8080
# Set the default command to run the jar
ENTRYPOINT ["java", "-jar", "scheduler-3.2.1.jar"]