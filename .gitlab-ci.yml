default:
 image:
  name: hashicorp/terraform:1.3.7
  entrypoint: [""]
 services:
  - docker:24.0.5-dind
 before_script:
  - apk add docker
  - apk update && apk add yq git
  - docker info
  - docker login -u $CI_REGISTRY_USER --password $CI_REGISTRY_PASSWORD $CI_REGISTRY
variables:
 DOCKER_HOST: tcp://docker:2375
 DOCKER_TLS_CERTDIR: ""
 TF_VAR_docker_tag: $CI_COMMIT_SHORT_SHA
 TF_VAR_docker_image: $CI_REGISTRY_IMAGE
 LSS_CONTAINER_W_COMMIT_HASH: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
 CONTAINER_RELEASE_IMAGE: $CI_REGISTRY_IMAGE:latest

workflow:
 name: "atg-terraform-workflow"
 rules:
  - if: $CI_COMMIT_TITLE =~ /-draft$/ || $CI_PIPELINE_SOURCE == "merge_request_event"
    when: never
  - if: $CI_COMMIT_BRANCH == "dev"
    variables:
     ENVIRONMENT: "dev"
     TIER: "development"
  - if: $CI_COMMIT_BRANCH == "qa"
    variables:
     ENVIRONMENT: "qa"
     TIER: "testing"
  - if: $CI_COMMIT_TAG =~ /^release-.*$/i || $CI_COMMIT_BRANCH == "main" # When Commit Tag exists Commit Branch is null/empty, adding Commit branch to run scanning jobs when committing to main.
    variables:
     ENVIRONMENT: "prod"
     TIER: "production"
  - if: $CI_COMMIT_BRANCH == "destroy"

build-docker-image:
 stage: build
 image: docker:28.0.4
 services:
  - docker:28-dind
 environment:
  name: $ENVIRONMENT
 script:
  - echo "Building Docker Image"
  - docker container prune; docker image prune; docker volume prune
  - DOCKER_BUILDKIT=1 docker build --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from $CI_REGISTRY_IMAGE:latest -t $LSS_CONTAINER_W_COMMIT_HASH -t $CI_REGISTRY_IMAGE:latest -f ./Dockerfile .
  - docker push $LSS_CONTAINER_W_COMMIT_HASH
  - docker push $CI_REGISTRY_IMAGE:latest
  - echo "Docker Image Build Completed"

plan:
 stage: build
 extends:
  - .get:token
  - .get:terraform:state:provider:roles
 environment:
  name: $ENVIRONMENT
 script:
  - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY
  - export AWS_REGION="us-west-2"
  - export TF_VAR_LSS_CONTAINER_W_COMMIT_HASH=$LSS_CONTAINER_W_COMMIT_HASH
  - cd ${TF_ENVIRONMENT_DIR}
  - apk update
  - apk add python3 bash docker
  - terraform fmt
  - terraform init
  - terraform validate
  - terraform workspace select $ENVIRONMENT || terraform workspace new $ENVIRONMENT
  - terraform plan --var-file="variables-$ENVIRONMENT.tfvars" -destroy -target="module.lss_ecs_service_definition.aws_security_group.this[0]"
  # - terraform state rm module.lss_ecs_service_definition.aws_security_group.this[0]
  # - terraform destroy --var-file="variables-$ENVIRONMENT.tfvars" -target="module.lss_ecs_service_definition.aws_security_group.this[0]"
  - terraform plan --var-file="variables-$ENVIRONMENT.tfvars" -compact-warnings -out=$TF_ENVIRONMENT_DIR/plan.out > $TF_ENVIRONMENT_DIR/terraform-plan.log
  - terraform show -json $TF_ENVIRONMENT_DIR/plan.out > $TF_ENVIRONMENT_DIR/plan.json
 needs:
  - job: build-docker-image
 artifacts:
  untracked: true
  when: always
  paths:
   - terraform/plan.out
   - terraform/terraform-plan.log
   - terraform/plan.json
  expire_in: 2 days

deploy-terraform:
 stage: deploy
 services:
  - docker:24.0.5-dind
 image:
  name: hashicorp/terraform:1.3.7
  entrypoint: [""]
 extends:
  - .get:token
  - .get:terraform:state:provider:roles
 environment:
  name: $ENVIRONMENT
 script:
  - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY
  - export AWS_REGION="us-west-2"
  - apk update
  - apk add python3 bash docker
  - docker login -u $CI_REGISTRY_USER --password $CI_REGISTRY_PASSWORD $CI_REGISTRY
  - docker pull $LSS_CONTAINER_W_COMMIT_HASH
  - export DOCKER_BUILDKIT=1
  - export TF_VAR_LSS_CONTAINER_W_COMMIT_HASH=$LSS_CONTAINER_W_COMMIT_HASH
  - cd ${TF_ENVIRONMENT_DIR}
  - export TF_LOG=ERROR
  - terraform fmt
  - terraform workspace select $ENVIRONMENT || terraform workspace new $ENVIRONMENT
  - terraform apply --var-file="variables-$ENVIRONMENT.tfvars" -auto-approve > $TF_ENVIRONMENT_DIR/terraform-apply.log
  - echo "Terraform apply completed successfully"
 needs:
  - job: build-docker-image
  - job: plan
    artifacts: true
 artifacts:
  untracked: false
  when: always
  paths:
   - terraform/terraform-apply.log
  access: all
  expire_in: "30 days"

destroy:
 stage: deploy
 extends:
  - .get:token
  - .get:terraform:state:provider:roles
 environment:
  name: $ENVIRONMENT
 script:
  - apk update
  - apk add nodejs
  - apk add npm
  - cd ${TF_ENVIRONMENT_DIR}
  - echo $ENVIRONMENT
  - terraform fmt
  - terraform init
  - terraform validate
  - terraform workspace select $ENVIRONMENT ||  terraform workspace new $ENVIRONMENT
  - terraform destroy --var-file="variables-$ENVIRONMENT.tfvars" -auto-approve #TODO, use terraform plan -destroy: https://developer.hashicorp.com/terraform/cli/commands/destroy
 rules:
  - if: $CI_COMMIT_BRANCH == "destroy"
 needs:
  - job: plan
    artifacts: true
 # when: manual
