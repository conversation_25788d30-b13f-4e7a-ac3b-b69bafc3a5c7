
# GC Studios Lessons Microservice

## Overview

The GC Studios Lessons Microservice is a comprehensive appointment scheduling system designed for Guitar Center Studios. This Spring Boot-based microservice enables efficient creation, optimization, and management of all appointments within studio establishments. The system provides robust functionality for managing rooms, setting availability schedules, and building instructor schedules within their defined availability windows.

## Features

- **Appointment Management**: Create, update, and manage studio appointments
- **Room Management**: Configure and manage studio rooms with availability settings
- **Instructor Scheduling**: Build and manage instructor schedules within availability constraints
- **Real-time Availability**: Integration with Elasticsearch for instructor availability feeds
- **Customer Integration**: Seamless integration with POS systems for customer data
- **Authentication**: Active Directory integration for secure login
- **Reporting**: Comprehensive reporting capabilities with JasperReports

## Technology Stack

- **Java**: 17
- **Framework**: Spring Boot 3.2.1
- **Build Tool**: Maven
- **Database**: SQL Database with Hibernate ORM
- **Search Engine**: Elasticsearch 7.9.3
- **Authentication**: Spring Security with LDAP/Active Directory
- **Containerization**: Docker
- **Infrastructure**: AWS ECS with Terraform

## External Integrations

- **Database**: Primary datastore for application data
- **Elasticsearch**: Instructor availability feed and search capabilities
- **TIBCO**: Integration with Point of Sale (POS) systems for customer data
- **SAM**: Web services and API integrations
- **EDW**: Employee and Instructor HR data feed
- **Active Directory**: Authentication and authorization services

## Prerequisites

- Java 17 or higher
- Maven 3.6+
- Docker (for containerized deployment)
- Access to required external services (Database, Elasticsearch, etc.)

## Local Development Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd lessons-microservice
```

### 2. Build the Application
```bash
# Clean build with tests
mvn clean package

# Build without tests (faster for development)
mvn clean package -DskipTests
```

### 3. Run Locally
```bash
# Using Maven
mvn spring-boot:run

# Using Java directly
java -jar target/scheduler-3.2.1.jar

# Using Docker
docker build -t gc-lessons-microservice .
docker run -p 8080:8080 gc-lessons-microservice
```

## Configuration

The application supports multiple environments through Spring profiles:

- **Development**: `application-dev.properties`
- **QA**: `application-qa.properties`
- **Production**: `application-prod.properties`

### Environment Variables

Key environment variables:
- `AWS_REGION`: AWS region for cloud services (default: us-west-2)
- `SPRING_PROFILES_ACTIVE`: Active Spring profile (dev/qa/prod)

## Deployment

### Docker Deployment

The application is containerized using Docker and can be deployed using the provided Dockerfile:

```bash
# Build Docker image
docker build -t gc-lessons-microservice:latest .

# Run container
docker run -d -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e AWS_REGION=us-west-2 \
  gc-lessons-microservice:latest
```

### Infrastructure as Code

The project includes Terraform configurations for AWS deployment:

```bash
cd terraform

# Initialize Terraform
terraform init

# Plan deployment
terraform plan -var-file="variables-prod.tfvars"

# Apply deployment
terraform apply -var-file="variables-prod.tfvars"
```

## Environment Details


**Deployment:**
- Application deployed on: ecm-app-001-lp, ecm-app-002-lp, ecm-app-003-lp
- Log location: `/var/lib/gcss/tomcat/logs`
- Restart script: `/var/lib/gcss/script_restart.sh`

## API Documentation

The microservice exposes RESTful APIs for appointment and scheduling management. Once the application is running, you can access:

- **Application**: `http://localhost:8080`
- **Health Check**: `http://localhost:8080/actuator/health`
- **API Documentation**: Available through the application interface

## Testing

### Running Tests

```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=YourTestClass

# Run tests with coverage
mvn test jacoco:report
```

### Integration Tests

```bash
# Run integration tests
mvn verify -Pintegration-tests
```

## Database Setup

The application includes SQL scripts for database setup:

- `sql/00_create_schema.sql` - Database schema creation
- `sql/01_populate_reference_values.sql` - Reference data population
- `sql/02_quartz_schema_prod.sql` - Quartz scheduler schema
- `sql/99_development_setup.sql` - Development environment setup

## Monitoring and Logging

### Logging

- **Configuration**: `src/main/resources/log4j2.xml`
- **Log Location**: `/var/lib/gcss/tomcat/logs` (production)
- **Local Development**: Logs output to console and application logs

### Health Monitoring

Spring Boot Actuator endpoints are available for monitoring:
- `/actuator/health` - Application health status
- `/actuator/info` - Application information
- `/actuator/metrics` - Application metrics


   ```

2. **Database Connection Issues**
   - Verify database credentials in application properties
   - Ensure database server is accessible
   - Check network connectivity

3. **Elasticsearch Connection**
   - Verify Elasticsearch cluster is running
   - Check Elasticsearch configuration in application properties
   - Validate network connectivity to Elasticsearch nodes

4. **Memory Issues**
   ```bash
   # Increase JVM memory
   java -Xmx2g -Xms1g -jar target/scheduler-3.2.1.jar
   ```

### Log Analysis

```bash
# View application logs
tail -f /var/lib/gcss/tomcat/logs/catalina.out

# Search for errors
grep -i error /var/lib/gcss/tomcat/logs/catalina.out

# Monitor real-time logs
tail -f /var/lib/gcss/tomcat/logs/application.log
```

## Contributing

### Development Workflow

1. Create a feature branch from `master`
2. Make your changes following coding standards
3. Write/update tests for your changes
4. Run tests locally: `mvn test`
5. Build and verify: `mvn clean package`
6. Submit a pull request

### Code Standards

- Follow Java coding conventions
- Write comprehensive unit tests
- Update documentation for new features
- Use meaningful commit messages

## Support

For technical support or questions:
- Create an issue in the project repository
- Contact the development team
- Refer to internal documentation and wikis

## License

This project is proprietary software owned by Guitar Center, Inc.

---

**Last Updated**: January 2025
**Version**: 3.2.1
**Maintainer**: Guitar Center IT Team
