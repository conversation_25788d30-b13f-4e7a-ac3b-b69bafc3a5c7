<?xml version="1.0" encoding="UTF-8"?>
<!-- <project xmlns="http://maven.apache.org/POM/4.0.0"
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
			     http://maven.apache.org/maven-v4_0_0.xsd"> -->


<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <!-- Maven configuration for GC Studios Scheduling project $Id: $ -->
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.guitarcenter</groupId>
    <artifactId>scheduler</artifactId>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.1</version>
        <relativePath /> <!-- lookup parent from repository -->
    </parent>
    <packaging>jar</packaging>
    <name>scheduler</name>
    <description>GC Lessons microservices-application</description>

    <!-- Properties of dependent components -->
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>17</java.version>
        <!-- Changes made for GSSP-160 -->
        <elasticsearch.version>7.9.3</elasticsearch.version>
        <springframework.version>6.1.3</springframework.version>
      <!--  <springsecurity.version>6.3.3</springsecurity.version>-->
        <spring-ldap.version>3.1.6</spring-ldap.version>
        <hibernate.version>6.3.2.Final</hibernate.version>
        <hibernatejpa.version>1.0.1.Final</hibernatejpa.version>
        <slf4j.version>1.7.29</slf4j.version>
        <!-- <log4j.version>2.10.0</log4j.version>-->
        <commons-lang.version>2.6</commons-lang.version>
        <junit.version>4.11</junit.version>
        <!-- Spring Web MVC depends on 3.0.1 and 2.1 for Servlet/JSP API respectively -->
        <!--  <servlet.version>3.1.0</servlet.version>-->
        <!-- SolrJ version -->
        <solrj.version>8.11.3</solrj.version>
        <!-- jackson -->
        <jackson.version>2.16.1</jackson.version>
        <!-- taglibs -->
        <!--  <taglibs-standard.version>1.1.2</taglibs-standard.version>-->
        <!-- validation -->
        <validation.version>1.1.0.Final</validation.version>
        <!-- hibernate-validator -->
        <hibernate-validator.version>8.0.1.Final</hibernate-validator.version>
        <!--javax.el-->
        <!--  <javax.el.version>3.0.0</javax.el.version>-->
        <!-- joda -->
        <joda-time.version>2.2</joda-time.version>
        <!--  <joda-time-jsptags.version>1.1.1</joda-time-jsptags.version>-->
        <jadira.version>5.0.0.GA</jadira.version>
        <!-- freemarker -->
        <freemarker.version>2.3.23</freemarker.version>
        <!-- jasperreports -->
        <jasperreports.version>6.21.0</jasperreports.version>
        <jasperreports.fonts.version>6.0.0</jasperreports.fonts.version>
        <jasperreports.output.path>src/main/webapp/WEB-INF/views/reports</jasperreports.output.path>
        <!-- JiBX -->
        <jibx.version>1.2.5</jibx.version>
        <guava.version>16.0.1</guava.version>
        <!-- ActiveMQ -->
        <activemq.version>6.0.1</activemq.version>
        <jsch.version>0.1.50</jsch.version>
        <!-- Quartz - still using 1.8.x -->
        <!-- Changes made for GSSP-160 -->
        <quartz.version>2.3.2</quartz.version>
        <quartz-oracle.version>2.1.7</quartz-oracle.version>
        <!-- supercsv version 2.2.0 -->
        <supercsv.version>2.2.0</supercsv.version>
        <!-- Pattern of tests to skip; should be empty unless set in a profile -->
        <exclude.tests>none</exclude.tests>
        <skipTests>true</skipTests>
    </properties>


    <dependencies>
        <!--spring
        boot core-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <!--   <exclusion>
                       <groupId>org.apache.tomcat.embed</groupId>
                       <artifactId>tomcat-embed-jasper</artifactId>
                   </exclusion>-->
                <!--   <exclusion>
                       <groupId>org.springframework.boot</groupId>
                       <artifactId>spring-boot-starter-tomcat</artifactId>
                   </exclusion>-->

                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-security</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- spring security -->
  <!--      <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->


        <!--hibernate
        jpa-->
        <!-- <dependency>
             <groupId>org.springframework.boot</groupId>
             <artifactId>spring-boot-starter-data-jpa</artifactId>
         </dependency>-->
        <!-- Spring Boot ehcache -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <!--Spring
        Boot Log4j2-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-simple</artifactId>
            <version>${slf4j.version}</version>
            <!-- <scope>compile</scope>-->
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-ldap</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    <!--    <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-acl</artifactId>
            <version>${springsecurity.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-taglibs</artifactId>
            <version>${springsecurity.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-ldap</artifactId>
        </dependency>-->

        <dependency>
            <groupId>org.hibernate.orm</groupId>
            <artifactId>hibernate-core</artifactId>
            <version>6.3.2.Final</version> <!-- Use the version that matches your setup -->
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>


        <!-- https://mvnrepository.com/artifact/org.springframework.data/spring-data-jpa -->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-jpa</artifactId>
            <version>3.2.2</version>
        </dependency>

        <!--    <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>runtime</scope>
        </dependency>-->

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-dbcp2</artifactId>
            <version>2.9.0</version>
        </dependency>


        <!-- <dependency>
             <groupId>org.hibernate.orm</groupId>
             <artifactId>hibernate-annotations</artifactId>
             <version>6.3.2.Final</version> &lt;!&ndash; Use the version that matches your setup &ndash;&gt;
         </dependency>-->

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-orm</artifactId>
            <version>6.0.14</version> <!-- Use the version that matches your Spring setup -->
        </dependency>

        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>2.5.0</version>
        </dependency>


        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <version>4.0.2</version>
        </dependency>


        <dependency>
            <groupId>org.ehcache</groupId>
            <artifactId>ehcache</artifactId>
            <version>3.10.8</version> <!-- Use the version that matches your setup -->
        </dependency>

        <!--  <dependency>
              <groupId>net.sf.ehcache</groupId>
              <artifactId>ehcache</artifactId>
              <exclusions>
                  <exclusion>
                      <groupId>org.slf4j</groupId>
                      <artifactId>slf4j-api</artifactId>
                  </exclusion>
              </exclusions>
          </dependency>-->

        <!--    <dependency>
     <groupId>org.hibernate</groupId>
     <artifactId>hibernate-ehcache</artifactId>
     <version>5.6.15.Final</version>
     <exclusions>
     <exclusion>
     <groupId>org.hibernate</groupId>
     <artifactId>hibernate-core</artifactId>
     </exclusion>
     </exclusions>
     </dependency>-->

        <!-- email dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- Spring Boot -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        </dependency>
        <!--spring
        framework oxm-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-oxm</artifactId>
            <version>${springframework.version}</version>
        </dependency>
        <!--spring
        framework jms-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jms</artifactId>
            <version>${springframework.version}</version>
        </dependency>
        <!--jackson-->
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-hibernate6</artifactId>
            <version>${jackson.version}</version>
        </dependency>

        <!-- JSR 349 validation dependency -->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>${hibernate-validator.version}</version>
        </dependency>
        <!--hibernate
        bean validatior need this-->
        <!-- https://mvnrepository.com/artifact/jakarta.el/jakarta.el-api -->
        <dependency>
            <groupId>jakarta.el</groupId>
            <artifactId>jakarta.el-api</artifactId>
            <version>6.0.0</version>
        </dependency>


        <!-- https://mvnrepository.com/artifact/org.glassfish/jakarta.el -->
        <dependency>
            <groupId>org.glassfish</groupId>
            <artifactId>jakarta.el</artifactId>
            <version>4.0.2</version>
            <scope>test</scope>
        </dependency>


        <!-- Guava dependency -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>

        <!-- SolrJ client library -->
        <dependency>
            <groupId>org.apache.solr</groupId>
            <artifactId>solr-solrj</artifactId>
            <version>${solrj.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>javax.servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Joda time dependency -->
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>${joda-time.version}</version>
        </dependency>
        <!--  <dependency>
              <groupId>joda-time</groupId>
              <artifactId>joda-time-jsptags</artifactId>
              <version>${joda-time-jsptags.version}</version>
          </dependency>-->
        <!--only
        used in test env-->
        <dependency>
            <groupId>org.jadira.usertype</groupId>
            <artifactId>usertype.extended</artifactId>
            <version>${jadira.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-core</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- freemarker dependency -->
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>${freemarker.version}</version>
        </dependency>


        <dependency>
            <groupId>jakarta.activation</groupId>
            <artifactId>jakarta.activation-api</artifactId>
            <version>2.1.3</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>${quartz.version}</version>
        </dependency>

        <!-- Servlet/JSP dependencies -->
        <!--  <dependency>
              <groupId>javax.servlet</groupId>
              <artifactId>javax.servlet-api</artifactId>
              <scope>provided</scope>
          </dependency>-->
        <!--  <dependency>
              <groupId>org.apache.tomcat.embed</groupId>
              <artifactId>tomcat-embed-jasper</artifactId>
              <scope>provided</scope>
          </dependency>-->

        <!--   <dependency>
               <groupId>org.apache.tomcat.embed</groupId>
               <artifactId>tomcat-embed-jasper</artifactId>
               <scope>provided</scope>
               <optional>true</optional>
           </dependency>-->

        <!-- JSTL dependencies -->

        <!-- https://mvnrepository.com/artifact/jakarta.servlet/jakarta.servlet-api -->
        <!--  <dependency>
              <groupId>jakarta.servlet</groupId>
              <artifactId>jakarta.servlet-api</artifactId>
              <version>6.0.0</version>
              <scope>provided</scope>
          </dependency>-->


        <!--<dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-jasper</artifactId>
            &lt;!&ndash;  <scope>provided</scope>&ndash;&gt;
        </dependency>-->

        <!-- https://mvnrepository.com/artifact/javax.servlet/jstl -->
        <!--  <dependency>
              <groupId>javax.servlet</groupId>
              <artifactId>jstl</artifactId>
              <version>1.2</version>
          </dependency>-->

        <!--  <dependency>
              <groupId>org.apache.tomcat</groupId>
              <artifactId>tomcat-jasper</artifactId>
              <version>9.0.1</version>
          </dependency>-->


        <!--  <dependency>
              <groupId>jakarta.servlet.jsp.jstl</groupId>
              <artifactId>jakarta.servlet.jsp.jstl-api</artifactId>
              <version>3.0.1</version>
          </dependency>

          <dependency>
              <groupId>org.glassfish.web</groupId>
              <artifactId>jakarta.servlet.jsp.jstl</artifactId>
              <version>3.0.1</version>
          </dependency>

          <dependency>
              <groupId>jakarta.servlet.jsp</groupId>
              <artifactId>jakarta.servlet.jsp-api</artifactId>
              <version>3.1.0</version>
              <scope>provided</scope>
          </dependency>-->


        <!--  https://mvnrepository.com/artifact/jakarta.servlet/jakarta.servlet-api
         <dependency>
             <groupId>jakarta.servlet</groupId>
             <artifactId>jakarta.servlet-api</artifactId>
             <version>6.0.0</version>
             <scope>provided</scope>
         </dependency>-->


        <!-- jasperreports dependency -->
        <!--  <dependency>
              <groupId>net.sf.jasperreports</groupId>
              <artifactId>jasperreports</artifactId>
              <version>${jasperreports.version}</version>
              <exclusions>
                  &lt;!&ndash; Exclude commons-logging in favour of SLF4j &ndash;&gt;
                  <exclusion>
                      <groupId>commons-logging</groupId>
                      <artifactId>commons-logging</artifactId>
                  </exclusion>
              </exclusions>
          </dependency>
          <dependency>
              <groupId>net.sf.jasperreports</groupId>
              <artifactId>jasperreports-fonts</artifactId>
              <version>${jasperreports.fonts.version}</version>
          </dependency>-->
        <!-- Itext dependency -->
        <!-- <dependency>
             <groupId>com.lowagie</groupId>
             <artifactId>itext</artifactId>
             <version>2.1.7</version>
         </dependency>-->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.0.6</version>
        </dependency>

        <!-- JiBX dependencies -->
        <dependency>
            <groupId>org.jibx</groupId>
            <artifactId>jibx-run</artifactId>
            <version>${jibx.version}</version>
        </dependency>
        <!--		jaxb2-->
        <dependency>
            <groupId>org.jvnet.jaxb2_commons</groupId>
            <artifactId>jaxb2-basics-runtime</artifactId>
            <version>1.11.1</version>
        </dependency>


        <!-- Jsch dependency -->
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>${jsch.version}</version>
        </dependency>
        <!-- supercsv dependency -->
        <dependency>
            <groupId>net.sf.supercsv</groupId>
            <artifactId>super-csv</artifactId>
            <version>${supercsv.version}</version>
        </dependency>

        <!-- Testing env dependencies -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${springframework.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-core</artifactId>
            <version>1.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hsqldb</groupId>
            <artifactId>hsqldb</artifactId>
            <version>2.3.0</version>
            <scope>test</scope>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.tomcat.embed/tomcat-embed-core -->
        <!-- <dependency>
             <groupId>org.apache.tomcat.embed</groupId>
             <artifactId>tomcat-embed-core</artifactId>
             <version>9.0.83</version>
         </dependency>-->

        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
            <version>5.1.3</version> <!-- Update to the latest version if needed -->
        </dependency>


        <!-- https://mvnrepository.com/artifact/org.apache.tomcat/tomcat-dbcp -->
        <!--
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-dbcp</artifactId>
            <version>10.1.7</version>
        </dependency>
-->


        <dependency>
            <groupId>org.apache.solr</groupId>
            <artifactId>solr-test-framework</artifactId>
            <version>${solrj.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.solr</groupId>
            <artifactId>solr-dataimporthandler</artifactId>
            <version>${solrj.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.icegreen</groupId>
            <artifactId>greenmail</artifactId>
            <version>1.3.1b</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.activemq</groupId>
            <artifactId>activemq-client</artifactId>
            <version>${activemq.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.activemq</groupId>
            <artifactId>activemq-broker</artifactId>
            <version>${activemq.version}</version>
            <scope>test</scope>
        </dependency>
        <!-- Excel dependency -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>3.17</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.17</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.bouncycastle/bcprov-jdk15on -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpg-jdk15on</artifactId>
            <version>1.47</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.oracle.database.jdbc/ojdbc8 -->
        <!-- https://mvnrepository.com/artifact/com.oracle.database.jdbc/ojdbc11 -->
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc11</artifactId>
            <version>23.5.0.24.07</version>
        </dependency>


        <!--
        https://mvnrepository.com/artifact/org.elasticsearch.client/elasticsearch-rest-high-level-client -->
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>${elasticsearch.version}</version>
            <exclusions>
                <!-- Exclude commons-logging in favour of SLF4j -->
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>

        </dependency>

        <!-- Exclude conflicting Lucene dependency -->
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>${elasticsearch.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.lucene</groupId>
                    <artifactId>lucene-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Add compatible Lucene dependency -->
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-core</artifactId>
            <version>8.6.3</version>
        </dependency>

        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <version>6.0.0</version>
            <!--  <scope>provided</scope>-->
        </dependency>
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
            <version>4.0.0</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/jakarta.xml.bind/jakarta.xml.bind-api-parent -->
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api-parent</artifactId>
            <version>4.0.0</version>
            <type>pom</type>
        </dependency>
        <!-- https://mvnrepository.com/artifact/jakarta.xml.bind/jakarta.xml.bind-api-test -->
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api-test</artifactId>
            <version>3.0.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/jakarta.platform/jakarta.jakartaee-api -->
        <dependency>
            <groupId>jakarta.platform</groupId>
            <artifactId>jakarta.jakartaee-api</artifactId>
            <version>10.0.0</version>
            <scope>provided</scope>
        </dependency>

        <!-- https://mvnrepository.com/artifact/jakarta.annotation/jakarta.annotation-api -->
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
            <version>3.0.0</version>
        </dependency>


        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.14.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.amazonaws/aws-java-sdk-signer -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-signer</artifactId>
            <version>1.12.90</version>
            <exclusions>
                <!-- Exclude commons-logging in favour of SLF4j -->
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- https://mvnrepository.com/artifact/commons-beanutils/commons-beanutils -->
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.9.4</version>
            <exclusions>
                <!-- Exclude commons-logging in favour of SLF4j -->
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- https://mvnrepository.com/artifact/commons-collections/commons-collections -->
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.2</version>
        </dependency>

        <!-- JAXB API -->


        <!-- JAXB Runtime -->
        <!-- https://mvnrepository.com/artifact/org.glassfish.jaxb/jaxb-runtime -->
        <!-- <dependency>
             <groupId>org.glassfish.jaxb</groupId>
             <artifactId>jaxb-runtime</artifactId>
             <version>4.0.4</version>
         </dependency>



         &lt;!&ndash; Activation Framework (necessary for JAXB) &ndash;&gt;
         &lt;!&ndash; https://mvnrepository.com/artifact/com.sun.activation/javax.activation &ndash;&gt;
         <dependency>
             <groupId>com.sun.activation</groupId>
             <artifactId>javax.activation</artifactId>
             <version>1.2.0</version>
         </dependency>-->

        <!-- https://mvnrepository.com/artifact/net.sf.jasperreports/jasperreports -->
        <dependency>
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports</artifactId>
            <version>${jasperreports.version}</version>
            <exclusions>
                <!-- <exclusion>
                     <groupId>com.lowagie</groupId>
                     <artifactId>itext</artifactId>
                 </exclusion>-->
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--    <dependency>
                <groupId>org.restlet.jee</groupId>
                <artifactId>org.restlet</artifactId>
                <version>2.1.1</version>
                <scope>system</scope>
                <systemPath>${project.basedir}/libs/server_lib/org.restlet-2.1.1.jar</systemPath>
            </dependency>-->
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>jakarta.mail</artifactId>
            <version>2.0.1</version>
        </dependency>
        <dependency>
            <groupId>jakarta.mail</groupId>
            <artifactId>jakarta.mail-api</artifactId>
            <version>2.0.1</version> <!-- Latest version -->
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-devtools -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <version>3.4.1</version>
        </dependency>


        <!--
        https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-actuator -->
        <!--  <dependency>
              <groupId>org.springframework.boot</groupId>
              <artifactId>spring-boot-starter-actuator</artifactId>
              <version>3.2.2</version>
          </dependency>-->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>2.20.0</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-logs</artifactId>
            <version>1.11.1000</version>
        </dependency>
        <dependency>
            <groupId>pro.apphub</groupId>
            <artifactId>aws-cloudwatch-log4j2</artifactId>
            <version>2.5.0</version>
        </dependency>


    </dependencies>

    <repositories>
        <repository>
            <id>central</id>
            <url>https://repo.maven.apache.org/maven2</url>
        </repository>
    </repositories>


    <build>
        <defaultGoal>package</defaultGoal>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>**/*.jrxml</exclude>
                    <exclude>**/node_modules/**</exclude>
                </excludes>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>true</filtering>
            </testResource>
            <testResource>
                <directory>sql</directory>
                <filtering>true</filtering>
            </testResource>
        </testResources>

        <plugins>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.2.2</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- compiler plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>


            <!-- resources plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.6</version>
            </plugin>

            <!-- run unit or integration test plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.15</version>
                <configuration>
                    <argLine>-Xmx256m -XX:MaxPermSize=128m</argLine>
                    <systemPropertyVariables>
                        <solr.solr.home>${project.build.testOutputDirectory}/solr</solr.solr.home>
                    </systemPropertyVariables>
                    <skipTests>${skipTests}</skipTests>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.maven.surefire</groupId>
                        <artifactId>surefire-junit47</artifactId>
                        <version>2.15</version>
                    </dependency>
                </dependencies>
            </plugin>

            <!-- war package plugin -->
            <!--  <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-war-plugin</artifactId>
                  <configuration>
                      <warName>${project.artifactId}</warName>
                      <warSourceExcludes>**/node_modules/**</warSourceExcludes>
                      <webResources>
                          <resource>
                              <directory>src/main/webapp</directory>
                              <filtering>true</filtering>
                              <includes>
                                  <include>**/*.jsp</include>
                              </includes>
                          </resource>
                      </webResources>

                  </configuration>
              </plugin>-->

            <!-- clean plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <configuration>
                    <filesets>
                        <fileset>
                            <directory>${jasperreports.output.path}</directory>
                            <followSymlinks>false</followSymlinks>
                            <useDefaultExcludes>true</useDefaultExcludes>
                            <includes>
                                <include>*.jasper</include>
                            </includes>
                        </fileset>
                    </filesets>
                </configuration>
            </plugin>

            <!-- enforcer plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <id>enforce-banned-dependencies</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireJavaVersion>
                                    <version>17</version>
                                </requireJavaVersion>
                                <bannedDependencies>
                                    <searchTransitive>true</searchTransitive>
                                    <excludes>
                                        <exclude>commons-logging</exclude>
                                        <exclude>aspectj:aspectj*</exclude>
                                        <exclude>org.springframework:2.*</exclude>
                                        <exclude>org.springframework:3.0.*</exclude>
                                    </excludes>
                                </bannedDependencies>
                            </rules>
                            <fail>true</fail>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- cobertura plugin -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
                <version>2.5.2</version>
                <configuration>
                    <instrumentation>
                        <excludes>
                            <exclude>**/model/**/*.class</exclude>
                            <exclude>**/*Controller.class</exclude>
                        </excludes>
                    </instrumentation>
                    <check />
                </configuration>
            </plugin>

            <!-- Replacer plugin: Modify SQL scripts to remove Oracle/HSQL incompatibilities -->
            <plugin>
                <groupId>com.google.code.maven-replacer-plugin</groupId>
                <artifactId>replacer</artifactId>
                <version>1.5.2</version>
                <executions>
                    <execution>
                        <id>forTesting</id>
                        <phase>process-test-resources</phase>
                        <goals>
                            <goal>replace</goal>
                        </goals>
                        <configuration>
                            <includes>
                                <include>target/test-classes/00_create_schema.*</include>
                                <include>target/test-classes/sql/10_create_junit_test_environment.*</include>
                            </includes>
                            <replacements>
                                <replacement>
                                    <token>MAXVALUE[\n ]+9+</token>
                                    <value>NOMAXVALUE</value>
                                </replacement>
                                <replacement>
                                    <token>CASCADE[\n ]CONSTRAINTS</token>
                                    <value>CASCADE</value>
                                </replacement>
                                <replacement>
                                    <token>TIMESTAMP WITH LOCAL TIME ZONE</token>
                                    <value>TIMESTAMP WITH TIME ZONE</value>
                                </replacement>
                                <replacement>
                                    <token>LOGGING ;</token>
                                    <value>;</value>
                                </replacement>
                                <replacement>
                                    <token>NOT[ \n]DEFERRABLE</token>
                                    <value></value>
                                </replacement>
                                <replacement>
                                    <token>(\.\d{3})(\d{6}|\d{3}) ([AP]M)</token>
                                    <value>$1 $3</value>
                                </replacement>
                                <replacement>
                                    <token>\.SSXFF</token>
                                    <value>\.SS\.FF</value>
                                </replacement>
                                <replacement>
                                    <token>-RR</token>
                                    <value>-YY</value>
                                </replacement>
                                <replacement>
                                    <token>to_timestamp_tz\('(\d{2})-(\w{3})-(\d{2})
                                        (\d{2})\.(\d{2})\.(\d{2})\.(\d{3})
                                        ([AP]M) ([+-]\d{2}:\d{2})','DD-MON-YY HH\.MI\.SS\.FF AM
                                        TZR'\)
                                    </token>
                                    <value>TIMESTAMP(to_char(TO_TIMESTAMP\('$1-$2-$3 $4.$5.$6.$7
                                        $8','DD-MON-YY
                                        HH\.MI\.SS\.FF AM'\),'YYYY-MM-DD HH24:MI:SS.FF') || '$9')
                                    </value>
                                </replacement>
                            </replacements>
                            <regexFlags>
                                <regexFlag>CASE_INSENSITIVE</regexFlag>
                                <regexFlag>MULTILINE</regexFlag>
                            </regexFlags>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Compile jrxml files -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>jasperreports-maven-plugin</artifactId>
                <version>1.0-beta-2</version>
                <configuration>
                    <outputDirectory>${jasperreports.output.path}</outputDirectory>
                    <sourceDirectory>src/main/resources/jasperreports</sourceDirectory>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile-reports</goal>
                        </goals>
                        <phase>test-compile</phase>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>net.sf.jasperreports</groupId>
                        <artifactId>jasperreports</artifactId>
                        <version>${jasperreports.version}</version>
                        <exclusions>
                            <!--  <exclusion>
                                  <groupId>com.lowagie</groupId>
                                  <artifactId>itext</artifactId>
                              </exclusion>-->
                            <exclusion>
                                <groupId>commons-logging</groupId>
                                <artifactId>commons-logging</artifactId>
                            </exclusion>
                        </exclusions>
                    </dependency>
                    <!--   <dependency>
                           <groupId>org.slf4j</groupId>
                           <artifactId>jcl-over-slf4j</artifactId>
                           <version>${slf4j.version}</version>
                       </dependency>-->
                    <!--   <dependency>
                           <groupId>org.slf4j</groupId>
                           <artifactId>slf4j-simple</artifactId>
                           <version>${slf4j.version}</version>
                       </dependency>-->
                </dependencies>
            </plugin>

            <!-- Using JiBX for XSD/Java mapping -->
            <plugin>
                <groupId>org.jibx</groupId>
                <artifactId>jibx-maven-plugin</artifactId>
                <version>${jibx.version}</version>
                <executions>
                    <execution>
                        <id>generate-java-from-xsd</id>
                        <goals>
                            <goal>schema-codegen</goal>
                        </goals>
                        <configuration>
                            <customizations>
                                <customization>src/main/config/jixb-customize.xml</customization>
                            </customizations>
                        </configuration>
                    </execution>
                    <execution>
                        <id>bind-generated-java-code</id>
                        <goals>
                            <goal>bind</goal>
                        </goals>
                        <configuration>
                            <schemaBindingDirectory>${project.build.directory}/generated-sources
                            </schemaBindingDirectory>
                            <load>true</load>
                            <validate>true</validate>
                            <verify>false</verify>
                            <verbose>false</verbose>
                        </configuration>
                    </execution>
                </executions>

                <!-- Changes made for GSSP-160 -->
                <dependencies>
                    <dependency>
                        <groupId>org.jibx</groupId>
                        <artifactId>jibx-bind</artifactId>
                        <version>${jibx.version}</version>
                        <exclusions>
                            <exclusion>
                                <artifactId>bcel</artifactId>
                                <groupId>bcel</groupId>
                            </exclusion>
                        </exclusions>
                    </dependency>
                    <dependency>
                        <groupId>org.apache.bcel</groupId>
                        <artifactId>bcel</artifactId>
                        <version>6.4.1</version>
                    </dependency>


                    <dependency>
                        <groupId>org.apache.bcel</groupId>
                        <artifactId>bcel</artifactId>
                        <version>6.0</version>
                    </dependency>
                </dependencies>
                <!-- End of  Changes made for GSSP-160 -->
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>production</id>
            <dependencies>
                <!-- Using Oracle for clustering in Production target -->
                <!-- <dependency>
                    <groupId>org.quartz-scheduler</groupId>
                    <artifactId>quartz-oracle</artifactId>
                    <version>${quartz-oracle.version}</version>
                </dependency>-->
            </dependencies>
        </profile>

        <profile>
            <id>dev</id>
            <dependencies>
                <dependency>
                    <groupId>org.apache.activemq</groupId>
                    <artifactId>activemq-client</artifactId>
                    <version>${activemq.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.apache.activemq</groupId>
                    <artifactId>activemq-broker</artifactId>
                    <version>${activemq.version}</version>
                </dependency>
            </dependencies>
        </profile>

        <!-- This profile prevents surefire from executing integration tests
             that are problematic to run in certain scenarios.

             1. When on-site at client, remote tests fail for network issues
                - RemoteDataIntegrationServiceTest
                - EmployeeDataIntegrationServiceTest
                - LocationDataIntegrationServiceTest
        -->
        <profile>
            <id>skip-integration-tests</id>
            <properties>
                <exclude.tests>
                    %regex[.*(RemoteData|Employee|Location|Customer)IntegrationServiceTest.*]</exclude.tests>
            </properties>
        </profile>

        <profile>
            <id>init-dev-database</id>
            <build>
                <pluginManagement>
                    <plugins>
                        <!--springboot
                        system scope compile-->
                        <plugin>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-maven-plugin</artifactId>
                            <version>3.2.2</version>
                            <configuration>

                                <!-- <port>9001</port>-->
                            </configuration>
                            <executions>
                                <execution>
                                    <goals>
                                        <goal>repackage</goal>
                                    </goals>
                                </execution>
                            </executions>
                        </plugin>

                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-resources-plugin</artifactId>
                            <executions>
                                <execution>
                                    <id>copy-sql-script-for-init-database</id>
                                    <phase>package</phase>
                                    <goals>
                                        <goal>copy-resources</goal>
                                    </goals>
                                    <configuration>
                                        <encoding>UTF-8</encoding>
                                        <outputDirectory>${project.build.directory}/sql</outputDirectory>
                                        <resources>
                                            <resource>
                                                <directory>sql/</directory>
                                                <includes>
                                                    <include>**/*.sql</include>
                                                </includes>
                                                <filtering>true</filtering>
                                            </resource>
                                        </resources>
                                    </configuration>
                                </execution>
                            </executions>
                        </plugin>

                        <!-- Replacer plugin: Modify SQL scripts to avoid error -->
                        <plugin>
                            <groupId>com.google.code.maven-replacer-plugin</groupId>
                            <artifactId>replacer</artifactId>
                            <version>1.5.2</version>
                            <executions>
                                <execution>
                                    <id>forInitDevDatabase</id>
                                    <phase>package</phase>
                                    <goals>
                                        <goal>replace</goal>
                                    </goals>
                                    <configuration>
                                        <includes>
                                            <include>target/sql/99_development_setup.sql</include>
                                            <include>target/sql/patches/*.sql</include>
                                        </includes>
                                        <replacements>
                                            <replacement>
                                                <token>\+00\:00</token>
                                                <value>${user.timezone}</value>
                                            </replacement>
                                            <replacement>
                                                <token>whenever sqlerror .*</token>
                                                <value></value>
                                            </replacement>
                                        </replacements>
                                        <regexFlags>
                                            <regexFlag>CASE_INSENSITIVE</regexFlag>
                                            <regexFlag>MULTILINE</regexFlag>
                                        </regexFlags>
                                    </configuration>
                                </execution>
                            </executions>
                        </plugin>
                        <plugin>
                            <groupId>org.codehaus.mojo</groupId>
                            <artifactId>sql-maven-plugin</artifactId>
                            <version>1.5</version>

                            <!--  <dependencies>
                                  &lt;!&ndash; specify the dependent jdbc driver here &ndash;&gt;
                                  &lt;!&ndash; 	<dependency>
                                          <groupId>com.oracle</groupId>
                                          <artifactId>ojdbc6</artifactId>
                                          <version>0</version>
                                          <scope>system</scope>
                                          <systemPath>libs/server_lib/ojdbc6.jar</systemPath>
                                      </dependency> &ndash;&gt;
                                  <dependency>
                                      <groupId>com.oracle.database.jdbc</groupId>
                                      <artifactId>ojdbc8</artifactId>
                                      <version>19.8.0.0</version>
                                  </dependency>
                              </dependencies>-->

                            <!-- common configuration shared by all executions -->
                            <!--   <configuration>
                                   <driver>oracle.jdbc.OracleDriver</driver>
                                   <url>*******************************************************************************
                                   </url>
                                   <username>gcstudio</username>
                                   <password>MGU86#nJA4ZbK4H</password>
                                   <autocommit>true</autocommit>
                                   &lt;!&ndash; ignore error when database is not avaiable &ndash;&gt;
                                   <onError>continue</onError>
                               </configuration>-->
                            <executions>
                                <execution>
                                    <phase>package</phase>
                                    <goals>
                                        <goal>execute</goal>
                                    </goals>
                                    <!-- specific configuration for this execution -->
                                    <configuration>
                                        <!-- specify your SQL commands, can be all of the following
                                        configurations -->
                                        <srcFiles>
                                            <srcFile>target/sql/00_create_schema.sql</srcFile>
                                            <srcFile>target/sql/01_populate_reference_values.sql</srcFile>
                                            <srcFile>target/sql/02_quartz_schema_prod.sql</srcFile>
                                            <srcFile>target/sql/03_data_migration_tables.sql</srcFile>
                                            <srcFile>target/sql/99_development_setup.sql</srcFile>
                                            <srcFile>
                                                target/sql/patches/02_home_recording_setup_gcss-547.sql</srcFile>
                                            <srcFile>
                                                target/sql/patches/03_add_break_activity_gcss-549.sql</srcFile>
                                            <srcFile>
                                                target/sql/patches/11_update_sparring_partner_mapping_TimeTrade_gcss-587.sql
                                            </srcFile>
                                            <srcFile>
                                                target/sql/patches/12_make_unending_series_gcss-591.sql</srcFile>
                                            <srcFile>
                                                target/sql/patches/13_create_time_off_table_gcss-590.sql</srcFile>
                                            <srcFile>
                                                target/sql/patches/19_create_one_time_availability_table_gcss-650.sql
                                            </srcFile>
                                        </srcFiles>
                                    </configuration>
                                </execution>
                            </executions>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
    </profiles>
</project>