Docker running notes

docker build -t scheduler-app ./
docker run -p 8080:8080 scheduler-app
docker run -p 8080:8080 -d scheduler-app
docker ps
docker stop <container_id>
docker rm <container_id>
docker rmi <image_id>
docker exec -it <container_id> /bin/bash
docker logs <container_id>
docker logs -f <container_id>
docker logs -f --tail 10 <container_id>
docker logs -f --tail 10 -t <container_id>
docker logs -f --tail 10 -t --since 2020-07-01T12:00:00 <container_id>
docker logs -f --tail 10 -t --since 2020-07-01T12:00:00 --until 2020-07-01T13:00:00 <container_id>
docker logs -f --tail 10 -t --since 2020-07-01T12:00:00 --until 2020-07-01T13:00:00 --timestamps <container_id>
docker logs -f --tail 10 -t --since 2020-07-01T12:00:00 --until 2020-07-01T13:00:00 --timestamps --details <container_id>
docker logs -f --tail 10 -t --since 2020-07-01T12:00:00 --until 2020-07-01T13:00:00 --timestamps --details --follow <container_id>
